<?php

namespace Nzoom\Mvc\ControllerTrait;

trait MultiActionTrait
{
    private function MultiActionFinnish() : void
    {
        /** @var \Messages $msg */
        $msg = $this->registry['messages'];
        /** @var \Request $request */
        $request = $this->registry['request'];
        $this->actionCompleted = true;

        if ($request->isAcceptHtml()) {
            $msg->insertInSession($this->registry);
            return;
        }

        if ($request->isAcceptJson()) {
            $data = $msg->getAll();

            $responseCode = http_response_code();
            // Make sure to set 400 if an error occured, but allow to have an error code set from the caller
            if (array_key_exists('errors', $data) && $data['errors'] && http_response_code() < 400) {
                $responseCode = 400;
            }
            header('Content-Type: application/json', true);
            $this->completeAction($responseCode, json_encode($data));
            return;
        }
    }
}
