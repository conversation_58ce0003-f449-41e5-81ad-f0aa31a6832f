##############################################################################################
### SQL nZoom Specific Updates Евролинк Мениджмънт ООД (http://eurolink.n-zoom.com/) ###
##############################################################################################

######################################################################################
# 2016-07-12 - Added report - 'due_or_recoverable_vat'

# Added report - 'due_or_recoverable_vat'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (220, 'due_or_recoverable_vat', 'allows_files_generation := 1\r\nfile_origin := self\r\nhide_export_default := 0', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (220, 'Дължимо/възстановимо ДДС', '01. Финанси', NULL, 'bg'),
  (220, 'Due/recoverable VAT', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '220', '0', '1'),
  ('reports', 'export', '220', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '220';

DELETE pi18n.*, p.* FROM `placeholders` AS p, `placeholders_i18n` AS pi18n
WHERE p.`id` = pi18n.`parent_id` AND p.`model` = 'Report' AND p.`pattern_id` = ',220,';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'invoice_expenses_and_incomes', 'Report', 'send', 'all', ',220,', 'invoice_expenses_and_incomes', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разходи/приходи по фактури (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Expenses/revenues by invoices (table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'due_or_recoverable_vat', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Due/recoverable VAT (table)', NULL, 'en');

DELETE pi18n.*, p.* FROM `patterns` AS p, `patterns_i18n` AS pi18n
WHERE p.`id` = pi18n.`parent_id` AND p.`model` = 'Report' AND p.`model_type` = 220;

INSERT INTO `patterns` (`id`, `model`, `model_type`, `section`, `list`, `for_printform`, `company`, `position`, `header`, `footer`, `background_image`, `background_image_position`, `landscape`, `prefix`, `format`, `force_generate`, `not_regenerate_finished_record`, `plugin`, `handover_direction`, `is_portal`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
  (NULL, 'Report', '220', 0, 0, 0, 0, 1, 0, 0, NULL, 'left_top', 0, '[current_date]_[name]', 'pdf', 0, 0, 0, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT INTO `patterns_i18n` (`parent_id`, `name`, `description`, `content`, `lang`, `translated`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС', '', '<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n', 'bg', NOW()),
  (LAST_INSERT_ID(), 'Due/recoverable VAT', '', '<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n', 'en', NOW());

######################################################################################
# 2016-08-05 - Added automations to translate records

# Added automation to translate records
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на документи', 0, NULL, 1, 'documents', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на контрагенти', 0, NULL, 1, 'customers', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на договори', 0, NULL, 1, 'contracts', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на приходни финансови документи', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на разходни финансови документи', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);

######################################################################################
# 2016-08-22 - Added new pattern plugin for documents preparing financial data
#            - Added placeholders for the pattern plugin

# Added new pattern plugin for documents preparing financial data
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(81, 'Document', 0, 'finance', 'prepareFinanceDetails', 'bank_account_var := bank_acc', NULL, NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(81, 'Подготовка на финансови данни', 'Подготвят се данни за банкови сметки, каси и пр..', 'bg', NOW()),
(81, 'Preparation of financial data', 'Preparation of data for bank accounts, cashboxes, etc.', 'en', NOW());

# Added placeholders for the pattern plugin
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('bank_account_name', 'Document', 'basic', 'pattern_plugins', ',81,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Банкова сметка', 'bg'),
  (LAST_INSERT_ID(), 'Bank Account', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('bank_account_bank', 'Document', 'basic', 'pattern_plugins', ',81,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Банкa', 'bg'),
  (LAST_INSERT_ID(), 'Bank', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('bank_account_iban', 'Document', 'basic', 'pattern_plugins', ',81,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'IBAN', 'bg'),
  (LAST_INSERT_ID(), 'IBAN', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('bank_account_bic', 'Document', 'basic', 'pattern_plugins', ',81,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'BIC', 'bg'),
  (LAST_INSERT_ID(), 'BIC', 'en');

######################################################################################
# 2016-11-07 - Added new automation for recording history for reg num changes of vehicle in Eurolink installation (EUROLINK)

# Added new automation for recording history for reg num changes of vehicle in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('История за промяна на регистрационен номер', 0, NULL, 1, 'nomenclatures', NULL, 'action', '5', 'registration_num := reg_num\r\nedit_date := date_change\r\nedit_employee := employee_change\r\nedit_employee_id := employee_change_id\r\nregistration_old_num := car_number_old\r\nregistration_new_num := car_number\r\ncontract_client := client_change\r\ncontract_client_id := client_change_id\r\n\r\ncontract_type_id := 1\r\ncontract_car_var := mark_model_id\r\ncontract_substatus_signed := 1\r\n\r\ncontract_client_tbl := assignor_man\r\ncontract_client_id_tbl := assignor_man_id', 'condition := ''[a_reg_num]'' != ''[prev_a_reg_num]''', 'plugin := eurolink\r\nmethod := writeHistoryChangingRegNum', NULL, 1, 0, 1);

######################################################################################
# 2016-11-08 - Added new automation for creating new paying tax document (incomes reason) when paying tax document (expenses reason) is finished in Eurolink installation (EUROLINK)

# Added new automation for creating new paying tax document (incomes reason) when paying tax document (expenses reason) is finished in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Създаване на документ за плащане на данъци на МПС', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '103', 'reason_type_id := 104', 'condition := ''[prev_b_status]'' != ''finished'' && ''[b_status]'' == ''finished''', 'plugin := eurolink\r\nmethod := createTaxPayment', NULL, 1, 1, 1);

######################################################################################
# 2016-11-09 - Added two new automations for nor allowing changing the car in the added annex for a contract and to check if the car in contract is not included in another signed contract in Eurolink installation (EUROLINK)

# Added two new automations for nor allowing changing the car in the added annex for a contract and to check if the car in contract is not included in another signed contract in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация за МПС при подписване на договор', 0, NULL, 1, 'contracts', NULL, 'before_action', '1', 'car_id := mark_model_id\r\nsubstatus_signed := 1', 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry[''request'']->get(''substatus'') == ''closed_1''', 'plugin := eurolink\r\nmethod := validateStatusChange', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация за МПС при добавяне на споразумение към договор', 0, NULL, 1, 'contracts', NULL, 'before_action', '1', 'car_id := mark_model_id', 'condition := ''[request_is_post]'' && ''[action]'' == ''addannex''', 'plugin := eurolink\r\nmethod := validateCarInAnnex', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2016-11-10 - Added new automation to cancel finished contracts without future annexes and clear the renter of the car in the nomenclature in Eurolink installation (EUROLINK)

# Added new automation to cancel finished contracts without future annexes and clear the renter of the car in the nomenclature in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Прекратяване на договор след изтичане', 0, NULL, 1, 'contracts', NULL, 'crontab', '1', 'start_time := 03:00\r\nstart_before := 03:30\r\n\r\ncar_id := mark_model_id\r\nnom_car_id := assignor_man_id\r\nnom_car_name := assignor_man\r\nsubstatus_signed := 1\r\nsubstatus_finished := 2', 'condition := 1', 'plugin := eurolink\r\nmethod := changeFinishedContractsSubstatus', NULL, 1, 0, 1);

######################################################################################
# 2016-11-15 - Fixed conditions of the automation for validating car change in the annex of the contract in Eurolink installation (EUROLINK)
#            - Fixed validation of duplicating cars in contracts to exclude annex status change validation in Eurolink installation (EUROLINK)

# Fixed conditions of the automation for validating car change in the annex of the contract in Eurolink installation (EUROLINK)
UPDATE `automations` SET `conditions` = 'condition := ''[request_is_post]'' && (''[action]'' == ''addannex'' || (''[action]'' == ''edittopic'' && ''[b_subtype]'' == ''annex''))' WHERE `conditions` NOT LIKE '%b_subtype%' AND `method` LIKE '%eurolink%' AND `method` LIKE '%validateCarInAnnex%';

# Fixed validation of duplicating cars in contracts to exclude annex status change validation in Eurolink installation (EUROLINK)
UPDATE `automations` SET `conditions` = 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry[''request'']->get(''substatus'') == ''closed_1'' && ''[b_subtype]'' != ''annex''' WHERE `conditions` NOT LIKE '%b_subtype%' AND `method` LIKE '%eurolink%' AND `method` LIKE '%validateStatusChange%';

######################################################################################
# 2016-11-29 - Added new automation to write the assignor of the automobile when a contract is marked as signed in Eurolink installation (EUROLINK)

# Added new automation to write the assignor of the automobile when a contract is marked as signed in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Вписване на възложител при смяна на статус', 0, NULL, 1, 'contracts', NULL, 'action', '1', 'car_id := mark_model_id\r\n\r\ncontract_client_tbl := assignor_man\r\ncontract_client_id_tbl := assignor_man_id', 'condition := ''[b_substatus]'' == ''1'' && ''[prev_b_substatus]'' != ''1'' && ''[a_mark_model_id]'' != '''' && ''[b_customer]'' != ''''', 'plugin := eurolink\r\nmethod := recordCarAssignor', NULL, 1, 1, 1);

######################################################################################
# 2016-11-30 - Added new automation to validate the add/edit of Paying tax document for car in Eurolink installation (EUROLINK)

# Added new automation to validate the add/edit of Paying tax document for car in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на Плащане на данък МПС', 0, NULL, 1, 'finance', 'expenses_reasons', 'before_action', '103', 'nom_car_type_id := 5\r\nnom_car_var_tax := tax_to', 'condition := ''[request_is_post]'' && (''[action]'' == ''add'' || ''[action]'' == ''edit'')', 'plugin := eurolink\r\nmethod := validatePayingCarTax', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2016-12-01 - Added new automation to complete the tax validity for vehicle in Eurolink installation (EUROLINK)

# Added new automation to complete the tax validity for vehicle in Eurolink installation (EUROLINK)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Запис на дата, до която е платен данък на МПС', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '103', 'nom_car_var_tax := tax_to', 'condition := ''[prev_b_status]'' != ''finished'' && ''[b_status]'' == ''finished''', 'plugin := eurolink\r\nmethod := completeVehicleTaxValidity', NULL, 2, 1, 1);

######################################################################################
# 2017-05-05 - Corrected settings and added missing labels of additional variables
#            - Added new report 'eurolink_vehicle_file' to EUROLINK installation

# Corrected settings and added missing labels of additional variables
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, '<deleted_by>=0', '<deleted_by> => 0') WHERE `source` LIKE '%<deleted_by>=0%';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Vertragspartner ID', 'de'
FROM `_fields_meta` WHERE `model` = 'Nomenclature' AND `model_type` = 5 AND `name` = 'assignor_man_id';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Fahrzeuginhaber ID', 'de'
FROM `_fields_meta` WHERE `model` = 'Nomenclature' AND `model_type` = 5 AND `name` = 'car_owner_id';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Er machte die Änderung ID', 'de'
FROM `_fields_meta` WHERE `model` = 'Nomenclature' AND `model_type` = 5 AND `name` = 'employee_change_id';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Vertragspartner ID', 'de'
FROM `_fields_meta` WHERE `model` = 'Nomenclature' AND `model_type` = 5 AND `name` = 'client_change_id';

INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Datum der erste Rechnung', 'de'
FROM `_fields_meta` WHERE `model` = 'Contract' AND `model_type` = 1 AND `name` = 'first_invoice_date'
ON DUPLICATE KEY UPDATE `content` = VALUES(`content`);

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Fakturierungszeitraum', 'de'
FROM `_fields_meta` WHERE `model` = 'Contract' AND `model_type` = 1 AND `name` = 'invoice_period';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Gerade eine Rechnung ausgeben', 'de'
FROM `_fields_meta` WHERE `model` = 'Contract' AND `model_type` = 1 AND `name` = 'direct_invoice';

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
SELECT `id`, 'label', 'Kasse', 'de'
FROM `_fields_meta` WHERE `model` = 'Contract' AND `model_type` = 1 AND `name` = 'cash_contract';

INSERT IGNORE INTO `_fields_options` (`id`, `parent_name`, `label`, `option_value`, `child_name`, `extended_value`, `position`, `active_option`, `lang`)
SELECT fo.`id`, 'electronic_invoice', 'wollen', 1, '', '', 2, 1, 'de'
FROM `_fields_meta` fm
LEFT JOIN `_fields_options` fo 
  ON fm.name = fo.parent_name AND fo.`option_value` = 1 AND fo.`lang` = 'de'
WHERE fm.`name` = 'electronic_invoice' AND fm.`model` = 'Contract' AND fm.`model_type` = 1;

INSERT IGNORE INTO `_fields_options` (`id`, `parent_name`, `label`, `option_value`, `child_name`, `extended_value`, `position`, `active_option`, `lang`)
SELECT fo.`id`, 'electronic_invoice', 'nicht wollen', 2, '', '', 1, 1, 'de'
FROM `_fields_meta` fm
LEFT JOIN `_fields_options` fo 
  ON fm.name = fo.parent_name AND fo.`option_value` = 2 AND fo.`lang` = 'de'
WHERE fm.`name` = 'electronic_invoice' AND fm.`model` = 'Contract' AND fm.`model_type` = 1;

# Added new report 'eurolink_vehicle_file' to EUROLINK installation
SET @id := 363;
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'eurolink_vehicle_file', 'nom_type_vehicle := 5\r\nnom_type_make := 8\r\nnom_type_model := 9\r\ncust_type_client := 2\r\ncust_type_user := 7\r\ncon_type_lease := 1\r\ndoc_type_proxy := 4\r\nfin_incomes_type_deposit := 105\r\nfin_expenses_type_deposit := 106\r\nfin_incomes_types := 101,104,107\r\nfin_expenses_types := 102,103,20\r\n\r\nvehicle_make_var := make_type\r\nvehicle_make_id_var := make_type_id\r\nvehicle_model_var := model_car\r\nvehicle_model_id_var := model_car_id\r\nvehicle_assignor_var := assignor_man\r\nvehicle_assignor_id_var := assignor_man_id\r\nvehicle_user_var := car_user\r\nvehicle_user_id_var := car_user_id\r\nvehicle_reg_num_var := reg_num\r\nvehicle_reg_date_var := pps_year_reg\r\nvehicle_acquisition_date_var := car_date_start\r\nvehicle_price_net_var := price_car\r\nvehicle_price_gross_var := price_br\r\nvehicle_civil_liability_to_var := go_to\r\nvehicle_casco_to_var := casco_date_to\r\nvehicle_tax_to_var := tax_to\r\nvehicle_technical_inspection_to_var := technical_review_finish\r\nvehicle_warranty_to_var := term_garanture\r\nvehicle_warranty_extended_to := prolonged_warranty\r\nvehicle_file_var := car_file\r\nvehicle_file_type_var := car_file_document\r\n\r\ncontract_vehicle_id_var := mark_model_id\r\ncontract_invoice_period_var := invoice_period\r\ncontract_invoice_start_var := first_invoice_date\r\ncontract_deposit_amount_var := guar_total\r\ncontract_substatus_signed := 1\r\n\r\nproxy_vehicle_id_var := car_id\r\nproxy_person_var := person_proxy\r\n\r\nnom_lease_id := 25\r\ndefault_currency := EUR\r\nmin_char_len_search := 3', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Досие на автомобил', NULL, NULL, 'bg'),
(@id, 'Fahrzeug-Dossier', NULL, NULL, 'de');
INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;


######################################################################################
# 2017-06-06 - Added standard automation for logging history of customr type 2 (Bug 4523)

# Added standard automation for logging history of customr type 2 (Bug 4523)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'История за клиент', 0, NULL, 1, 'customers', NULL, 'action', '2', '# Настройки на променливи. \r\n# Вляво е променлива, която се одитира (свободната), а вдясно - поле от ГРУПОВА ТАБЛИЦА (ГТ) съхраняваща историята\r\n# history_log_var_<име на одитирана променлива> := <име на променлива в ГТ>\r\n# ВАЖНО: Имената на променливите не включват префикс b_ или a_ !\r\n#\r\n# Възможна е настройка за запис на стойността преди промяната (стара стойност) или след промяната (нова).\r\n#\r\n# За нова стойност: \r\n# history_log_var_<име на одитирана променлива> := <име на променлива в ГТ>\r\n#\r\n# За стара стойност:\r\n# history_log_<име на одитирана променлива_old> := <име на променлива в ГТ>\r\n\r\n# Системни променливи:\r\n# history_log_var_date_modified - дата на модификация (без час)\r\n# history_log_var_datetime_modified - дата и час на модификация\r\n# history_log_var_time_modified - час на модификация\r\n# history_log_var_user_modified - ид на потребител направил модификацията\r\n# history_log_var_user_name_modified - име на потребител направил модификацията\r\n# history_log_var_employee_modified - ид на служител отговарящ на потребителя направил модификацията\r\n# history_log_var_employee_name_modified - име на служител отговарящ на потребителя направил модификацията\r\n\r\n# Действащи настройки\r\nhistory_log_var_datetime_modified := to_date\r\nhistory_log_var_mol_old := old_mol\r\nhistory_log_var_ucn_client_old := old_ucn\r\nhistory_log_var_card_client_old := old_idcard\r\nhistory_log_var_issued_card_old := old_issue_from\r\nhistory_log_var_card_date_old := old_issue\r\nhistory_log_var_expire_client_old := old_expire\r\nhistory_log_var_employee_modified := old_user_id\r\nhistory_log_var_employee_name_modified := old_user\r\n\r\n# Допълнителни настройки\r\n# не запазва при добавяне (по подразбиране работи при редакция И при добавяне)\r\n# history_log_skip_add := 1\r\n\r\n# запазва САМО променливите с променени стойности (за останалите се пише тире, дори и за стари стойности)\r\n# history_log_only_modified := 1\r\n\r\n# ако предходната настройка е установена в 1, то може да се укаже какъв да бъде символа в историята при непроменена стойност (по подразбиране е празен низ)\r\n# ВАЖНО: за radio/dropdowns/date/datetime/time винаги се записва празна стойност без значение от настоящата настройка \r\n# history_log_unmodified_value_string := -\r\n\r\n', 'condition := 1', 'method := writeCustomHistory', NULL, 1, 0, 1);

######################################################################################
# 2017-07-07 - Add new automation for adding finance documents for cars insurance

# Add new automation for adding finance documents for cars insurance
DELETE FROM automations WHERE method LIKE '%addCarInsuranceFinance%';
INSERT INTO automations
  SET name = 'Добавяне на ОР, ОП и Фактури за вноските по застраховките (какско и ГО) на автомобили предоставяни на клиенти',
    module = 'nomenclatures',
    start_model_type = '5',
    automation_type = 'crontab',
    conditions = 'condition := \'[b_active]\' == \'1\' && (\'[a_casco_finissue_date]\' == General::strftime($this->i18n(\'date_iso_short\')) || \'[a_go_finissue_date]\' == General::strftime($this->i18n(\'date_iso_short\')))',
    method = 'plugin := eurolink\r\nmethod := addCarInsuranceFinance',
    settings = 'start_time := 02:00\r\nstart_before := 05:00\r\n\r\ndate_of_payment_add_days := 10',
    nums = 0;

######################################################################################
# 2017-07-11 - Fixed name of automation addCarInsuranceFinance

# Fixed name of automation addCarInsuranceFinance
UPDATE automations
  SET name = 'Добавяне на ОР, ОП и Фактури за вноските по застраховките (каско и ГО) на автомобили предоставяни на клиенти'
  WHERE method LIKE '%addCarInsuranceFinance%';

######################################################################################
# 2017-07-17 - Change conditions of automation addCarInsuranceFinance

# Change conditions of automation addCarInsuranceFinance
UPDATE automations
  SET conditions = 'where := n.deleted = 0\r\nwhere := n.active = 1\r\nwhere := (SELECT 1 FROM nom_cstm AS nc WHERE nc.model_id = n.id AND nc.var_id IN (SELECT fm.id FROM _fields_meta AS fm WHERE fm.model = \'Nomenclature\' AND fm.model_type = n.type AND fm.name IN (\'casco_finissue_date\', \'go_finissue_date\')) AND DATE(nc.value) = CURDATE() LIMIT 1)'
  WHERE method LIKE '%addCarInsuranceFinance%'
    AND conditions NOT LIKE '%where%';
