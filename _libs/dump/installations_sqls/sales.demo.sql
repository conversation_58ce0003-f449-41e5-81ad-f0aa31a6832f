#############################################################################################
### SQL nZoom Specific Updates Commercial companies (http://sales.demo.n-zoom.com/) ###
#############################################################################################

######################################################################################
# 2011-05-26 - Added new report - 'payments_by_customer' - for Commercial companies installation (1747)

# Added new report - 'payments_by_customer' - for Commercial companies installation (1747)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(147 , 'payments_by_customer', 'income_reason_id:=101' , 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(147, 'Плащания по клиент', NULL, NULL, 'bg'),
(147, 'Payments by client', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 147, 0, 1),
('reports', 'export', 147, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=147;

######################################################################################
# 2011-08-02 - Added new report - 'investor_week_activity' - for Commercial companies installation (1747)

# Added new report - 'investor_week_activity' - for Commercial companies installation (1747)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(183, 'investor_week_activity', NULL , 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(183, 'Седмична активност', NULL, NULL, 'bg'),
(183, 'Week activity', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 183, 0, 1),
('reports', 'export', 183, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=183;

########################################################################
# 2014-03-05 - Updated settings for universal report 'timesheets_per_employees_and_customers'

# Updated settings for universal report 'timesheets_per_employees_and_customers'
UPDATE `reports` SET `settings`='documents_types := 1,2,5,6,7,8,9,10,26\r\nprojects_types := 1,2,3\r\ntasks_types := 2\r\n\r\nshow_billing_time := 0\r\ninclude_partial_periods := 0\r\n\r\nlawyer_installation := 0\r\n\r\n#allows_files_generation := 1\r\n#files_origin := self' WHERE `type`='timesheets_per_employees_and_customers';

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 10 HOUR) <= NOW()' WHERE `id` = 1;
