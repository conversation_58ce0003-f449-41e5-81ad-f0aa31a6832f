###########################################################################
### SQL nZoom Specific Updates - НИК ООД (http://nik.n-zoom.com/) ###
###########################################################################

########################################################################
# 2013-02-06 - Added dashlet plugin for quick adding of contracts for NIK installation (1759)

# Added dashlet plugin for quick adding of contracts for NIK installation (1759)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'nik_add_contract', '# Types\r\ncontract_type_contract := 1\r\ncustomer_type_client := 2\r\nnom_type_service := 5\r\nnom_type_machine_type := 6\r\n\r\n# Service nomenclature variables\r\nservice_category_type_var := category_type\r\nservice_machine_type_var := machine_type\r\nservice_reporting_period_var := reporting_period\r\n\r\n# Contract contract variables\r\ninvoice_firstdate_var := issue_invoce_firstdate\r\ninvoice_auto_var := issue_invoice_auto\r\n\r\n# Data for contract\r\ncontract_company := 1\r\ncontract_office := 1\r\ncontract_department := 1\r\n\r\n# Option values\r\nservice_machinery_tracking := 1\r\nissue_invoice_auto_yes := 1\r\nissue_invoice_auto_no := 2', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Бързо добавяне на договор', '', 'bg'),
(LAST_INSERT_ID(), 'Quick adding of contract', '', 'en');

########################################################################
# 2013-02-08 - Added plugin before_action automation for creation of invoices templates for contracts for NIK installation (1759)

# Added plugin before_action automation for creation of invoices templates for contracts for NIK installation (1759)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Създаване на шаблони за фактури от договор', 0, NULL, 1, 'contracts', NULL, 'before_action', 1, 'proforma := 0\r\npayment_type := bank\r\ncontainer_id := 1\r\nobserver := default_financial_contact_person\r\nrecurrence_period := month\r\nsingle_period_period := month\r\nsingle_period_length := 0\r\nsingle_period_rows := all_one\r\nfirst_period_invoice := full\r\nissue_start_count := 1\r\nissue_start_period_type := working\r\nissue_start_period := day\r\nissue_start_direction := after\r\nissue_start_point := start\r\nissue_date_count := 1\r\nissue_date_period_type := working\r\nissue_date_period := day\r\nissue_date_direction := after\r\nissue_date_point := start\r\n\r\nnom_type_service := 5\r\nservice_category_type_var := category_type\r\nservice_machinery_tracking := 1', 'condition := 1', 'plugin := nik\r\nmethod := createInvoicesTemplates', 'cancel_action_on_fail := 1', 1, 0);

######################################################################################
# 2014-05-13 - Added report for invoices

# Added report for invoices
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(278, 'biotrade_invoices', '', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(278, 'Фактури', NULL, NULL, 'bg'),
(278, 'Invoices', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '278', '0', '1'),
  ('reports', '', 'export',          '278', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '278';

######################################################################################
# 2014-09-29 - Removed unnecessary settings from automation for creation of invoices templates for contracts

# Removed unnecessary settings from automation for creation of invoices templates for contracts
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_type_service := 5\r\nservice_category_type_var := category_type\r\nservice_machinery_tracking := 1', '')
WHERE `method` LIKE 'plugin := nik\r\nmethod := createInvoicesTemplates' AND `settings` LIKE '%\r\n\r\nnom_type_service := 5\r\nservice_category_type_var := category_type\r\nservice_machinery_tracking := 1%';

######################################################################################
# 2014-10-03 - Added new automation for sending invoices as mail

# Added new automation for sending invoices as mail
INSERT INTO automations
  SET name = 'Изпращане на фактура при избор на таг',
    module = 'finance',
    controller = 'incomes_reasons',
    automation_type = 'action',
    start_model_type = '1',
    `settings` = 'tag := 2\r\ngenerate_pattern := 3\r\nemail_pattern := 44\r\nfield_cus_email := invoce_email_to',
    conditions = 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\'))',
    method = 'method := sendTaggedAsMail',
    nums = 1;

######################################################################################
# 2014-10-06 - Added automations taking care of the date of payment of invoices issued from contracts

# Added automations taking care of the date of payment of invoices issued from contracts
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Валидация на клиент', 0, NULL, 1, 'customers', NULL, 'before_action', '2', '', 'condition := 1', 'plugin := nik\r\nmethod := validateDateOfPayment', 'cancel_action_on_fail := 1', 0, 0, 1),
(NULL, 'Обновяване на падеж на договори на клиент', 0, NULL, 1, 'customers', NULL, 'action', '2', '', 'condition := [prev_a_maturity_value] != [a_maturity_value] || [prev_a_maturity_value_work_nonwork] != [a_maturity_value_work_nonwork]\r\ncondition := [a_maturity_value] !== \'\'', 'plugin := nik\r\nmethod := updateContractsDateOfPayment', '', 0, 0, 1);

######################################################################################
# 2014-10-13 - Updated settings of dashlet plugin for adding of contracts

# Updated settings of dashlet plugin for adding of contracts
UPDATE `dashlets_plugins` SET `settings`='# Types\r\ncontract_type_contract := 1\r\ncustomer_type_client := 2\r\nnom_type_service := 5\r\n\r\n# Service nomenclature variables\r\nservice_machine_type_var := machine_type\r\nservice_reporting_period_var := reporting_period\r\n\r\n# Contract contract variables\r\ninvoice_firstdate_var := issue_invoce_firstdate\r\ninvoice_auto_var := issue_invoice_auto\r\n\r\n# Data for contract\r\ncontract_company := 1\r\ncontract_office := 1\r\ncontract_department := 1\r\n\r\n# Option values\r\nissue_invoice_auto_no := 2' WHERE `type`='nik_add_contract';

######################################################################################
# 2014-11-06 - Added BGS plugin for export of financial documents and payments to Microinvest Delta

# PRE-DEPLOYED # Added BGS plugin for export of financial documents and payments to Microinvest Delta
#INSERT INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
#(41, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 1, 'export_file_name := invoices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\npositive_advance_value := 412-411%', 1),
#(42, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 3, 'export_file_name := credit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\npositive_advance_value := 412-411%', 1),
#(43, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 4, 'export_file_name := debit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\npositive_advance_value := 412-411%', 1),
#(44, 'bgservice_to_microinvest', 'Finance_Payment', 0, 'export_file_name := payments\r\nexport_encoding := utf-8\r\nexport_delimiter := #\r\nexport_tags := 4\r\nexport_hide_filters := file_name, format, separator\r\n', 1);
#INSERT INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#(41, 'Експорт на фактури към Microinvest Delta', 'Фактурите се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
#(41, 'Export of invoices to Microinvest Delta', 'The invoices are exported in two files with special format', 'en'),
#(42, 'Експорт на кредитни известия към Microinvest Delta', 'Кредитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
#(42, 'Export of Credit Notices to Microinvest Delta', 'The credit notices are exported in two files with special format', 'en'),
#(43, 'Експорт на дебитни известия към Microinvest Delta', 'Дебитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
#(43, 'Export of Debit Notices to Microinvest Delta', 'The debit notices are exported in two files with special format', 'en'),
#(44, 'Експорт на платежни документи към Microinvest Delta', 'Експортира се разпределението на сумите по финансови документи', 'bg'),
#(44, 'Export of payments to Microinvest Delta', 'The payments are exported in special format', 'en');

######################################################################################
# 2014-11-26 - Added sort parameter in the plugin for export of financial documents and payments to Microinvest Delta

# Added sort parameter in the plugin for export of financial documents and payments to Microinvest Delta
UPDATE `exports` SET settings=REPLACE(settings, 'export_delimiter := |\r\n', 'export_delimiter := |\r\nexport_sort := id\r\n') WHERE settings NOT LIKE '%export_sort%' AND type='bgservice_to_microinvest';

######################################################################################
# 2015-10-27 - Add new setting for automation sendTaggedAsMail

# Add new setting for automation sendTaggedAsMail
UPDATE automations
  SET settings = CONCAT(settings, '\r\nemails_separators := ";, "')
  WHERE method LIKE '%sendTaggedAsMail%'
    AND settings NOT LIKE '%emails_separators%';

######################################################################################
# 2015-12-21 - Added report for sending emails for debt collection

# Added report for sending emails for debt collection
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(334, 'bgs_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := 1,2,3,101\r\ncustomers_types := 2\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 1\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(334, 'Писма за дължими суми', '01. Финанси', NULL, 'bg'),
(334, 'Debt collection emails', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 334, 0, 1);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 334;

######################################################################################
# 2017-01-04 - Added settings for tags to 'bgs_debt_collection_email_sender' report
#            - Added crontab automation for notification for overdue invoices

# Added settings for tags to 'bgs_debt_collection_email_sender' report
UPDATE `reports`
SET `settings` = '# settings for report filters\r\nfin_documents_types := 1,2,3,101\r\ncustomers_types := 2\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 1\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for customer tags to alter report behaviour\r\nuse_tags := 1\r\ncustomers_tags := 6, 7\r\n\r\ntag6_days := 1, 3, 31, 61\r\ntag6_days1_email := 1002\r\ntag6_days3_email := 1002\r\ntag6_days31_email := 1002\r\ntag6_days61_email := 1002\r\n\r\ntag7_days := 1, 31\r\ntag7_days1_email := 1002\r\ntag7_days31_email := 1002\r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date\r\n\r\nfreeze_table_headers :=1'
WHERE `type` = 'bgs_debt_collection_email_sender' AND `settings` NOT LIKE '%use_tags%';

# Added crontab automation for notification for overdue invoices
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Известяване за неплатени фактури', 0, NULL, 1, 'customers', NULL, 'crontab', '0', 'company := 1\r\ndocument_type1 := 1\r\nfin_documents_types := 1\r\n\r\ntag6_days10_users := 10, 11, assigned\r\ntag6_days10_email := 1003\r\n\r\ntag6_days65_users := 11\r\ntag6_days65_email := 1004\r\n\r\ntag7_days44_users := 11\r\ntag7_days44_email := 1005\r\n\r\nstart_time := 01:00\r\nstart_before := 02:00\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := nik\nmethod := notifyUnpaidInvoices', NULL, 1, 0, 1);

#########################################################################################
# 2017-01-05 - Added additional setting show_fin_columns
#            - Added show_eik_column, show_vat_num_column, show_customer_address_column settings instead of show_fin_columns

# Added additional setting show_fin_columns
UPDATE reports
  SET settings = 'show_fin_columns := true'
  WHERE `type` = 'invoices';

# Added show_eik_column, show_vat_num_column, show_customer_address_column settings instead of show_fin_columns
UPDATE reports
  SET settings = 'show_eik_column := true\r\nshow_vat_num_column := false\r\nshow_customer_address_column := false'
  WHERE `type` = 'invoices';

#########################################################################################
# 2017-01-06 - Added additional setting show_fullname_column

# Added additional setting show_fullname_column
UPDATE reports
  SET settings = 'show_eik_column := true\r\nshow_vat_num_column := false\r\nshow_customer_address_column := false\r\nshow_fullname_column := false'
  WHERE `type` = 'invoices';

#########################################################################################
# 2017-01-25 - Changed the payment date of finance invoice templates to 20 working days after the invoice issue date

# PRE-DEPLOYED # Changed the payment date of finance invoice templates to 20 working days after the invoice issue date
# UPDATE fin_invoices_templates SET modified=NOW(), modified_by=-1, date_of_payment="count := 20
# period_type := calendar
# period := day
# direction := after
# point := issue"
# WHERE deleted_by=0 AND status!="finished";

#########################################################################################
# 2017-09-07 - Added rest client settings for the export of invoices
#            - Added search definitions to search invoices (revenue documents) by customer EIK and customer name

# Added rest client settings for the export of invoices
UPDATE `settings` SET `value`='NikInvoices' WHERE  `name`='allowed_rest_user_agents' AND section='rest';
INSERT IGNORE INTO settings SET
  section='rest',
  name='filter_vars_finance_incomes_reasons_1',
  value='num, eik, vat_num, customer_name, issue_date, date_of_payment, total_with_vat, vars'
ON DUPLICATE KEY UPDATE value='num, eik, vat_num, customer_name, issue_date, date_of_payment, total_with_vat, vars';

# Added search definitions to search invoices by customer EIK and customer name
INSERT IGNORE INTO `_search_defs` (`module`, `controller`, `var_name`, `label_param`, `sortable`, `field_type`) VALUES
('finance', 'incomes_reasons', 'fir.eik', 'finance_incomes_reasons_eik', '', 'text'),
('finance', 'incomes_reasons', 'firi18n.customer_name', 'finance_incomes_reasons_customer', '', 'text');

#########################################################################################
# 2017-09-11 - Add new settings for report 'invoices'

# Add new settings for report 'invoices'
UPDATE reports
  SET settings = CONCAT(settings, '\r\nperiods_gt2_field := free_text5\r\ndevices_gt2_field := free_field2')
  WHERE type = 'invoices'
    AND settings NOT LIKE '%periods_gt2_field%';


#########################################################################################
# 2017-09-13 - Added new rest client settings for the export of invoices

# Added new rest client settings for the export of invoices
UPDATE `settings` SET `value`='NikInvoices' WHERE  `name`='allowed_rest_user_agents' AND section='rest';
INSERT IGNORE INTO settings SET
  section='rest',
  name='filter_vars_finance_incomes_reasons_1',
  value='num, eik, vat_num, customer_name, issue_date, date_of_payment, total_with_vat, type, vars'
ON DUPLICATE KEY UPDATE value='num, eik, vat_num, customer_name, issue_date, date_of_payment, total_with_vat, type, vars';

######################################################################################
# 2019-07-10 - Added crontab automation for notification for issued invoices

# PRE-DEPLOYED # Added crontab automation for notification for issued invoices
# INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# (NULL, 'Известяване на отговорници на клиенти за издадени фактури', 0, NULL, 1, 'finance', 'incomes_reasons', 'crontab', '1', 'email_template := \r\nprint_pattern := 3\r\n', 'where := fir.status = \'finished\'\r\nwhere := # fir.added >= DATE_SUB(NOW(), INTERVAL 1 HOUR)', 'plugin := nik\r\nmethod := notifyIssuedInvoices', NULL, 2, 1, 1);

######################################################################################
# 2020-12-01 - Changed conditions of validateDateOfPayment to start only upon add/edit

# Changed conditions of validateDateOfPayment to start only upon add/edit
UPDATE `automations`
SET `conditions`='condition := \'[request_is_post]\'\r\ncondition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')'
WHERE method like '%validateDateOfPayment%';

######################################################################################
# 2020-12-12 - Added new report: 'nik_debt_collection_email_sender'
#            - Removed the 'debt_collection_email_sender' report
#            - Reconfigured the notifyUnpaidInvoices automations
#            - Reconfigured the notifyIssuedInvoices automation with different recipient options

# Added 'nik_debt_collection_email_sender' report
SET @id := 425;

INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'nik_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := 1,2,3,101\r\ncustomers_types := 2\r\nignore_tags := \r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 1\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for customer tags to alter report behaviour\r\nuse_tags := 1\r\ncustomers_tags := 6, 7\r\n\r\ntag6_days := 1, 3, 31, 61\r\ntag6_days1_email := 1002\r\ntag6_days3_email := 1002\r\ntag6_days31_email := 1002\r\ntag6_days61_email := 1002\r\n\r\ntag7_days := 1, 31\r\ntag7_days1_email := 1002\r\ntag7_days31_email := 1002\r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date\r\n\r\nfreeze_table_headers :=1', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Писма за дължими суми', '01. Финанси', NULL, 'bg'),
(@id, 'Debt collection emails', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;

#  Removed the 'debt_collection_email_sender' report
UPDATE reports SET visible=0 WHERE type='debt_collection_email_sender';

# Reconfigured the notifyUnpaidInvoices automation
UPDATE `automations`
SET settings='report := nik_debt_collection_email_sender\r\ncompany := 1\r\ndocument_type1 := 1\r\nfin_documents_types := 1\r\n\r\n# Tag Normal, Due 10 days, Office Ids 1, 2\r\ntag6_days10_office1,2_users := 10, 52, 54, assigned\r\ntag6_days10_office1,2_email := 1003\r\n\r\n# Tag Normal, Due 10 days, Office Ids 3\r\ntag6_days10_office3_users := 10, 47, 54, assigned_soft\r\ntag6_days10_office3_email := 1003\r\n\r\n# Tag Normal, Due 30 days, Office Ids 1, 2\r\ntag6_days30_office1,2_users := 52, 54\r\ntag6_days30_office1,2_email := 1013\r\n\r\n# Tag Normal, Due 30 days, Office Ids 3\r\ntag6_days30_office3_users := 47, 54\r\ntag6_days30_office3_email := 1013\r\n\r\n# Tag VIP, Due 44 days, Office Ids 1, 2\r\ntag7_days44_office1,2_users := 52, 54\r\ntag7_days44_office1,2_email := 1005\r\n\r\n# Tag VIP, Due 44 days, Office Ids 3\r\ntag7_days44_office3_users := 47, 54\r\ntag7_days44_office3_email := 1005\r\n\r\n# Tag Normal, Due 65 days, Office Ids 1, 2\r\ntag6_days65_office1,2_users := 52, 54\r\ntag6_days65_office1,2_email := 1004\r\n\r\n# Tag Normal, Due 65 days, Office Ids 3\r\ntag6_days65_office3_users := 47, 54\r\ntag6_days65_office3_email := 1004\r\n\r\n\r\n#start_time := 01:00\r\n#start_before := 02:00\r\nsend_to_email := <EMAIL>\r\n'
WHERE method like '%notifyUnpaidInvoices%' AND settings NOT LIKE '%nik_debt_collection_email_sender%';

# Reconfigured the notifyIssuedInvoices automation with different recipient options
UPDATE `automations`
SET `settings`='email_template := 1010\r\nprint_pattern := 3\r\ndefault_recipient_field := assigned\r\noffice3_recipient_field := assigned_soft\r\n'
WHERE  `method` LIKE '%notifyIssuedInvoices%';

######################################################################################
# 2022-02-01 - Export customers contact persons

# Export customers contact persons
INSERT IGNORE INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
  (60, 'contact_persons', 'Customer', 3, '# Име по подразбиране на експортирания файл\r\nexport_file_name := Contact_persons\r\n# Кои от стандартните филтри да не се показват\r\nexport_hide_filters := format, separator\r\n# Разделител на колоните\r\nexport_delimiter := ;\r\n# Кодиране на съдържанието\r\nexport_encoding := windows-1251\r\n\r\n# ID на таг, с който да се маркира, че контрагентите вече са били експортирани и да не се експортират повторно. Незадължителна настройка. Ако не е зададена не се ползва тагване.\r\n#export_tags := \r\n\r\n# Ако за едно лице за контакт има повече от една данни за контакт, те да се разделят със следния разделител. Незадължителна настройка, ако не е зададена по подразбиране се използва: ;\r\ncontacts_delimiter := ;', 1);
INSERT IGNORE INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (60, 'Лица за контакт', 'Експорт на лицата за контакт към избраните контрагенти.', 'bg'),
  (60, 'Contact persons', 'Export of the contact persons to the selected customers.', 'en');

######################################################################################
# 2022-02-08 - Remove export for customers contact persons

# Remove export for customers contact persons
DELETE FROM `exports` WHERE `id` = 60;
DELETE FROM `exports_i18n` WHERE `parent_id` = 60;
