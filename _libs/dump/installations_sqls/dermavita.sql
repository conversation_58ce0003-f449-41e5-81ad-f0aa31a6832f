####################################################################################
### SQL nZoom Specific Updates - Дерма Вита (http://dermavita.n-zoom.com/) ###
####################################################################################

######################################################################################
# 2014-07-08 - Added schedule dashlet plugin for DermaVita installation (dermavita)

# Added schedule dashlet plugin for DermaVita installation (dermavita)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'dermavita_schedule', 'document_type_visit_id := 1\r\nnom_procedure_type_id := 6\r\nnom_medic_office_type_id := 5\r\npacient_type_id := 2\r\n\r\nvisit_date := visit_date\r\nvisit_start := visit_start_hour\r\nvisit_end := visit_end_hour\r\nvisit_duration := visit_duration\r\nvisit_medic_office := visit_room\r\nvisit_procedure := procedure_id\r\nvisit_medic_nurse := staff_id\r\n\r\nmedic_nurse_color := staff_color\r\nmedic_nurse_type := staff_type\r\nnurse_option := 1\r\n\r\nvisit_substatus_canceled := 5\r\nvisit_substatus_annulled := 6\r\n\r\nworking_time_start := 09:00\r\nworking_time_end := 19:00\r\nweekend_days := 7\r\n\r\ncalendar_inraval_px_height := 70\r\nside_panel_time_interval := 60\r\n\r\ndocument_substatuses := 1,2,3,4,5', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'График', 'График', 'bg'),
(LAST_INSERT_ID(), 'Schedule', 'Schedule', 'en');

######################################################################################
# 2014-07-10 - Update settings for schedule dashlet plugin for DermaVita installation (dermavita)

# PRE-DEPLOYED # Update settings for schedule dashlet plugin for DermaVita installation (dermavita)
#UPDATE `dashlets_plugins` SET `settings`='document_type_visit_id := 1\r\nnom_procedure_type_id := 6\r\nnom_medic_office_type_id := 5\r\npacient_type_id := 2\r\n\r\nvisit_date := visit_date\r\nvisit_start := visit_start_hour\r\nvisit_end := visit_end_hour\r\nvisit_duration := visit_duration\r\nvisit_medic_office := visit_room\r\nvisit_procedure := procedure_id\r\nvisit_procedure_name := procedure_name\r\nvisit_procedure_duration := procedure_duration\r\nvisit_medic_nurse := staff_id\r\nviist_procedure_group_table := procedure_group\r\n\r\nmedic_nurse_color := staff_color\r\nmedic_nurse_type := staff_type\r\nmedic_nurse_special_user := 4\r\nnurse_option := 1\r\n\r\nvisit_substatus_canceled := 5\r\nvisit_substatus_annulled := 6\r\n\r\nworking_time_start := 09:00\r\nworking_time_end := 19:00\r\nweekend_days := 7\r\n\r\ncalendar_inraval_px_height := 70\r\nside_panel_time_interval := 60\r\n\r\ndocument_substatuses := 1,2,3,4,5\r\n\r\nnom_procedure_stuff_type := staff_type\r\nnom_procedure_stuff_nurse := 1' WHERE `type`='dermavita_schedule';

######################################################################################
# 2014-07-17 - Update the name of medicine room var for 'dermavita_schedule' dashlet plugin for DermaVita installation (dermavita)

# PRE-DEPLOYED # Update the name of medicine room var for 'dermavita_schedule' dashlet plugin for DermaVita installation (dermavita)
#UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'visit_medic_office := visit_room', 'visit_medic_office := visit_room_id') WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%visit_room_id%';

######################################################################################
# 2014-08-29 - Update settings for schedule dashlet plugin for DermaVita installation (dermavita)

# Update settings for schedule dashlet plugin for DermaVita installation (dermavita)
UPDATE `dashlets_plugins` SET `settings`='document_type_visit_id := 1\r\nnom_procedure_type_id := 6\r\nnom_medic_office_type_id := 5\r\npacient_type_id := 2\r\n\r\nvisit_date := visit_date\r\nvisit_start := visit_start_hour\r\nvisit_end := visit_end_hour\r\nvisit_duration := visit_duration\r\nvisit_medic_office := visit_room_id\r\nvisit_procedure := procedure_id\r\nvisit_procedure_name := procedure_name\r\nvisit_procedure_duration := procedure_duration\r\nvisit_medic_nurse := staff_id\r\nvisit_medic_nurse_name := staff_name\r\nviist_procedure_group_table := procedure_group\r\nvisit_equipment := facilities_id\r\nvisit_nurse := staff_nurse_id\r\nvisit_nurse_name := staff_nurse_name\r\n\r\nmedic_nurse_color := staff_color\r\nmedic_nurse_type := staff_type\r\nmedic_nurse_special_user := 4\r\nnurse_option := 1\r\n\r\nvisit_substatus_canceled := 5\r\nvisit_substatus_annulled := 6\r\n\r\nworking_time_start := 09:00\r\nworking_time_end := 19:00\r\nweekend_days := 7\r\n\r\ncalendar_inraval_px_height := 110\r\nside_panel_time_interval := 60\r\n\r\ndocument_substatuses := 1,2,3,4,5\r\n\r\nnom_procedure_stuff_type := staff_type\r\nnom_procedure_stuff_nurse := 1\r\n\r\nnom_equipment_type_id := 7\r\nstatus_to_create_new_document := locked_3\r\n\r\ndocument_work_leaving_form := 3\r\ndocument_work_leaving_form_currency := BGN\r\ndocument_work_leaving_form_measure := 1\r\ndocument_work_leaving_form_unpaid_tag_id := 4\r\n\r\nnom_procedure_company := cosmetic_company\r\ncustomer_companies_type_id := 3' WHERE `type`='dermavita_schedule';

######################################################################################
# 2014-09-11 - Added work leaving form dashlet plugin for DermaVita installation (dermavita)

# Added schedule dashlet plugin for DermaVita installation (dermavita)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'dermavita_work_leaving_form', 'customer_patient_type_id := 2\r\n\r\ndocument_visit_type_id := 1\r\ndocument_work_leaving_form_type_id := 3\r\ndocument_work_leaving_form_total := total_with_vat\r\ndocument_work_leaving_form_tag_paid := 2\r\ndocument_work_leaving_form_tag_partial := 3\r\ndocument_work_leaving_form_tag_unpaid := 4\r\n\r\ndocument_payment_type_id := 4\r\ndocument_payment_total_value := payment_value\r\ndocument_payment_payment_value := payment_value_parts\r\ndocument_payment_document_id := document_procedure_id\r\ndocument_payment_document_name := document_procedure\r\ndocument_payment_company_id := cosmetic_company_id\r\ndocument_payment_company_name := cosmetic_company\r\ndocument_payment_cash_bank_id := cash_bank_id\r\ndocument_payment_cash_bank_name := cash_bank\r\n\r\ncustomer_derma_vita := 72\r\ncustomer_derma_beauty := 74\r\n\r\nnom_cashbox_bank_type := 10\r\nnom_cashbox_bank_company := cosmetic_company_id', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обходен лист', 'Обходен лист', 'bg'),
(LAST_INSERT_ID(), 'Work leaving form', 'Work leaving form', 'en');

######################################################################################
# 2014-09-24 - Update the 'dermavita_work_leaving_form' settings by adding var for visit date for visits documents

# Update the 'dermavita_work_leaving_form' settings by adding var for visit date for visits documents
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'document_visit_type_id := 1', 'document_visit_type_id := 1\r\ndocument_visit_date := visit_date') WHERE `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%document_visit_date := visit_date%';

#########################################################################################
# 2014-11-19 - Added new report - 'dermavita_manage_schedule_visits' - for Derma Vita installation (dermavita)

# Added new report - 'dermavita_manage_schedule_visits' - for Derma Vita installation (dermavita)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (301, 'dermavita_manage_schedule_visits', 'past_periods := 3\r\nfuture_periods := 6\r\ncustomer_tag := 8\r\nnom_type_working_time := 11\r\ndocument_type_schedule := 5\r\n\r\ndoc_schedule_medic_name := personal_name\r\ndoc_schedule_medic_id := personal_name_id\r\ndoc_schedule_description := schedule_description\r\ndoc_schedule_bb := bb_group\r\ndoc_schedule_bb_el := bb_elements\r\ndoc_schedule_date := schedule_date\r\ndoc_schedule_gruop_table := shedule_group\r\ndoc_work_shift := work_shift\r\n\r\ndoc_type_visit := 1\r\ndoc_visit_date := visit_date\r\ndoc_visit_start := visit_start_hour\r\ndoc_visit_end := visit_end_hour\r\ndoc_visit_duration := visit_duration\r\ndoc_visit_procedure := procedure_id\r\ndoc_visit_procedure_name := procedure_name\r\ndoc_visit_procedure_duration := procedure_duration\r\ndoc_visit_staff_member := staff_id\r\ndoc_visit_staff_member_name := staff_name\r\ndoc_visit_nurse_member := staff_nurse_id\r\ndoc_visit_nurse_member_name := staff_nurse_name\r\ndoc_visit_facility := facilities_id\r\ndoc_visit_room := visit_room_id\r\ndoc_visit_procedure_group_table := procedure_group\r\ndoc_visit_substatuses := 1,2,3,4,5\r\n\r\ndoc_status_to_create_new_document := locked_3\r\n\r\nnom_procedure_stuff_type := staff_type\r\nnom_procedure_stuff_nurse := 1\r\nnom_equipment_type_id := 7\r\n\r\ncustomer_medic_nurse_type := staff_type\r\ncustomer_nurse_option := 1\r\n\r\ndocument_work_leaving_form := 3\r\ndocument_work_leaving_form_currency := BGN\r\ndocument_work_leaving_form_measure := 1\r\ndocument_work_leaving_form_unpaid_tag_id := 4', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (301, 'Управление на график/посещения', NULL, NULL, 'bg'),
  (301, 'Manage schedule/visits', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 301, 0, 1),
  ('reports', 'export', 301, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=301;

#########################################################################################
# 2014-12-17 - Added new settings for 'dermavita_schedule' dashlet plugin (for Derma Vita installation (dermavita)) needed for showing working time

# Added new settings for 'dermavita_schedule' dashlet plugin (for Derma Vita installation (dermavita)) needed for showing working time
UPDATE `dashlets_plugins`
SET `settings`=CONCAT(settings, '\r\n\r\ndocument_type_schedule := 5\r\ndoc_schedule_medic_id := personal_name_id\r\ndoc_schedule_gruop_table := shedule_group\r\ndoc_schedule_date := schedule_date\r\ndoc_work_shift := work_shift\r\n\r\nnom_working_time := 11\r\nnom_working_time_from := worktime_from\r\nnom_working_time_to := worktime_to')
WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_type_schedule%';

#########################################################################################
# 2015-02-04 - Added new report - 'dermavita_patient_file' - for Derma Vita installation (dermavita)

# PRE-DEPLOYED # Added new report - 'dermavita_patient_file' - for Derma Vita installation (dermavita)
#INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  (305, 'dermavita_patient_file', 'customer_type_patient := 2\r\nnomenclature_type_procedure := 6\r\ndocument_visit_protocol := 7\r\ndocument_visit := 1\r\n\r\npatient_recommended_by := recommendation_from\r\npatient_allergy := allergy\r\npatient_allergy_comment := allergy_comment\r\n\r\npatient_medicaments_use := medicaments_use\r\npatient_medicaments_comment := medicaments_comment\r\npatient_recommended_treatment := recommended_treatment\r\npatient_medical_notes := medical_notes\r\n\r\nvisit_protocol_procedure := procedure_name_id\r\nvisit_protocol_visitation := med_view\r\nvisit_protocol_procedure_name := procedure_name\r\nvisit_protocol_procedure_price := med_price\r\nvisit_protocol_procedure_employee := employee_name\r\nvisit_protocol_procedure_diagnostic := diagnostic_name\r\nvisit_protocol_procedure_code := mkb_code\r\nvisit_protocol_procedure_parameters := med_parameters\r\nvisit_protocol_procedure_cosmetics := cosmetics\r\nvisit_protocol_procedure_cosmetics_price := cosmetics_price\r\nvisit_protocol_procedure_cosmetics_notes := cosmetics_notes\r\nvisit_protocol_procedure_file1 := pic_file1\r\nvisit_protocol_procedure_file2 := pic_file2\r\nvisit_protocol_procedure_file3 := pic_file3\r\nvisit_protocol_procedure_file4 := pic_file4\r\nvisit_protocol_procedure_file5 := pic_file5\r\nvisit_protocol_procedure_agreement_by := agree_name\r\nvisit_protocol_procedure_notes := cosmetics_dicrip\r\n\r\nvisit_protocol_picture_date := picture_name\r\nvisit_protocol_picture_note := picture_note\r\nvisit_protocol_picture_file := picture_file\r\n\r\nvisit_protocol_planned_date := made_date\r\nvisit_protocol_planned_procedure := next_procedure\r\nvisit_protocol_planned_count := document_quantity\r\nvisit_protocol_planned_price := program_price\r\nvisit_protocol_planned_notes := program_notes\r\n\r\nvisit_date := visit_date\r\nvisit_start := visit_start_hour\r\nvisit_end := visit_end_hour\r\nvisit_procedure := procedure_name\r\nvisit_doctor := staff_name\r\nvisit_nurse := staff_nurse_name\r\nvisit_comment := procedure_comment', 0, 0, 1);
#INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  (305, 'Досие на пациент', NULL, NULL, 'bg'),
#  (305, 'Patient file', NULL, NULL, 'en');
#INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', 305, 0, 1),
#  ('reports', 'export', 305, 0, 2);
#INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=305;

#########################################################################################
# 2015-03-16 - Added new report - 'dermavita_turnovers' - for Derma Vita installation (dermavita)

# Added new report - 'dermavita_turnovers' - for Derma Vita installation (dermavita)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (308, 'dermavita_turnovers', 'customer_company := 3\r\nnom_cashbox_bank := 10\r\nnom_cosmetics := 8\r\nnom_procedure := 6\r\ndocument_sheet := 3\r\ndocument_payment := 4\r\ndocument_visit := 1\r\nprocedure_types := 6\r\n\r\ntag_system_container := 11\r\ncategories_procedures_sections := 3\r\n\r\ndocument_visit_start_hour := visit_start_hour\r\ndocument_payment_sheet_id := document_procedure_id\r\ndocument_payment_container := cash_bank_id\r\ndocument_payment_value := payment_value_parts\r\ndocument_payment_company := cosmetic_company_id\r\n\r\nnom_container_company := cosmetic_company_id\r\nnom_bank_cashbox_current_amount := account_balance\r\n\r\nsheet_tag_paid := 2\r\nsheet_tag_partial := 3\r\nsheet_tag_unpaid := 4', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (308, 'Обороти за клиента', NULL, NULL, 'bg'),
  (308, 'Clients turnovers', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 308, 0, 1),
  ('reports', 'export', 308, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=308;

#########################################################################################
# 2015-03-17 - Added new var needed for 'dermavita_work_leaving_form' dashlet plugin for Derma Vita installation (dermavita)

# Added new var needed for 'dermavita_work_leaving_form' dashlet plugin for Derma Vita installation (dermavita)alter
UPDATE `dashlets_plugins` SET `settings`=CONCAT(settings, '\r\nnom_cashbox_balance := account_balance')
WHERE `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%nom_cashbox_balance%';

######################################################################################
# 2015-05-11 - Added SMS notification automation for document type 6

# Added SMS notification automation for document type 6
 INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
 (NULL, 'Валидация на телефонни номера при установяване на статус "За изпращане"', 0, NULL, 1, 'documents', NULL, 'before_action', '6', 'var_phone := patient_mobile\r\nvar_status := send_sms_status\r\nsend_status_unsent := 0\r\nsend_status_sent := 1\r\nsend_status_received := 2\r\nsend_status_unreceived := 3\r\nsend_status_invalid := -1\r\nsend_to_email := <EMAIL>', 'condition :=  \'[request_is_post]\' == \'1\'  && $this->registry->get(\'request\')->get(\'substatus\') == \'opened_7\' && [action] == \'setstatus\'', 'plugin := dermavita\r\nmethod := validateSMSRecipientNumbers\r\n', 'cancel_action_on_fail := 1', 1, 0, 1),
 (NULL, 'Изпраща SMS', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 10:00\r\nstart_before := 11:00\r\n\r\nvar_phone := patient_mobile\r\nvar_date := visit_date\r\nvar_time := visit_time\r\nvar_status := send_sms_status\r\nvar_patient_name := patient_name\r\nvar_sms_id := sms_id\r\nsend_status_unsent := 0\r\nsend_status_sent := 1\r\nsend_status_received := 2\r\nsend_status_unreceived := 3\r\nsend_status_invalid := -1\r\ncorrection_status := opened\r\ncorrection_substatus := 10\r\ndocument_send_status := closed\r\ndocument_send_substatus_sent := 8\r\ndocument_send_substatus_received := 9\r\ndocument_send_substatus_unreceived := 11\r\nsend_to_email := <EMAIL>\r\n\r\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX) се изпраща SMS до този получател\r\ntest_recipient := ************\r\nsms_gateway_url := http://cgw.mtelecom.bg/acceptor/txtSend.php\r\nsms_gateway_uid := 2499\r\nsms_gateway_vasms := 2250\r\nsms_content := DermaVita napomnia za chasa Vi za [date] v [time] h. Molia, da ni uvedomite v sluchai, che chasat otpada. Za kontakt: 02/9622533.\r\n', 'condition := \'[b_status]\' == \'opened\'\r\ncondition := \'[b_substatus]\' == 7\r\ncondition := \'[b_date]\' <= date(\'Y-m-d\')\r\nfilter_sql_condition := status = \'opened\'\r\nfilter_sql_condition := substatus = \'7\'\r\nfilter_sql_condition := date <= CURDATE()\r\n\r\n', 'plugin := dermavita\r\nmethod := sendSMS\r\n', '', 1, 0, 1),
 (NULL, 'Получаване на SMS (актуализира информация за получаване)', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'var_status := send_sms_status\r\nvar_sms_id := sms_id\r\nsend_status_unsent := 0\r\nsend_status_sent := 1\r\nsend_status_received := 2\r\nsend_status_unreceived := 3\r\nsend_status_invalid := -1\r\ndocument_send_status := closed\r\ndocument_send_substatus_sent := 8\r\ndocument_send_substatus_received := 9\r\ndocument_send_substatus_unreceived := 11\r\nsend_to_email := <EMAIL>\r\n', 'condition := \'[b_status]\' == \'closed\'\r\ncondition := \'[b_substatus]\' == 8\r\nfilter_sql_condition := 5 OR 5 AND status = \'closed\'\r\nfilter_sql_condition := substatus = 8\r\n\r\n\r\n', 'plugin := dermavita\r\nmethod := updateSMSDeliveryInfo\r\n', '', 2, 0, 1);

DROP TABLE IF EXISTS `sms`;
CREATE TABLE `sms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL DEFAULT '0',
  `recipient_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `recipient_number` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `content` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `notes` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `send_status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `send_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `dlr_status` enum('unsent','sent','received','unreceived') COLLATE utf8_unicode_ci DEFAULT NULL,
  `dlr_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='contains SMS messages sent for DERMAVITA only';

######################################################################################
# 2015-05-26 - Fixed SMS automation comment in settings causing troubles with test recipient

# Fixed SMS automation comment in settings causing troubles with test recipient
UPDATE automations SET settings=REPLACE(settings, '08XXXXXXXX', '3598XXXXXXXX') WHERE settings like '%08XXXXXXXX%';

######################################################################################
# 2015-06-11 - Added new settings for 'dermavita_schedule' dashlet plugin (DERMAVITA), needed properly maanging categories in AC for procedure

# Added new settings for 'dermavita_schedule' dashlet plugin (DERMAVITA), needed properly maanging categories in AC for procedure
UPDATE `dashlets_plugins` SET `settings`=CONCAT(settings, '\r\nnom_type_procedure := 6\r\nnom_type_category := 13\r\nnom_procedure_category := cat_name_id')
WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%nom_procedure_category%';

######################################################################################
# 2015-06-17 - Added new settings for 'dermavita_turnovers' report (DERMAVITA) so categories can be used in the procedures autocompleter

# Added new settings for 'dermavita_turnovers' report (DERMAVITA) so categories can be used in the procedures autocompleter
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\nprocedure_types_categories := 13\r\nnom_procedure_category := cat_name_id') WHERE `type`='dermavita_turnovers' AND `settings` NOT LIKE '%nom_procedure_category%';

######################################################################################
# 2015-06-26 - Fixed conditions of automation that updates the SMS statuses

# Fixed conditions of automation that updates the SMS statuses
UPDATE automations SET nums=100, settings=REPLACE(settings, 'send_to_email := <EMAIL>', ''), conditions= 'condition := \'[b_status]\' == \'closed\'\r\ncondition := \'[b_substatus]\' == 8 || \'[b_substatus]\' == 11\r\nfilter_sql_condition := status = \'closed\'\r\nfilter_sql_condition := substatus = 8 OR substatus = 11' WHERE method like '%updateSMSDeliveryInfo%';

######################################################################################
# 2015-06-30 - Fixed conditions of automation that updates the SMS statuses

# Fixed conditions of automation that updates the SMS statuses
# IMPORTANT: the condition "5 OR 5 AND status = 'closed'" is deliberately used to get closed documents!
UPDATE automations SET conditions= 'condition := \'[b_status]\' == \'closed\'\r\ncondition := \'[b_substatus]\' == 8 || \'[b_substatus]\' == 11\r\nfilter_sql_condition := 5 OR 5 AND status = \'closed\' AND deleted = 0 AND active=1 AND type = \'6\'\r\nfilter_sql_condition := substatus IN (8, 11)' WHERE method like '%updateSMSDeliveryInfo%';

######################################################################################
# 2015-08-04 - Correct the medic ids for work leaving form since the field was changed from free_text1 to free_field2

# Correct the medic ids for work leaving form since the field was changed from free_text1 to free_field2
UPDATE `gt2_details` AS gt2, gt2_details_i18n AS gt2i18n, customers_i18n AS ci18n, documents AS d, customers AS c
SET gt2.free_field2=ci18n.parent_id
WHERE gt2i18n.parent_id = gt2.id AND gt2i18n.lang = "bg" AND CONCAT( ci18n.name, " ", ci18n.lastname ) = gt2i18n.free_text2 AND gt2i18n.lang = "bg" AND gt2.model_id=d.id AND d.type=3
 AND gt2.model = "Document" AND ci18n.parent_id IS NOT NULL AND c.id=ci18n.parent_id AND c.type=1 AND gt2.free_field2 = "";

######################################################################################
# 2015-08-18 - Added new setting for 'dermavita_turnovers' report (DERMAVITA) to point which roles can see the balance table

# Added new setting for 'dermavita_turnovers' report (DERMAVITA) to point which roles can see the balance table
UPDATE `reports` SET `settings` = CONCAT('roles_full_rights := 1\r\n\r\n', `settings`) WHERE `type`='dermavita_turnovers' AND `settings` NOT LIKE '%roles_full_rights%';

######################################################################################
# 2015-09-16 - Added new settings for 'dermavita_schedule' and 'dermavita_work_leaving_form' (DERMAVITA) dashlet plugins

# Added new settings for 'dermavita_schedule' and 'dermavita_work_leaving_form' (DERMAVITA) dashlet plugins
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'document_work_leaving_form_tag_unpaid := 4', 'document_work_leaving_form_tag_unpaid := 4\r\ndocument_work_leaving_form_tag_prepaid := 12') WHERE `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%document_work_leaving_form_tag_prepaid%';
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ndocument_work_leaving_form_payment_type_regular := 1\r\ndocument_work_leaving_form_payment_type_prepaid := 2') WHERE  `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%document_work_leaving_form_payment_type_prepaid%';
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ndocument_work_leaving_regular_payment := 1') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_work_leaving_regular_payment%';

######################################################################################
# 2015-10-22 - Fixed menu links in roles settings for the previously named 'dermavita_manage_schedule_visits' report

# Fixed menu links in roles settings for the previously named 'dermavita_manage_schedule_visits' report
UPDATE `roles` SET `menu_settings`=REPLACE(`menu_settings`, ';s:69:"index.php?launch=reports&report_type=dermavita_manage_schedule_visits";', ';s:59:"index.php?launch=reports&report_type=manage_schedule_visits";') WHERE `menu_settings` LIKE '%dermavita_manage_schedule_visits%';

######################################################################################
# 2015-11-13 - Added new automation to handle with the annuled payments and its related records

# Added new automation to handle with the annuled payments and its related records
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Анулиране на плащания', 0, NULL, 1, 'documents', NULL, 'action', '4', 'payment_value := payment_value_parts\r\ncontainer_id := cash_bank_id\r\nsheet_id := document_procedure_id\r\n\r\nnomenclature_container_value := account_balance\r\n\r\nsheet_tag_unpaid := 4\r\nsheet_tag_partial := 3', '#condition := ''[prev_b_substatus]'' != ''14''\r\ncondition := ''[b_substatus]'' == ''14''\r\ncondition := ''[action]'' == ''setstatus''', 'plugin := dermavita\r\nmethod := annulPayment', NULL, 1, 1, 1);

######################################################################################
# 2015-11-17 -  Added new settings for 'dermavita_schedule' dashlet plugins for creating financial document (AESHTECLINIC specific)

# Added new settings for 'dermavita_schedule' dashlet plugins for creating financial document (AESHTECLINIC specific)
UPDATE `dashlets_plugins` SET `settings`=CONCAT(settings, '\r\n\r\n# AESTHE CLINIC specific settings\r\ndocument_work_leaving_form_fin_type :=\r\ndocument_work_leaving_form_fin_default_office :=\r\ncustomer_5_company :=\r\ncustomer_7_company :=') WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_work_leaving_form_fin_type%';

######################################################################################
# 2015-11-27 - Changed the interval of sending SMS from 12:00 -16:30 to 12:00 - 17:30

# Changed the interval of sending SMS from 12:00 -16:30 to 12:00 - 17:30
UPDATE automations SET `settings`=REPLACE(settings, 'start_before := 16:30', 'start_before := 17:30') WHERE `method` LIKE '%sendSMS%' AND `settings` NOT LIKE '%start_before := 17:30%';

######################################################################################
# 2015-11-30 - Added new setting for 'dermavita_turnovers' report (DERMAVITA) to point which is the substatus annulled for payments

# Added new setting for 'dermavita_turnovers' report (DERMAVITA) to point which is the substatus annulled for payments
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ndocument_payment_substatus_annulled := 14') WHERE `type`='dermavita_turnovers' AND `settings` NOT LIKE '%document_payment_substatus_annulled%';

######################################################################################
# 2015-12-28 - Changed the conditions of automation that sends SMS (get only documents set to be sent at current date only)

# Changed the conditions of automation that sends SMS (get only documents set to be sent at current date only)
UPDATE automations SET `conditions`=REPLACE(conditions, "date <= CURDATE()", "date = CURDATE()") WHERE `method` LIKE '%sendSMS%';
UPDATE automations SET `conditions`=REPLACE(conditions, "date >= CURDATE()", "date = CURDATE()") WHERE `method` LIKE '%sendSMS%';
UPDATE automations SET `conditions`=REPLACE(conditions, "'[b_date]' <= date('Y-m-d')", "'[b_date]' == date('Y-m-d')") WHERE `method` LIKE '%sendSMS%';
UPDATE automations SET `conditions`=REPLACE(conditions, "'[b_date]' >= date('Y-m-d')", "'[b_date]' == date('Y-m-d')") WHERE `method` LIKE '%sendSMS%';

######################################################################################
# 2016-01-18 - Changed the conditions of automation that sends SMS (get only documents set to be sent at current date only)

# PRE-DEPLOYED INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Корекция на наличност при директно добавяне на плащане', 0, NULL, 1, 'documents', NULL, 'action', '4', 'payment_value := payment_value_parts\r\ncontainer_id := cash_bank_id\r\n\r\ncontainer_amount := account_balance', 'condition := (''[action]'' == ''add'' || ''[action]'' == ''edit'') && [request_is_post]', 'plugin := dermavita\r\nmethod := updateContainersAmounts', NULL, 1, 0, 1);

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status = ''opened''\r\nwhere := d.substatus = ''7''\r\nwhere := DATE_FORMAT(d.date, ''%Y-%m-%d'') >= CURDATE()' WHERE `id` = 2;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status = ''closed''\r\nwhere := d.substatus IN (8, 11)' WHERE `id` = 3;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_FORMAT(a__made_date_reminder_for_call, ''%Y-%m-%d'') = DATE_ADD(CURDATE(), INTERVAL 1 DAY)' WHERE `id` = 4;

######################################################################################
# 2016-06-02 - Added settings for 'dermavita_schedule' dashlet plugin for Dermavita deactivation of payments and for automatic completion of medic and medic id in Aesthe Clinic

# Added settings for 'dermavita_schedule' dashlet plugin for Dermavita deactivation of payments and for automatic completion of medic and medic id in Aesthe Clinic
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\n# Payment document vars (Dermavita)\r\ndocument_payment_type_id := 4\r\ndocument_payment_document_id := document_procedure_id')  WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_payment_type_id%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\n# Schedule validation vars', '\r\nemployee_staff_type :=\r\nemployee_clinic :=\r\n\r\n# Schedule validation vars') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%employee_staff_type%';

######################################################################################
# 2016-06-09 - Updated settings for 'dermavita_schedule' dashlet plugin to contain option for setting the duration of single interval in the schedule

# Updated settings for 'dermavita_schedule' dashlet plugin to contain option for setting the duration of single interval in the schedule
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, 'side_panel_time_interval :=', 'main_panel_hours_interval := 60\r\nside_panel_time_interval :=')  WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%main_panel_hours_interval%';

######################################################################################
# 2016-08-08 - Updated payments dates for the payments added by the dashlet 'dermavita_work_leaving_form' after 17.03.2016

# Updated payments dates for the payments added by the dashlet 'dermavita_work_leaving_form' after 17.03.2016
UPDATE `documents` SET `date`=DATE_FORMAT(`added`, '%Y-%m-%d') WHERE `date`='0000-00-00' AND `type`=4 AND DATE_FORMAT(`added`, '%Y-%m-%d')>'2016-03-17';

######################################################################################
# 2018-04-13 - Changed the settings of the SMS service (Motivian)
#            - Added table for delivery messages

# PRE-DEPLOYED # Changed the settings of the SMS service (Motivian)
#UPDATE automations SET settings=REPLACE(settings, 'sms_gateway_url := http://cgw.mtelecom.bg/acceptor/txtSend.php', 'sms_gateway_url := https://bulksms.motivian.bg/smsapi/bsms/sendsms/') WHERE method like '%sendSMS%';
#UPDATE automations SET settings=REPLACE(settings, 'sms_gateway_uid := 2499', 'sms_gateway_sid := 1836') WHERE method like '%sendSMS%';
#UPDATE automations SET settings=REPLACE(settings, 'sms_gateway_vasms := 2250\r\n', '') WHERE method like '%sendSMS%';

# PRE-DEPLOYED # Added table for delivery messages
#CREATE TABLE `sms_delivery` (
#  `id` INT(11) NOT NULL AUTO_INCREMENT,
#  `smsID` INT(11) NOT NULL,
#  `dlr` INT(11) NOT NULL,
#  `dlr_status` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
#  `to` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
#  `request` TEXT NOT NULL COLLATE 'utf8_unicode_ci',
#  `remote_addr` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci',
#  `date` DATETIME NOT NULL,
#  PRIMARY KEY (`id`)
#) ENGINE=MyISAM COMMENT='records SMS delivery (DLR) push requests' DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

#########################################################################################
# 2023-02-23 - Changed the character set and collation

# Changed the character set and collation
ALTER TABLE `sms` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sms_delivery` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
