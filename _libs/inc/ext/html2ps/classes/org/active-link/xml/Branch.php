<?php

/*
	This file is part of ActiveLink PHP XML Package (www.active-link.com).
	Copyright (c) 2002-2004 by <PERSON><PERSON><PERSON>

	You can contact the author of this software via E-mail at
	<EMAIL>

	ActiveLink PHP XML Package is free software; you can redistribute it and/or modify
	it under the terms of the GNU Lesser General Public License as published by
	the Free Software Foundation; either version 2.1 of the License, or
	(at your option) any later version.

	ActiveLink PHP XML Package is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser General Public License for more details.

	You should have received a copy of the GNU Lesser General Public License
	along with ActiveLink PHP XML Package; if not, write to the Free Software
	Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
*/

import("org.active-link.xml.Tree");

/**
  *	Branch class is part of a Tree-Branch-Leaf trio
  *	@class		Branch
  *	@package	org.active-link.xml
  *	<AUTHOR>
  *	@version	0.4.0
  *	@extends	Tree
  *	@requires	Tree
  *	@see		Tree, Leaf
  */

class Branch extends Tree {

}

?>
