/findfont-enc {                    % => FontName Encoding
  exch
  findfont                         % => FontSize <Font>
  dup length dict begin {
    1 index /FID ne {
      def 
    } {
      pop pop
    } ifelse
  } forall
  /Encoding 1 index def
  currentdict
  end

  /Font-Enc exch definefont        % => <Font>
  exch pop
} def

/font-ascender {                   % => <<Font>>
  dup /FontBBox get 3 get 0
  2 index /FontMatrix get transform
  pop                              % => <<Font>> Ascender
  %1 index font-descender sub       % => <<Font>> Ascender'
  exch pop
} def

/font-descender {                  % => <<Font>>
  dup /FontBBox get 1 get 0
  2 index /FontMatrix get transform
  pop
  exch pop
  neg
} def

/font-underline-pos {             % => <<Font>>
  dup /FontInfo get               % => <<Font>> <<Info>>
% some fonts (especially when you're using Adobe Distiller) do not contain /UnderlinePosition. 
  dup /UnderlinePosition known {  
    /UnderlinePosition get
  } {
    pop 2 px
  } ifelse                        % => <<Font>> UP

  0 exch                          % => <<Font>> O UP
  
  3 2 roll /FontMatrix get        % => <<Font>> O UP
  transform                       % => 0 UPos
  exch pop
} def

/font-overline-pos {               % => <<Font>>
  dup /FontBBox get 3 get          % => <<Font>> Top
  0.8 mul

  0 exch 
  2 index /FontMatrix get          
  transform
  exch pop
  exch pop
} def

/font-line-through-pos {           % => <<Font>>
  dup /FontBBox get 3 get          % => <<Font>> Top
  0.25 mul

  0 exch 
  2 index /FontMatrix get          
  transform
  exch pop
  exch pop
} def