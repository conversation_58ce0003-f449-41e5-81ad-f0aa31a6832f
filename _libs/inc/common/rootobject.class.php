<?php

require_once 'logger.class.php';

class RootObject {

    public $logger;

    public function __construct() {
    }

    public function get($var) {

       if (isset($this->$var)) {
           return $this->$var;
       }

       $privateVar = '_' . $var;
       if (isset($this->$privateVar)) {
           return $this->$privateVar;
       }

       return false;
   }

    public function set($var, $value) {

       if (isset($this->$var)) {
           $this->$var = $value;
           return $this->$var;
       }

       $privateVar = '_' . $var;
       if (isset($this->$privateVar)) {
           $this->$privateVar = $value;
           return $this->$privateVar;
       }

       return false;
   }

    public function setLogger($logFileName = '') {
        //set logger
        $className = get_class($this);
        $this->logger = new Logger($className, $logFileName);
    }

    public function getLang() {
        return @$_SESSION['lang'];
    }

    public function getTheme() {
        return @$_SESSION['theme'];
    }

    public function __destruct() {
        //set logger
        if ($this->logger instanceof Logger) {
            unset($this->logger);
        }
    }
}

?>
