<?php

class Dropdown {

    /**
     * Defines model language to get dropdown options into, checking all
     * possible parameters, until one is found
     *
     * @param array $params - params of dropdown method, the element with index 0 is always the registry
     * @return string - model lang
     */
    protected static function defineModelLang(array $params) {
        if (!empty($params['model_lang'])) {
            return $params['model_lang'];
        } elseif (!empty($params['lang'])) {
            return $params['lang'];
        } elseif (!empty($params['self']) && is_object($params['self']) && is_subclass_of($params['self'], 'Model') && $params['self']->get('model_lang')) {
            return $params['self']->get('model_lang');
        } elseif (!empty($params[0]) && is_object($params[0]) && get_class($params[0]) == 'Registry') {
            $registry = $params[0];
            if ($registry['request'] && $registry['request']->get('model_lang')) {
                return $registry['request']->get('model_lang');
            } else {
                return $registry['lang'];
            }
        } else {
            return '';
        }
    }

    /**
     * Get customers
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCustomers($params = array()) {
        $registry = $params[0];

        $query = 'SELECT c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label, IF(c.active AND c.deleted=0, 1, 0) as active_option ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                 '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 ((!empty($params['tag'])) ?
                    'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS ctags' . "\n" .
                    '  ON (ctags.model=\'Customer\' AND c.id=ctags.model_id AND ' . General::buildClause('ctags.tag_id', preg_split('#\s*,\s*#', $params['tag'])).')' . "\n"
                    : '');

        $where = array(
            'deleted' => 'c.deleted=0',
            'subtype' => 'c.subtype="normal"'
        );
        if (isset($params['is_company']) && $params['is_company'] !== '') {
            $where['is_company'] = 'c.is_company = "' . $params['is_company'] . '"';
        }
        if (isset($params['id'])) {
            $where['id'] = 'c.id = "' . $params['id'] . '"';
        }
        if (isset($params['type']) && $params['type'] !== '') {
            $where['type'] = 'c.type in (' . $params['type'] . ')';
        }

        if (isset($where) && count($where)) {
            $query .= ' WHERE ' . implode(' AND ', $where);
        }

        $order_by = 'ci18n.name, ci18n.lastname';
        if (isset($params['order'])) {
            $order_by = $params['order'];
        }
        $query .= ' ORDER BY ' . $order_by . "\n";

        $query .= ' LIMIT ' . PH_MAX_DROPDOWN_OPTIONS . "\n";

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get customer branches
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCustomerBranches($params = array()) {
        $registry = $params[0];

        //respect the model lang
        $lang = self::defineModelLang($params);

        //define customer id
        if (!empty($params['customer'])) {
            //customer id is specified in the method params
            if (preg_match('#^\$.*#', $params['customer'])) {
                //the customer ID should be fetched from additional var
                $var_name = preg_replace('#^\$(.*)$#', '\\1', $params['customer']);
                $customer_id = $params['self']->getVarValue($var_name, true);
            } else {
                //the customer ID is set as integer value
                $customer_id = $params['customer'];
            }
        } elseif (!empty($params['self'])) {
            //customer id is the current customer assigned to the record or the id of the customer itself
            $customer_id = ($params['self']->modelName == 'Customer') ? $params['self']->get('id') : $params['self']->get('customer');
        }

        if (empty($customer_id)) {
            return array();
        } else {
            $query = 'SELECT c.id as option_value, ci18n.name as label, IF(c.active=1 AND c.deleted=0, 1, 0) as active_option ' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                     'WHERE c.parent_customer="' . $customer_id . '"' . "\n" .
                     '  AND c.subtype="branch"' . "\n" .
                     'ORDER BY c.active DESC, c.is_main DESC, ci18n.name ASC, c.id DESC';
            $records = $registry['db']->GetAll($query);
        }

        return $records;
    }


    /**
     * Get customer contact persons
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCustomerContactPersons($params = array()) {
        $registry = $params[0];

        //respect the model lang
        $lang = self::defineModelLang($params);

        //define customer id
        if (!empty($params['customer'])) {
            //customer id is specified in the method params
            if (preg_match('#^\$.*#', $params['customer'])) {
                //the customer ID should be fetched from additional var
                $var_name = preg_replace('#^\$(.*)$#', '\\1', $params['customer']);
                $customer_id = $params['self']->getVarValue($var_name, true);
            } else {
                //the customer ID is set as an integer value
                $customer_id = $params['customer'];
            }
        } elseif (!empty($params['self'])) {
            //customer id is the current customer assigned to the record or the id of the customer itself
            $customer_id = ($params['self']->modelName == 'Customer') ? $params['self']->get('id') : $params['self']->get('customer');
        }

        // check the type of the customer (person or company)
        if (empty($customer_id)) {
            return array();
        } else {
            $customer_info_query = 'SELECT c.id, CONCAT(ci18n.name, " ", ci18n.lastname) as name' . "\n" .
                                   'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                   '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                                   'WHERE c.id="' . $customer_id . '" AND c.subtype="normal" AND c.is_company = 0';
            $customer_info = $registry['db']->GetRow($customer_info_query);
            if ($customer_info) {
                // if there is a customer that is person then the current customer's name
                //has to be the only one in the dropdown
                $records = array(
                    array(
                        'option_value' => $customer_info['id'],
                        'label'        => $customer_info['name']
                    )
                );
                return $records;
            }
        }

        // by default there is no customer branch filter (i.e. get contact persons for all branches)
        $customer_branch_filter = '';
        if (!empty($params['customer_branch'])) {
            if (trim($params['customer_branch']) == 'all') {
                //simply do not filter by branch when all branches are specified
                $customer_branch_filter = '';
            } else {
                //customer branch id is specified in the method params
                if (preg_match('#^\$.*#', $params['customer_branch'])) {
                    //the customer branch ID should be fetched from additional var
                    $var_name = preg_replace('#^\$(.*)$#', '\\1', $params['customer_branch']);
                    // get the value without formatting (i.e. get option value, not label of a dropdown option)
                    $customer_branch = $params['self']->getVarValue($var_name, true);
                } else {
                    //the customer branch ID is set as an integer value
                    $customer_branch = $params['customer_branch'];
                }
                if ($customer_branch) {
                    $customer_branch_filter = ' AND cb.id IN (' . trim($customer_branch) . ')';
                } else {
                    $customer_branch_filter = '';
                }
            }
        }

        $query = 'SELECT ccp.id as option_value, CONCAT(ccpi18n.name, " ", ccpi18n.lastname) as label, IF(ccp.active=1 AND ccp.deleted=0, 1, 0) as active_option ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS cb' . "\n" .
                 'JOIN ' . DB_TABLE_CUSTOMERS . ' AS ccp' . "\n" .
                 '  ON (ccp.parent_customer=cb.id AND cb.subtype="branch")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ccpi18n' . "\n" .
                 '  ON (ccp.id=ccpi18n.parent_id AND ccpi18n.lang="' . $lang . '")' . "\n" .
                 'WHERE cb.parent_customer="' . $customer_id . '"' . "\n" .
                 '  AND ccp.subtype="contact"' . "\n" .
                 $customer_branch_filter . "\n" .
                 'ORDER BY ccp.active DESC, ccp.is_main DESC, CONCAT_WS(\' \', ccpi18n.name, ccpi18n.lastname) ASC, ccp.id DESC';
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get users
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getUsers($params = array()) {
        $registry = $params[0];

        if (!empty($params['department'])) {
            $query = 'SELECT parent_id FROM ' . DB_TABLE_USERS_DEPARTMENTS . ' AS ud' . "\n" .
                     'WHERE department_id IN (' . $params['department'] . ')';
            $params['ids'] = $registry['db']->GetCol($query);
        }

        $sql = $where = array();
        $sql['select']  = 'SELECT DISTINCT(u.id) as option_value, CONCAT(ui18n.firstname, " ", ui18n.lastname) as label, IF(u.active AND u.deleted=0, 1, 0) as active_option, u.is_portal ';
        $sql['from']    = 'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                          'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                          '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . self::defineModelLang($params) . '")';
        $where[]        = 'WHERE u.hidden=0';

        if (!empty($params['ids'])) {
            $where[] = General::buildClause('id', $params['ids']);
        } elseif (!empty($params['department'])) {
            return array();
        }

        if (isset($params['is_portal'])) {
            $where[] = General::buildClause('u.is_portal', $params['is_portal']);
        }

        $sql['where']   = implode(" AND ", $where);
        $sql['order']   = 'ORDER BY u.is_portal ASC, active_option DESC, label ASC';

        $query = implode("\n", $sql);

        $records = $registry['db']->GetAll($query);

        $users_optgroups = array();

        if (isset($params['include_not_assign_option']) && $params['include_not_assign_option']) {
            // add opt group and option for no user assigned
            $no_user_assigned_label = $registry['translater']->translate('no_user_assigned');
            $users_optgroups[$no_user_assigned_label][] = array(
                'label' => "<< $no_user_assigned_label >>",
                'option_value' => 'no_user_assigned');
        } else if ($registry->get('module') == 'announcements' && isset($params['include_not_read_option']) && $params['include_not_read_option']) {
            $not_read_by_anyone_label = $registry['translater']->translate('not_read_by_anyone');
            $users_optgroups[$not_read_by_anyone_label][] = array(
                'label' => "<< $not_read_by_anyone_label >>",
                'option_value' => 'not_read_by_anyone'
            );
        }

        if ($registry['request']->get('real_module') == 'dashlets' ||
            $registry->get('module') == 'dashlets' && $registry->get('action') != 'search' ||
            isset($params['current_user']) && $params['current_user']) {
            //add 'current user' option in the array
            $current_user_label = $registry['translater']->translate('current_user');
            $users_optgroups[$current_user_label][] = array(
                'label' => "<< $current_user_label >>",
                'option_value' => 'currentUser');
        }

        if (isset($params['include_nobody']) && $params['include_nobody']) {
            // add opt group and option for nobody
            $nobody_label = $registry['translater']->translate('nobody');
            $users_optgroups[$nobody_label][] = array(
                'label' => "<< $nobody_label >>",
                'option_value' => '0');
        }

        $normal_users_label = $registry['translater']->translate('normal_users');
        $portal_users_label = $registry['translater']->translate('portal_users');

        $users_optgroups[$normal_users_label] = array();
        $users_optgroups[$portal_users_label] = array();
        foreach ($records as $record) {
            $optgroup_label = ($record['is_portal']) ? $portal_users_label : $normal_users_label;
            $users_optgroups[$optgroup_label][] = $record;
        }

        if (empty($users_optgroups[$normal_users_label])) {
            unset($users_optgroups[$normal_users_label]);
        }

        if (empty($users_optgroups[$portal_users_label])) {
            unset($users_optgroups[$portal_users_label]);
        }

        $count_optgroups = count(array_keys($users_optgroups));
        if ($count_optgroups == 1) {
            $options = array_shift($users_optgroups);
            return $options;
        } else {
            if ($count_optgroups) {
                $users_optgroups['contain_optgroups'] = 1;
            }
            return $users_optgroups;
        }
    }

    /**
     * Get unfinished subprojects
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getUnfinishedSubprojects($params = array()) {
        $registry = $params[0];
        $project_id = !empty($params['self']) ? $params['self']->get('project') : '';

        $query = 'SELECT p.id as option_value, pi18n.name as label ' . "\n" .
                 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                 ' ON (p.id=pi18n.parent_id AND pi18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 ' WHERE p.parent_project="' . $project_id . '"' . "\n" .
                 ' AND p.parent_project <> 0' . "\n" .
                 ' AND p.status in ("planning", "progress", "control")';
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get unfinished phases
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getUnfinishedPhases($params = array()) {
        if (empty($params['self']) || !is_object($params['self']) || get_class($params['self']) != 'Project') {
            return array();
        }

        $registry = $params[0];
        $project_id = $params['self']->get('id');
        $project_type = $params['self']->get('type');

        $query = 'SELECT s.id as option_value, si18n.name as label ' . "\n" .
                 'FROM ' . DB_TABLE_STAGES . ' AS s' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS si18n' . "\n" .
                 ' ON (s.id=si18n.parent_id AND si18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_STAGES_HISTORY . ' AS sh' . "\n" .
                 ' ON (s.id=sh.stage_id AND sh.model_id="' . $project_id . '")' . "\n" .
                 ' WHERE s.model="Project"' . "\n" .
                 ' AND s.model_type="' . $project_type . '"' . "\n" .
                 ' AND sh.finished = "0000-00-00 00:00:00"' . "\n" .
                 'ORDER BY s.active DESC, s.model_type ASC';
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get projects
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getProjects($params = array()) {
        $registry = $params[0];

        $sql['select'] = 'SELECT p.id as option_value,   CONCAT(p.code, " ", pi18n.name) as label, IF(p.active AND p.deleted=0, 1, 0) as active_option ';
        $sql['from'] = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                 ' ON (p.id=pi18n.parent_id AND pi18n.lang="' . self::defineModelLang($params) . '")';

        $where[] = 1;
        if (isset($params['type'])) {
            $where[] = 'p.type = ' . $params['type'];
        }
        if (isset($params['status'])) {
            $where[] = 'p.status = ' . $params['status'];
        }

        $sql['where']   = 'WHERE ' . implode(" AND ", $where);
        $sql['order']   = 'ORDER BY label';

        $query = implode("\n", $sql);

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get employees
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getEmployees($params = array()) {
        $registry = $params[0];

        $query = 'SELECT c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label, IF(c.active AND c.deleted=0, 1, 0) as active_option ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                 (!empty($params['employees_with_users_only']) ?
                    'JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                    '  ON c.id=u.employee' : '') . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                 '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . self::defineModelLang($params) . '")' . "\n";

        $where[] = 'type = ' . PH_CUSTOMER_EMPLOYEE;
        if (isset($params['deleted'])) {
            $where[] = 'c.deleted_by = '.$params['deleted'];
        } else {
            $where[] = 'c.deleted_by = 0';
        }

        if (isset($where) && count($where)) {
            $query .= ' WHERE ' . implode(' AND ', $where);
        }
        $query .= ' ORDER BY c.active DESC, ci18n.name, ci18n.lastname';
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get on change options
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getOnChangeOptions($params = array()) {
        $registry = $params[0];
        $parent_name = $params['parent_name'];
        $records = array();
        if ($registry['request']->isRequested('change_options')) {
            //from request on change
            $option_value = $registry['request']->get($parent_name);
        } elseif (!empty($params['self'])) {
            //default
            $model = $params['modelName'];
            $model_id = $params['$model_id'];
            $model_type = $params['self']->get('type');
            $table_name = 'DB_TABLE_'.strtoupper($model).'S_CSTM';
            if (defined($table_name) && $model_id) {
                $table_name = constant($table_name);
                $query = 'SELECT id FROM '.DB_TABLE_FIELDS_META.
                        ' WHERE name="'.$parent_name.'" and model="'.$model .'"'.
                        ' AND model_type='.$model_type;
                $var_id = $registry['db']->GetOne($query);
                $query = 'SELECT value FROM '.$table_name.' WHERE model_id='.$model_id.' and var_id='.$var_id;
                $option_value = $registry['db']->GetOne($query);
            }
        }

        // if option_value is not set then the options for the related dropdown are not taken
        if (isset($option_value)) {
            $query = 'SELECT DISTINCT fo2.label as label, fo2.option_value, fo2.extended_value FROM '.DB_TABLE_FIELDS_OPTIONS. ' AS fo'.
                     ' LEFT JOIN '.DB_TABLE_FIELDS_OPTIONS.' AS fo2 ON fo2.parent_name=fo.child_name '.
                     ' WHERE fo.parent_name='."'".$parent_name."' AND fo2.parent_name IS NOT NULL".
                     ' AND (fo2.lang = \'' . self::defineModelLang($params) . '\' OR fo2.lang IS NULL)'.
                     ' AND fo.option_value='."'".$option_value."'".
                     ' ORDER BY fo2.position';
            $records = $registry['db']->GetAll($query);
        }

        return $records;
    }

    /**
     * Get article categories
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getArticleCategories($params = array()) {
        $registry = $params[0];

        $category_params = !empty($params['category']) ? preg_split('#\s*,\s*#', $params['category']) : array(1);

        $filters = array(
            'model_lang' => self::defineModelLang($params),
            'sanitize' => true,
            'where' => array(
                'c1.id = ' . (count($category_params) == 1 ? intval($category_params[0]) : 1),
            ),
        );

        // GET CATEGORIES
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php';
        $categories = Nomenclatures_Categories::getTree($registry, $filters);

        // if multiple subtrees should be taken, get node ids of each
        $category_selection = array();
        if (count($category_params) > 1) {
            foreach ($category_params as $category) {
                $category = intval($category);
                if ($category <= 0 || in_array($category, $category_selection)) {
                    continue;
                }
                $category_selection = array_merge($category_selection, Nomenclatures_Categories::getTreeDescendantsIds($registry, $category));
            }
        }
        // order selected nodes in the same way as the tree
        // so that level offset works correctly later
        if ($category_selection && $categories) {
            $category_selection = array_keys(array_intersect_key($categories, array_flip($category_selection)));
        }

        if (!empty($params['exclude_root']) && !$category_selection) {
            array_shift($categories);
            $level_correction = -1;
        } else {
            $level_correction = 0;
        }

        $records = array();
        foreach ($categories as &$category) {
            if ($category_selection) {
                if (!in_array($category->get('id'), $category_selection)) {
                    continue;
                }
                // adjust levels so that selected nodes start from level 0
                // but only if they don't belong to the subtree of another selected node
                if (in_array($category->get('id'), $category_params) && !(array_key_exists($category->get('ancestor'), $records))) {
                    $category->set('level', 0, true);
                } elseif (array_key_exists($category->get('ancestor'), $categories)) {
                    $category->set('level', $categories[$category->get('ancestor')]->get('level') + 1, true);
                }
            }
            $records[$category->get('id')] = array(
                'label' => sprintf('%s%s', str_repeat('&nbsp;&nbsp;', $category->get('level')+$level_correction), $category->get('name')),
                'option_value' => $category->get('id'),
                //extended value is used for print
                //do not indent the category name for print
                'extended_value' => $category->get('name'),
                'active_option' => (!$category->isActivated() || $category->isDeleted()) ? 0 : 1,
            );
        }
        $records = array_values($records);

        return $records;
    }

    /**
     * Get related customers
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getRelatedCustomers($params = array()) {
        $registry = $params[0];
        if (!isset($params['self'])) {
            return self::getCustomers($params);
        }
        $main_customer_id = $params['self']->get('customer');
        if (!$main_customer_id) {
            return array();
        }

        $where = array('parent_id=' . $main_customer_id);
        if (!empty($params['relative_type'])) {
            $where[] = sprintf("relative_type='%s'", $params['relative_type']);
        }

        //get the ids of the related customers
        $query = 'SELECT child_id ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_RELATIVES . "\n" .
                 ' WHERE parent_id=' . $main_customer_id;
        $related_customers_ids = $registry['db']->GetCol($query);

        //append the main customer id
        $related_customers_ids[] = $main_customer_id;

        //get names and ids of the related customers
        $query = 'SELECT c.id as option_value, IF((c.is_company), ci18n.name, CONCAT(ci18n.name, " ", ci18n.lastname)) AS label, IF(c.active AND c.deleted=0, 1, 0) as active_option ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                 ' ON (c.id=ci18n.parent_id AND ci18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 ' WHERE c.id IN (' . implode(', ', $related_customers_ids) . ')' . "\n" .
                 ' ORDER BY ci18n.name, ci18n.lastname';
        $records = $registry['db']->GetAll($query);

        $indent_char = '--';
        if (preg_match('#view.*|generate|print#', $registry['action'])) {
            $indent_char = '';
        }

        //rearrange the main customer to be at the beginning and the relative customers labels have a prefix
        foreach ($records as $idx => $item) {
            if ($item['option_value'] == $main_customer_id) {
                $main_customer = $item;
                unset($records[$idx]);
            } else {
                $records[$idx]['label'] = $indent_char . $item['label'];
            }
        }

        @array_unshift($records, $main_customer);

        return $records;
    }

    /**
     * Get custom dropdown info
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCustomDropdown($params = array()) {

        $registry = $params[0];
        //get table structure
        $fields = $registry['db']->MetaColumns(constant($params['table']));
        if (empty($fields) || !is_array($fields)) {
            return array();
        }

        //exception for layouts table
        $id_param = 'id';
        if (isset($params['id_param'])) {
            $id_param = $params['id_param'];
        } elseif ($params['table'] == 'DB_TABLE_LAYOUTS') {
            $id_param = 'layout_id';
        } elseif ($params['table'] == 'DB_TABLE_COUNTRY_LIST') {
            $id_param = 'country_code';
        }

        $sql = array('select' => 'SELECT ',
                     'from' => 'FROM ',
                     'join' => '',
                     'where' => 'WHERE ',
                     'order' => '');//'ORDER BY ' . $params['label']);

        //construct WHERE clause in function of table structure
        if (array_key_exists('DELETED_BY', $fields) && (!empty($params['where']) && !preg_match('#t\.deleted#', $params['where']) || empty($params['where']))) {
            $sql['where'] .= 't.deleted_by = 0 AND ';
        }
        if (array_key_exists('HIDDEN', $fields) && (!empty($params['where']) && !preg_match('#t\.hidden#', $params['where']) || empty($params['where']))) {
            $sql['where'] .= 't.hidden = 0 AND ';
        }
        if (!empty($params['action']) && $params['action'] == 'add' &&
          array_key_exists('INHERITANCE', $fields) && (!empty($params['where']) && !preg_match('#t\.inheritance#', $params['where']) || empty($params['where']))) {
              $sql['where'] .= 't.inheritance = 0 AND ';
        }
        if ($registry['currentUser']->get('role') != PH_ROLES_ADMIN && empty($params['get_system']) && !in_array($params['table'], array('DB_TABLE_FINANCE_PAYMENTS_TYPES', 'DB_TABLE_COUNTRY_LIST'))) {
            $sql['where'] .= '(t.' . $id_param . ' > 0' . (preg_match('/DB_TABLE_ANNOUNCEMENTS_(TYPES|CATEGORIES)/', $params['table']) ? ' OR t.' . $id_param . ' = ' . PH_ANNOUNCEMENT_COMMERCIAL_TYPE : '') . ') AND ';
        }

        //construct query

        //default option label is NAME
        if (empty($params['label'])) {
            $params['label'] = 'name';
        }
        $parts = preg_split('#\s*,\s*#', $params['label']);
        $params['label'] = 'CONCAT_WS(\' \'';
        foreach ($parts as $part) {
            $params['label'] .= sprintf(', IF(%s IS NOT NULL, %s, \'\')', $part, $part);
        }
        $params['label'] .= ')';

        //default option value is ID
        if (empty($params['value'])) {
            $params['value'] = 'id';
        }
        $parts = preg_split('#\s*,\s*#', $params['value']);
        $params['value'] = 'CONCAT_WS(\'\'';
        foreach ($parts as $part) {
            $params['value'] .= sprintf(', IF(%s IS NOT NULL, %s, \'\')', $part, $part);
        }
        $params['value'] .= ')';

        if (!empty($params['value_prefix'])) {
            $vpr = $params['value_prefix'];
            //prefix for financial documents types and sections needs to be
            //replaced according to "model" field of the table we are searching in
            preg_match('#finance_documents_(types|sections)_#', $vpr, $matches);
        } else {
            $vpr = '';
        }

        //prepare select statement
        if (!empty($params['assoc']) && $params['assoc']) {
            $sql['select'] .= 'CONCAT(' . (!empty($matches[1]) ? 'LCASE(`model`), \'s_' . $matches[1] . '_\'' : '\'' . $vpr . '\'') . ', ' . $params['value'] . '),' . "\n";
        }
        $sql['select'] .= $params['label'] . ' as label,' . "\n" .
                          'CONCAT(' . (!empty($matches[1]) ? 'LCASE(`model`), \'s_' . $matches[1] . '_\'' : '\'' . $vpr . '\'') . ', ' . $params['value'] . ') AS option_value';
        if (array_key_exists('ACTIVE', $fields)) {
            $sql['select'] .= ', t.active as active_option';
        }
        if (!empty($params['class_name'])) {
            $sql['select'] .= ', ' . $params['class_name'] . ' AS class_name';
        }

        //prepare from and join
        $sql['from'] .= '`' . constant($params['table']) . '` as t';
        if (isset($params['table_i18n'])) {
            $sql['join'] = 'LEFT JOIN ' . constant($params['table_i18n']) . ' as ti18n' . "\n";
            $sql['join'] .= '  ON t.' . $id_param . '=ti18n.parent_id AND ti18n.lang = \'' . self::defineModelLang($params) . '\'';
        } else {
            $sql['where'] .= 't.lang = \'' . self::defineModelLang($params) . '\' AND ';
        }

        //prepare where
        //replace any variable that might occur
        //the WHERE might contain some variables defined in the parameters
        //EXAMPLE:
        //$requested_nom_type := request('nom_type')
        //where := type = $requested_nom_type
        if (isset($params['where']) && preg_match('#\$#', $params['where'])) {
            $variables = array();
            $values = array();
            foreach ($params as $var => $val) {
                if (preg_match('#^\$.*#', $var)) {
                    $variables[] = $var;
                    $values[] = $val;
                }
            }
            if (!empty($variables) && !empty($values)) {
                $params['where'] = str_replace($variables, $values, $params['where']);
            }
        }
        // avoid SQL errors when condition is incorrent after replacement
        if (!empty($params['where']) && preg_match('#IN\s*\(\s*\)#i', $params['where'])) {
            $params['where'] = preg_replace('#IN\s*\(\s*\)#i', 'IN (0)', $params['where']);
        }
        //replace any model's property that might occur
        if (isset($params['where']) && preg_match_all('#\<([^\<\>]*)\>#', $params['where'], $matches)) {
            foreach ($matches[1] as $match) {
                $replace = '0';
                if (!empty($params['self'])) {
                    $replace = $params['self']->get($match);
                    // avoid annoying SQL errors when model has no id or is not an existing model, just a blank one
                    if ($match == 'id' && !$replace) {
                        $replace = $params['self']->get('model_id') ?: '0';
                    } elseif (!$params['self']->isDefined($match)) {
                        $replace = '0';
                    }
                }
                $params['where'] = str_replace('<' . $match . '>', $replace, $params['where']);
            }
        }

        //replace with id of system type of task if searching for task types
        if (isset($params['where']) && preg_match('#PH_TASK_SYSTEM_TYPE#', $params['where'])) {
            $params['where'] = str_replace('PH_TASK_SYSTEM_TYPE', PH_TASK_SYSTEM_TYPE, $params['where']);
        }

        $sql['where'] .= !empty($params['where']) ? $params['where'] : 1;

        //build the ORDER BY clause
        if (empty($params['order_by'])) {
            $sql['order'] = 'ORDER BY ' . $params['label'];
        } else {
            $sql['order'] = 'ORDER BY ' . $params['order_by'] . ', ' . $params['label'];
        }

        $query = implode("\n", $sql);

        // TODO: This queries are executed twice when opening the advanced search
        if (!empty($params['assoc']) && $params['assoc']) {
            $options = $registry['db']->GetAssoc($query);
        } else {
            $options = $registry['db']->GetAll($query);
        }

        //if dropdown contains departments, add extra option for current user's departments
        if (constant($params['table']) == DB_TABLE_DEPARTMENTS && empty($params['skip_current_user_departments'])) {
            $user_depts_label = '<< ' . $registry['translater']->translate('current_user_departments') . ' >>';
            $user_depts_option = array('label' => $user_depts_label, 'option_value' => 'currUserDepartments');
            if (!empty($params['assoc'])) {
                $options = array('currUserDepartments' => $user_depts_option) + $options;
            } else {
                array_unshift($options, $user_depts_option);
            }
        }

        // if a parameter is set to add custom option(s) before the found ones
        if (isset($params['add_first_option'])) {
            $params['add_first_option'] = preg_split('#\s*,\s*#', $params['add_first_option']);
            $new_opts = array();
            foreach ($params['add_first_option'] as $opt) {
                $opt_array = array(
                    'label' => ($opt === '' ?
                        '[' . $registry['translater']->translate('please_select') . ']' :
                        ($registry['translater']->translate(constant($params['table']) . '_' . $opt) ?:
                            $registry['translater']->translate($opt))),
                    'option_value' => $opt
                );
                if (!empty($params['assoc'])) {
                    $new_opts[$opt] = $opt_array;
                } else {
                    $new_opts[] = $opt_array;
                }
            }
            if (!empty($params['assoc'])) {
                $options = $new_opts + $options;
            } else {
                $options = array_merge($new_opts, $options);
            }
        }

        return $options;
    }

    /**
     * Get defined tag colors from model and get their labels from tags.ini
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getTagsColors($params = array()) {

        $registry = $params[0];

        require_once PH_MODULES_DIR . 'tags/models/tags.factory.php';
        $lang_file = PH_MODULES_DIR . 'tags/i18n/' . $registry['lang'] . '/tags.ini';
        if (!$registry['translater']->isLoadedFile($lang_file, 'module')) {
            $registry['translater']->loadFile($lang_file);
        }

        $records = array();
        foreach (Tags::$availableColors as $color) {
            $records[] = array(
                'option_value' => $color,
                'label' => $registry['translater']->translate('tags_color_' . $color)
            );
        }

        return $records;
    }

    /**
     * Gets models which tags are defined for.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getTagsModels($params = array()) {
        $registry = $params[0];

        require_once PH_MODULES_DIR . 'tags/models/tags.factory.php';
        $lang_file = PH_MODULES_DIR . 'tags/i18n/' . $registry['lang'] . '/tags.ini';
        if (!$registry['translater']->isLoadedFile($lang_file, 'module')) {
            $registry['translater']->loadFile($lang_file);
        }

        $records = array();
        foreach (Tags::$availableModels as $model) {
            $records[] = array(
                'option_value' => $model,
                'label' => $registry['translater']->translate('tags_model_' . $model),
                'class_name' => Model_Factory::hasTypes($model) ? '' : 'no_model_types'
            );
        }
        usort($records, function ($a, $b) { return $a['label'] > $b['label']; });

        return $records;
    }

    /**
     * Get departments and users
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getDepartmentsAndUsers($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $db = &$registry['db'];

        $query = 'SELECT CONCAT(\'Users-\', u.id) as option_value, CONCAT(ui18n.firstname, \' \', ui18n.lastname) as label, IF(u.active AND u.deleted=0, 1, 0) as active_option, u.is_portal ' . "\n" .
                 'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                 '  ON u.id=ui18n.parent_id AND ui18n.lang = \'' . $lang . '\'' . "\n" .
                 'WHERE u.hidden=0' . "\n" .
                 'ORDER BY u.is_portal ASC, active_option DESC, label ASC';
        $users = $db->GetAll($query);

        $query = 'SELECT CONCAT(\'Departments-\', d.id) as option_value, di18n.name as label, IF(d.active AND d.deleted=0, 1, 0) as active_option' . "\n" .
                 'FROM ' . DB_TABLE_DEPARTMENTS . ' AS d' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS di18n' . "\n" .
                 '  ON d.id=di18n.parent_id AND di18n.lang = \'' . $lang . '\'' . "\n" .
                 'ORDER BY active_option DESC, label ASC';
        $departments = $db->GetAll($query);

        $normal_users_label = $registry['translater']->translate('normal_users');
        $portal_users_label = $registry['translater']->translate('portal_users');

        $optgroups = array($normal_users_label => array(),
                           $portal_users_label => array(),
                           $registry['translater']->translate('menu_departments') => $departments,
                           'contain_optgroups' => true);

        foreach ($users as $record) {
            $optgroup_label = ($record['is_portal']) ? $portal_users_label : $normal_users_label;
            $optgroups[$optgroup_label][] = $record;
        }

        if (empty($optgroups[$normal_users_label])) {
            unset($optgroups[$normal_users_label]);
        }

        if (empty($optgroups[$portal_users_label])) {
            unset($optgroups[$portal_users_label]);
        }

        // prepare optgroup and option for current user
        if ($registry['request']->get('real_module') == 'dashlets' ||
            $registry->get('module') == 'dashlets' && $registry->get('action') != 'search' ||
            isset($params['current_user']) && $params['current_user']) {
            //add 'current user' optgroup and option in the array
            $current_user_label = $registry['translater']->translate('current_user');
            $current_user_optgroup = array(array('label' => '<< ' . $current_user_label . ' >>',
                                                 'option_value' => 'Users-currentUser'));

            $optgroups = array_merge(array($current_user_label => $current_user_optgroup),
                                     $optgroups);
        }

        // prepare optgroup and option for no user or departments assigned
        $no_user_assigned_label = $registry['translater']->translate('no_user_assigned');
        $no_user_assigned_optgroup = array(array('label' => "<< $no_user_assigned_label >>",
                                                 'option_value' => 'no_user_assigned'));

        $optgroups = array_merge(array($no_user_assigned_label => $no_user_assigned_optgroup),
                                 $optgroups);

        return $optgroups;
    }

    /**
     * Get departments to show them in a dropdown
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of departments
     */
    public static function getDepartments($params) {
        $registry = $params[0];

        $filters = array(
            'model_lang' => self::defineModelLang($params),
            'where' => array(
                'd1.id IN (' . (!empty($params['department']) ? $params['department'] : 1) . ')',
            ),
        );

        // GET DEPARTMENTS
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $departments = Departments::getTree($registry, $filters);

        if (!empty($params['exclude_root'])) {
            array_shift($departments);
            $level_correction = -1;
        } else {
            $level_correction = 0;
        }

        $indent_char = '-';
        if (preg_match('#view.*|generate|print#', $registry['action'])) {
            $indent_char = '';
        }

        $records = array();
        foreach ($departments as $department) {
            $records[] = array(
                'label' => sprintf('%s%s', str_repeat($indent_char, $department->get('level')+$level_correction), $department->get('name')),
                'option_value' => $department->get('id'),
                'active_option' => (!$department->isActivated() || $department->isDeleted()) ? 0 : 1,
            );
        }

        return $records;
    }

    /**
     * Get offices to show them in a dropdown
     *
     * @param array $params params as array, the element with index 0 is always the registry
     * @return array
     */
    public static function getOffices($params) {
        $registry = $params[0];

        // Get offices
        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
        $offices = Offices::search($registry);

        $offices_array = array();
        foreach($offices as $office_object){
            $offices_array[] = array(
                'option_value' => $office_object->get('id'),
                'label' => $office_object->get('name'),
                'active_option' => $office_object->get('active')
            );
        }

        return $offices_array;
    }

    /**
     * Gets announcements from the database
     * Available filters:
     * type := -1, 1 - a list of types
     * assignments_users := currentUser, 38 - assignments for users as list of users ids (allows special keyword currentUser)
     * assignments_departments := 1 - assignments for departments as list of department ids
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getAnnouncements($params) {
        $registry = $params[0];

        $filters = array(
            'sanitize' => true,
            'model_lang' => self::defineModelLang($params),
            'where' => array(),
        );

        //manage the filters
        //search all announcements (including archived)
        $filters['where'][] = 'a.search_archive = \'all\' AND';

        //filter by types
        if (!empty($params['type'])) {
            $types = preg_split('#\s*,\s*#', $params['type']);
            foreach ($types as $idx => $type) {
                $filters['where'][] = 'a.type = \''.$type.'\' ' . (($idx+1 < count($types)) ? 'OR' : ' AND');
            }
        }
        //filter by assignments by users
        if (!empty($params['assignments_users'])) {
            $users = preg_split('#\s*,\s*#', $params['assignments_users']);
            foreach ($users as $idx => $user) {
                $filters['where'][] = 'aa.assigned_to = \'Users-'.$user.'\' ' . (($idx+1 < count($users)) ? 'OR' : 'AND');
            }
        }
        //filter by assignments by departments
        if (!empty($params['assignments_departments'])) {
            $departments = preg_split('#\s*,\s*#', $params['assignments_departments']);
            foreach ($departments as $idx => $department) {
                $filters['where'][] = 'aa.assigned_to = \'Departments-'.$department.'\' ' . (($idx+1 < count($departments)) ? 'OR' : 'AND');
            }
        }

        // GET ANNOUNCEMENTS
        require_once PH_MODULES_DIR . 'announcements/models/announcements.factory.php';
        $announcements = Announcements::search($registry, $filters);

        $records = array();
        foreach ($announcements as $announcement) {
            $records[] = array(
                'label'         => $announcement->get('subject'),
                'option_value'  => $announcement->get('id'),
                //the archived announcements are marked as inactive
                'active_option' => (!$announcement->isActivated() || $announcement->isDeleted() || $announcement->isArchived()) ? 0 : 1,
            );
        }

        return $records;
    }

    /**
     * Get groups to show them in a dropdown
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of groups
     */
    public static function getGroups($params) {
        $registry = $params[0];

        $filters = array(
            'model_lang' => self::defineModelLang($params),
            'where' => array(
                'gn.id = ' . (!empty($params['group']) ? $params['group'] : 1),
            ),
        );

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups = Groups::getTree($registry, $filters);

        if (!empty($params['exclude_root'])) {
            array_shift($groups);
            $level_correction = -1;
        } else {
            $level_correction = 0;
        }

        $indent_char = '-';
        if (preg_match('#view.*|generate|print#', $registry['action'])) {
            $indent_char = '';
        }

        $records = array();
        foreach ($groups as $group) {
            $records[] = array(
                'label' => sprintf('%s%s', str_repeat($indent_char, $group->get('level')+$level_correction), $group->get('name')),
                'option_value' => $group->get('id'),
                'active_option' => (!$group->isActivated() || $group->isDeleted()) ? 0 : 1,
            );
        }

        return $records;
    }

    /**
     * Get active states for the records (activated or deactivated)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of active states
     */
    public static function getActiveStates($params) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('activated'),
                                 'option_value' => '1'),
                      1 => array('label' => $registry['translater']->translate('deactivated'),
                                 'option_value' => '0')
                     );
    }

    /**
     * Get active options (deactivated only or all)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of active options
     */
    public static function getActiveOptions($params) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('activated_deactivated'),
                                 'option_value' => 'IS NOT NULL'),
                      1 => array('label' => $registry['translater']->translate('only_deactivated'),
                                 'option_value' => '= 0')
        );
    }

    /**
     * Get archive options (archived only or all)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of archive options
     */
    public static function getArchiveOptions($params) {
        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('archive_state_all'),
                                 'option_value' => 'all'),
                      1 => array('label' => $registry['translater']->translate('archive_state_archive'),
                                 'option_value' => 'archive')
                     );
    }

    /**
     * Get deleted options (deleted only or all)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of deleted options
     */
    public static function getDeletedOptions($params) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('deleted_all'),
                                 'option_value' => 'IS NOT NULL'),
                      1 => array('label' => $registry['translater']->translate('deleted_only'),
                                 'option_value' => ' > 0')
                     );
    }

    /**
     * Get annulled options (annulled or all records)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of annulled options
     */
    public static function getAnnulledOptions($params) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('annulled_all'),
                                 'option_value' => 'IS NOT NULL'),
                      1 => array('label' => $registry['translater']->translate('annulled_only'),
                                 'option_value' => '> 0')
                     );
    }

    /**
     * Get layouts types (system or custom)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of layout types
     */
    public static function getLayoutsTypes($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('layouts_type_system'),
                                 'option_value' => '1'),
                      1 => array('label' => $registry['translater']->translate('layouts_type_custom'),
                                 'option_value' => '0')
                     );
    }

    /**
     * Get patterns parts types (header/footer)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of types for patterns parts
     */
    public static function getPatternsPartsTypes($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('patterns_parts_header'),
                                 'option_value' => 'header'),
                      1 => array('label' => $registry['translater']->translate('patterns_parts_footer'),
                                 'option_value' => 'footer')
                     );

    }

    /**
     * Get options for portal records (portal/not portal)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of is_portal options
     */
    public static function getIsPortal($params) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('is_portal'),
                                 'option_value' => '1'),
                      1 => array('label' => $registry['translater']->translate('is_not_portal'),
                                 'option_value' => '0')
                     );
    }

    /**
     * Get file origins
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of files origins
     */
    public static function getFilesOrigin($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('files_origin_attached'),
                                 'option_value' => 'attached'),
                      1 => array('label' => $registry['translater']->translate('files_origin_generated'),
                                 'option_value' => 'generated'),
                      2 => array('label' => $registry['translater']->translate('files_origin_exported'),
                                 'option_value' => 'exported')
                     );
    }

    /**
     * Get file permissions
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of files permissions
     */
    public static function getFilesPermissions($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('files_permission_all'),
                                 'option_value' => 'all'),
                      1 => array('label' => $registry['translater']->translate('files_permission_group'),
                                 'option_value' => 'group'),
                      2 => array('label' => $registry['translater']->translate('files_permission_mine'),
                                 'option_value' => 'mine')
                     );
    }

    /**
     * Gets model types for layouts
     *
     * @param array $params - params as array, the element with index 0 is always the registry,
     *                        element with key 'model' holds model name
     * @return array - $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getLayoutsModelTypes($params = array()) {

        $registry = $params[0];
        $options = array (0 => array('label' => $registry['translater']->translate('all'),
                                     'option_value' => $params['model'] . '_0'));

        $types = array();
        if ($params['model'] == 'User') {
            $options[0]['label'] = $registry['translater']->translate('menu_users_mynzoom');
        } elseif (!preg_match('#^finance#i', $params['model'])) {
            $method = 'get' . General::singular2plural($params['model']) . 'Types';
            if (method_exists('Dropdown', $method)) {
                if ($params['model'] == 'Event') {
                    $params['include_system'] = 1;
                }
                $types = self::$method($params);
            }
        } elseif ($params['model'] == 'Finance_Payment') {
            $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini');
            require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
            $types = Finance_Dropdown::getFinancePaymentsTypes($params);
        } elseif ($params['model'] == 'Finance_Transfer') {
            $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_transfers.ini');
            $options[0]['label'] = $registry['translater']->translate('finance_transfer');
        } else {
            $types = self::getFinanceDocumentsTypes($params);
        }
        foreach ($types as $idx => $type) {
            $type['option_value'] = $params['model'] . '_' . $type['option_value'];
            $types[$idx] = $type;
        }

        $options = array_merge($options, $types);

        return $options;
    }

    /**
     * Gets models which patterns are defined for.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getPatternsModels($params = array()) {
        $registry = $params[0];

        $records = array(
           array('option_value' => 'Document', 'label' => $registry['translater']->translate('patterns_model_document')),
           array('option_value' => 'Customer', 'label' => $registry['translater']->translate('patterns_model_customer')),
           array('option_value' => 'Event', 'label' => $registry['translater']->translate('patterns_model_event')),
           array('option_value' => 'Report', 'label' => $registry['translater']->translate('patterns_model_report')),
           array('option_value' => 'Contract', 'label' => $registry['translater']->translate('patterns_model_contract')),
           array('option_value' => 'Nomenclature', 'label' => $registry['translater']->translate('patterns_model_nomenclature')),
           array('option_value' => 'Project', 'label' => $registry['translater']->translate('patterns_model_project')),
           array('option_value' => 'Task', 'label' => $registry['translater']->translate('patterns_model_task')),
           array('option_value' => 'Finance_Incomes_Reason', 'label' => $registry['translater']->translate('patterns_model_finance_incomes_reason')),
           array('option_value' => 'Finance_Expenses_Reason', 'label' => $registry['translater']->translate('patterns_model_finance_expenses_reason')),
           array('option_value' => 'Finance_Warehouses_Document', 'label' => $registry['translater']->translate('patterns_model_finance_warehouses_document')),
           array('option_value' => 'Finance_Annulment', 'label' => $registry['translater']->translate('patterns_model_finance_annulment')),
           array('option_value' => 'Finance_Payment', 'label' => $registry['translater']->translate('patterns_model_finance_payment')),
           array('option_value' => 'Finance_Invoices_Template', 'label' => $registry['translater']->translate('patterns_model_finance_invoices_template'))
        );

        return $records;
    }

    /**
     * Gets options for list and for record for patterns.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getPatternsList($params = array()) {
        $registry = $params[0];

        $records = array (
            array('option_value' => '0', 'label' => $registry['translater']->translate('patterns_list1')),
            array('option_value' => '1', 'label' => $registry['translater']->translate('patterns_list2'))
        );

        return $records;
    }

    /**
     * Gets options for type and for section for patterns.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getPatternsTypeSection($params = array()) {
        $registry = $params[0];

        $records = array (
            array('option_value' => '0', 'label' => $registry['translater']->translate('patterns_for_type')),
            array('option_value' => '1', 'label' => $registry['translater']->translate('patterns_for_section'))
        );

        return $records;
    }

    /**
     * Gets model types and sections (if available) for specified model for search in Patterns.
     *
     * @param array $params - params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getPatternsModelTypesSections($params = array()) {
        $registry = $params[0];

        if (empty($params['model'])) {
            return array();
        }
        $lang = self::defineModelLang($params);

        $model = $params['model'];
        $options = array (0 => array('label' => $registry['translater']->translate('all'),
                                     'option_value' => $model . '_0'));
        if ($model != 'Report') {
            $options[1] = array('label' => $registry['translater']->translate('patterns_no_section_type'),
                                'option_value' => $model . '_');
        }

        $type_sections_file = '';
        $sections_model_factory_name = '';
        $model_types = array();
        $module = '';

        if (preg_match('#^Finance_#', $model)) {
            if (preg_match('#^Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment|Invoices_Template|Payment)$#', $model)) {
                if ($model != 'Finance_Invoices_Template' && $model != 'Finance_Payment') {
                    $type_sections_file = PH_MODULES_DIR . 'finance/models/finance.documents_sections.factory.php';
                    $sections_model_factory_name = 'Finance_Documents_Sections';
                }

                $params['value'] = 'id';
                $params['label'] = 'name';
                if ($model == 'Finance_Payment') {
                    $params['table'] = 'DB_TABLE_FINANCE_PAYMENTS_TYPES';
                    $params['table_i18n'] = 'DB_TABLE_FINANCE_PAYMENTS_TYPES_I18N';
                } else {
                    $params['table'] = 'DB_TABLE_FINANCE_DOCUMENTS_TYPES';
                    $params['table_i18n'] = 'DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N';
                    $params['where'] = 't.model="' . $model . '"';
                }
                $model_types = self::getCustomDropdown($params);
            }
        } else {
            $sections_model_factory_name = General::singular2plural($model) . '_Sections';
            $module = strtolower(General::singular2plural($model));

            $type_sections_file = PH_MODULES_DIR . $module . '/models/' . $module . '.sections.factory.php';
            if (!file_exists($type_sections_file)) {
                $type_sections_file = '';
            }

            $types_method_name = 'get' . ucfirst($module) . 'Types';
            $model_types = self::$types_method_name($params);
        }

        foreach ($model_types as $idx => $mtype) {
            $mtype['option_value'] = $model . '_' . $mtype['option_value'];
            if (empty($mtype['label'])) {
                $mtype['label'] = ' ';
            }
            $model_types[$idx] = $mtype;
        }

        if ($type_sections_file) {

            $types_label = $registry['translater']->translate('patterns_type');
            $options = array_merge(array('' => $options), array($types_label => $model_types));

            require_once $type_sections_file;
            $alias = $sections_model_factory_name::getAlias($sections_model_factory_name, '');

            $filters = array('model_lang' => $lang,
                             'sort' => array($alias . '.name ASC'),
                             'sanitize' => true);
            if ($sections_model_factory_name == 'Finance_Documents_Sections') {
                $filters['where'] = array('fds.model="' . $model . '"');
            }

            $model_sections = $sections_model_factory_name::search($registry, $filters);

            $sections_label = $registry['translater']->translate('patterns_section');
            foreach ($model_sections as $section) {
                $options[$sections_label][] = array('label' => ($section->get('name') ? $section->get('name') : ' '),
                                                    'option_value' => $model . '_-' . $section->get('id'));
            }

            $options['contain_optgroups'] = 1;

        } else {
            $options = array_merge($options, $model_types);
        }

        return $options;
    }

    /**
     * Get notes origin to show then in a dropdown
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list notes origin
     */
    public static function getNotesOrigin($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('notes_manual'),
                                 'option_value' => 'manual'),
                      1 => array('label' => $registry['translater']->translate('notes_email'),
                                 'option_value' => 'email'),
                      2 => array('label' => $registry['translater']->translate('notes_phone'),
                                 'option_value' => 'phone')
                     );
    }

    /**
     * Get stages to show them in a dropdown
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of stages
     */
    public static function getStagesStatuses($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('stages_status_planning'),
                                 'option_value' => 'planning'),
                      1 => array('label' => $registry['translater']->translate('stages_status_progress'),
                                 'option_value' => 'progress'),
                      2 => array('label' => $registry['translater']->translate('stages_status_control'),
                                 'option_value' => 'control'),
                      3 => array('label' => $registry['translater']->translate('stages_status_finished'),
                                 'option_value' => 'finished')
                     );
    }

    /**
     * Gets distinct modules and controllers for predefined filters
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFiltersModulesControllers($params = array()) {
        $registry = $params[0];
        $query = 'SELECT DISTINCT CONCAT_WS(\'_\', module, controller) AS mc FROM ' . DB_TABLE_FILTERS . "\n" .
                 '  WHERE user_defined = 0' . "\n" .
                 '  ORDER BY module ASC';
        $db = $registry['db'];
        $modules = $db->GetCol($query);

        $records = array();
        foreach ($modules as $module) {
            $records[] = array('label' => $registry['translater']->translate('filters_module_' . $module),
                               'option_value' => $module);
        }
        return $records;
    }

    /**
     * Gets distinct calling modules and controllers for predefined filters
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFiltersModulesControllersFrom($params = array()) {
        $registry = $params[0];
        $query = 'SELECT DISTINCT CONCAT_WS(\'_\', module_from, controller_from) AS mc_from FROM ' . DB_TABLE_FILTERS . "\n" .
                 '  WHERE user_defined = 0' . "\n" .
                 '  ORDER BY module_from ASC';
        $db = $registry['db'];
        $modules = $db->GetCol($query);

        $records = array();
        foreach ($modules as $module) {
            $records[] = array('label' => $registry['translater']->translate('filters_module_from_' . $module),
                               'option_value' => $module);
        }
        return $records;
    }

    /**
     * Get documents types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getDocumentsTypes($params = array()) {
        $registry = $params[0];

        $action = 'search';
        if (!empty($params['action'])) {
            $action = $params['action'];
        }
        $additional_where = '';
        if ($action == 'add') {
            $additional_where = ' AND dt.inheritance = 0';
        }

        $query = 'SELECT dt.id as option_value, dti18n.name as label, dt.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                 '  ON (dt.id=dti18n.parent_id AND dti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE dt.deleted=0' . $additional_where . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get contracts types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getContractsTypes($params = array()) {
        $registry = $params[0];

        $action = 'search';
        if (!empty($params['action'])) {
            $action = $params['action'];
        }
        $additional_where = '';
        if ($action == 'add') {
            $additional_where = ' AND cot.inheritance = 0';
        }

        $query = 'SELECT cot.id as option_value, coti18n.name as label, cot.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS cot' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS coti18n' . "\n" .
                 '  ON (cot.id=coti18n.parent_id AND coti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE cot.deleted=0' . $additional_where . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get customer types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCustomersTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT ct.id as option_value, cti18n.name as label, ct.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_TYPES . ' AS ct' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                 '  ON ct.id = cti18n.parent_id AND cti18n.lang="' . self::defineModelLang($params) . '"' . "\n" .
                 'WHERE ct.deleted=0' . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get project types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getProjectsTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT pt.id as option_value, pti18n.name as label, pt.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_PROJECTS_TYPES . ' AS pt' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                 '  ON (pt.id=pti18n.parent_id AND pti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE pt.deleted=0' . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get event types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getEventsTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT et.id AS option_value, eti18n.name AS label, et.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_EVENTS_TYPES . ' AS et' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                 '  ON (et.id=eti18n.parent_id AND eti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE et.deleted=0' . (empty($params['include_system']) ? ' AND et.keyword NOT IN (\'reminder\', \'plannedtime\')' : '') . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get nomenclature types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getNomenclaturesTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT nt.id as option_value, nti18n.name as label, nt.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES_TYPES . ' AS nt' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                 '  ON (nt.id=nti18n.parent_id AND nti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE nt.deleted=0' . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get task types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getTasksTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT tt.id as option_value, tti18n.name as label, tt.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_TASKS_TYPES . ' AS tt' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS_TYPES_I18N . ' AS tti18n' . "\n" .
                 '  ON (tt.id=tti18n.parent_id AND tti18n.lang="' . self::defineModelLang($params) . '")' . "\n" .
                 'WHERE tt.deleted=0 AND tt.id!=' . PH_TASK_SYSTEM_TYPE . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get finance documents types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFinanceDocumentsTypes($params = array()) {
        $registry = $params[0];

        $query = 'SELECT fdt.id as option_value, fdti18n.name as label, fdt.active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                 '  ON fdt.id = fdti18n.parent_id AND fdti18n.lang="' . self::defineModelLang($params) . '"' . "\n" .
                 'WHERE fdt.deleted=0' . (isset($params['model']) ? ' AND fdt.model="' . $params['model'] . '"' : '') . "\n" .
                 'ORDER BY label';

        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get report types that print patterns can be added for
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getReportsTypes($params = array()) {
        // Get the registry
        $registry = $params[0];

        // Get the reports as dropdown options array
        $query = "
            SELECT r.id    AS option_value,
                ri18n.name AS label
              FROM " . DB_TABLE_REPORTS . " AS r
              LEFT JOIN " . DB_TABLE_REPORTS_I18N . " AS ri18n
                ON (ri18n.parent_id = r.id
                  AND ri18n.lang = '" . self::defineModelLang($params) . "')
              WHERE r.settings RLIKE 'allows_files_generation *:= *[1-9]+[0-9]*'
              ORDER BY label";
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get reports
     *
     * @param array $params - params as array, the element with index 0 is always the registry
     * @return array        - the reports as dropdown options array
     */
    public static function getReports($params = array()) {
        // Get the registry
        $registry = $params[0];

        // Get the reports models
        require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
        $filters = array(
            'sanitize' => true,
            'sort' => array('ri18n.name ASC'),
        );
        if (isset($params['visible'])) {
            $filters['visible'] = $params['visible'];
        }
        $reports = Reports::getReports($registry, $filters);

        // Get the reports as dropdown options array
        $reports_options = array();
        foreach ($reports as $report) {
            // Check the rights
            if (empty($params['skip_permissions_check']) && !$report->checkRights()) {
                continue;
            }

            // Prepare the option
            $reports_options[] = array(
                'label'        => $report->get('name'),
                'option_value' => 'reports_' . $report->get('type'),
            );
        }

        return $reports_options;
    }

    /**
     * Get attached files for current document
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getAttachments($params = array()) {
        $registry = $params[0];
        $current_user = $registry->get('currentUser');

        if (!empty($params['action'])) {
            $action = $params['action'];
        }

        $model = $params['self'];

        //get the model lang
        $lang = self::defineModelLang($params);

        $query = 'SELECT f.id, f.revision, f.filename, f.path, fi18n.name as name ' . "\n" .
                 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                 '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $lang . '")' . "\n" .
                 'WHERE f.model="' . $model->modelName . '" AND f.model_id="' . $model->get('id') . '" AND f.origin=\'attached\' AND f.deleted=0' . "\n" .
                 'ORDER BY f.id ASC' . "\n";

        $files = $registry['db']->GetAll($query);

        //the destination is the viewfile mode of the file itself
        if (!empty($params['output']) && $params['output'] == 'link_with_permissions') {
            $module = strtolower(General::singular2plural($model->modelName));
            $destination = sprintf('http://%s%s?%s=%s&%s=viewfile&viewfile=%s&file=', $_SERVER['SERVER_NAME'], $_SERVER['SCRIPT_NAME'], $registry['module_param'], $module, $module, $model->get('id'));
        } elseif (!empty($params['output']) && $params['output'] == 'just_id') {
            $destination = '';
        } else {
            $destination = sprintf('http://%s%s?%s=files&files=viewfile&viewfile=', $_SERVER['SERVER_NAME'], $_SERVER['SCRIPT_NAME'], $registry['module_param']);
        }

        $records = array();
        foreach ($files as $file) {
            if (file_exists($file['path'])) {
                $file_id = $file['id'];
                if (empty($params['output'])) {
                    //encrypt the file id so that no one could guess it
                    $file_id = General::encrypt($file['id'], '_viewfile_', 'xtea');
                }
                $option_value = $destination . $file_id;
                $records[] = array(
                    'label' => $file['name'],
                    'option_value' => $option_value
                );
            }
        }

        return $records;
    }

    /**
     * Get timezones
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of timezones
     */
    public static function getTimeZones($params = array()) {

        //prepare time zones
        $time_zones = DateTimeZone::listIdentifiers();

        $zones = array();
        foreach ($time_zones as $time_zone) {
            $tz = explode('/', $time_zone);
            if (count($tz) > 1) {
                $city = array_pop($tz);
                if (!empty($zones[$tz[0]]) && ($key = array_search($city, $zones[$tz[0]])) !== false) {
                    unset($zones[$tz[0]][$key]);
                }
                $zones[implode('/',$tz)][] = $city;
            } else {
                $zones['Others'][] = $tz[0];
            }
        }
        //sort time zones groups
        ksort($zones);

        //custom sort of the time zones
        //push the Other, Etc, SystemV zone groups to the end of the array
        if (isset($zones['Others'])) {
            $temp = $zones['Others'];
            unset($zones['Others']);
            $zones['Others'] = $temp;
        }
        if (isset($zones['Etc'])) {
            $temp = $zones['Etc'];
            unset($zones['Etc']);
            $zones['Etc'] = $temp;
        }
        if (isset($zones['SystemV'])) {
            $temp = $zones['SystemV'];
            unset($zones['SystemV']);
            $zones['SystemV'] = $temp;
        }

        //sort time zones
        foreach ($zones as $key => $group) {
            foreach ($group as $idx => $zone) {
                unset($zones[$key][$idx]);
                $time_zone = ($key != 'Others') ? $key . '/' . $zone : $zone;
                $dt = new DateTime('', timezone_open($time_zone));
                $offset = $dt->getOffset()/60;
                $hours = floatval($offset/60);
                $parts = explode('.', $hours);
                $parts[1] = !empty($parts[1])? $parts[1]*60/100 : '00';
                if ($offset < 0) {
                    $hours = -$parts[0] . ':' . $parts[1];
                    $hours = ' [- ' . $hours . ']';
                } else {
                    $hours = $parts[0] . ':' . $parts[1];
                    $hours = ' [+ ' . $hours . ']';
                }
                //ToDo: Check whether this transitions should be used or not
                //$transitions = timezone_open($time_zone)->getTransitions();
                $zones[$key][$time_zone] = array('label' => str_replace('_', ' ', $zone) . $hours,
                                                 'option_value' => $time_zone,
                                                 'offset' => $offset);
            }
            ksort($zones[$key]);
        }

        return $zones;
    }

    /**
     * Function to get date periods as a dropdown.
     * Used for search by date (relative)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getDatePeriods($params = array()) {

        if (empty($params[0])) {
            return array();
        }
        $registry = $params[0];

        return array(0 => array('label' => $registry['translater']->translate('date_days'),
                                'option_value' => 'DAY'),
                     1 => array('label' => $registry['translater']->translate('date_weeks'),
                                'option_value' => 'WEEK'),
                     2 => array('label' => $registry['translater']->translate('date_months'),
                                'option_value' => 'MONTH'));

    }

    /**
     * Get the names of the months
     *
     * @return array - the name of the months in array prepared for dropdown
     */
    public static function getMonthsNames() {
        $months_names = array();
        for ($i = 1; $i <= 12; $i++) {
            $months_names[] = array(
                'option_value' => (string)$i,
                'label'        => General::strftime('%B', mktime(0, 0, 0, $i, 1, General::strftime('%Y')), true)
            );
        }

        return $months_names;
    }

    /**
     *  Get all imports types
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label'] - import types
     */
    public static function getImportTypes($params = array()) {
        $registry = $params[0];
        $lang = self::defineModelLang($params);

        $query = "SELECT ii18n.parent_id as option_value, ii18n.name as label \n
                  FROM " . DB_TABLE_IMPORTS_I18N . " as ii18n \n
                  WHERE lang = '{$lang}' \n
                  ORDER BY label ASC ";

        return $registry['db']->GetAll($query);
    }

    /**
     * Get week days names
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of week days
     */
    public static function getDaysOfWeek($params = array()) {

        $days = array();
        for ($i = 0; $i < 7; $i++) {
            //use %w because windows machines CANNOT interpret the format %u
            $days[General::strftime('%w', strtotime('+' . $i . ' day'))] = General::strftime('%A', strtotime('+' . $i . ' day'));
        }

        ksort($days);
        foreach ($days as $idx => $day) {
            //ToDo: use not system days of week for Windows machines
            $days[$idx] = array('label' => $day, 'option_value' => "$idx");
        }
        //put Sunday to the last position
        $sunday = $days[0];
        $sunday['option_value'] = '0';
        unset($days[0]);
        $days[0] = $sunday;

        return $days;
    }

    /**
     * Get span of years from specified start year to specified offset years
     * before/after current year. With every new year list will have one more option.
     *
     * @param array $params - method params
     * @return array - list of years
     */
    public static function getYears($params) {
        $start = isset($params['start']) ? intval($params['start']) : date('Y', 0);
        $end = date('Y') + (isset($params['offset_from_current']) ? intval($params['offset_from_current']) : 0);
        $first_active = !empty($params['num_active']) ? $end - intval($params['num_active']) + 1 : $start;
        $option_prefix = !empty($params['option_prefix']) ? $params['option_prefix'] : '';

        $years = $start <= $end ? range($start, $end) : array();
        if (!empty($params['sort']) && strtolower($params['sort']) == 'desc') {
            rsort($years);
        }
        array_walk($years, function (&$y, $idx) use ($first_active, $option_prefix) {
            $y = array(
                'option_value' => $option_prefix . $y,
                'label' => (string) $y,
                'active_option' => (int)($y >= $first_active)
            );
        });

        return $years;
    }

    /**
     * Get the days of month
     *
     * @param array $params - method params
     * @return array - list of days
     */
    public static function getDays($params) {
        $start = 1;
        $end = 31;
        if (!empty($params['month']) && !empty($params['year'])) {
            $end = date('t', strtotime($params['year'] . '-' . $params['month'] . '-1'));
        }
        $first_active = !empty($params['num_active']) ? $end - intval($params['num_active']) + 1 : $start;
        $option_prefix = !empty($params['option_prefix']) ? $params['option_prefix'] : '';

        $days = $start <= $end ? range($start, $end) : array();
        if (!empty($params['sort']) && strtolower($params['sort']) == 'desc') {
            rsort($days);
        }
        array_walk($days, function (&$d, $idx) use ($first_active, $option_prefix) {
            $d = array(
                'option_value' => $option_prefix . $d,
                'label' => (string) $d,
                'active_option' => (int)($d >= $first_active)
            );
        });

        return $days;
    }

    /**
     * Gets list of banks used to get currency rates
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getBanks($params = array()) {
        $registry = $params[0];

        //get the model lang
        $lang = self::defineModelLang($params);

        //get currency rates for the selected bank
        require_once 'currency_rates.class.php';
        $banks = Currency_Rates::getBankInfo('', $lang);

        $options = array();
        foreach ($banks as $code => $name) {
            //skip the central bank (BNB)
            if ($code == 'BNB') {
                continue;
            }
            $options[] = array('option_value' => $code, 'label' => $name);
        }

        return $options;
    }

    /**
     * Gets the currencies defined as available in fin_currencies_available DB table
     *
     * @param array $params - parrams to filter the currencies (format)
     * @return array $options - the currencies (the value is the ISO code of the currency)
     */
    public static function getCurrencies($params = array()) {
        $registry = $params[0];

        //get only active currencies
        //to set other currencies just set them as active in fin_currencies_available
        $filters = array('active' => 1, 'lang' => self::defineModelLang($params));

        //get the available currencies
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $currencies = Finance_Currencies::getAvailableCurrencies($registry, $filters);

        $options = array();
        foreach ($currencies as $code => $currency) {
            if (!empty($params['format']) && $params['format'] == 'verbose') {
                $options[] = array('option_value' => $code, 'label' => "({$code}) {$currency['name']}");
            } else {
                $options[] = array('option_value' => $code, 'label' => "{$code}");
            }
        }

        return $options;
    }

    /**
     * Get available fonts for PDF generation
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of fonts
     */
    public static function getAvailableFonts($params = array()) {

        $replace_fonts = array(
            'Droid Sans' => 'Calibri',
        );

        //sans serif fonts
        $sans_serif_regex = '#(Tahoma|Verdana|Arial|Calibri)#i';

        $ttfInfo = new TTF_Info;
        $ttfInfo->setFontsDir(PH_EXT_DIR . 'html2ps/fonts/');
        $ttfInfo->readFontsDir();
        unset($ttfInfo->text);

        $fonts = $ttfInfo->array;
        foreach ($fonts as $font) {
            $name = preg_replace('#\s*(bold|italic)\s*#i', '', $font[4]);
            if (isset($replace_fonts[$name])) {
                $name = $replace_fonts[$name];
            }
            if (!isset($options[$name]['label'])) {
                $options[$name]['label'] = $name;
                $options[$name]['option_value'] = $name;
                if (preg_match($sans_serif_regex, $name)) {
                    $options[$name]['option_value'] .= ',sans-serif';
                }
            }
            if (preg_match('#bold#i', $font[4]) && preg_match('#italic#i', $font[4])) {
                $options[$name]['both'] = true;
            } elseif (preg_match('#bold#i', $font[4])) {
                $options[$name]['bold'] = true;
            } elseif (preg_match('#italic#i', $font[4])) {
                $options[$name]['italic'] = true;
            }
        }
        ksort($options);
        return $options;
    }

    /**
     * Get yes/no options
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of optioins
     */
    public static function getYesNo($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('yes'),
                                 'option_value' => '1'),
                      1 => array('label' => $registry['translater']->translate('no'),
                                 'option_value' => '0')
                     );
    }

    /**
     * Get required options
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of optioins
     */
    public static function getRequiredOptions($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('yes'),
                                 'option_value' => '1'),
                      1 => array('label' => $registry['translater']->translate('yes_allow_zero'),
                                 'option_value' => '2'),
                      2 => array('label' => $registry['translater']->translate('no'),
                                 'option_value' => '0')
                     );
    }

    /**
     * Get possible text alignments for HTML
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of allignments
     */
    public static function getHTMLAllignments($params = array()) {

        $translater = &$params[0]['translater'];

        return array(array('label' => $translater->translate('gt2_alignment_left'),
                           'option_value' => 'left'),
                     array('label' => $translater->translate('gt2_alignment_center'),
                           'option_value' => 'center'),
                     array('label' => $translater->translate('gt2_alignment_right'),
                           'option_value' => 'right'));
    }

    /**
     * Get options for gt2 fields permissions
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of options
     */
    public static function getGT2DependAll($params = array()) {

        $translater = &$params[0]['translater'];

        return array(array('label' => $translater->translate('gt2_for_all'),
                           'option_value' => '1'),
                     array('label' => $translater->translate('gt2_depends'),
                           'option_value' => '0'));
    }

    /**
     * Get available HTML measures
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of measures
     */
    public static function getHTMLMeasures($params = array()) {

        return array(array('label' => 'px', 'option_value' => 'px'),
                     array('label' => '%', 'option_value' => '%'),
                     array('label' => 'pt', 'option_value' => 'pt'),
                     array('label' => 'pc', 'option_value' => 'pc'),
                     array('label' => 'mm', 'option_value' => 'mm'),
                     array('label' => 'cm', 'option_value' => 'cm'),
                     array('label' => 'in', 'option_value' => 'in'),
                     array('label' => 'em', 'option_value' => 'em'),
                     array('label' => 'ex', 'option_value' => 'ex'));
    }

    /**
     * Get VAT options for specified company or default ones for system
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of options
     */
    public static function getVatOptions($params = array()) {

        $registry = $params[0];

        $company = 0;
        if (isset($params['company'])) {
            // company is specified in parameters
            $company = $params['company'];
        } elseif (!empty($params['self'])) {
            // company id is company of record or the id of the company itself
            $company = ($params['self']->modelName == 'Finance_Company') ? $params['self']->get('id') : $params['self']->get('company');
        }

        $query = 'SELECT CONCAT(value, " %") AS label, value AS option_value, active AS active_option' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_VAT_RATES . "\n" .
                 'WHERE parent_id = ' . sprintf('%d', $company) . "\n" .
                 'ORDER BY is_default DESC, value DESC';
        $vat_options = $registry['db']->GetAll($query);

        return $vat_options;
    }

    /**
     * Get statuses for email campaigns
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- list of options
     */
    public static function getEmailsCampaignsStatuses($params = array()) {

        $registry = $params[0];
        return array (0 => array('label' => $registry['translater']->translate('emails_campaigns_status_preparation'),
                                 'option_value' => 'preparation'),
                      1 => array('label' => $registry['translater']->translate('emails_campaigns_status_ready'),
                                 'option_value' => 'ready'),
                      2 => array('label' => $registry['translater']->translate('emails_campaigns_status_partial'),
                                 'option_value' => 'partial'),
                      3 => array('label' => $registry['translater']->translate('emails_campaigns_status_sent'),
                                 'option_value' => 'sent'),
                      4 => array('label' => $registry['translater']->translate('emails_campaigns_status_cancelled'),
                                 'option_value' => 'cancelled')
                     );
    }

    /**
     * Gets salutation options as a dropdown.
     *
     * @param array $params - params, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getSalutations($params = array()) {

        if (empty($params[0])) {
            return array();
        }
        $registry = $params[0];

        $options = array(0 => array('label' => $registry['translater']->translate('salutation_mr'),
                                    'option_value' => 'mr'),
                         1 => array('label' => $registry['translater']->translate('salutation_mrs'),
                                    'option_value' => 'mrs'),
                         2 => array('label' => $registry['translater']->translate('salutation_ms'),
                                    'option_value' => 'ms'));

        //add undefined option at first position
        if (!empty($params['include_empty'])) {
            array_unshift($options,
                         array('label' => '[' . $registry['translater']->translate('undefined') . ']',
                               'option_value' => ''));
        }

        return $options;
    }

    /**
     * Gets available measures for nomenclatures
     *
     * @param array $params - params, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label']- list of options
     */
    public static function getMeasures($params = array()) {
        $registry = $params[0];

        // if method is called for a model ('self'), use its model_lang
        $lang = self::defineModelLang($params);

        $where = array(sprintf("lang = '%s'", $lang));

        $query = 'SELECT * FROM ' . DB_TABLE_MEASURES . ' as am ' .
                 'WHERE ' . implode(",", $where);
        $result = $registry['db']->GetAll($query);

        $options = array();
        foreach ($result as $measure) {
            if ($measure['parent_id'] == '0') {
                $options[] = array (
                    'option_value' => $measure['id'],
                    'label' => $measure['name']
                );
            }
        }

        return $options;
    }

    /**
     * Function to prepare the country list in array as dropdown options
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getCountries($params = array()) {
        $registry = $params[0];

        $db = $registry['db'];

        $lang = self::defineModelLang($params);

        $where = array();
        $where[] = sprintf("`lang`='%s'", $lang);

        $query = 'SELECT * FROM ' . DB_TABLE_COUNTRY_LIST .
                 ' WHERE ' . implode(",", $where) . ' ORDER BY position DESC, country_name ASC';
        $result = $registry['db']->GetAll($query);

        $options = array();
        foreach ($result as $res) {
            $options[] = array (
                'option_value' => $res['country_code'],
                'label'        => $res['country_name']
            );
        }

        return $options;
    }

    /**
     * Get all model types with GT2 as optgroups (grouped by model)
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getGT2Types($params = array()) {

        $registry = $params[0];
        $db = $registry['db'];
        $lang = self::defineModelLang($params);

        $optgroups = array();

        $query = 'SELECT ti.name AS label, CONCAT(\'Contract^\', t.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0 AND t.gt2=1' . "\n" .
                 '  AND (SELECT COUNT(id) FROM ' . DB_TABLE_FIELDS_META . ' WHERE model=\'Contract\' AND model_type=t.id AND gt2=1)>0' . "\n" .
                 'ORDER BY ti.name ASC';
        $types = $db->GetAll($query);
        if ($types) {
            $optgroups[$registry['translater']->translate('menu_contracts')] = $types;
        }

        $query = 'SELECT ti.name AS label, CONCAT(\'Document^\', t.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0 AND t.gt2=1' . "\n" .
                 '  AND (SELECT COUNT(id) FROM ' . DB_TABLE_FIELDS_META . ' WHERE model=\'Document\' AND model_type=t.id AND gt2=1)>0' . "\n" .
                 'ORDER BY ti.name ASC';
        $types = $db->GetAll($query);
        if ($types) {
            $optgroups[$registry['translater']->translate('menu_documents')] = $types;
        }

        $query = 'SELECT ti.name AS label, CONCAT(t.model, \'^\', t.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0' . "\n" .
                 'ORDER BY ti.name ASC';
        $types = $db->GetAll($query);
        if ($types) {
            $optgroups[$registry['translater']->translate('menu_finance')] = $types;
        }

        return $optgroups;
    }

    /**
     * Get print patterns of all model types with GT2 as optgroups (grouped by model)
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getGT2Patterns($params = array()) {

        $registry = $params[0];
        $db = $registry['db'];
        $lang = self::defineModelLang($params);

        $optgroups = array();

        $query = 'SELECT IF(ti.name IS NOT NULL, ti.name, \'\') AS type_name, IF(pi.name IS NOT NULL, pi.name, \'\') AS label,' . "\n" .
                 '  CONCAT(p.model, \'^\', t.id, \'^\', p.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'JOIN ' . DB_TABLE_PATTERNS . ' AS p' . "\n" .
                 '  ON p.model=\'Contract\' AND p.model_type=t.id AND p.active=1 AND p.deleted_by=0 AND p.list=0' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PATTERNS_I18N . ' AS pi' . "\n" .
                 '  ON p.id=pi.parent_id AND pi.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0 AND t.gt2=1' . "\n" .
                 '  AND (SELECT COUNT(id) FROM ' . DB_TABLE_FIELDS_META . ' WHERE model=\'Contract\' AND model_type=t.id AND gt2=1)>0' . "\n" .
                 'ORDER BY ti.name ASC, p.position ASC, p.id ASC';
        $patterns = $db->GetAll($query);
        if ($patterns) {
            $patterns_ordered = array();
            $type_name = false;
            foreach ($patterns as $p) {
                if ($type_name === false || $type_name !== $p['type_name']) {
                    $type_name = $p['type_name'];
                    $patterns_ordered[] = array('label' => $type_name, 'option_value' => preg_replace('#^(.*\^)\d+$#', '${1}0', $p['option_value']), 'active_option' => 0, 'class_name' => 'strong legend');
                }
                unset($p['type_name']);
                $p['label'] = '&nbsp;&nbsp;&nbsp;&nbsp;' . $p['label'];
                $patterns_ordered[] = $p;
            }
            $patterns = $patterns_ordered;
            unset($patterns_ordered);
            $optgroups[$registry['translater']->translate('menu_contracts')] = $patterns;
        }

        $query = 'SELECT IF(ti.name IS NOT NULL, ti.name, \'\') AS type_name, IF(pi.name IS NOT NULL, pi.name, \'\') AS label,' . "\n" .
                 '  CONCAT(p.model, \'^\', t.id, \'^\', p.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'JOIN ' . DB_TABLE_PATTERNS . ' AS p' . "\n" .
                 '  ON p.model=\'Document\' AND p.model_type=t.id AND p.active=1 AND p.deleted_by=0 AND p.list=0' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PATTERNS_I18N . ' AS pi' . "\n" .
                 '  ON p.id=pi.parent_id AND pi.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0 AND t.gt2=1' . "\n" .
                 '  AND (SELECT COUNT(id) FROM ' . DB_TABLE_FIELDS_META . ' WHERE model=\'Document\' AND model_type=t.id AND gt2=1)>0' . "\n" .
                 'ORDER BY ti.name ASC, p.position ASC, p.id ASC';
        $patterns = $db->GetAll($query);
        if ($patterns) {
            $patterns_ordered = array();
            $type_name = false;
            foreach ($patterns as $p) {
                if ($type_name === false || $type_name !== $p['type_name']) {
                    $type_name = $p['type_name'];
                    $patterns_ordered[] = array('label' => $type_name, 'option_value' => preg_replace('#^(.*\^)\d+$#', '${1}0', $p['option_value']), 'active_option' => 0, 'class_name' => 'strong legend');
                }
                unset($p['type_name']);
                $p['label'] = '&nbsp;&nbsp;&nbsp;&nbsp;' . $p['label'];
                $patterns_ordered[] = $p;
            }
            $patterns = $patterns_ordered;
            unset($patterns_ordered);
            $optgroups[$registry['translater']->translate('menu_documents')] = $patterns;
        }

        $query = 'SELECT IF(ti.name IS NOT NULL, ti.name, \'\') AS type_name, CONCAT(IF(pi.name IS NOT NULL, pi.name, \'\'), IF(p.company!=0, CONCAT(\' (\', IF(ci.name IS NOT NULL, ci.name, \'\'), \')\'), \'\')) AS label,' . "\n" .
                 '  CONCAT(p.model, \'^\', t.id, \'^\', p.id) AS option_value' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS t' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS ti' . "\n" .
                 '  ON t.id=ti.parent_id AND ti.lang="' . $lang . '"' . "\n" .
                 'JOIN ' . DB_TABLE_PATTERNS . ' AS p' . "\n" .
                 '  ON p.model=t.model AND p.model_type=t.id AND p.active=1 AND p.deleted_by=0 AND p.list=0' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PATTERNS_I18N . ' AS pi' . "\n" .
                 '  ON p.id=pi.parent_id AND pi.lang="' . $lang . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS ci' . "\n" .
                 '  ON p.company=ci.parent_id AND ci.lang="' . $lang . '"' . "\n" .
                 'WHERE t.active=1 AND t.deleted_by=0' . "\n" .
                 'ORDER BY ti.name ASC, p.company ASC, p.position ASC, p.id ASC';
        $patterns = $db->GetAll($query);
        if ($patterns) {
            $patterns_ordered = array();
            $type_name = false;
            foreach ($patterns as $p) {
                if ($type_name === false || $type_name !== $p['type_name']) {
                    $type_name = $p['type_name'];
                    $patterns_ordered[] = array('label' => $type_name, 'option_value' => preg_replace('#^(.*\^)\d+$#', '${1}0', $p['option_value']), 'active_option' => 0, 'class_name' => 'strong legend');
                }
                unset($p['type_name']);
                $p['label'] = '&nbsp;&nbsp;&nbsp;&nbsp;' . $p['label'];
                $patterns_ordered[] = $p;
            }
            $patterns = $patterns_ordered;
            unset($patterns_ordered);
            $optgroups[$registry['translater']->translate('menu_finance')] = $patterns;
        }

        return $optgroups;
    }

    /**
     * Get email templates of all model types with GT2 as optgroups (grouped by model)
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getGT2Emails(array $params = array()) {
        $registry = $params[0];
        /** @var ADODB_mysqli $db */
        $db = $registry['db'];
        $lang = self::defineModelLang($params);

        $models = $db->GetCol("
            SELECT DISTINCT SUBSTRING_INDEX(model, '_', 1)
            FROM " . DB_TABLE_FIELDS_META . "
            WHERE type = 'gt2' AND model != 'GT2_Sample'
        ");
        $optgroups = array();
        foreach ($models as $model) {
            $module = strtolower($model);
            $wildcard = '';
            if ($module == 'finance') {
                $table = $module . '_documents';
                $wildcard = '%';
            } else {
                $table = $module = General::singular2plural($module);
            }
            $table = strtoupper($table);
            if (!defined("DB_TABLE_{$table}_TYPES") || !defined("DB_TABLE_{$table}_TYPES_I18N")) {
                continue;
            }
            $table_i18n = constant("DB_TABLE_{$table}_TYPES_I18N");
            $table = constant("DB_TABLE_{$table}_TYPES");

            $records = $db->GetAll("
                SELECT IF(ti.name IS NOT NULL, ti.name, '') AS type_name,
                    IF(ei.subject IS NOT NULL, ei.subject, '') AS label,
                    CONCAT(e.model, '^', t.id, '^', e.id) AS option_value
                FROM {$table} AS t
                LEFT JOIN {$table_i18n} AS ti
                  ON t.id = ti.parent_id AND ti.lang = '{$lang}'
                JOIN " . DB_TABLE_EMAILS . " AS e
                  ON e.model LIKE '{$model}{$wildcard}' AND e.model_type = t.id AND e.active=1 AND e.deleted_by=0
                LEFT JOIN " . DB_TABLE_EMAILS_I18N . " AS ei
                  ON e.id = ei.parent_id AND ei.lang = '{$lang}'
                JOIN " . DB_TABLE_FIELDS_META . " AS fm
                  ON fm.model = e.model AND fm.model_type = e.model_type AND fm.type = 'gt2'
                WHERE t.active = 1 AND t.deleted_by = 0
                GROUP BY e.id
                ORDER BY ti.name ASC, ei.subject ASC, e.id ASC
            ");

            if ($records) {
                $type_name = false;
                $options = array();
                foreach ($records as $opt) {
                    if ($type_name === false || $type_name !== $opt['type_name']) {
                        $type_name = $opt['type_name'];
                        $options[] = array(
                            'label' => $type_name,
                            'option_value' => preg_replace('#^(.*\^)\d+$#', '${1}0', $opt['option_value']),
                            'active_option' => 0,
                            'class_name' => 'strong legend',
                        );
                    }
                    unset($opt['type_name']);
                    $opt['label'] = str_repeat('&nbsp;', 4) . $opt['label'];
                    $options[] = $opt;
                }
                $optgroups[$registry['translater']->translate("menu_{$module}")] = $options;
            }
        }

        return $optgroups;
    }

    /**
     * Get available tags, filtered by specified parameters
     * IMPORTANT: tags permissions are applied
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array - options or optgroups for dropdown
     */
    public static function getTags($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $no_group_label = sprintf('[%s]', $registry['translater']->translate('tags_no_section'));

        require_once(PH_MODULES_DIR . 'tags/models/tags.factory.php');
        $filters = array('where' => array(),
                         'sort' => array('ts.place ASC', 'tsi18n.name ASC', 't.place ASC', 'ti18n.name ASC', 't.active DESC'),
                         'model_lang' => $lang,
                         'sanitize' => true);
        if (!empty($params['model'])) {
            $filters['where'][] = 't.model = \'' . $params['model'] . '\'';
        }
        if (!empty($params['model_types'])) {
            if (!is_array($params['model_types'])) {
                $params['model_types'] = array($params['model_types']);
            }
            $filters['where'][] = 'tt.type_id IN (\'' . implode('\', \'', $params['model_types']) . '\')';
        }
        if (!empty($params['active'])) {
            $filters['where'][] = 't.active = \'' . $params['active'] . '\'';
        }
        // get tags not available for current user according to tag permissions
        $not_available_tags = Tags::getNotPermittedTags($registry, $params);
        // filter not to get not available tags
        if ($not_available_tags) {
            $filters['where'][] = 't.id NOT IN (\'' . implode('\', \'', $not_available_tags) . '\')';
        }

        $tags = Tags::search($registry, $filters);

        $available_tags_grouped = array();
        foreach ($tags as $tag) {
            $section_name = $tag->get('section_name') ?: $no_group_label;
            if (!isset($available_tags_grouped[$section_name])) {
                $available_tags_grouped[$section_name] = array();
            }
            $available_tags_grouped[$section_name][] = array(
                'label' => $tag->get('name'),
                'option_value' => $tag->get('id'),
                'active_option' => $tag->get('active')
            );
        }

        if ($available_tags_grouped) {
            // tags not in a group should be sorted by active and name, not by place
            if (!empty($available_tags_grouped[$no_group_label])) {
                usort($available_tags_grouped[$no_group_label], function($a, $b) {
                    return ($a['active_option'] != $b['active_option']) ?
                           $a['active_option'] < $b['active_option'] :
                           $a['label'] > $b['label'];
                });
            }
            if (count($available_tags_grouped) == 1 && array_key_exists($no_group_label, $available_tags_grouped)) {
                $available_tags_grouped = reset($available_tags_grouped);
            } else {
                $available_tags_grouped['contain_optgroups'] = 1;
            }
        }

        return $available_tags_grouped;
    }

    /**
     * Get available activites for timesheets
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array - options or optgroups for dropdown
     */
    public static function getTasksTimesheetsActivities($params = array()) {
        $registry = $params[0];

        $query = 'SELECT id AS option_value, name AS label, event_type AS class_name' . "\n" .
                 'FROM ' . DB_TABLE_TASKS_TIMESHEETS_ACTIVITIES . "\n" .
                 'WHERE lang="' . self::defineModelLang($params) . '"' . (!empty($params['event_type']) ? ' AND event_type=\'' . $params['event_type'] . '\'' : '') . "\n" .
                 'ORDER BY id ASC';
        $records = $registry['db']->GetAll($query);

        return $records;
    }

    /**
     * Get all additional vars types
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array - options for dropdown
     */
    public static function getVarsTypes($params = array()) {
        $options = array();
        if (!empty($params[0]) && is_object($params[0])) {
            $types = $params[0]['db']->GetRow("SHOW COLUMNS FROM " . DB_TABLE_FIELDS_META . " WHERE `Field` = 'type'");
            preg_match("/enum\('(.+)'\)/", $types['Type'], $matches);
            if (!empty($matches[1])) {
                $types = explode("','", $matches[1]);
                foreach ($types as $type) {
                    $options[] = array(
                        'label' => $type,
                        'option_value' => $type
                    );
                }
            }
        }
        return $options;
    }

    /**
     * Define a list of all additional vars validation filters
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array - options for dropdown
     */
    public static function getVarsValidateFilters($params = array()) {
        // The functions are ordered alphabetically
        $functions = array(
            'cancelDigits',
            'cancelEnter',
            'digitsToX',
            'insertOnlyDigits',
            'insertOnlyFloats',
            'insertOnlyNegativeFloats',
            'insertOnlyPositiveIntegers',
            'insertOnlyReals',
            'lettersToUpperCase',
            'umlautsToASCII'
        );
        $options = array();
        foreach ($functions as $function) {
            $options[] = array(
                'label' => $function,
                'option_value' => $function
            );
        }
        return $options;
    }

    /**
     * Define a list of all GT2 aggregate functions
     *
     * @param array $params - array with parameters, $params[0] is registry object
     * @return array - options for dropdown
     */
    public static function getGT2Agregates($params = array()) {
        $options = array();
        if (!empty($params[0]['translater']) && is_object($params[0]['translater'])) {
            $agregates = array('count', 'min', 'max', 'average', 'sum');
            foreach ($agregates as $agregate) {
                $options[] = array(
                    'label' => $params[0]['translater']->translate("gt2_agregates_{$agregate}"),
                    'option_value' => $agregate
                );
            }
        }
        return $options;
    }

    /**
     * Get warehouses for certain company and office
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getWarehouses($params = array()) {

        $registry = $params[0];
        $db = $registry['db'];
        // get the model lang
        $lang = self::defineModelLang($params);
        // load the interface lang file
        $lang_file = PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini';
        if (!$registry['translater']->isLoadedFile($lang_file, 'module')) {
            $registry['translater']->loadFile($lang_file);
        }
        $query = 'SELECT DISTINCT fw.id AS option_value, fw.active AS active_option, fwd.id as locker, fwd.status as locker_status,' . "\n" .
            'CONCAT(fwi18n.name, IF(fw.locked > 0, CONCAT(" (", "' . $registry["translater"]->translate('finance_warehouses_inspection_lock') . '",")"), "")) AS label' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_WAREHOUSES . ' AS fw' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_I18N . ' AS fwi18n' . "\n" .
            '  ON fw.id=fwi18n.parent_id AND fwi18n.lang="' . $lang . '"' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
            '  ON fwd.id = fw.locked AND fwd.annulled_by = 0 AND fwd.status != "finished"' . "\n" .
            'WHERE fw.deleted=0' . (!empty($params['active']) ? ' AND fw.active=1' : '') . "\n";
        if (!empty($params['company'])) {
            $query .= '  AND fw.company = "' . $params['company'] . '"';
        }
        if (!empty($params['office'])) {
            $query .= '  AND fw.office = "' . $params['office'] . '"';
        }
        $warehouses = $db->GetAll($query);

        return $warehouses;
    }

    /**
     * Get bb elements (compound inner variables of group/config/gt2 type) for model
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getBBElements($params = array()) {

        $records = array();

        foreach ($params['self']->getBBElements() as $element) {
            $records[] = array('label' => $element['label'], 'option_value' => $element['id']);
        }

        return $records;
    }
}

?>
