{literal}
<script id="grid-col-contacts-{/literal}{$contactType}" type="text/x-template">
    <ul class="grid-col-contacts-list grid-col-contacts-list__web nz-contact-type__{$contactType}{literal}">
    ${if(properties.{/literal}{$contactType}{literal})}
        ${for(contact of properties.{/literal}{$contactType}{literal})}
            ${if(contact)}
            <li class="grid-col-contacts-list__item"><a class="nz-contact nz-contact__web"
                href="{/literal}{if $contactType == 'email'}mailto:${ldelim}contact{rdelim}{else}${rdelim}contact{ldelim}{/if}{literal}">${contact}</a></li>
            ${/if}
        ${/for}
    ${/if}
    </ul>
</script>
{/literal}
