{if isset($available_actions.adds)}
  {if !empty($available_actions.adds.options)}
    <span class="nz-add-button nz-button nz-action__add nz-popout-trigger nz-popout-autoinit"
        data-popout-template="#add-popout"
        data-popout-position="panel: top center at: bottom center 0 0">
          <i class="material-icons">add_circle</i>
          {$available_actions.adds.label}
    </span>
    {include file="`$moduleTemplatesDir`_add_popout_xtemplate.html" actionData=$available_actions.adds}
  {elseif !empty($available_actions.adds.url)}
    <a class="nz-add-button nz-button nz-action__add"
        href="{$available_actions.adds.url}">
          <i class="material-icons">add_circle</i>
          {$available_actions.adds.label}
    </a>
  {/if}
{/if}

{if isset($available_actions.add)}
  {if !empty($available_actions.add.options)}
    <span class="nz-add-button nz-button nz-action__add nz-popout-trigger nz-popout-autoinit"
        data-popout-template="#add-popout"
        data-popout-position="panel: top center at: bottom center 0 0">
      <i class="material-icons">add_circle</i>
      {$available_actions.add.label}
    </span>
  {elseif !empty($available_actions.add.url)}
    <a class="nz-add-button nz-button nz-action__add"
       href="{$available_actions.add.url}">
      <i class="material-icons">add_circle</i>
      {$available_actions.add.label}
    </a>
  {/if}
  {include file="`$moduleTemplatesDir`_add_popout_xtemplate.html" actionData=$available_actions.add}
{/if}
