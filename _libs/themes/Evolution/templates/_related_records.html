<div class="subpanel_container nz-subpanel_container">
    {if $smarty.cookies.$coockiename && in_array($smarty.cookies.$coockiename, $related_records_modules)}
    {assign var='rel_type' value=$smarty.cookies.$coockiename}
    {else}
    {assign var='rel_type' value=$related_records_modules.0}
    {/if}
    <input type="hidden" id="rel_type" name="rel_type" value="{$rel_type}" />
    <a name="{$subpanel_name}"></a>
    {include file=`$theme->templatesDir`actions_box.html available_actions=$available_actions_related_records tabs=true selected_tab=$rel_type}
    <div class="m_header_m_menu scroll_box_container nz-surface nz-subpanel_contents">
        {foreach from=$related_records_modules item=module key=model}
        <div id="{$session_params.$module}" data-mod="{$module}" class="rel_tab">
            {include file="`$theme->templatesDir`__inline-loading.html"}
        </div>
        {/foreach}
    </div>
</div>
