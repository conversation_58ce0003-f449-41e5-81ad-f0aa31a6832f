'use strict';
class NzDragNDropList {
    listEl;
    dragSrcEl;
    dragCpEl;
    dragOverTail;
    onDrop;

    constructor(options) {
        if (options && typeof options.onDrop == 'function') {
            this.onDrop = options.onDrop;
        }
    }

    attach(eleemnt) {
        if (typeof eleemnt === 'string') {
            this.listEl = document.querySelector(element);
        } else {
            this.listEl = eleemnt
        }
        this.listEl.classList.add('nz-drag-list');
        this.listEl.querySelectorAll('.nz-drag-element').forEach((item)=>{
            this.initDraggable(item);
        });
    }

    detach() {

    }

    initDraggable(element) {
        element.draggable = true;
        element.addEventListener('dragstart', this.handleDragStart.bind(this));
        element.addEventListener('dragover', this.handleDragOver);
        element.addEventListener('dragenter', this.handleDragEnter.bind(this));
        element.addEventListener('dragleave', this.handleDragLeave.bind(this));
        element.addEventListener('dragend', this.handleDragEnd.bind(this));
        element.addEventListener('drop', this.handleDrop.bind(this));
    }

    createCopyElement() {
        if (!this.dragSrcEl) {
            throw Error('Drag source element is not defined');
        }
        const copyEl = this.dragSrcEl.cloneNode(true);
        copyEl.removeAttribute('id');
        copyEl.classList.add('nz-drag-element__copy');
        return copyEl;
    }

    handleDragStart(e) {
        this.listEl.classList.add('nz--active');
        this.dragOverTail = 0;

        this.dragSrcEl = e.target.closest('.nz-drag-element');
        this.dragSrcEl.classList.add('nz--dragging');

        this.dragCpEl = this.createCopyElement();
        document.body.append(this.dragCpEl);

        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.dragSrcEl.innerHTML);
        e.dataTransfer.setDragImage(this.dragCpEl, e.offsetX, e.offsetY);

    }

    handleDragOver(e) {
        e.preventDefault();
        return false;
    }

    handleDragEnter(e) {
        const el = e.target.closest('.nz-drag-element');
        if(el) {
            this.listEl.querySelectorAll('.nz--drag-over').forEach((el)=>{
                el.classList.remove('nz--drag-over');
            });
            el.classList.add('nz--drag-over');
            e.dataTransfer.dropEffect = 'move';
        } else {
            e.dataTransfer.dropEffect = 'none';
        }
        /*if (this.dragOverTail === 0) {
            e.target.classList.add('nz--drag-over');
        }*/
        this.dragOverTail++;
    }

    handleDragLeave(e) {
        this.dragOverTail--;
        if (this.dragOverTail === 0) {
            const bottomEl = this.listEl.querySelector('.nz--drag-over');
            /*if (bottomEl.contains(e.target)) {
                return;
            }*/
            bottomEl.classList.remove('nz--drag-over');
            e.dataTransfer.dropEffect = 'none';
        }
    }

    handleDragEnd(e) {
        this.dragOverTail = 0;
        this.dragSrcEl.classList.remove('nz--dragging');
        this.listEl.classList.remove('nz--active');

        this.dragCpEl.remove();
        this.dragCpEl = null;

        this.listEl.querySelectorAll('.nz--drag-over').forEach((el)=>{
            el.classList.remove('nz--drag-over');
        });
    }

    handleDrop(e) {
        e.stopPropagation(); // stops the browser from redirecting.
        const el = e.target.closest('.nz-drag-element');
        if (this.dragSrcEl !== el) {
            this.listEl.insertBefore(this.dragSrcEl, el);
        }

        if (typeof this.onDrop === 'function') {
            this.onDrop.apply(this, [this.dragSrcEl, el]);
        }

        return false;
    }

}
