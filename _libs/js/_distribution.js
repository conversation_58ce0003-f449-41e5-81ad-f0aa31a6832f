
/**
 * Expands/collapses distribution of a gt2 row.
 *
 * @param element - input element
 */
function toggleAnalysisRow(element) {
    var force_mode = arguments[1];

    var row_data_container = element.id.replace(/_switch/, '');
    //get container by id
    var row_data_content = $(row_data_container);

    if (force_mode && force_mode == 'expand' || !force_mode && row_data_content.style.display == 'none') {
        row_data_content.style.display = '';
        toggleSwitchArrow(element, 'collapse');
    } else if (force_mode && force_mode == 'collapse' || !force_mode && row_data_content.style.display == '') {
        row_data_content.style.display = 'none';
        toggleSwitchArrow(element, 'expand');
    }
    return true;
}

/**
 * Expands/collapses distribution of all gt2 rows.
 *
 * @param element - input element
 * @param mode - expand or collapse
 */
function toggleAnalysisRowAll(element, mode) {
    var table = element.parentNode.parentNode.parentNode.parentNode;
    var elements = table.getElementsByTagName('div');

    for (var i=0; i<elements.length ;i++ ) {
        if (elements[i].id && elements[i].id.match(/^row_(.*)_switch$/)) {
            toggleAnalysisRow(elements[i], mode);
        }
    }
    return true;
}

/**
 * Shows rows with elements with zero share for a finance analysis item.
 *
 * @param element - input element
 */
function showZeroShareElements(element) {
    var element_table = element.parentNode.parentNode.parentNode.parentNode;
    var rows = element_table.getElementsByTagName('tr');
    for (var i = rows.length - 1; i > 0; i--) {
        if (rows[i].style.display == 'none') {
            rows[i].style.display = '';
        }
    }
    element.style.display = 'none';

    return true;
}

/**
 * Calculation, default distribution and validation of subtotals from gt2.
 *
 * @param field - input field
 * @param recalc_only - optional; used for items to recalculate sum of items without applying default distribution of items
 */
function distribution_calc(field) {
    var recalc_only = distribution_calc.arguments.length > 1 ? distribution_calc.arguments[1] : false;
    //setting for calculation precision
    var pow = Math.pow(10, env.precision.gt2_rows);

    field = $(field);
    if (!field) {
        return false;
    }

    var custom_class;
    if (!field.id.match(/^(submit_distribution)/)) {
        custom_class = field.id.replace(/([^_\d]*)[_\d]+$/, '$1');
        if (recalc_only) {
            custom_class += '_recalc';
        }
    } else {
        custom_class = 'submit_distribution';
    }

    //define formulas
    var formulas = {
        /**
         * Calculates distributed amount among items of an article (row in gt2).
         */
        item_amounts_recalc: function(field) {
            var unique_items = true;
            var no_empty_items = true;
            var item_ids = Array();
            var items_total = 0;

            var nom_amount_id = field.id.replace(/^item(.+)_\d+$/, 'nom$1');
            var nom_amount = $(nom_amount_id);
            if (nom_amount) {
                nom_amount = Math.round(parseFloat(nom_amount.value) * pow) / pow;
            }

            var tbl_id = field.id.replace(/^item_amounts(.+)_\d+$/, 'row_table$1');
            var item_amounts = $$('#' + tbl_id + ' .item_amounts');

            for (var i = 0; i < item_amounts.length; i++) {
                if (!item_amounts[i].disabled) {
                    var item_amount = Math.round(parseFloat(item_amounts[i].value) * pow) / pow;
                    if (isNaN(item_amount)) {
                        item_amount = 0;
                        item_amounts[i].value = 0;
                    }
                    items_total += item_amount;
                    item_ids.push($(item_amounts[i].id.replace(/_amounts_/, '_ids_')).value);
                }
            }
            items_total = Math.round(items_total * pow)/pow;
            if (isNaN(items_total)) {
                items_total = 0;
            }
            unique_items = item_ids.length == item_ids.uniq().length;
            no_empty_items = (item_ids.indexOf('') > -1) ? false : true;

            var is_valid = (items_total == nom_amount) && unique_items && no_empty_items;
            for (var i = 0; i < item_amounts.length; i++) {
                if (!item_amounts[i].disabled) {
                    removeClass(item_amounts[i], (is_valid ? 'red' : 'green'));
                    addClass(item_amounts[i], (is_valid ? 'green' : 'red'));
                }
            }
            var nom_amounts_remaining = $(nom_amount_id.replace(/^(nom_amounts)/, '$1_remaining'));
            var nom_amounts_distributed = $(nom_amount_id.replace(/^(nom_amounts)/, '$1_distributed'));
            if (nom_amounts_remaining) {
                var na_rem = Math.round((nom_amount - items_total) * pow) / pow;
                nom_amounts_remaining.innerHTML = na_rem.toFixed(env.precision.gt2_rows);
                removeClass(nom_amounts_remaining, (is_valid ? 'red' : 'green'));
                addClass(nom_amounts_remaining, (is_valid ? 'green' : 'red'));
            }
            if (nom_amounts_distributed) {
                var na_dist = Math.round(items_total * pow) / pow;
                nom_amounts_distributed.innerHTML = na_dist.toFixed(env.precision.gt2_rows);
                removeClass(nom_amounts_distributed, (is_valid ? 'red' : 'green'));
                addClass(nom_amounts_distributed, (is_valid ? 'green' : 'red'));
            }

            return is_valid;
        },

        /**
         * Calculates distributed amount among elements of an item.
         */
        element_amounts_recalc: function(field) {
            if (field.id.match(/percentage/)) {
                field = $(field.id.replace(/percentages/, 'amounts'));
            }

            var elements_total = 0;

            var item_amount_id = field.id.replace(/^element(.+)_\d+$/, 'item$1');
            var item_amount = $(item_amount_id);
            if (item_amount) {
                item_amount = Math.round(parseFloat(item_amount.value) * pow) / pow;
            }

            var tbl_id = field.parentNode.parentNode.parentNode.parentNode.id;
            var element_amounts = $$('#' + tbl_id + ' .element_amounts');
            for (var i = 0; i < element_amounts.length; i++) {
                var element_amount = Math.round(parseFloat(element_amounts[i].value) * pow) / pow;
                if (isNaN(element_amount)) {
                    element_amount = 0;
                    element_amounts[i].value = 0;
                }
                elements_total += element_amount;
            }
            elements_total = Math.round(elements_total * pow) / pow;
            if (isNaN(elements_total)) {
                elements_total = 0;
            }

            var is_valid = (elements_total == item_amount);
            for (var i = 0; i < element_amounts.length; i++) {
                if (!element_amounts[i].disabled) {
                    removeClass(element_amounts[i], (is_valid ? 'red' : 'green'));
                    addClass(element_amounts[i], (is_valid ? 'green' : 'red'));
                }
            }
            var item_amounts_remaining = $(item_amount_id.replace(/^(item_amounts)/, '$1_remaining'));
            if (item_amounts_remaining) {
                var ia_rem = Math.round((item_amount - elements_total) * pow) / pow;
                item_amounts_remaining.innerHTML = ia_rem.toFixed(env.precision.gt2_rows);
                removeClass(item_amounts_remaining, (is_valid ? 'red' : 'green'));
                addClass(item_amounts_remaining, (is_valid ? 'green' : 'red'));
            }

            return is_valid;
        },

        /**
         * Calculates percentage in distributed element of an item.
         */
        element_amounts_recalc_percentage: function(field) {
            var item_amount_id = field.id.replace(/^element_amounts_(.+)_\d+$/, 'item_amounts_$1');
            var item_amount = $(item_amount_id);
            if (item_amount) {
                item_amount = Math.round(parseFloat(item_amount.value) * pow) / pow;
            }

            //the field is for amount, calculate percentage
            var element_amount_id = field.id;
            var element_percentage_id = field.id.replace(/amount/, 'percentage');
            var element_amount = Math.round(parseFloat($(element_amount_id).value) * pow) / pow;
            var element_percentage = Math.round((element_amount/item_amount)*100 * pow) / pow;

            $(element_percentage_id).value = element_percentage;

            return true;
        },

        /**
         * Calculates amount in distributed element of an item.
         */
        element_percentages_recalc_amount: function(field) {
            var item_amount_id = field.id.replace(/^element_percentages_(.+)_\d+$/, 'item_amounts_$1');
            var item_amount = $(item_amount_id);
            if (item_amount) {
                item_amount = Math.round(parseFloat(item_amount.value) * pow) / pow;
            }

            //the field is for percentage, calculate amount
            var element_amount_id = field.id.replace(/percentage/, 'amount');
            var element_percentage_id = field.id;
            var element_percentage = Math.round(parseFloat($(element_percentage_id).value) * pow) / pow;
            var element_amount = Math.round((element_percentage*item_amount)/100  * pow) / pow;

            $(element_amount_id).value = element_amount;

            var elements_percentages_total = 0;

            var tbl_id = field.parentNode.parentNode.parentNode.parentNode.id;
            var element_percentages = $$('#' + tbl_id + ' .element_percentages');
            for (var i = 0; i < element_percentages.length; i++) {
                var element_percentage = Math.round(parseFloat(element_percentages[i].value) * pow) / pow;
                if (isNaN(element_percentage)) {
                    element_percentage = 0;
                    element_percentages[i].value = 0;
                }
                elements_percentages_total += element_percentage;
            }
            elements_percentages_total = Math.round(elements_percentages_total * pow) / pow;
            if (isNaN(elements_percentages_total)) {
                elements_percentages_total = 0;
            }

            var is_valid = (elements_percentages_total == 100);
            for (var i = 0; i < element_percentages.length; i++) {
                if (!element_percentages[i].disabled) {
                    removeClass(element_percentages[i], (is_valid ? 'red' : 'green'));
                    addClass(element_percentages[i], (is_valid ? 'green' : 'red'));
                }
            }

            if (is_valid) {
                formulas['item_dd']($(item_amount_id));
            }

            return is_valid;
        },

        /**
         * Calculates distributed amount among centers of a main center.
         */
        center_amounts_recalc: function(field) {
            var centers_total = 0;
            var regex_c = new RegExp(field.id.replace(/_\d+$/, ''));

            var element_amount_id = field.id.replace(/^center(.+)_\d+_\d+$/, 'element$1');
            var element_amount = $(element_amount_id);
            if (element_amount) {
                element_amount = Math.round(parseFloat(element_amount.value) * pow) / pow;
            }

            var tr_id = field.parentNode.parentNode.id;
            var all_center_amounts = $$('#' + tr_id + ' .center_amounts');
            var center_amounts = Array();
            for (var i = 0; i < all_center_amounts.length; i++) {
                if (all_center_amounts[i].id.match(regex_c)) {
                    center_amounts.push(all_center_amounts[i]);
                }
            }

            for (var i = 0; i < center_amounts.length; i++) {
                var center_amount = Math.round(parseFloat(center_amounts[i].value) * pow) / pow;
                if (isNaN(center_amount)) {
                    center_amount = 0;
                    center_amounts[i].value = 0;
                }
                centers_total += center_amount;
            }
            centers_total = Math.round(centers_total * pow) / pow;
            if (isNaN(centers_total)) {
                centers_total = 0;
            }

            var is_valid = (centers_total == element_amount);
            for (var i = 0; i < center_amounts.length; i++) {
                if (!center_amounts[i].disabled) {
                    removeClass(center_amounts[i], (is_valid ? 'red' : 'green'));
                    addClass(center_amounts[i], (is_valid ? 'green' : 'red'));
                }
            }
            return is_valid;
        },

        /**
         * Sets default distribution down from an item (by elements and by centers).
         */
        item_dd: function(field) {
            if (field.disabled) {
                return true;
            }

            var item_value = parseFloat(field.value);
            if (isNaN(item_value)) {
                item_value = 0;
            } else {
                item_value = Math.round(item_value * pow) / pow;
            }

            var indices = field.id.replace(/[^\d]*([\d_]+)$/, '$1').split('_');
            //eval('var elements_dd = elements_dd_' + indices[0] + '_' + indices[1]);
            var item_row = field.up('tr');
            var tbl_id = item_row.id.replace(/item_/, 'elements_table_');
            var element_percentages = $$('#' + tbl_id + ' .element_percentages');


            var element_sum = 0;
            var input_id = '';
            for (var e = 0; e < element_percentages.length; e++) {
                input_id = field.id.replace(/item/, 'element') + '_' + e;
                if (input_id) {
                    var element_percentage = parseFloat(element_percentages[e].value);
                    var next_element_percentage = (element_percentages[e + 1]) ? parseFloat(element_percentages[e + 1].value) : null;
                    var element_value = Math.round((item_value * parseFloat(element_percentage) / 100) * pow) / pow;
                    //var element_value = Math.round((item_value * parseFloat(elements_dd[e]) / 100) * pow) / pow;
                    //set remainder for last non-zero element
                    if (element_percentage > 0 && (next_element_percentage == 0 || e == element_percentages.length - 1)) {
                        $(input_id).value = Math.round((item_value - element_sum) * pow) / pow;
                    } else {
                        // we don't want to have a negative value for the last element due to rounding
                        if (Math.abs(element_value) > Math.abs(item_value - element_sum)) {
                            element_value = Math.round((item_value - element_sum) * pow) / pow;
                        }
                        $(input_id).value = element_value;
                        element_sum += element_value;
                    }
                    //cascade default distribution of element by centers
                    formulas['element_dd']($(input_id));
                }
            }
            //if item has distribution by elements,
            //validate whether sum of all element amounts equals item amount
            if (input_id) {
                formulas['element_amounts_recalc']($(input_id));
            } else {
                var item_amounts_remaining = $(field.id.replace(/^(item_amounts)/, '$1_remaining'));
                //show error if no item selected
                var sel_item_id = $(field.id.replace(/amounts/, 'ids')).value;
                if (item_amounts_remaining) {
                    var ia_rem = sel_item_id ? 0 : (Math.round(parseFloat(field.value) * pow) / pow);
                    item_amounts_remaining.innerHTML = ia_rem.toFixed(env.precision.gt2_rows);
                    removeClass(item_amounts_remaining, (sel_item_id ? 'red' : 'green'));
                    addClass(item_amounts_remaining, (sel_item_id ? 'green' : 'red'));
                }
            }
            return true;
        },

        /**
         * Sets default distribution down from element (by centers).
         */
        element_dd: function(field) {
            if (field.id.match(/percentage/)) {
                field = $(field.id.replace(/percentages/, 'amounts'));
            }
            var indices = field.id.replace(/[^\d]*([\d_]+)$/, '$1').split('_');
            eval('var centers_dd = centers_dd_' + indices[0] + '_' + indices[1]);

            var element_value = parseFloat(field.value);
            if (isNaN(element_value)) {
                element_value = 0;
            } else {
                element_value = Math.round(element_value * pow) / pow;
            }

            if (centers_dd) {
                for (var mc = 0; mc < centers_dd[indices[2]].length; mc++) {
                    var centers_percentages = centers_dd[indices[2]][mc];
                    var center_sum = 0;
                    var num_centers = Object.keys(centers_percentages).length;
                    var n = 1;
                    var input_id = '';
                    for (var c in centers_percentages) {
                        var prop_type = typeof(centers_percentages[c]);
                        if (!prop_type.match(/string/i)) {
                            continue;
                        }
                        input_id = field.id.replace(/element/, 'center') + '_' + mc + '_' + c;
                        if (input_id) {
                            var center_value = Math.round((element_value * parseFloat(centers_percentages[c]) / 100) * pow) / pow;
                            // we don't want to have a negative value for the last center due to rounding
                            if (Math.abs(center_value) > Math.abs(element_value - center_sum)) {
                                center_value = Math.round((element_value - center_sum) * pow) / pow;
                            }
                            if (n < num_centers) {
                                center_sum += center_value;
                                n++;
                            } else {
                                center_value = Math.round((element_value - center_sum) * pow) / pow;
                            }
                            $(input_id).value = center_value;
                        }
                    }
                    if (input_id) {
                        //validate whether sum of all center amounts of a main center equals element amount
                        formulas['center_amounts_recalc']($(input_id));
                    }
                }
            }
            return true;
        },

        /**
         * Validates distribution start and end dates of an item.
         */
        item_dates_compare: function(field) {
            var start_date, end_date;
            if (field.id.match(/_start_/)) {
                start_date = field;
                end_date = ($(field.id.replace(/_start_/, '_end_')));
            } else {
                start_date = field;
                end_date = ($(field.id.replace(/_end_/, '_start_')));
            }

            var valid = true;
            if (start_date.disabled && end_date.disabled) {
                return valid;
            }

            if (start_date.value && end_date.value) {
                if (start_date.value > end_date.value) {
                    valid = false;
                }
                removeClass($(start_date.id + '_formatted'), (!valid ? 'green' : 'red'));
                addClass($(start_date.id + '_formatted'), (!valid ? 'red' : 'green'));
                removeClass($(end_date.id + '_formatted'), (!valid ? 'green' : 'red'));
                addClass($(end_date.id + '_formatted'), (!valid ? 'red' : 'green'));
            } else {
                valid = false;
                removeClass($(start_date.id + '_formatted'), (!start_date.value ? 'green' : 'red'));
                addClass($(start_date.id + '_formatted'), (!start_date.value ? 'red' : 'green'));
                removeClass($(end_date.id + '_formatted'), (!end_date.value ? 'green' : 'red'));
                addClass($(end_date.id + '_formatted'), (!end_date.value ? 'red' : 'green'));
            }
            return valid;
        },

        /**
         * Validates all amounts and dates in distribution form before submit.
         */
        validate: function(field) {
            var result = true;
            //cycling rows from gt2
            var g = 0;
            while ($('row_' + g)) {
                var first_item_id = 'item_amounts_' + g + '_0';
                if ($(first_item_id)) {
                    if (!formulas['item_amounts_recalc']($(first_item_id))) {
                        result = false;
                    }
                }

                //cycling items
                var i = 0;
                while ($('item_' + g + '_' + i)) {
                    if ($('item_amounts_' + g + '_' + i).disabled) {
                        i++;
                        continue;
                    }

                    //validate dates
                    if (!formulas['item_dates_compare']($('distribution_start_dates_' + g + '_' + i))) {
                        result = false;
                    }

                    var first_element_id = 'element_amounts_' + g + '_' + i + '_0';
                    if ($(first_element_id)) {
                        if (!formulas['element_amounts_recalc']($(first_element_id))) {
                            result = false;
                        }

                        //cycling elements
                        var e = 0;
                        while ($('element_' + g + '_' + i + '_' + e)) {
                            var all_centers = $$('#element_' + g + '_' + i + '_' + e + ' .center_amounts');
                            //holds unique indices of all main centers
                            var mc_indices = Array();
                            for (var c = 0; c < all_centers.length; c++) {
                                //main center index
                                var mc_idx = all_centers[c].id.replace(/.+_(\d+)_\d+$/, '$1');
                                if (mc_indices.indexOf(mc_idx) == -1) {
                                    mc_indices.push(mc_idx);
                                    if (!formulas['center_amounts_recalc'](all_centers[c])) {
                                        result = false;
                                    }
                                }
                            }
                            e++;
                        }
                    }
                    i++;
                }
                g++;
            }
            return result;
        }
    };

    //define sequences for the formulas
    var sequences = {
        item_amounts:           ['item_amounts_recalc', 'item_dd'],
        item_amounts_recalc:    ['item_amounts_recalc'],
        element_amounts:        ['element_amounts_recalc_percentage', 'element_amounts_recalc', 'element_dd'],
        element_percentages:    ['element_percentages_recalc_amount', 'element_amounts_recalc', 'element_dd'],
        center_amounts:         ['center_amounts_recalc'],
        submit_distribution:    ['validate']
    };

    var sequence = sequences[custom_class];
    var result = true;
    if (sequence.length > 0) {
        for (var i = 0; i < sequence.length; i ++) {
            if (!formulas[sequence[i]](field)) {
                result = false;
            }
        }
    }

    return result;
}

/**
 * Changes distribution data when changing analysis item.
 *
 * @param form - the object form in the DOM
 * @param element - input element - items dropdown
 * @param row_id - object from the DOM where response should be loaded
 */
function changeFinanceAnalysisItem(form, element, row_id) {
    var input_id = element.id;
    var item_id = element.value;
    var item_amount = 0;
    var item_amount_input = $(input_id.replace(/_ids_/, '_amounts_'));
    if (item_amount_input) {
        item_amount = parseFloat(item_amount_input.value);
        if (isNaN(item_amount)) {
            item_amount = 0;
        }
    }

    var data_cell = $$('#' + row_id + '>td');
    data_cell = data_cell[0];

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }

            //eval inline scripts in template
            result.evalScripts();

            //if no item selected or item has no distribution by elements, hide data row
            eval('var el_var = ' + input_id.replace(/item_ids/, 'elements_dd'));
            if (item_id && el_var) {
                $(row_id).style.display = '';
            } else {
                $(row_id).style.display = 'none';
            }

            //replace distribution start and end dates
            eval('var start_date = ' + input_id.replace(/item_ids/, 'start_date'));
            eval('var end_date = ' + input_id.replace(/item_ids/, 'end_date'));
            if (start_date && end_date) {
                start_date = start_date.split('-');
                end_date = end_date.split('-');

                //the months begin from 0, that is why we subtract 1 from the month number
                start_date = new Date(parseInt(start_date[0].replace(/^0/, '')),
                                      parseInt(start_date[1].replace(/^0/, ''))-1,
                                      parseInt(start_date[2].replace(/^0/, '')),
                                      0, 0, 0);
                end_date = new Date(parseInt(end_date[0].replace(/^0/, '')),
                                    parseInt(end_date[1].replace(/^0/, ''))-1,
                                    parseInt(end_date[2].replace(/^0/, '')),
                                    0, 0, 0);
                var d_start_date_id = input_id.replace(/item_ids/, 'distribution_start_dates');
                if ($(d_start_date_id)) {
                    $(d_start_date_id + '_formatted').value = start_date.print("%d.%m.%Y");
                    $(d_start_date_id + '_formatted').style.color = '';
                    $(d_start_date_id).value = start_date.print("%Y-%m-%d");
                }
                var d_end_date_id = input_id.replace(/item_ids/, 'distribution_end_dates');
                if ($(d_end_date_id)) {
                    $(d_end_date_id + '_formatted').value = end_date.print("%d.%m.%Y");
                    $(d_end_date_id + '_formatted').style.color = '';
                    $(d_end_date_id).value = end_date.print("%Y-%m-%d");
                }
            }

            data_cell.innerHTML = result;

            //perform distribution calc as if the item amount input's value has been changed
            if (item_amount_input) {
                distribution_calc(item_amount_input);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.action_param + '=ajax_change_item&item_id=' + item_id + '&item_amount=' + item_amount + '&input_id=' + input_id;

    new Ajax.Request(url, opt);
}

/**
 * Copies two rows (item row and data row) of a table and clones the elements in them.
 *
 * @param table - id of row table where rows should be added
 */
function addItem(table) {
    //define the max allowed rows with fields
    var max_table_rows = env.max_group_rows;

    //all inputs will be with empty values
    var dont_copy_values = true;

    //get the table element
    var tbl = $(table);

    //if row is collapsed, expand it
    if (tbl.parentNode.parentNode.style.display = 'none') {
        var switch_element = $(tbl.parentNode.parentNode.id + '_switch');
        toggleAnalysisRow(switch_element, 'expand');
    }

    //define the last row number (each item uses 2 rows)
    var last_row_total = tbl.rows.length;
    var last_row = last_row_total / 2;
    if (last_row + 1 > max_table_rows) {
        return;
    }

    //define which are item and data source rows
    var source_row = tbl.rows[last_row_total - 2];
    var source_row2 = tbl.rows[last_row_total - 1];
    var source_row_idx = last_row - 1;

    //insert new item row in the table
    var row = tbl.insertRow(last_row_total);
    row.id = source_row.id.replace(/(.*_)\d+$/, '$1' + last_row);
    row.className = source_row.className;
    //insert new data row (empty) in the table
    var row2 = tbl.insertRow(last_row_total + 1);
    row2.id = row.id.replace(/item/, 'data');
    row2.style.display = 'none';
    var cell2 = row2.insertCell(0);
    cell2.className = source_row2.cells[0].className;
    cell2.colSpan = source_row2.cells[0].colSpan;
    var element_script = document.createElement('script');
    element_script.setAttribute('type', 'text/javascript');
    var row_idx = table.replace(/.*_(\d+)$/, '$1');
    element_script.text = 'elements_dd_' + row_idx + '_' + last_row + ' = \'\';\n';
    element_script.text += 'centers_dd_' + row_idx + '_' + last_row + ' = \'\';';
    cell2.appendChild(element_script);

    //get the cells' count
    var cells_count = source_row.cells.length;

    //change the fields' attributes in each cell
    for (var c = 0; c < cells_count; c++) {
        var cell = source_row.cells[c].cloneNode(true);
        cell.innerHTML = '';
        if (source_row.cells[c].id) {
            cell.id = row.id + '_' + c;
        }
        removeClass(cell, 'input_inactive');
        row.appendChild(cell);
        var cell_innerHTML = source_row.cells[c].innerHTML;
        var fields = cell_innerHTML.match(/id="?[\d\w_]*"?\s/gi);
        var items = 0;
        if (fields) {
            items = fields.length;
        }

        //check if the cell is hidden
        if (source_row.cells[c].style.display == 'none') {
            cell.style.display = 'none';
        }

        for (var i = 0; i < items; i++) {
            //get the field and its name to be added
            var field_id = fields[i].replace(/^id="?([\d\w_]*)_[0-9]+"?\s$/gi, "$1");
            var field = $(field_id + '_' + (source_row_idx));

            if (!field) {
                //it is possible that the cell is empty and no field is inserted
                continue;
            }

            if (field.name) {
                var field_name = field.name.replace(/\[[0-9]+\]$/, '');
                var regex_name = new RegExp(field_name.replace(/(\[|\])/g, '\\$1') + '\\[[0-9]+\\]','gi');
                cell_innerHTML = cell_innerHTML.replace(regex_name, field_name + '[' + (last_row) + ']');
            }
            //update parameter of onchange function of dropdown
            cell_innerHTML = cell_innerHTML.replace(/('data_\d+_)\d+(')/gi, '$1' + last_row + '$2');

            //update span with remaining amount - set "red" class and "0" value
            cell_innerHTML = cell_innerHTML.replace(/(<span[^>]*(\s|"))(green|red)((\s|")[^>]*>)[.\d]*(<\/span>)/, '$1' + 'red' + '$4' + '0' + '$6');

            var regex_id = new RegExp(field_id + '_[0-9]+','gi');
            cell_innerHTML = cell_innerHTML.replace(regex_id, field_id + '_' + last_row);

            //var regex_row = new RegExp('row=[0-9]+','gi');
            //cell_innerHTML = cell_innerHTML.replace(regex_row, 'row=' + last_row);
        }
        cell.innerHTML = cell_innerHTML;
        for (var i = 0; i < items; i++) {

            //get the field and its name to be added
            var field_id = fields[i].replace(/^id="?([\d\w_]*)_[0-9]+"?\s$/gi, "$1");
            var is_formatted = false;
            //custom treatment of formatted date inputs
            if (field_id.match(/_formatted"?\s$/gi)) {
                field_id = field_id.replace(/^id="?([\d\w_]*)_[0-9]+_formatted"?\s$/gi, "$1");
                is_formatted = true;
            }
            var field = $(field_id + '_' + (source_row_idx) + (is_formatted ? '_formatted' : ''));
            var new_field = $(field_id + '_' + last_row + (is_formatted ? '_formatted' : ''));

            if (!field || !new_field) {
                //it is possible that the cell is empty and no field is inserted
                continue;
            }

            if (!dont_copy_values) {
                new_field.value = field.value;
                new_field.defaultValue = new_field.value;
            } else {
                if (is_formatted) {
                    new_field.value = defaultDate;
                    new_field.defaultValue = defaultDate;
                } else {
                    new_field.value = '';
                    new_field.defaultValue = '';
                }
            }
            if (new_field.tagName == 'SELECT') {
                toggleUndefined(new_field);
            }

            //remove special class for the disabled elements
            if (new_field.className.match(/input_inactive/)) {
                new_field.disabled = false;
                removeClass(new_field, 'input_inactive');
            }

            //search for file field and if such exists removes everything in the cell
            //leaving only the file field activated
            var upload_fields = cell.getElementsByTagName('input');
            for (var k = 0; k < upload_fields.length; k++) {
                if (upload_fields[k].className.match(/file_upload/) && upload_fields[k].type == 'file') {
                    var upload_field = upload_fields[k].cloneNode(true);
                    cell.innerHTML = '';
                    upload_field.style.display = 'block';
                    upload_field.disabled = false;
                    cell.appendChild(upload_field);
                }
            }

            //cTranslator is used (alternative keyboard inputs)
            //Install the keypressed events in the texts and textareas elements
            if (isDefined('cTranslator') && !new_field.readonly &&
                ((new_field.tagName.toLowerCase() == 'input' && new_field.type.toLowerCase() == 'text') || new_field.tagName.toLowerCase() == 'textarea')) {
                cTranslator.install(new_field);
            }
        }
    }

    var scripts = row.getElementsByTagName('script');
    for (var j = 0; j < scripts.length; j++) {
        ajaxLoadJS(scripts[j]);
    }

    //get the first column, which is system
    var cell_0 = row.cells[0];

    var regex_1 = new RegExp("\,\'[0-9]+\\'\\)",'gi');
    cell_innerHTML = cell_0.innerHTML.replace(regex_1, ',\'' + last_row + '\')');
    regex_1 = new RegExp('>[0-9]+<','gi');
    cell_innerHTML = cell_innerHTML.replace(regex_1, '>' + (last_row + 1) + '<');
    regex_1 = new RegExp("(visibility:.*hidden)",'gi');
    cell_innerHTML = cell_innerHTML.replace(regex_1, '');
    cell_0.innerHTML = cell_innerHTML;

    //manage the +/- buttons
    var plusButton = $(tbl.id + '_plusButton');
    var minusButton = $(tbl.id + '_minusButton');

    if (last_row < max_table_rows) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    if (last_row > 0) {
        removeClass(minusButton, 'disabled');
    } else {
        addClass(minusButton, 'disabled');
    }

    //show the X image button in the first row
    var first_row = '';
    for (var i = 0; i < tbl.rows.length; i += 2) {
        //current row
        var r = tbl.rows[i];

        if (r.style.display != 'none') {
            if (!first_row) {
                first_row = r;
                break;
            }
        }
    }
    if (first_row) {
        var x = $$('.hide_row');
        if (x) {
            for (var i = 0; i < x.length; i++) {
                if (x[i].parentNode.parentNode.id == first_row.id) {
                    x[i].style.visibility = 'visible';
                }
            }
        }
    }

    //recalculate distribtion of row amount among active items
    distribution_calc($(tbl.id.replace(/.*(_\d+)$/, 'item_amounts$1_' + last_row)), true);

}

/**
 * Removes by completely deleting the last two rows (item row and data row) of a table
 * ATTENTION: there is no way to restore removed rows
 *
 * @param table - id of row table where rows should be removed from
 * @param string item_idx - optional parameter; index of item within row table
 */
function removeItem(table) {
    var item_idx = '', row_idx = '';
    if (removeItem.arguments.length > 1) {
        item_idx = removeItem.arguments[1];
        //index of data row; index of item row is row_idx + 1
        row_idx = item_idx * 2;
    }
    var tbl = $(removeItem.arguments[0]);

    //if row is collapsed, expand it
    if (tbl.parentNode.parentNode.style.display = 'none') {
        var switch_element = $(tbl.parentNode.parentNode.id + '_switch');
        toggleAnalysisRow(switch_element, 'expand');
    }

    if (!row_idx) {
        row_idx = tbl.rows.length - 2;
        item_idx = parseInt(row_idx / 2);
    }
    if (row_idx > 1) {
        //delete item and data rows
        tbl.deleteRow(row_idx + 1);
        tbl.deleteRow(row_idx);
        //index of next to last item
        item_idx -= 1;
    }

    //manage the +/- buttons
    var plusButton = $(tbl.id + '_plusButton');
    var minusButton = $(tbl.id + '_minusButton');

    if (item_idx + 1 < env.max_group_rows) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    if (item_idx > 0) {
        removeClass(minusButton, 'disabled');
    } else {
        addClass(minusButton, 'disabled');
    }

    //hide the X image button in the first row (only when there is one visible row)
    var first_row = '';
    var remove_x = true;
    for (var i = 0; i < tbl.rows.length; i += 2) {
        //current row
        var r = tbl.rows[i];

        if (r.style.display != 'none') {
            if (!first_row) {
                first_row = r;
            } else {
                remove_x = false;
                break;
            }
        }
    }
    if (remove_x && first_row) {
        var x = $$('#' + first_row.id + ' .hide_row');
        if (x) {
            x = x[0];
            x.style.visibility = 'hidden';
        }
    }

    //recalculate distribtion of row amount among active items
    distribution_calc($(tbl.id.replace(/.*(_\d+)$/, 'item_amounts$1_' + item_idx)), true);
}

/**
 * Enables/disables two rows of elements (item row and data row) in a table.
 * Note: the disabled rows can be restored.
 *
 * @param string table - id of table containing item
 * @param string item_idx - index of item within row table
 */
function disableItem(table, item_idx) {
    var tbl = $(table);
    //index of item row; index of data row is row_idx + 1
    var row_idx = item_idx * 2;

    var rows = [tbl.rows[row_idx], tbl.rows[row_idx + 1]];
    for (var j = 0; j < rows.length; j++) {
        if (rows[j].className.match(/search_inactive/)) {
            return;
        }
        var row_innerHTML = rows[j].innerHTML;
        var items = row_innerHTML.match(/id="?[\d\w_]*"?\s/gi);

        if (items) {
            for (var i = 0; i < items.length; i++) {
                var field = items[i].replace(/^id="?([\d\w_]*)"?\s$/gi, '$1');
                field = $(field);
                if (!field) {
                    //it is possible that the cell is empty and no field is inserted
                    continue;
                }
                if (field.disabled && !field.className.match(/input_inactive/)) {
                    continue;
                }
                if (field.disabled || field.tagName == 'A' || field.tagName == 'SPAN') {
                    field.disabled = false;
                    removeClass(field, 'input_inactive');
                }
                else {
                    addClass(field, 'input_inactive');
                    field.disabled = true;
                    field.style.color = '';
                }
            }
        }
        //hide the row
        if (rows[j].className.match(/input_inactive/)) {
            removeClass(rows[j], 'input_inactive');
        }
        else {
            addClass(rows[j], 'input_inactive');
        }
    }

    //recalculate distribtion of row amount among active items
    distribution_calc($(table.replace(/.*(_\d+)$/, 'item_amounts$1_' + item_idx)), true);
}

/**
 * Hides two rows of elements (item row and data row) in a table (with display: none).
 * ATTENTION: removed rows cannot be restored.
 *
 * @param string table - id of table containing item
 * @param string item_idx - index of item within row table
 */
function hideItem(table, item_idx) {
    // convert to int
    item_idx = parseInt(item_idx);

    //index of item row; index of data row is num + 1
    var row_idx = item_idx * 2;

    //convert row_idx to integer
    row_idx = parseInt(row_idx);

    var tbl = $(table);

    //if this is the last and not the only couple of rows of the table, simply remove it
    if (tbl.rows.length - 2 == row_idx && tbl.rows.length - 2 > 0) {
        tbl.deleteRow(row_idx + 1);
        tbl.deleteRow(row_idx);
        item_idx -= 1;
        //also remove all the rows that are hidden (display: none) at the end of the table
        for (var i = tbl.rows.length; i > 0; i -= 2) {
            if (tbl.rows[i - 2].style.display == 'none' && tbl.rows.length - 2 > 0) {
                tbl.deleteRow(i - 1);
                tbl.deleteRow(i - 2);
                item_idx -= 1;
            }
            else {
                break;
            }
        }
    } else {
        //hide the two rows and add special className so that the fields should be recognizable
        var rows = [tbl.rows[row_idx], tbl.rows[row_idx + 1]];

        for (var j = 0; j < rows.length; j++) {
            var row_innerHTML = rows[j].innerHTML;
            var items = row_innerHTML.match(/id="?[\d\w_]*"?\s/gi);

            if (items) {
                for (var i = 0; i < items.length; i++) {
                    var field = items[i].replace(/^id="?([\d\w_]*)"?\s$/gi, '$1');
                    field = $(field);
                    if (!field) {
                        //it is possible that the cell is empty and no field is inserted
                        continue;
                    }
                    if (field.disabled || field.tagName == 'A') {
                        continue;
                    }
                    else {
                        addClass(field, 'input_inactive');
                        field.disabled = true;
                    }
                }
            }
            //hide the row
            rows[j].style.display = 'none';
            if (!rows[j].className.match(/input_inactive/)) {
                addClass(rows[j], 'input_inactive');
            }
        }
    }

    //define the max allowed rows with fields
    var max_table_rows = env.max_group_rows;

    //manage the +/- buttons
    var plusButton = $(tbl.id + '_plusButton');
    var minusButton = $(tbl.id + '_minusButton');
    if (plusButton && minusButton) {
        if (item_idx + 1 < max_table_rows) {
            removeClass(plusButton, 'disabled');
        } else {
            addClass(plusButton, 'disabled');
        }
        if (tbl.rows.length - 2 > 0) {
            removeClass(minusButton, 'disabled');
        } else {
            addClass(minusButton, 'disabled');
        }
    }

    //hide the X image button in the first row (only when there is one visible row)
    var first_row = '';
    var remove_x = true;
    for (var i = 0; i < tbl.rows.length; i += 2) {
        //current row
        var r = tbl.rows[i];

        if (r.style.display != 'none') {
            if (!first_row) {
                first_row = r;
            } else {
                remove_x = false;
                break;
            }
        }
    }
    if (remove_x && first_row) {
        var x = $$('#' + first_row.id + ' .hide_row');
        if (x) {
            x = x[0];
            x.style.visibility = 'hidden';
        }
    }

    //recalculate distribtion of row amount among active items
    distribution_calc($(table.replace(/.*(_\d+)$/, 'item_amounts$1_' + item_idx)), true);
}
