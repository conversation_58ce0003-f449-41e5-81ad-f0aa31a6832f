/* $Id: rounded.css 6485 2007-02-28 13:19:06Z roman $ */
@import url("layout/basic.css");

/* Top and Sub menus */
.zpMenuRounded .zpMenu a,
.zpMenuRounded .zpMenu .zpMenu-label {
  font-family: Tahoma, Heltvetica, Arial, sans-serif;
  color:  #FF0000;
  font-weight: bold;
  font-size: 11px;
}

/* Top and Sub menus */
.zpMenuRounded .zpMenu .zpMenu-item-selected .zpMenu-label,
.zpMenuRounded .zpMenu .zpMenu-item-selected a {
  color: #0066CC;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item,
.zpMenuRounded .zpMenuContainer .zpMenu-item {
  height:20px;
  background: url("rounded/middle.gif") repeat-x center;
  background-color: #F7F7F7;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item .zpMenu-label{
  padding-left: 10px;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenuContainer .zpMenu-item .zpMenu-label{
  padding-left: 0px;
}


.zpMenuRounded .zpMenuContainer .zpMenuContainer  {
  border: 0px;
  margin-top: 2px;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenuContainer  {
  border: 0px;
  margin-top: 0px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenuContainer{
  margin-left:2px;
}

/* Sub menu */
.zpMenuRounded .zpMenu-vertical-mode .zpMenu-level-1,
.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item {
  margin-top:0px;
  padding-top: 0px;
  border: 0px;
  background: url("rounded/center.gif");
  padding:0px;
  color: #00008B;
  width: 150px;
}


/* Sub, HR */
.zpMenuRounded .zpMenuContainer .zpMenu-item-hr,
.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
/*  background: #AA7755;  */
  border-top:1px solid #AA7755;
  height:2px
}

.zpMenuRounded .zpMenuContainer .zpMenu-item-first {
  margin: 0px;
  background: url("rounded/first.gif") no-repeat left center;
  border: 0px;
}

.zpMenuRounded .zpMenuContainer .zpMenu-item-last {
  margin: 0px;
  background: url("rounded/last.gif") no-repeat right center;
  border: 0px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-last {
  margin-top: 0px;
  border: 0px;
  background: url("rounded/bottom.gif") no-repeat left bottom;
  padding-bottom:5px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-first {
  background: url("rounded/top.gif") no-repeat left top;
  margin: 0px;
  border: 0px;
  padding-top:5px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-last .zpMenu-item-first {
  margin-top: 0px;
  border: 0px;
  background: url("rounded/top_bottom.gif") no-repeat left bottom;
  padding-bottom:5px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-first {
  background: url("rounded/top.gif") no-repeat left top;
  margin: 0px;
  border: 0px;
  padding-top:5px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-single {
  background: url("rounded/single.gif") no-repeat left top;
  margin: 0px;
  border: 0px;
  padding-top:5px;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item-last {
  margin-top: 0px;
  background: url("rounded/bottom.gif") no-repeat left bottom;
  border: 0px;
  padding-bottom:5px;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item-first {
  background: url("rounded/top.gif") no-repeat left top;
  margin: 0px;
  border: 0px;
  padding-top:5px;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-collapsed .zpMenu-label {
  background: url("rounded/arrow2.gif") #F7F7F7 no-repeat right center;
}

.zpMenuRounded .zpMenuContainer .zpMenuContainer .zpMenu-item-expanded .zpMenu-label {
  background: url("rounded/arrow2.gif") #F7F7F7 no-repeat right center;
}

.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item-collapsed .zpMenu-table {
	width: 100%;
  background: url("rounded/arrow2.gif") #F7F7F7 no-repeat right center; 
}
.zpMenuRounded .zpMenu-vertical-mode .zpMenu-item-expanded .zpMenu-table {
	width: 100%;
  background: url("rounded/arrow2.gif") #F7F7F7 no-repeat right center; 
}
