/* $Id: default.css 6693 2007-03-21 09:26:59Z smaxim $ */
.zpSliderDefaultContainer .zpSliderBgVertical {
  width: 25px;
  background: url(default/fon_v.gif) repeat-y center transparent;
}

.zpSliderDefaultContainer .zpSliderBgHorizontal {
  height: 25px;
  background: url(default/fon_h.gif) repeat-x center transparent;
}

.zpSliderDefaultContainer .zpSliderButtonVertical{
  width: 25px;
  height: 11px;
  font-size: 2px;
}

.zpSliderDefaultContainer .zpSliderButtonVerticalInactive {
  background: url(default/sl_v.gif) no-repeat;
  z-index : 2;
}

.zpSliderDefaultContainer .zpSliderButtonVerticalActive {
  background: url(default/sl_v_o.gif) no-repeat;
  z-index:3;
} 

.zpSliderDefaultContainer .zpSliderButtonHorizontal {
  width: 11px;
  height: 25px;
}

.zpSliderDefaultContainer .zpSliderButtonHorizontalInactive {
  background: url(default/sl_h.gif) no-repeat;
  z-index : 2;
}

/* .zpSliderDefaultContainer .zpSliderButtonHorizontal:hover, 
.zpSliderDefaultContainer .zpSliderButtonHorizontal:focus,
.zpSliderDefaultContainer .zpSliderButtonHorizontal:active {
  width: 11px;
  height: 25px;
  background: url(default/sl_o.gif) no-repeat;
}

*/

.zpSliderDefaultContainer .zpSliderButtonHorizontalActive {
  background: url(default/sl_o.gif) no-repeat;
  z-index : 3;
}

.zpSliderDefaultContainer .zpSliderLeftSpin {
  width: 17px;
  height: 25px;
  font-size: 2px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderLeftSpinInactive {
  background: url(default/l_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderRightSpin {
  width: 17px;
  height: 25px;
  font-size: 2px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderRightSpinInactive {
  background: url(default/r_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderUpSpin {
  width: 25px;
  height: 17px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderUpSpinInactive {
  background: url(default/u_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderDownSpin {
  width: 25px;
  height: 17px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderDownSpinInactive {
  background: url(default/d_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderLeftFirstSpin {
  width: 20px;
  height: 25px;
  font-size: 2px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderLeftFirstSpinInactive {
  background: url(default/l_f_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderRightLastSpin {
  width: 20px;
  height: 25px;
  font-size: 2px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderRightLastSpinInactive {
  background: url(default/r_l_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderUpFirstSpin {
  width: 25px;
  height: 20px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderUpFirstSpinInactive {
  background: url(default/u_f_inc.gif) no-repeat center;
}

.zpSliderDefaultContainer .zpSliderDownLastSpin {
  width: 25px;
  height: 20px;
  display : block;
}

.zpSliderDefaultContainer .zpSliderDownLastSpinInactive {
  background: url(default/d_l_inc.gif) no-repeat center;
}
