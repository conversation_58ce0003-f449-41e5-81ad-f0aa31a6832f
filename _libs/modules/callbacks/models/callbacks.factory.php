<?php

/**
 * Callbacks factory class
 */
Class Callbacks extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Callback';

    /**
     * DB alias
     */
    public static $alias = 'clb';

    public static $slug = 'callback';


    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                'from' => '',
                'where' => '',
                'group' => '',
                'order' => '',
                'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort1 = implode(', ', $filters['sort']);
            $sort = 'ORDER BY ';
            if (!preg_match('#clb.added#', $sort1)) {
                $sort .= 'clb.added desc, ';
            }
            $sort .= $sort1;
        } else {
            $sort = 'ORDER BY clb.added DESC';
        }
        $sort .= ', clb.id DESC';

        //select clause
        $sql['select'] = 'SELECT DISTINCT (clb.id)' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CALLBACKS . ' AS clb' . "\n";

        //where clause
        $sql['where'] = $where . "\n";

        //order by clause
        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (!empty($filters['paginate']) && !empty($ids)) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(DISTINCT clb.id) AS total';
                $sql['order'] = '';
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $filters['total'] = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $filters['total'] = count($ids);
            }
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
            'from' => '',
            'where' => '',
            'group' =>'',
            'order' => '',
            'limit' => '');

        if ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            //so getIds is not needed
            $ids = self::constructWhere($registry, $filters);
        } else {
            $ids = self::getIds($registry, $filters, $sql);
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT(clb.id), clb.*' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CALLBACKS . ' AS clb' . "\n";

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE clb.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }
        $sql['group'] = 'GROUP BY clb.id';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        $models = self::createModels($registry, $records, self::$modelName, false);

        foreach ($models as $key => $model) {
            if (isset($filters['sanitize']) && $filters['sanitize'] === true) {
                $models[$key]->sanitize();
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, &$filters = array()) {
        $where[] = 'WHERE (';

        if (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        return $where;
    }

    /**
     * get the main slug of the callback module
     */
    public static function getSlug() {
        return self::$slug;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];


        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, DB_TABLE_CALLBACKS);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the result is true if there is no transaction error
        $result = !$db->ErrorMsg();

        return $result;
    }

    /**
     * @throws Exception
     */
    public static function initNewCallback($registry, $properties):Callback {
        $properties['hash'] = self::generateRandomHash();
        $callback = new Callback($registry, $properties);
        if (!$callback->save()) {
            throw new Exception('Cannot init callback!');
        }

        return $callback;
    }

    public static function generateRandomHash($length = 12) {
        return substr(md5(openssl_random_pseudo_bytes($length)), $length);
    }

    /**
     * @param Registry $registry
     * @param int $id
     * @return void
     * @throws Exception
     */
    public static function setProcessed(Registry $registry, int $id):void
    {
        if (empty($id)) {
            throw new \Exception("Invalid callback id!");
        }

        $db = $registry['db'];

        //query to update the main table
        $query = 'UPDATE ' . DB_TABLE_CALLBACKS . "\n" .
            'SET processed=NOW()' . "\n" .
            'WHERE id=' . $id;

        $db->Execute($query);

        if ($db->ErrorMsg()) {
            throw new \Exception("Cannot set callback {$id} as processed!");
        }
    }

}

?>
