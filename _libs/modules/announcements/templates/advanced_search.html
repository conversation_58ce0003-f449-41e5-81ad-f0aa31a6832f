<h1>{$title}</h1>
<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td colspan="2" class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements=search&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
    </td>
  </tr>
  <tr>
    <td colspan="2">{include file=`$theme->templatesDir`actions_box.html}</td>
  </tr>
  <tr class="t_list">
    <td id="categories_holder" class="t_table" style="width: 180px; border-right: none;">
      <table border="0" cellpadding="5" cellspacing="0" width="100%">
        <tr>
          <td class="t_caption"><div class="t_caption_title">{#announcements_categories_short#}</div></td>
        </tr>
        <tr>
          <td>
            <a{if empty($filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements=search&amp;category=&amp;type=">
            {assign var='all_count' value=0}
            {assign var='unread_count' value=0}
            {foreach name='c' from=$categories item='category'}
            {math x=$all_count y=$category->get('count_announcements') assign='all_count' equation=x+y}
            {math x=$unread_count y=$category->get('count_unread') assign='unread_count' equation=x+y}
            {/foreach}
              {#all#|escape} ({$all_count})
            </a>
          </td>
        </tr>
        <tr>
          <td>
            <a{if in_array('unread',$filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements=search&amp;category=unread&amp;type=">
              {#announcements_not_read#|escape} ({$unread_count})
            </a>
          </td>
        </tr>
        {foreach name='c' from=$categories item='category'}
          <tr>
            <td>
              <a{if in_array($category->get('id'),$filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements=search&amp;category={$category->get('id')}&amp;type=">
                {$category->get('name')} ({$category->get('count_announcements')})
              </a>
            </td>
          </tr>
        {/foreach}
      </table>
    </td>
    <td id="list_holder" class="t_table">
    <div class="form_container">
      <form name="announcements" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements" method="post" enctype="multipart/form-data">
      {foreach from=$announcements item='announcement'}
        {strip}
          {capture assign='info'}
            <strong><u>{#announcements_full_num#|escape}:</u></strong> {$announcement->get('full_num')}<br />
            <strong>{#announcements_subject#|escape}:</strong> {$announcement->get('subject')|escape}<br />
            <strong>{#added#|escape}:</strong> {$announcement->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$announcement->get('added_by_name')|escape}<br />
            <strong>{#modified#|escape}:</strong> {$announcement->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$announcement->get('modified_by_name')|escape}<br />
            {if $announcement->isDeleted()}<strong>{#deleted#|escape}:</strong> {$announcement->get('deleted')|date_format:#date_mid#|escape}{if $announcement->get('deleted_by_name')} {#by#|escape} {$announcement->get('deleted_by_name')|escape}{/if}<br />{/if}

            <strong>{#translations#|escape}:</strong>
              <span class="translations">
              {foreach from=$announcement->get('translations') item='trans'}
                <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $announcement->get('model_lang')} class="selected"{/if} />
              {/foreach}
              </span>
          {/capture}
        {/strip}
        <a name="announcement_{$announcement->get('id')}"></a>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table" style="width:100%!important">
          <tr>
            <td class="t_caption" style="background: url('{$theme->imagesUrl}t_caption_{$announcement->get('color')}.jpg')">
              <div class="t_caption_title" style="background: none;padding-left:10px">
                {if $announcement->get('files_count')}
                <div style="float:left;width:16px!important;padding-right:10px">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements&amp;announcements=view&amp;view={$announcement->get('id')}">
                    <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                        onmouseover="showFiles(this, '{$module}', '{$controller}', {$announcement->get('id')})"
                        onmouseout="mclosetime()" />
                  </a>
                </div>
                {/if}
                <div style="float:left;">
                {if $announcement->get('user_read')}
                  <img src="{$theme->imagesUrl}announcements_read.png" alt="" {popup caption=#help# text=#announcements_is_read#} />
                {else}
                  <img src="{$theme->imagesUrl}announcements.png" id="announcements_read_{$announcement->get('id')}" style="border: none;" alt="" class="pointer" onclick="readAnnouncement({$announcement->get('id')});" {popup caption=#help# text=#announcements_set_read#} />
                {/if}
                </div>
                <div style="float:left; padding-left: 10px;">
                  {capture assign='priority'}announcements_{$announcement->get('priority')}{/capture}
                  {capture assign='priority_help'}{$smarty.config.$priority} {#announcements_priority#}{/capture}
                  <img src="{$theme->imagesUrl}{$announcement->get('priority')}.png" alt="" {popup caption=#help# text=$priority_help} />
                </div>
                <div style="float:left; padding-left:10px;">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements&amp;announcements=view&amp;view={$announcement->get('id')}">
                    <span style="color: #000000;">
                      {$announcement->get('type_name')}:&nbsp;{$announcement->get('subject')|escape|mb_truncate} ({$announcement->get('full_num')})
                    </span>
                  </a>
                </div>
                <div class="switch_collapse" style="float:right" onclick="toggleAnnouncement('announcements_list_{$announcement->get('id')}', this);"></div>
                <div style="float: right; padding-right: 10px;">
                  <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
                </div>
              </div>
            </td>
          </tr>
          <tr class="{cycle values='t_odd1,t_even1'}{if !$announcement->get('active')} t_inactive{/if}{if $announcement->get('deleted_by')} t_deleted{/if}" id="announcements_list_{$announcement->get('id')}">
            <td>
              <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;" class="t_layout_table t_borderless">
                <tr>
                  <td colspan="2">
                    {$announcement->get('added_by_name')|escape}, {$announcement->get('added')|date_format:#date_mid#}
                    <br /><br />
                    <div id="announcements_content_{$announcement->get('id')}" 
                         style="height: 100px;min-height:100px;overflow:hidden;" class="announcements_content">
                    {if $announcement->get('switch_html') eq 'html'}
                      {$announcement->get('content')}
                    {else}
                      {$announcement->get('content')|url2href|nl2br}
                    {/if}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="labelbox">
                  {strip}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements&amp;announcements=communications&amp;communications={$announcement->get('id')}&amp;communication_type=comments">
                      <img src="{$theme->imagesUrl}small/comments.png" alt="" style="border: none;" />&nbsp;
                      {#comments#|escape} ({$announcement->getComments()|@count})
                    </a>
                  {/strip}
                  </td>
                  <td>
                    <div id="announcements_more_{$announcement->get('id')}" style="text-align: right; padding-right:10px; display:block; vertical-align: middle; white-space: nowrap;">
                    {strip}
                      <img src="{$theme->imagesUrl}expand.png" alt="" class="pointer" onclick="return expandAnnouncement({$announcement->get('id')});" />
                      <a class="pointer" onclick="return expandAnnouncement({$announcement->get('id')});">&nbsp;{#expand#|escape}</a>
                    {/strip}
                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      {foreachelse}
        <table border="0" cellpadding="0" cellspacing="0" class="t_table" style="width: 100%!important;">
          <tr>
            <td class="t_caption">
              <div class="t_caption_title"><span class="error">{#no_items_found#}</span></div>
            </td>
          </tr>
        </table>
      {/foreach}
        <script type="text/javascript">
          hideUnnecessaryAnnouncementToggles();
        </script>
      </form>
    </div>
    </td>
  </tr>
  <tr>
    <td colspan="2" class="pagemenu">
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_selection_stats=1
        }
    </td>
  </tr>
</table>