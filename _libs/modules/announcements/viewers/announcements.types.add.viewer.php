<?php

class Announcements_Types_Add_Viewer extends Viewer {
    public $template = 'types_add.html';

    public function prepare() {
        $this->model = $this->registry['announcements_type'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare counters
        require_once $this->modelsDir . 'announcements.counters.factory.php';
        $filters = array('sanitize' => true);
        $counters = Announcements_Counters::search($this->registry, $filters);
        $this->data['counters'] = $counters;

        require_once(PH_MVC_DIR . 'dropdown.class.php');
        $this->data['colors'] = Dropdown::getTagsColors(array($this->registry));
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('announcements_types_add');
    }
}

?>
