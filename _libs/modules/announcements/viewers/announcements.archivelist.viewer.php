<?php

class Announcements_Archivelist_Viewer extends Viewer {
    public $template = 'advanced_list.html';

    public function prepare() {

        $filters = array();
        $filters['where'] = array('a.search_archive = \'archive\'');

        require_once $this->modelsDir . 'announcements.factory.php';
        $filters = Announcements::saveSearchParams($this->registry, $filters, 'archivelist_');

        $filters['sanitize'] = true;
        list($announcements, $pagination) = Announcements::pagedSearch($this->registry, $filters);

        if ($this->registry['currentUser']->getPersonalSettings('announcements', 'list_view') == 'simple') {
            $this->template = 'list.html';
        } else {
            foreach ($announcements as $key => $announcement) {
                if (strip_tags($announcement->get('content')) != $announcement->get('content')) {
                    $announcements[$key]->set('switch_html', 'html', true);
                }
            }
        }

        $filtered_categories = array();
        if (!empty($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('#a\.category\s*=\s*\'?(-?\d+|unread)\'?#', $filter)) {
                    $filtered_categories[] = preg_replace('#a\.category\s*=\s*\'?(-?\d+|unread)\'?.*#', '$1', $filter);
                } elseif (preg_match('#a\.search_archive\s*=\s*\'?archive\'?#', $filter)) {
                    $search_archive = 'archive';
                } elseif (preg_match('#a\.search_archive\s*=\s*\'?all\'?#', $filter)) {
                    $search_archive = 'all';
                }
            }
        }
        $this->data['filtered_categories'] = $filtered_categories;

        //prepare announcements categories
        require_once($this->modelsDir . 'announcements.categories.factory.php');
        $filters = array('sanitize' => true);
        $filters['count_assigned'] = 1;
        $filters['search_archive'] = isset($search_archive) ? $search_archive : false;
        $this->data['categories'] = Announcements_Categories::search($this->registry, $filters);

        $this->data['announcements'] = $announcements;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('announcements') . ' &raquo; ' . $this->i18n('archivelist');
        $this->data['title'] = $title;
    }
}

?>
