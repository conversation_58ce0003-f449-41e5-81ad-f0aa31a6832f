<?php

class Announcements_Dashlet_Viewer extends Viewer {
    public $template = 'dashlet.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'announcements.factory.php';

        $dashlets_filters = array('where' => array('d.active = 1',
                                                   'd.id = ' . $this->registry['request']->get('dashlet')),
                                  'sanitize' => true);
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $dashlet = Dashlets::searchOne($this->registry, $dashlets_filters);
        $all_columns = Dashlets::getModuleFields($this->registry, array('module' => 'announcements', 'controller' => 'announcements'));

        $filters = $dashlet->get('filters');
        $settings = $dashlet->get('settings');
        if (!$settings) {
            $settings = array();
        }
        unset($dashlet->properties['filters']);
        unset($dashlet->properties['settings']);
        $filters['display'] = ($dashlet->get('records_per_page') ? $dashlet->get('records_per_page') : PH_DASHLETS_MAX_ROWS);

        $session_param = 'dashlets_' . $dashlet->get('id') . '_announcement';
        $this->registry['session']->set($session_param, $filters, '', true);

        $filters = Announcements::saveSearchParams($this->registry, array(), 'dashlets_' . $dashlet->get('id') . '_');
        $filters['sanitize'] = true;
        list($announcements, $pagination) = Announcements::pagedSearch($this->registry, $filters);

        foreach($settings['columns'] as $key => $column) {
            if (!isset($settings['visible'][$key])) {
                unset($settings['columns'][$key]);
            }
        }
        $this->data['dashlet_id'] = $dashlet->get('id');
        $this->data['columns'] = $settings['columns'];
        $this->data['announcements'] = $announcements;
        $this->data['pagination'] = $pagination;
        $this->data['all_columns'] = $all_columns;
        $this->data['session_param'] = $session_param;
        $this->setFrameset('frameset_blank.html');

        //prepare sort array for the listing
        $secondary_controller = '';
        if ($this->registry['module'] != $this->registry['controller']) {
            $secondary_controller = $this->registry['controller'];
        }

        //set sort link
        $sort_base = sprintf("%s?%s=%s%s&amp;%s=%s&amp;%s=%s",
        $_SERVER['PHP_SELF'],
        $this->registry['module_param'], $this->module,
        ($secondary_controller) ?
                                    '&amp;' . $this->registry['controller_param'] . '=' . $secondary_controller : '',
        $this->registry['action_param'], $this->action, $this->action, $dashlet->get('id'));
        $this->prepareAjaxSort($filters, $session_param, 'content_dashlet_' . $dashlet->get('id'), $sort_base);

    }
}

?>
