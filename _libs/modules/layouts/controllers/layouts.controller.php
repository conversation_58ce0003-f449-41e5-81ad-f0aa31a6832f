<?php

class Layouts_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Layout';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Layouts';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit');

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate');

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_change_assignment_types':
            $this->_changeAssignmentTypes();
            break;
        case 'multiassignpermissions':
        case 'ajax_multiassignpermissions':
            $this->_multiAssignPermissions();
            break;
        case 'saveorder':
            $this->_saveOrder();
            break;
        case 'search':
            $this->_search();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
            break;
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $layout = Layouts::buildModel($this->registry);

            if ($layout->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_layouts_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_layouts_add_failed'), '', -1);
            }
        } else {
            if (!$request->get('model')) {
                //show error no such type
                $this->registry['messages']->setError($this->i18n('error_no_model_specified'));
                $this->registry['messages']->insertInSession($this->registry);
                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            } else {
                //create empty user model
                $layout = Layouts::buildModel($this->registry);
                if (!empty($layout)) {
                    $layout->getGroups('view');
                    $layout->getGroups('edit');
                    $layout->getAssignmentsByType('view');
                    $layout->getAssignmentsByType('edit');
                }
            }
        }

        if (!empty($layout)) {
            if ($layout->get('model')) {
                //get model types
                $model_types = $layout->getTypes($layout->get('model'));
            } else {
                $model_types = array();
            }

            $this->registry->set('model_types', $model_types, true);

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('layout', $layout->sanitize());
        }

        return true;
    }

    /**
     * Edits of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $layout = Layouts::buildModel($this->registry);

            if ($layout->save()) {
                //show message 'message_layouts_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_layouts_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_layouts_edit_failed'), '', -1);
                //register the model, with all the posted details
                $this->registry->set('layout', $layout);
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('l.layout_id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $layout = Layouts::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($layout);

            if (!empty($layout)) {
                $layout->getGroups('view');
                $layout->getGroups('edit');
                $layout->getAssignmentsByType('view');
                $layout->getAssignmentsByType('edit');
            }
        }

        if (!empty($layout)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('layout')) {
                $this->registry->set('layout',  $layout->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_layout'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $layout = Layouts::buildModel($this->registry);

            if ($layout->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_layouts_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_layouts_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('l.layout_id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $layout = Layouts::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($layout);

            if (!empty($layout)) {
                $layout->getGroups('view');
                $layout->getGroups('edit');
                $layout->getAssignmentsByType('view');
                $layout->getAssignmentsByType('edit');
            }
        }

        if (!empty($layout)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('layout', $layout->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_layout'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('l.layout_id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $layout = Layouts::searchOne($this->registry, $filters);

        if (!empty($layout)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($layout);

            if (!empty($layout)) {
                $layout->getGroups('view');
                $layout->getGroups('edit');
                $layout->getAssignmentsByType('view');
                $layout->getAssignmentsByType('edit');
            }
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('layout')) {
                $this->registry->set('layout', $layout->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_layout'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Layouts::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete layouts
        $result = Layouts::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Layouts::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Layouts::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Prepares data to reload permissions by assignment types when model type
     * of layout is changed - it can happen only in add mode of layout.
     */
    private function _changeAssignmentTypes() {
        $request = &$this->registry['request'];

        $model_name = $request->get('model');
        $model_type = $request->get('model_type');

        $modes = array('view', 'edit');

        $result = array_combine($modes, array_fill(0, 2, ''));

        if (in_array($model_name, array('Document', 'Contract'))) {
            //get the requested model ID
            $id = $request->get('id');

            // build model from request
            $layout = Layouts::buildModel($this->registry);
            $layout->sanitize();

            // assignment types for installation (hard-coded)
            $available_assignment_types = array('owner', 'responsible', 'observer', 'decision');

            //gets enabled assignment types for current model type or all if no model type
            $assignment_types =
                $model_type ?
                $this->registry['config']->getParamAsArray(strtolower(General::singular2plural($model_name)),
                                                           'assignment_types_' . $model_type) :
                $available_assignment_types;

            if ($model_type) {
                foreach ($modes as $mode) {
                    $selected_assignments = $layout->get('assignments_' . $mode);
                    if (is_array($selected_assignments)) {
                        // clear selected options for assignment types
                        // if they are not enabled for sselected model type
                        foreach ($selected_assignments as $idx => $sa) {
                            if ($sa != 'added' && !in_array($sa, $assignment_types)) {
                                unset($selected_assignments[$idx]);
                            }
                        }
                        $selected_assignments = array_values($selected_assignments);

                        $layout->set('assignments_' . $mode, $selected_assignments, true);
                    }
                }
            }

            foreach ($modes as $mode) {
                // prepare assignment types checkbox group
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset(PH_MODULES_DIR . 'layouts/templates/_assignment_types.html');
                $viewer->data['layout'] = $layout;
                $viewer->data['mode'] = $mode;
                $viewer->data['available_assignment_types'] = $available_assignment_types;
                $viewer->data['assignment_types'] = $assignment_types;

                $result[$mode] = $viewer->fetch();
            }
        }

        echo json_encode($result);
        exit;
    }

    /**
     * Assign group permissions to multiple layouts
     *
     * @return boolean - result of the operation
     */
    private function _multiAssignPermissions() {
        $db = &$this->registry['db'];
        $request = &$this->registry['request'];
        $lang = $this->registry['lang'];

        $permission = $this->checkActionPermissions($this->module, 'edit');


        $after_action = 'list';
        if (!empty($_SERVER['HTTP_REFERER'])) {
            preg_match('/&layouts=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
            if (isset($matches[1])) {
                $after_action = $matches[1];
            }
        }

        if (!$permission) {
            //no permissions, redirect to list
            $this->registry['messages']->setError($this->i18n('error_no_permissions_to_edit'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $after_action);
        }

        //get the previous list filters
        $prev_filters = Layouts::saveSearchParams($this->registry, array(), $after_action . '_');

        // get available filter for the current action or selected items from the request
        if ($request->get('items')) {
            $filters = array(
                'where' => array('l.id IN (' . implode(', ', $request->get('items')) . ')'),
                'sort' => $prev_filters['sort'],
                'sanitize' => false,
            );
        } else {
            //$filters = $prev_filters;
            $this->registry['messages']->setError($this->i18n('no_selected_records'), '');
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $after_action, array(), $this->controller);
        }

        // display lightbox and exit
        if ($this->action == 'ajax_multiassignpermissions') {
            $result = array();
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset(PH_MODULES_DIR . 'layouts/templates/_multiassignpermissions.html');
            //prepare group tree
            require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
            $viewer->data['groups_tree'] = array_values(Groups::getTree($this->registry));
            $this->registry['include_tree'] = true;
            $result['content'] = $viewer->fetch();
            //print json encoded result of operation
            print json_encode($result);
            exit;
        }
        // end of display lightbox

        // get models for the defined filters
        $layouts = Layouts::search($this->registry, $filters);

        $error = false;
        foreach ($layouts as $idx => $layout) {
            $layout->set('groups_view', $this->registry['request']->get('groups_view'), true);
            $layout->set('groups_edit', $this->registry['request']->get('groups_edit'), true);

            if (!$layout->assignGroups()) {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_layouts_edit_failed'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, $after_action, array(), $this->controller);
                break;
            }
        }

        $this->registry['messages']->setMessage($this->i18n('message_multiassignpermissions_success'), '', -1);
        $this->registry['messages']->insertInSession($this->registry);

        // remove selected items from session
        $this->registry['session']->remove($after_action . '_' . strtolower($this->modelName), 'selected_items');

        $this->redirect($this->module, $after_action, array(), $this->controller);
    }

    /**
     * Reorder all layouts for a model and a model type
     */
    private function _saveOrder() {

        $permission = $this->checkActionPermissions($this->module, 'edit');

        if (!$permission) {
            //no permissions to edit
            $this->registry['messages']->setError($this->i18n('error_no_permissions_to_edit'));
        }

        $after_action = '';
        if (!empty($_SERVER['HTTP_REFERER'])) {
            preg_match('/&layouts=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
            if (isset($matches[1])) {
                $after_action = $matches[1];
            }
        }
        // invalid referer or no data
        if ($after_action != 'search' || !$this->registry['request']->get('items')) {
            $this->registry['messages']->setError($this->i18n('error_layouts_saveorder_failed'));
            $after_action = 'search';
        } else {
            //get the previous list filters
            $prev_filters = Layouts::saveSearchParams($this->registry, array(), $after_action . '_');

            // invalid search filters
            if (!(!empty($prev_filters['sort']) && $prev_filters['sort'][0] == 'l.place ASC' &&
                  !empty($prev_filters['where']) && count($prev_filters['where']) == 1 &&
                  preg_match('#^l\.model\d+ = \'([a-zA-Z_]+_([1-9]\d*|[A-Z]+)|(User|Finance_Transfer)_0)\'#', $prev_filters['where'][0]) &&
                  !preg_match('#\'Customer_[1-9]\d*\'#', $prev_filters['where'][0]))) {
                $this->registry['messages']->setError($this->i18n('error_layouts_saveorder_failed'));
            }
        }

        if ($this->registry['messages']->getErrors()) {
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $after_action);
        }

        if (Layouts::saveOrder($this->registry)) {
            $this->registry['messages']->setMessage($this->i18n('message_layouts_saveorder_success'), '', -1);
        } else {
            $this->registry['messages']->setError($this->i18n('error_layouts_edit_failed'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        // remove selected items from session
        $this->registry['session']->remove($after_action . '_' . strtolower($this->modelName), 'selected_items');

        $this->redirect($this->module, $after_action, array(), $this->controller);
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            //prepare options
            $_options_model = array(
                array('label' => $this->i18n('menu_documents'),     'option_value' => 'Document'),
                array('label' => $this->i18n('menu_customers'),     'option_value' => 'Customer'),
                array('label' => $this->i18n('menu_projects'),      'option_value' => 'Project'),
                array('label' => $this->i18n('menu_nomenclatures'), 'option_value' => 'Nomenclature'),
                array('label' => $this->i18n('menu_contracts'),     'option_value' => 'Contract'),
            );
            usort($_options_model, function($a, $b) { return $a['label'] > $b['label'] ? 1 : -1; });

            $add_options = array(
                array (
                    'custom_id'     => 'model___',
                    'name'          => 'model',
                    'type'          => 'dropdown',
                    'required'      => 1,
                    'label'         => $this->i18n('model_name'),
                    'help'          => $this->i18n('model_name'),
                    'options'       => $_options_model
                )
            );

            $actions['add']['options'] = $add_options;
            $actions['add']['ajax_no'] = 1;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Sets custom after actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            //prepare options
            $_options_model = array(
                array('label' => $this->i18n('menu_documents'),     'option_value' => 'Document'),
                array('label' => $this->i18n('menu_customers'),     'option_value' => 'Customer'),
                array('label' => $this->i18n('menu_projects'),      'option_value' => 'Project'),
                array('label' => $this->i18n('menu_nomenclatures'), 'option_value' => 'Nomenclature'),
                array('label' => $this->i18n('menu_contracts'),     'option_value' => 'Contract'),
            );
            usort($_options_model, function($a, $b) { return $a['label'] > $b['label'] ? 1 : -1; });

            $add_options = array(
                array (
                    'custom_id'     => 'model____',
                    'name'          => 'aa1_model',
                    'type'          => 'dropdown',
                    'required'      => 1,
                    'label'         => $this->i18n('model_name'),
                    'help'          => $this->i18n('model_name'),
                    'options'       => $_options_model
                )
            );

            $actions['add']['options'] = $add_options;
            $actions['add']['ajax_no'] = 1;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }
}

?>
