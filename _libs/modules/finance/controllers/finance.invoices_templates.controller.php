<?php
require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';

class Finance_Invoices_Templates_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Finance_Invoices_Template';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Finance_Invoices_Templates';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array('list', 'search', 'export', 'printlist');

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array();

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array();

    /**
     * Action which are at the up right position (without tabs)
     */
    public $actionDefinitionsUpRight = array(
        'printlist'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'insertids':
            $this->_insertIds();
            break;
        case 'issue_invoice':
            $this->_issueInvoice();
            break;
        case 'ajax_manage_config':
            $this->_manageConfig();
            break;
        case 'approve_send':
        case 'approve':
        case 'disapprove':
        case 'issue_send':
        case 'issue':
            $this->_approval($this->action);
            break;
        case 'ajax_change_observer':
            $this->_changeObserver();
            break;
        case 'ajax_preview_invoice':
            $this->_previewGT2Invoice();
            break;
        case 'preview':
            $this->_previewPDFInvoice();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'search':
            $this->_search();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
            break;
        }
    }

    /**
     * Issue an invoice from this template
     *
     * @return void
     */
    private function _issueInvoice() {
        $id = $this->registry['request']->get($this->action);
        $errors = array();
        if (!$id) {
            $this->registry['messages']->setError($this->i18n('error_invoice_issue_failed'));
            $errors[] = $this->i18n('error_invoice_issue_failed');
        } else {
            //approve the template for issuing (but without sending to customer)
            $result = Finance_Invoices_Templates::approval($this->registry, array($id), 'approve');

            //INVALID ISSUE DATE
            if (!empty($result['err_invalid_issue_date'])) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_invalid_issue_date_approve3');
                $this->registry['messages']->setError($text);
                $errors[] = $text;

                $item = reset($result['err_invalid_issue_date']);
                $link_to_last_doc = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=view&amp;view=',
                                            $_SERVER['PHP_SELF'],$this->registry['module_param'],$this->registry['controller_param']);
                $text = sprintf($this->i18n('error_finance_invoices_templates_invalid_issue_date_details2'),
                                $item['name'],
                                General::strftime('%d.%m.%Y', $item['issue_date']),
                                $link_to_last_doc . $item['fin_document_id'],
                                $this->i18n('error_finance_invoices_templates_invalid_issue_date_details_type_' . $item['fin_document_type']),
                                General::strftime('%d.%m.%Y', $item['last_issue_date']));

                $this->registry['messages']->setError($text);
                $errors[] = $text;
            }

            //TEMPLATE IS LOCKED FOR ISSUE
            if (!empty($result['err_changed_already'])) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_already_changed_approve3');
                $this->registry['messages']->setError($text);
                $errors[] = $text;

                $item = reset($result['err_changed_already']);
                $text = sprintf($this->i18n('error_finance_invoices_templates_db_failure_details2'), $item['name'], General::strftime('%d.%m.%Y', $item['issue_date']));
                $this->registry['messages']->setError($text);
                $errors[] = $text;
            }

            //DB FAILURE, MISSING COUNTER
            if (!empty($result['err_updated'])) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_db_failure_approve3');
                $this->registry['messages']->setError($text);
                $errors[] = $text;

                $item = reset($result['err_updated']);
                $text = sprintf($this->i18n('error_finance_invoices_templates_db_failure_details2'), $item['name'], General::strftime('%d.%m.%Y', $item['issue_date']));
                $this->registry['messages']->setError($text);
                $errors[] = $text;
            }

            //current user hasn't rights to set issue date in the past
            if (!empty($result['err_invalid_issue_date_before'])) {
                $text = sprintf($this->i18n('error_finance_invalid_issue_date_before'));
                $this->registry['messages']->setError($text);
                $errors[] = $text;
            }

            //current user hasn't rights to set issue date in the future
            if (!empty($result['err_invalid_issue_date_after'])) {
                $text = sprintf($this->i18n('error_finance_invalid_issue_date_after'));
                $this->registry['messages']->setError($text);
                $errors[] = $text;
            }

            //issue invoice
            if (empty($errors) && $records = Finance_Invoices_Templates::getTemplatesForIssue($this->registry, $id)) {
                $this->registry->set('issue_one_invoice_only', $id, true);
                $this->loadI18NFiles(PH_MODULES_DIR . 'crontab/i18n/' . $this->registry['lang'] . '/crontab.ini');
                $issue_results = Finance_Invoices_Templates::issueInvoices($this->registry, $records);
                // results are returned per template observer (it could be different from current user)
                $current_user_issue_results =
                    isset($issue_results[$this->registry['currentUser']->get('id')]) ?
                    $issue_results[$this->registry['currentUser']->get('id')] :
                    reset($issue_results);
                if (!empty($current_user_issue_results['errors'])) {
                    $errors = reset($current_user_issue_results['errors']);
                    $this->registry['messages']->flush();
                    foreach ($errors as $err) {
                        $this->registry['messages']->setError($err);
                    }
                } else {
                    // notify observer of invoice template about issued invoice
                    Finance_Invoices_Templates::sendNotification($this->registry, $issue_results);
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_invoice_issue_failed'), '', -3);
                $errors[] = $this->i18n('error_invoice_issue_failed');
            }

            if (empty($errors)) {
                $this->registry['messages']->setMessage($this->i18n('message_invoice_issue_successful'));
            }
        }

        if (!empty($_SERVER['HTTP_REFERER']) && !preg_match('#^ajax_#', $this->action)) {
            $this->registry['messages']->insertInSession($this->registry);
            $this->actionCompleted = true;
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
        } else {
            $result = array();
            if (! empty($errors)) {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'] = $errors;
                $financeViewer->data['display'] = 'error';
                $result['errors'] = $financeViewer->fetch();
            } else {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'][] = $this->i18n('message_invoice_issue_successful');
                $financeViewer->data['display'] = 'message';
                $result['messages'] = $financeViewer->fetch();
                if ($this->registry['messages']->getWarnings()) {
                    $financeViewer = new Viewer($this->registry);
                    $financeViewer->setFrameset('message.html');
                    $financeViewer->data['items'] = $this->registry['messages']->getWarnings();
                    $financeViewer->data['display'] = 'warning';
                    $result['warnings'] = $financeViewer->fetch();
                }
            }
            echo json_encode($result);
            exit;
        }
    }

    private function _list() {
        //all the actions are in the viewer
    }

    private function _search() {
        //all the actions are in the viewer
    }

    /**
     * Function to approve/disapprove issuing of invoices from templates or
     * approve and issue in background mode selected invoices from templates
     *
     * @param string $action - 'approve_send', 'approve', 'disapprove', 'issue_send', 'issue'
     * @param array $ids - ids of info records for templates
     * @return bool - result of the operation
     */
    private function _approval($action, $ids = '') {
        //ids of the models to be approved/disapproved

        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }
        if (!is_array($ids)) {
            $ids = array($ids);
        }

        if (empty($ids)) {
            //no templates selected
            $text = $this->i18n('error_finance_invoices_templates_no_items_' . $this->action);
            $this->registry['messages']->setError($text);

            $this->registry['messages']->insertInSession($this->registry);

            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);

            //set action as completed
            $this->actionCompleted = true;
            return false;
        }

        //approve/disapprove/issue
        //we need to be able to approve canceled invoices -> for $check_observer_response param
        $check_observer_response = preg_match('#^issue(_send)?$#', $action) ? 'none' : '';
        $result = Finance_Invoices_Templates::approval($this->registry, $ids, $action/*, $check_observer_response*/);

        //DETAILED MESSAGING
        //max count of invoices in the success and error message
        $max_shown = 15;

        //SUCCESS
        if (!empty($result['msg_updated'])) {
            if (count($result['msg_updated']) <= $max_shown) {
                //show detailed result
                $text = $this->i18n('message_finance_invoices_templates_' . $this->action . '2');
                $this->registry['messages']->setMessage($text);
                $count = 0;
                foreach($result['msg_updated'] as $item) {
                    $text = sprintf($this->i18n('message_finance_invoices_templates_details'),
                                    ++$count, $item['name'], General::strftime('%d.%m.%Y', $item['issue_date']));
                    $this->registry['messages']->setMessage($text);
                }
            } else {
                //too many results to display, just display the count
                $concerning = (count($ids) == count($result['msg_updated'])) ?
                                $this->i18n('finance_invoices_templates_all_of') :
                                $this->i18n('finance_invoices_templates_part_of');
                $text = sprintf($this->i18n('message_finance_invoices_templates_' . $this->action), $concerning, count($result['msg_updated']));
                $this->registry['messages']->setMessage($text);
            }

            //remove the selection of the checkboxes in the session
            //they are already approved
            $selected_items = $this->registry['session']->get('selected_items');
            unset($selected_items['list_finance_invoices_template']);
            $this->registry['session']->set('selected_items', $selected_items, '', true);

            if (preg_match('#^issue(_send)?$#', $action)) {
                // the successfully approved invoices to issue
                $info_ids = array_keys($result['msg_updated']);

                // launch issuing of invoices from crontab with specific parameters (original_user and item_ids)
                $location = sprintf('%s/index.php?%s=%s&%s=%s&%s=%s&%s=%s',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry->get('module_param'), 'crontab',
                                    'crontab', 'issue_invoices',
                                    'original_user', $this->registry['currentUser']->get('id'),
                                    'item_ids', implode(',', $info_ids));

                //create folder for wget logs
                FilesLib::createDir(PH_LOGGER_DIR . 'wget', 0777, false);
                if (preg_match('#WIN#i', PHP_OS)) {
                    // IMPORTANT: In every windows nzoom installation there should be a wget.exe inside _libs/inc/ext/wget/
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = PH_EXT_DIR . 'wget\wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate "' . $location . '"';
                    //silence the output of command line
                    $command .= ' > nul &';
                    pclose(popen($command, 'r'));
                } else {
                    $location = preg_replace('#\&#', '\&', $location);
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = 'wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate ' . $location;
                    //silence the output of command line
                    $command .= ' > /dev/null 2>&1 & ';
                    exec($command, $output, $status);
                }

                /*header('Location: ' . $location);
                die($location);*/
            }
        }

        //INVALID ISSUE DATE
        if (!empty($result['err_invalid_issue_date'])) {
            //this is only for approve and approve_send action
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            if (count($result['err_invalid_issue_date']) <= $max_shown) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_invalid_issue_date_' . $this->action . '2');
                $this->registry['messages']->$message_type($text);
                $count = 0;
                $link_to_last_doc = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=view&amp;view=',
                                            $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param']);
                foreach($result['err_invalid_issue_date'] as $item) {
                    $text = sprintf($this->i18n('error_finance_invoices_templates_invalid_issue_date_details'),
                                    ++$count,
                                    $item['name'],
                                    General::strftime('%d.%m.%Y', $item['issue_date']),
                                    $link_to_last_doc . $item['fin_document_id'],
                                    $this->i18n('error_finance_invoices_templates_invalid_issue_date_details_type_' . $item['fin_document_type']),
                                    General::strftime('%d.%m.%Y', $item['last_issue_date']));
                    $this->registry['messages']->$message_type($text);
                }
            } else {
                //too many results to display, just display the count
                $concerning = (count($ids) == count($result['err_invalid_issue_date'])) ?
                                $this->i18n('finance_invoices_templates_all_of') :
                                $this->i18n('finance_invoices_templates_part_of');
                $text = sprintf($this->i18n('error_finance_invoices_templates_invalid_issue_date_' . $this->action), $concerning, count($result['err_invalid_issue_date']));
                $this->registry['messages']->$message_type($text);
            }
        }

        //TEMPLATES ALREADY APPROVED/CANCELED (by other user or by the current user)
        if (!empty($result['err_changed_already'])) {
            //this is for all the actions
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            if (count($result['err_changed_already']) <= $max_shown) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_already_changed_' . $this->action . '2');
                $this->registry['messages']->$message_type($text);
                $count = 0;
                foreach($result['err_changed_already'] as $item) {
                    $text = sprintf($this->i18n('error_finance_invoices_templates_already_changed_details'),
                                    ++$count, $item['name'], General::strftime('%d.%m.%Y', $item['issue_date']));
                    $this->registry['messages']->$message_type($text);
                }
            } else {
                //too many results to display, just display the count
                $concerning = (count($ids) == count($result['err_invalid_issue_date'])) ?
                                $this->i18n('finance_invoices_templates_all_of') :
                                $this->i18n('finance_invoices_templates_part_of');
                $text = sprintf($this->i18n('error_finance_invoices_templates_already_changed_' . $this->action), $concerning, count($result['err_changed_already']));
                $this->registry['messages']->$message_type($text);
            }
        }

        //DB FAILURE
        if (!empty($result['err_updated'])) {
            //this is for all the actions
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            if (count($result['err_updated']) <= $max_shown) {
                //show detailed result
                $text = $this->i18n('error_finance_invoices_templates_db_failure_' . $this->action . '2');
                $this->registry['messages']->$message_type($text);
                $count = 0;
                foreach($result['err_updated'] as $item) {
                    $text = sprintf($this->i18n('error_finance_invoices_templates_db_failure_details'),
                                    ++$count, $item['name'], General::strftime('%d.%m.%Y', $item['issue_date']));
                    $this->registry['messages']->$message_type($text);
                }
            } else {
                //too many results to display, just display the count
                $concerning = (count($ids) == count($result['err_invalid_issue_date'])) ?
                                $this->i18n('finance_invoices_templates_all_of') :
                                $this->i18n('finance_invoices_templates_part_of');
                $text = sprintf($this->i18n('error_finance_invoices_templates_db_failure_' . $this->action), $concerning, count($result['err_updated']));
                $this->registry['messages']->$message_type($text);
            }
        }

        //current user hasn't rights to set issue date in the past
        if (!empty($result['err_invalid_issue_date_before'])) {
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            $text = sprintf($this->i18n('error_finance_invalid_issue_date_before'));
            $this->registry['messages']->$message_type($text);
        }

        //current user hasn't rights to set issue date in the future
        if (!empty($result['err_invalid_issue_date_after'])) {
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            $text = sprintf($this->i18n('error_finance_invalid_issue_date_after'));
            $this->registry['messages']->$message_type($text);
        }

        //current user hasn't rights to add invoices
        if (!empty($result['err_issue'])) {
            $message_type = (empty($result['msg_updated'])) ? 'setError' : 'setWarning';
            $text = sprintf($this->i18n('error_finance_issue'));
            $this->registry['messages']->$message_type($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        if ($_SERVER['HTTP_REFERER']) {
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
        }

        return true;
    }

    /**
     * Action to store saved configuration
     * for invoices templates
     */
    private function _manageConfig() {

        $config = $this->registry['request']->get('config');
        $is_custom = $this->registry['request']->get('config_isCustom');
        $action = $this->registry['request']->get($this->action);
        $result = Finance_Invoices_Templates::manageConfiguration($this->registry, $action, $config, $is_custom);
        if (!$result) {
            die('0');
        }

        if ($action != 'load') {
            $result = Finance_Invoices_Templates::getConfigurations($this->registry);
        }
        die(json_encode($result));

    }

    /**
     * Changes user assigned as observer of an invoice template of contract.
     */
    public function _changeObserver() {

        $registry = &$this->registry;
        $request = &$registry['request'];

        $model_id = $request->get('model_id');
        $invoice_id = $request->get('invoice_id');
        $observer = $request->get('observer');

        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
        $filters = array('where' => array('fit.id = ' . $invoice_id,
                                          'fit.contract_id = ' . $model_id));
        $invoices_template = Finance_Invoices_Templates::searchOne($registry, $filters);

        $invoices_template->set('observer', $observer, true);

        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        $this->loadI18NFiles(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');
        $filters = array('where' => array('co.id = ' . $model_id),
                         'sanitize' => true);
        $contract = Contracts::searchOne($this->registry, $filters);

        if ($invoices_template->get('auto_send')) {
            $invoices_template->set('email_template', $invoices_template->get('auto_send'), true);
        }
        $invoices_template->save();

        unset($invoices_template);
        unset($contract);

        $msg = $this->registry['messages']->getErrors();
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('message.html');
        $result = array();
        if (!empty($msg)) {
            $viewer->data['display'] = 'error';
            $viewer->data['items'] = $msg;
            $result['errors'] = $viewer->fetch();
        } else {
            $result['success'] = 1;
            $this->registry['messages']->setMessage($this->i18n('message_finance_invoices_templates_observer_edit_success'), '', -1);
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset('message.html');
            $viewer->data['items'] = $this->registry['messages']->getMessages();
            $viewer->data['display'] = 'message';
            $result['messages'] = $viewer->fetch();
            if ($this->registry['messages']->getWarnings()) {
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset('message.html');
                $viewer->data['items'] = $this->registry['messages']->getWarnings();
                $viewer->data['display'] = 'warning';
                $result['warnings'] = $viewer->fetch();
            }
        }
        echo json_encode($result);
        exit;
    }

    /**
     * Preview the GT2 of an invoice by invoice template id
     */
    public function _previewGT2Invoice() {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = &$this->registry['db'];

        $template_id = $request->get('template');

        $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '  ON fit.id = fiti.parent_id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 '  ON co.id = fit.contract_id' . "\n" .
                 'WHERE fiti.invoice_id = 0' . "\n" .
                 '  AND co.subtype="contract" AND co.status="closed"' . "\n" .
                 '  AND fit.deleted_by = 0' . "\n" .
                 '  AND fiti.id=' . $template_id;
        $records = $db->GetAssoc($query);

        if (!empty($records)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
            $results = Finance_Invoices_Templates::issueInvoices($this->registry, $records, true);

            $info = reset($results);

            //check if any errors occurred
            if (!empty($info['errors'])) {
                //oooups some error occurred!!!
                $occurred_errors = array();
                foreach($info['errors'] as $contract => $contract_errors) {
                    $occurred_errors[$contract] = implode("<br />\n", $contract_errors);
                }
                printf('<span class="error">%s</span>', implode("<br />\n", $occurred_errors));
                exit;
            }

            //there should be only one invoice in the results array, but just to be sure loop them all
            foreach($info['invoices'] as $invoice) {
                if (is_object($invoice)) {
                    //this is the preview of GT2 itself
                    $viewer = new Viewer($registry);
                    $viewer->setFrameset('_gt2_view.html');
                    $viewer->data['table'] = $invoice->get('grouping_table_2');
                    $viewer->display();
                    exit;
                }
            }
        }

        //the script should not go further but anyway display an user-friendly and polite error
        printf('<span class="error">%s</span>', $this->registry['translater']->translate('error_finance_invoice_preview'));
        exit;
    }

    /**
     * Preview an invoice by invoice template id in PDF
     */
    public function _previewPDFInvoice() {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = &$this->registry['db'];

        $template_id = $request->get('preview');

        $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '  ON fit.id = fiti.parent_id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 '  ON co.id = fit.contract_id' . "\n" .
                 'WHERE fiti.invoice_id = 0' . "\n" .
                 '  AND co.subtype="contract" AND co.status="closed"' . "\n" .
                 '  AND fit.deleted_by = 0' . "\n" .
                 '  AND fiti.id=' . $template_id;
        $records = $db->GetAssoc($query);

        if (!empty($records)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
            $results = Finance_Invoices_Templates::issueInvoices($this->registry, $records, true);

            //the results are grouped by observers
            $info = reset($results);

            //check if any errors occurred
            if (!empty($info['errors'])) {
                //oooups some error occurred!!!
                $occurred_errors = array();
                foreach($info['errors'] as $contract => $contract_errors) {
                    foreach($contract_errors as $error) {
                        $this->registry['messages']->setError($error);
                    }
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list', array(), $this->controller);
            }

            //there should be only one invoice in the results array, but just to be sure loop them all
            foreach($info['invoices'] as $invoice) {
                if (is_object($invoice)) {
                    $invoice->set('id', -1, true);
                    $invoice->set('translations', array($invoice->get('model_lang')), true);

                    //this is the preview of GT2 in PDF
                    $pattern_id = $request->get('pattern');
                    $filters = array('where' => array('p.id = ' . $pattern_id,
                                                      'p.active = 1'),
                                     'model_lang' => $invoice->get('model_lang'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                    $pattern = Patterns::searchOne($registry, $filters);

                    if ($invoice->isSanitized()) {
                        $invoice->unsanitize();
                    }
                    $patterns_vars = $invoice->getPatternsVars();
                    $invoice->extender = new Extender();
                    $invoice->extender->model_lang = $invoice->get('model_lang');
                    $invoice->extender->module = $invoice->module;
                    foreach($patterns_vars as $key => $value) {
                        $invoice->extender->add($key, $value);
                    }

                    $invoice->generatePDF(true);
                    exit;
                }
            }
        }

        //the script should not go further but anyway display an user-friendly and polite error
        $this->registry['messages']->setError($this->registry['translater']->translate('error_finance_invoice_preview'));
        $this->registry['messages']->insertInSession($this->registry);
        $this->redirect($this->module, 'list', array(), $this->controller);
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {

        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        if ($this->model && $this->model->get('type')) {
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne($this->registry, array(
                                                            'where' => array('fdt.id=' . $this->model->get('type'))));
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'fdt.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_finance_invoices_template')) {
                $custom_filters = $this->registry['session']->get($this->action . '_finance_invoices_template');
            }

            if (!empty($custom_filters)) {
                if (isset($custom_filters['search_fields'])) {
                    foreach($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fit\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'fdt.id="' . $custom_filters['values'][$key] . '"';
                            $found++;
                        }
                    }
                }
            }

            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^fdt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }}

?>
