        {counter start=0 print=false}
          <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <tr>
              <td class="t_caption3 t_border" nowrap="nowrap" width="10"><div class="t_caption3_title">{#num#}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_incomes_reasons'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_incomes_reasons_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_incomes_reasons_paid_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_invoice_paid_amount_from_document'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_invoice_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_incomes_reasons_remaining_amount'}</div></td>
              <td class="t_caption3" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_distribute'}</div></td>
            </tr>
            {capture assign='paid_var'}paid_amount_{$finance_payment->modelName}_{$finance_payment->get('id')}{/capture}
            {foreach name='i' from=$finance_payment->get('incomes_reasons') item='reason'}
            {strip}
            {capture assign='reason_info'}
              {if $reason->get('name')}<strong>{#finance_incomes_reasons_name#|escape}:</strong> {$reason->get('name')|escape}<br />{/if}
              <strong>{#added#|escape}:</strong> {$reason->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$reason->get('added_by_name')|escape}<br />
              <strong>{#modified#|escape}:</strong> {$reason->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$reason->get('modified_by_name')|escape}<br />
            {/capture}
            {/strip}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border hright">{$smarty.foreach.i.iteration} </td>
              <td class="t_border" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$reason->get('id')}"{if @in_array($reason->get('id'),$finance_payment->get('repayment_plan_documents'))} style="color:red"{assign var='show_repayment_plans' value=1}{/if} {help label_content=$reason->get('type_name') text_content=$reason_info popup_only=1}>{$reason->get('num')|escape}</a></td>
              <td class="t_border hright" nowrap="nowrap">{$reason->get('total_with_vat')} {$finance_payment->get('currency')|escape}</td>
              <td class="t_border hright" nowrap="nowrap">{$reason->get('paid_amount')|string_format:"%.2f"} {$finance_payment->get('currency')|escape}</td>
              <td class="t_border hright" nowrap="nowrap">{$reason->get($paid_var)|string_format:"%.2f"} {$finance_payment->get('currency')|escape}
              {if !$finance_payment->get('exported_not_allowed_edit')}
                {if $reason->get($paid_var) gt 0}<img onclick="return confirmAction('delete', function() {ldelim} updateBalance('', 'balance', '{$submitLink}&amp;empty={$reason->get('id')}'); {rdelim}, this, '{#finance_payments_confirm_empty#|escape:'quotes'|escape}');" src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" border="0" alt="{#delete#|escape}" title="{#delete#|escape}" class="pointer" />
                {/if}
              {/if}
              </td>
              <td class="t_border hright" nowrap="nowrap">{$reason->get('invoices_amount')|string_format:$gt2_total_precision} {$finance_payment->get('currency')|escape}
              </td>
              <td class="t_border hright" nowrap="nowrap">{math equation="x - y - z" x=$reason->get('total_with_vat') y=$reason->get('paid_amount') z=$reason->get('invoices_amount') assign='pay_amount' format="%.2F"}{$pay_amount} {$finance_payment->get('currency')|escape}
              </td>
              <td class="hright" nowrap="nowrap">
                {if !$finance_payment->get('exported_not_allowed_edit') && $finance_payment->get('remaining_amount') gt '0' && $pay_amount gt '0'}
                <input type="text" class="pricebox small relatives_payments" name="relatives_payments[{$reason->get('id')}]" id="relatives_payments_{counter}" value="{$reason->get('suggest_amount')|string_format:'%.2f'}" onkeyup="recalculatePaymentAmount()" onkeypress="return changeKey(this, event, insertOnlyFloats);" autocomplete="off" /> {$finance_payment->get('currency')|escape}
                  {assign var=show_submit value=1}
                {else}
                  -
                {/if}
              </td>
            </tr>
            {foreachelse}
            <tr>
              <td colspan="8" class="error">{#finance_payments_no_incomes_reasons_customer#|escape}</td>
            </tr>
            {/foreach}
            <tr>
              <td colspan="8">
              {if $show_submit}
                <button type="button" name="saveButton1" class="button" onclick="updateBalance(this.form, 'balance', '{$submitLink}');">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
              {/if}
              {if $show_repayment_plans}
                <span class="red">*{#help_finance_show_repayment_plans#|escape}</span>
              {/if}
              </td>
            </tr>
          </table>
