{if $available_action.show_form}
  <form method="get" action="{$smarty.server.PHP_SELF}">
    <input type="hidden" name="{$available_action.module_param}" value="{$available_action.module}" />
    <input type="hidden" name="{$available_action.controller_param}" value="{$available_action.controller}" />
    <input type="hidden" name="{$available_action.controller}" value="{$available_action.action}" />
    {if $available_action.model_id}
      <input type="hidden" name="{$available_action.action}" value="{$available_action.model_id}" />
    {/if}
    {if $available_action.model_lang}
      <input type="hidden" name="model_lang" value="{$available_action.model_lang}" />
    {/if}
    {if $available_action.name eq 'search' || $available_action.name eq 'filter'}
      <input type="hidden" name="{$available_action.session_param}" value="1" />
      <input type="hidden" name="{$available_action.name}_module" value="{$available_action.module}" />
      <input type="hidden" name="{$available_action.name}_controller" value="{$available_action.controller}" />
      {if $event}
      <input type="hidden" name="event" value="{$event}" />
      {/if}
      {if $form_name}
      <input type="hidden" name="form_name" value="{$form_name}" />
      {/if}
    {/if}
    {assign var='lb_suffix' value='_'}
{/if}

  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0">
          <tr>
              <td class="labelbox"><a name="error_status"><label for="status"{if $messages->getErrors('status')} class="error"{/if}>{if $available_action.show_form}{#repayment_plans_status#}{else}{help label='status'}{/if}</label></a></td>
              <td>&nbsp;</td>
              <td class="databox" nowrap="nowrap">
                {capture assign='current_status'}{$model->get('status')}{/capture}
                {capture assign='current_substatus'}{$model->get('substatus')}{/capture}
                {assign var='substatuses' value=$model->get('substatuses')}
                <input type="hidden" name="current_status_base" value="{$current_status}" id="current_status_base" class="current_status_base" />
                <input type="hidden" name="current_substatus_base" value="{$current_substatus}" id="current_substatus_base" class="current_substatus_base" />
                <input type="hidden" name="statuses_unlock" value="{$model->checkPermissions('setstatus_unlock')}" id="statuses_unlock" class="statuses_unlock" />
                <input type="hidden" name="current_selected_status" value="{if $current_substatus}substatus_{$current_substatus}{/if}" id="current_selected_status" class="current_selected_status" />
                <input type="radio" name="status" id="status_opened" class="status status_opened" onclick="if (validateStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');" value="opened"{if $model->get('status') eq 'opened' or !$model->get('status')} checked="checked"{/if} />
                <input type="hidden" name="requires_comment_opened" id="requires_comment_opened" class="requires_comment_opened" value="{$model->get('opened_requires_comment')}" />
                <label for="status_opened" class="documents_status opened">{#finance_repayment_plans_status_opened#|escape}</label><br />
                {if $substatuses.opened}
                  <blockquote id="substatus_opened" class="block_quote_status substatus_opened">
                    {foreach from=$substatuses.opened item='substat_properties'}
                      <input type="radio" name="substatus" id="substatus_{$substat_properties.id}" class="substatus_{$substat_properties.id}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $model->get('substatus') eq $substat_properties.id} checked="checked"{/if} /><label for="substatus_{$substat_properties.id}" class="documents_status opened">{$substat_properties.name}</label><br />
                      <input type="hidden" name="requires_comment_opened_{$substat_properties.id}" id="requires_comment_opened_{$substat_properties.id}" class="requires_comment_opened_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                    {/foreach}
                  </blockquote>
                {/if}
                <input type="radio" name="status" id="status_locked" class="status status_locked" onclick="if (validateStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');" value="locked"{if $model->get('status') eq 'locked'} checked="checked"{/if} />
                <input type="hidden" name="requires_comment_locked" id="requires_comment_locked" class="requires_comment_locked" value="{$model->get('locked_requires_comment')}" />
                <label for="status_locked" class="documents_status locked">{#finance_repayment_plans_status_locked#|escape}</label><br />
                {if $substatuses.locked}
                  <blockquote id="substatus_locked" class="block_quote_status substatus_locked">
                    {foreach from=$substatuses.locked item='substat_properties'}
                      <input type="radio" name="substatus" id="substatus_{$substat_properties.id}" class="substatus_{$substat_properties.id}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $model->get('substatus') eq $substat_properties.id} checked="checked"{/if} /><label for="substatus_{$substat_properties.id}" class="documents_status locked">{$substat_properties.name}</label><br />
                      <input type="hidden" name="requires_comment_locked_{$substat_properties.id}" id="requires_comment_locked_{$substat_properties.id}" class="requires_comment_locked_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                    {/foreach}
                  </blockquote>
                {/if}
                <input type="radio" name="status" id="status_finished" class="status status_finished" onclick="if (validateStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');" value="finished"{if $model->get('status') eq 'finished'} checked="checked"{/if} />
                <input type="hidden" name="requires_comment_finished" id="requires_comment_finished" class="requires_comment_finished" value="{$model->get('finished_requires_comment')}" />
                <label for="status_finished" class="documents_status closed">{#finance_repayment_plans_status_finished#|escape}</label><br />
                {if $substatuses.finished}
                  <blockquote id="substatus_finished" class="block_quote_status substatus_finished">
                    {foreach from=$substatuses.finished item='substat_properties'}
                      <input type="radio" name="substatus" id="substatus_{$substat_properties.id}" class="substatus_{$substat_properties.id}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $model->get('substatus') eq $substat_properties.id} checked="checked"{/if} /><label for="substatus_{$substat_properties.id}" class="documents_status finished">{$substat_properties.name}</label><br />
                      <input type="hidden" name="requires_comment_finished_{$substat_properties.id}" id="requires_comment_finished_{$substat_properties.id}" class="requires_comment_finished_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                    {/foreach}
                  </blockquote>
                {/if}
              </td>
          </tr>
        </table>
      </td>
      <td align="right" style="vertical-align: top;">
        {capture assign='current_status_requires_comment_name'}{$model->get('status')}_requires_comment{/capture}
        {capture assign='current_status_requires_comment'}{$model->get($current_status_requires_comment_name)}{/capture}
        {capture assign='current_substatus_requires_comment'}{if $model->get('substatus')}{foreach from=$substatuses.$current_status item="substatus"}{if $substatus.id == $model->get('substatus')}{$substatus.requires_comment}{/if}{/foreach}{else}0{/if}{/capture}
        <table id="available_comment_table" class="available_comment_table" style="visibility: {if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment' || $current_substatus_requires_comment == 'optional_comment'}visible{else}hidden{/if}{else}{if $current_status_requires_comment == 'requires_comment' || $current_status_requires_comment == 'optional_comment'}visible{else}hidden{/if}{/if};">
          <tr>
            <td class="required required_comment" id="required_comment" rowspan="2" style="visibility: {if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment'}visible{else}hidden{/if}{else}{if $current_status_requires_comment == 'requires_comment'}visible{else}hidden{/if}{/if};">{#required#}<input type="hidden" name="requires_comment" id="requires_comment" class="requires_comment" value="{if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment'}1{else}0{/if}{else}{if $current_status_requires_comment == 'requires_comment'}1{else}0{/if}{/if}" /></td>
            <td class="labelbox"><a name="error_comment"><label for="comment"{if $messages->getErrors('comment')} class="error"{/if}>{#finance_status_change_comment#}</label></a></td>
          </tr>
          <tr>
            <td>
              <textarea class="areabox comment" name="comment" id="comment" style="height: {math equation='(2+x+y+z)*18' x=$substatuses.opened|@count y=$substatuses.locked|@count z=$substatuses.finished|@count}px;"></textarea><br />
              {capture assign="is_portal_suffix"}_{uniqid}{/capture}
              <input type="radio" name="is_portal" id="is_portal1{$is_portal_suffix}" value="1" title="{#is_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" checked="checked" /><label for="is_portal1{$is_portal_suffix}">{#is_portal#|escape}</label>
              <input type="radio" name="is_portal" id="is_portal2{$is_portal_suffix}" value="0" title="{#is_not_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" /><label for="is_portal2{$is_portal_suffix}">{#is_not_portal#|escape}</label>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="2">
        <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.options.label}" onclick="return checkRequiredComment(this)">{$available_action.options.label}</button>
      </td>
    </tr>
  </table>

{if $available_action.show_form}
  </form>
{/if}
