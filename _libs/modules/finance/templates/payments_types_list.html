<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=payments_types&amp;payments_types={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_payments_type" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=payments_types" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#finance_payments_types_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.name_plural.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name_plural.link}">{#finance_payments_types_name_plural#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$finance_payments_types item='finance_payments_type'}
      {strip}
      {capture assign='info'}
        <strong>{#finance_payments_type#|escape}:</strong> {$finance_payments_type->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$finance_payments_type->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$finance_payments_type->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$finance_payments_type->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$finance_payments_type->get('modified_by_name')|escape}<br />
        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$finance_payments_type->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $finance_payments_type->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$finance_payments_type->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$finance_payments_type->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="3" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
        </tr>
      {else}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$finance_payments_type->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($finance_payments_type->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($finance_payments_type->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=payments_types&amp;{$action_param}=edit&amp;edit={$finance_payments_type->get('id')}">{$finance_payments_type->get('name')|escape}</a>
          </td>
          <td class="t_border {$sort.name_plural.isSorted}">{$finance_payments_type->get('name_plural')|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$finance_payments_type}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="5">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit,activate,deactivate,delete,restore' session_param=$session_param|default:$pagination.session_param}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
