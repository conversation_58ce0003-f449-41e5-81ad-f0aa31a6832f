<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" id="finance_form" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$company->get('model_lang')|default:$lang}" />
<input type="hidden" name="id" id="id" value="{$company->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='companies_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$company->get('name')|escape}" title="{#finance_companies_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_mol"><label for="mol"{if $messages->getErrors('mol')} class="error"{/if}>{help label='companies_mol'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="mol" id="mol" value="{$company->get('mol')|escape}" title="{#finance_companies_mol#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_code"><label for="code"{if $messages->getErrors('code')} class="error"{/if}>{help label='companies_code'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="code" id="code" value="{$company->get('code')|escape}" title="{#finance_companies_code#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_phone"><label for="phone"{if $messages->getErrors('phone')} class="error"{/if}>{help label='companies_phone'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="phone" id="phone" value="{$company->get('phone')|escape}" title="{#finance_companies_phone#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_email"><label for="email"{if $messages->getErrors('email')} class="error"{/if}>{help label='companies_email'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="email" id="email" value="{$company->get('email')|escape}" title="{#finance_companies_email#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_position"><label for="position"{if $messages->getErrors('position')} class="error"{/if}>{help label='companies_position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox small hright" name="position" id="position" value="{$company->get('position')|escape}" title="{#finance_companies_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_fiscal_event_date_indent"><label for="fiscal_event_date_indent"{if $messages->getErrors('fiscal_event_date_indent')} class="error"{/if}>{help label='companies_fiscal_event_date_indent'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox small hright" name="fiscal_event_date_indent" id="fiscal_event_date_indent" value="{$company->get('fiscal_event_date_indent')|default:0}" title="{#finance_companies_fiscal_event_date_indent#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
            <select class="selbox small" name="fiscal_event_date_type" id="fiscal_event_date_type" title="{#finance_companies_fiscal_event_date_indent#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              <option value="working"{if $company->get('fiscal_event_date_type') eq 'working'} selected="selected"{/if}>{#working_days#}</option>
              <option value="calendar"{if $company->get('fiscal_event_date_type') eq 'calendar'} selected="selected"{/if}>{#calendar_days#}</option>
            </select>
            {#days#}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_VAT_registered"><label for="VAT_registered"{if $messages->getErrors('VAT_registered')} class="error"{/if}>{help label='companies_VAT_registered'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="checkbox" name="VAT_registered" id="VAT_registered" value="1"{if $company->get('VAT_registered')} checked="checked"{/if} title="{#finance_companies_VAT_registered#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onclick="if (this.checked) {ldelim} $('VAT_number_required').innerHTML='*'; $('vat_row').style.display=''; {rdelim} else {ldelim} $('VAT_number_required').innerHTML='&nbsp;'; $('vat_row').style.display='none'; {rdelim}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_VAT_number"><label for="VAT_number"{if $messages->getErrors('VAT_number')} class="error"{/if}>{help label='companies_VAT_number'}</label></a></td>
          <td class="required" id="VAT_number_required">{if $company->get('VAT_registered')}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="VAT_number" id="VAT_number" value="{$company->get('VAT_number')|escape}" title="{#finance_companies_VAT_number#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <img src="{$theme->imagesUrl}small/refresh.png" class="icon_button pointer" width="14" height="14" alt="{#finance_companies_refresh_company_info#}" title="{#finance_companies_refresh_company_info#}" border="0" style="vertical-align: middle;" onclick="return confirmAction('refresh_company_info', function() {ldelim} getCompanyInfoByVat(); {rdelim}, this);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_eik"><label for="eik"{if $messages->getErrors('eik')} class="error"{/if}>{help label='companies_eik'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="eik" id="eik" value="{$company->get('eik')|escape}" title="{#finance_companies_eik#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        {include file=input_dropdown.html
                 options=$currencies
                 name=default_currency
                 label=#finance_companies_default_currency#
                 value=$company->get('default_currency')
                 required=1
                 custom_class='short'
        }
        <tr id="vat_row"{if !$company->get('VAT_registered')} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_vats"><label for="vats"{if $messages->getErrors('vats')} class="error"{/if}>{help label='companies_VATs'}</label></a></td>
          <td class="required">&nbsp;</td>
          <td>
            <table id="vats_container" cellspacing="0" cellpadding="2" border="0" class="t_table">
              <tr>
                <td nowrap="nowrap" class="t_caption t_border t_border_left" style="width: 50px;">
                  <div class="t_caption_title">{#num#}</div>
                </td>
                <td nowrap="nowrap" class="t_caption t_border">
                  <div class="t_caption_title">{#activated#}</div>
                </td>
                <td nowrap="nowrap" class="t_caption t_border">
                  <div class="t_caption_title">{#finance_companies_VAT_rate#}</div>
                </td>
                <td nowrap="nowrap" class="t_caption t_border">
                  <div class="t_caption_title floatl">{#finance_companies_default_VAT#}</div>
                  <div class="t_buttons">
                    <div id="vats_container_plusButton" onclick="var ov = getRadioValue($('finance_form').elements['default_vat']); addField('vats_container', false, false, true); var ind = $('vats_container'); ind = ind.rows[ind.rows.length-1].id.replace(/^.*_(\d+)$/, '$1'); $('default_vat_' + ind).value = ind-1; setRadioValue($('finance_form').elements['default_vat'], ov); var val = $('vats_value_' + ind); removeClass(val, 'readonly'); val.readOnly = false; $('vats_active_' + ind).checked = true;" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                    {*<div id="vats_container_minusButton"{if empty($vats) || count($vats) le 1} class="disabled"{/if} onclick="removeField('vats_container')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>*}
                  </div>
                </td>
              </tr>
              {assign var=vats value=$company->get('vats')}
              {foreach from=$vats key='j' item='vat' name='i'} 
                  <tr id="vats_container_{$smarty.foreach.i.iteration}">
                  {if !$vat.used}
                    <td class="t_border t_v_border t_border_left hright">
                      <div>
                        <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($vats) || count($vats) le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('vats_container','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />
                        <a href="javascript: disableField('vats_container','{$smarty.foreach.i.iteration}');">{$smarty.foreach.i.iteration}</a>
                      </div>
                    </td>
                    <td nowrap="nowrap" class="t_border t_v_border hcenter">
                      {include file="input_checkbox.html"
                          name=vats_active
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.active
                          option_value=1
                      }
                      {include file="input_hidden.html"
                          name=vats_used
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.used
                      }
                    </td>
                    <td nowrap="nowrap" class="t_border t_v_border">
                      {include file="input_text.html"
                          name=vats_value
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.value
                          restrict=insertOnlyFloats
                          custom_class='hright'
                          label=#finance_companies_VAT_rate#
                      }
                    </td>
                  {else}
                    <td class="t_border t_v_border t_border_left hright">
                      <div>
                        <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function() {ldelim} hideField('vats_container','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />
                        <a href="#" onclick="return false;">{$smarty.foreach.i.iteration}</a>
                      </div>
                    </td>
                    <td nowrap="nowrap" class="t_border t_v_border hcenter">
                      {include file="input_checkbox.html"
                          name=vats_active
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.active
                          option_value=1
                      }
                      {include file="input_hidden.html"
                          name=vats_used
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.used
                      }
                    </td>
                    <td nowrap="nowrap" class="t_border t_v_border">
                      {include file="input_text.html"
                          name=vats_value
                          standalone=true
                          index=$smarty.foreach.i.iteration
                          value=$vat.value
                          restrict=insertOnlyFloats
                          custom_class='hright'
                          readonly=true
                          label=#finance_companies_VAT_rate#
                      }
                    </td>
                  {/if}
                    <td nowrap="nowrap" class="t_border t_v_border hcenter">
                      <input type="radio" name="default_vat" value="{$smarty.foreach.i.iteration-1}"{if $company->get('default_vat') eq $smarty.foreach.i.iteration-1} checked="checked"{/if} id="default_vat_{$smarty.foreach.i.iteration}" title="{#finance_companies_default_VAT#}" />
                    </td>
                  </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_registration_file"><label for="registration_file"{if $messages->getErrors('registration_file')} class="error"{/if}>{help label='companies_registration_file'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="registration_file" id="registration_file" value="{$company->get('registration_file')|escape}" title="{#finance_companies_registration_file#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_registration_volume"><label for="registration_volume"{if $messages->getErrors('registration_volume')} class="error"{/if}>{help label='companies_registration_volume'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="registration_volume" id="registration_volume" value="{$company->get('registration_volume')|escape}" title="{#finance_companies_registration_volume#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_registration_number"><label for="registration_number"{if $messages->getErrors('registration_number')} class="error"{/if}>{help label='companies_registration_number'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="registration_number" id="registration_number" value="{$company->get('registration_number')|escape}" title="{#finance_companies_registration_number#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_registration_address"><label for="registration_address"{if $messages->getErrors('registration_address')} class="error"{/if}>{help label='companies_registration_address'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="registration_address" id="registration_address" title="{#finance_companies_registration_address#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$company->get('registration_address')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_post_code"><label for="post_code"{if $messages->getErrors('post_code')} class="error"{/if}>{help label='companies_post_code'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="post_code" id="post_code" value="{$company->get('post_code')|escape}" title="{#finance_companies_post_code#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_country"><label for="country"{if $messages->getErrors('country')} class="error"{/if}>{help label='companies_country'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox{if !$company->get('country')} undefined{/if}" name="country" id="country" title="{#finance_companies_country#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
              <option value="" class="undefined"{if !$company->get('country')} selected="selected"{/if}>[{#please_select#|escape}]</option>
              {foreach from=$countries item='country'}
                <option value="{$country.option_value}"{if $company->get('country') eq $country.option_value} selected="selected"{/if}>{$country.label}</option>
              {/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_city"><label for="city"{if $messages->getErrors('city')} class="error"{/if}>{help label='companies_city'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="city" id="city" value="{$company->get('city')|escape}" title="{#finance_companies_city#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_correspondation_address"><label for="correspondation_address"{if $messages->getErrors('correspondation_address')} class="error"{/if}>{help label='companies_correspondation_address'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="correspondation_address" id="correspondation_address" title="{#finance_companies_correspondation_address#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$company->get('correspondation_address')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label='companies_notes'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="notes" id="notes" title="{#finance_companies_notes#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$company->get('notes')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#edit#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$company exclude='is_portal'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
