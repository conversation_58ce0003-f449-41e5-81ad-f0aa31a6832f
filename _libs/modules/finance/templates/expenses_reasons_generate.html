<h1>{$title}</h1>

<div id="form_container">
{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$finance_expenses_reason->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_expenses_reason->get('model_lang')|default:$lang}" />
<input type="hidden" name="pattern" id="pattern" value="{$pattern->get('id')}" />
<input type="hidden" name="format" id="format" value="{$pattern->get('format')|escape}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_expenses_reasons_info_header.html}
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="filename"{if isset($error.filename)} class="error"{/if}>{#finance_expenses_reasons_generated_get_revision#}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox" name="from_file" id="from_file" title="{#finance_expenses_reasons_generated_filename#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="reloadPage(this, '{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}={$action}&amp;{$action}={$finance_expenses_reason->get('id')}&amp;pattern={$pattern->get('id')}&amp;revision=')">
              <option value="" class="undefined">[{#finance_expenses_reasons_generated_add_new#|escape}]</option>
{if $finance_expenses_reason->get('revisions')}
{foreach from=$finance_expenses_reason->get('revisions') item='revision'}
              <option value="{$revision->get('id')}"{if $current_revision->get('id') eq $revision->get('id')} selected="selected"{/if}>{$revision->get('revision')|string_format:'%02d'}. {$revision->get('name')|escape}</option>
{/foreach}
{/if}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="revision">{#finance_expenses_reasons_generated_save_revision#}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox" name="revision" id="revision" title="{#finance_expenses_reasons_generated_revision#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="">
              <option value="" class="undefined">[{#finance_expenses_reasons_generated_add_new#|escape}]</option>
{if $finance_expenses_reason->get('revisions')}
{foreach from=$finance_expenses_reason->get('revisions') item='revision'}
              <option value="{$revision->get('revision')}"{if $current_revision->get('id') eq $revision->get('id')} selected="selected"{/if}>{$revision->get('revision')|string_format:'%02d'}. {$revision->get('name')|escape}</option>
{/foreach}
{/if}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label>{#finance_expenses_reasons_generate_revision_title#}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" name="name" id="name" value="{$current_revision->get('name')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#finance_expenses_reasons_generate_revision_title#|escape}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label>{#finance_expenses_reasons_generate_revision_description#}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox doubled" name="description" id="description" title="{#finance_expenses_reasons_generate_revision_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$current_revision->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            {strip}
            <input type="hidden" name="submit_button" id="submit_button" value="" />
            <button type="submit" name="saveButton1" class="button" onclick="setHiddenInput('submit_button','generate');this.form.target='_self';">{#generate#|escape}</button>
            <button type="submit" name="saveButton2" class="button" onclick="setHiddenInput('submit_button','preview');this.form.target='_blank';">{#preview#|escape}</button>
            {include file=`$theme->templatesDir`cancel_button.html}
            {/strip}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
