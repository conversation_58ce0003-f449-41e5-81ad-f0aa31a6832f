{assign var='bank_info' value=$finance_currency->get('bank_info')}
<h1>{$title}</h1>
<h2>{#finance_currencies_edit_legend#|escape} {$finance_currency->get('name')|escape} ({$finance_currency->get('code')|escape}) {#for#} {$finance_currency->get('date')|date_format:#date_short#}</h2>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html show_divider=true}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$finance_currency->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='currencies_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {include file=`$templatesDir`_currencies_info.html currency=$finance_currency image=1}
            ({$finance_currency->get('code')|escape}) {$finance_currency->get('name')|escape}
            <input type="hidden" name="name" id="name" value="{$finance_currency->get('name')|escape}" />
            <input type="hidden" name="code" id="code" value="{$finance_currency->get('code')|escape}" />
          </td>
          <td class="vtop t_border divider_cell" rowspan="12">&nbsp;</td>
          <td rowspan="12" style="width: 15px;">&nbsp;</td>
          <td colspan="5" rowspan="3">
            <em>{#finance_currencies_copy_bank_rates1#|escape}</em>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_date"><label for="date"{if $messages->getErrors('date')} class="error"{/if}>{help label='currencies_date'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$finance_currency->get('date')|date_format:#date_short#}
            <input type="hidden" name="date" id="date" value="{$finance_currency->get('date')|escape}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_units"><label for="units"{if $messages->getErrors('date')} class="error"{/if}>{help label='currencies_units'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$finance_currency->get('units')}
            <input type="hidden" name="units" id="units" value="{$finance_currency->get('units')|escape}" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="6">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
          <td style="text-align: center">
            <img src="{$theme->imagesUrl}small/refresh.png" class="icon_button pointer" width="14" height="14" alt="{#finance_currencies_refresh_bank_rates#|escape}" title="{#finance_currencies_refresh_bank_rates#|escape}" border="0" style="vertical-align: middle;" onclick="return confirmAction('finance_update_currency_rate', function(el) {ldelim} getLatestCurrencyRates('{$finance_currency->get('code')}', '{$finance_currency->get('bank')}'); {rdelim}, this);" />
          </td>
          <td colspan="3">
            {#finance_currencies_copy_bank_rates2#|escape} <a href="{$bank_info.url}" target="_blank"><strong>{$bank_info.name}</strong></a> {#for#} <strong><span id="currency_date">{$finance_currency->get('date')|date_format:#date_short#}</span></strong>
          </td>
        </tr>
        <tr>
          <td class="labelbox" rowspan="5"><a name="error_rate"><label for="rate"{if $messages->getErrors('rate')} class="error"{/if}>{help label='currencies_rate'}</label></a></td>
          <td class="required" rowspan="5">{#required#}</td>
          <td nowrap="nowrap" rowspan="5" class="vtop">
            <input type="text" class="pricebox" name="rate" id="rate" value="{$finance_currency->get('rate')|string_format:'%.5F'}" title="{#finance_currencies_rate#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
            {if $currentUser->checkRights('finance_currencies', 'settings')}
              <br />
              <input type="checkbox" name="set_as_fixed" id="set_as_fixed" value="1"{if $finance_currency->get('update_method') eq 'fixed'} checked="checked"{/if} /><label for="set_as_fixed">{#finance_currencies_update_methods_fixed#|escape}</label>
              <input type="hidden" name="set_as_fixed_prev" value="{if $finance_currency->get('update_method') eq 'fixed'}1{else}0{/if}" />
            {/if}
          </td>

          <td style="width: 25px;">
            <button type="button" name="copy1_rate" id="copy1_rate" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td style="width: 25px;">
            <img src="{$theme->imagesUrl}finance_bank_accounts.png" class="icon_button" width="14" height="14" alt="finance_currencies_bank_rates" title="{#finance_currencies_buys#|escape} {#finance_currencies_bank_rates#|escape}" border="0" style="vertical-align: middle;" />
          </td>
          <td style="width: 25px;">
            <input type="text" class="pricebox distinctive" name="bm1_rate" id="bm1_rate" value="{$finance_currency->get('buys_bank')|string_format:'%.5F'}" title="{#finance_currencies_bank_rates#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
          <td nowrap="nowrap"{if $finance_currency->get('update_method') eq 'buys_bank'} class="strong"{/if}>
            {#finance_currencies_update_methods_buys_bank#|escape}
          </td>
        </tr>

        <tr>
          <td>
            <button type="button" name="copy2_rate" id="copy2_rate" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <img src="{$theme->imagesUrl}finance_bank_accounts.png" class="icon_button" width="14" height="14" alt="finance_currencies_bank_rates" title="{#finance_currencies_sells#|escape} {#finance_currencies_bank_rates#|escape}" border="0" style="vertical-align: middle;" />
          </td>
          <td>
            <input type="text" class="pricebox distinctive" name="bm2_rate" id="bm2_rate" value="{$finance_currency->get('sells_bank')|string_format:'%.5F'}" title="{#finance_currencies_bank_rates#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
          <td{if $finance_currency->get('update_method') eq 'sells_bank'} class="strong"{/if}>
            {#finance_currencies_update_methods_sells_bank#|escape}
          </td>
        </tr>

        <tr>
          <td>
            <button type="button" name="copy3_rate" id="copy3_rate" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <img src="{$theme->imagesUrl}finance_currencies.png" class="icon_button" width="14" height="14" alt="finance_currencies_cash_rates" title="{#finance_currencies_sells#|escape} {#finance_currencies_cash_rates#|escape}" border="0" style="vertical-align: middle;" />
          </td>
          <td>
            <input type="text" class="pricebox distinctive" name="bm3_rate" id="bm3_rate" value="{$finance_currency->get('buys_cash')|string_format:'%.5F'}" title="{#finance_currencies_cash_rates#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
          <td{if $finance_currency->get('update_method') eq 'buys_cash'} class="strong"{/if}>
            {#finance_currencies_update_methods_buys_cash#|escape}
          </td>
        </tr>

        <tr>
          <td>
            <button type="button" name="copy4_rate" id="copy4_rate" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <img src="{$theme->imagesUrl}finance_currencies.png" class="icon_button" width="14" height="14" alt="finance_currencies_cash_rates" title="{#finance_currencies_buys#|escape} {#finance_currencies_cash_rates#|escape}" border="0" style="vertical-align: middle;" />
          </td>
          <td>
            <input type="text" class="pricebox distinctive" name="bm4_rate" id="bm4_rate" value="{$finance_currency->get('sells_cash')|string_format:'%.5F'}" title="{#finance_currencies_cash_rates#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
          <td{if $finance_currency->get('update_method') eq 'sells_cash'} class="strong"{/if}>
            {#finance_currencies_update_methods_sells_cash#|escape}
          </td>
        </tr>

        <tr>
          <td>
            <button type="button" name="copy5_rate" id="copy5_rate" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <img src="{$theme->imagesUrl}finance_fixing.png" class="icon_button" width="14" height="14" alt="finance_currencies_fixing_rates" title="{#finance_currencies_buys#|escape} {#finance_currencies_fixing_rates#|escape}" border="0" style="vertical-align: middle;" />
          </td>
          <td>
            <input type="text" class="pricebox distinctive" name="bm5_rate" id="bm5_rate" value="{$finance_currency->get('fixing')|string_format:'%.5F'}" title="{#finance_currencies_fixing_rates#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
          <td{if $finance_currency->get('update_method') eq 'fixing'} class="strong"{/if}>
            {#finance_currencies_update_methods_fixing#|escape}
          </td>
        </tr>

        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="4">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#edit#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
          <td colspan="4">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
