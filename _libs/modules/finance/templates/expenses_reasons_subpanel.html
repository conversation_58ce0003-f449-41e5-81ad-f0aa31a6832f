<form name="finance_expenses_reasons" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=expenses_reasons" method="post" enctype="multipart/form-data">
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
    <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#finance_expenses_reasons_name#|escape}</div></td>
    <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{#finance_expenses_reasons_customer#|escape}</div></td>
    <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#finance_type#|escape}</div></td>
    <td class="t_caption t_border {$sort.total.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.total.link}">{$basic_vars_labels.total|default:#gt2_total#|escape}</div></td>
    <td class="t_caption t_border {$sort.total_with_vat.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.total_with_vat.link}">{$basic_vars_labels.total_with_vat|default:#gt2_total_with_vat#|escape}</div></td>
    <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#finance_incomes_reasons_status#|escape}</div></td>
    <td class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.tags.link}">{#finance_expenses_reasons_tags#|escape}</div></td>
    <td class="t_caption" width="80">&nbsp;</td>
  </tr>
{counter start=$pagination.start name='item_counter' print=false}
{foreach name='i' from=$finance_expenses_reasons item='expenses_reason'}
{strip}
{capture assign='info'}
  <strong>{#finance_expenses_reasons_name#|escape}:</strong> {$expenses_reason->get('name')|escape}<br />
  <strong>{#added#|escape}:</strong> {$expenses_reason->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$expenses_reason->get('added_by_name')|escape}<br />
  <strong>{#modified#|escape}:</strong> {$expenses_reason->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$expenses_reason->get('modified_by_name')|escape}<br />

  <strong>{#translations#|escape}:</strong>
    <span class="translations">
    {foreach from=$expenses_reason->get('translations') item='trans'}
      <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $expenses_reason->get('model_lang')} class="selected"{/if} />
    {/foreach}
    </span><br />
{/capture}
{capture assign='expenses_reason_status'}
  {if $expenses_reason->get('status') eq 'opened'}
    {#help_finance_documents_status_opened#}
  {elseif $expenses_reason->get('status') eq 'locked'}
    {#help_finance_documents_status_locked#}
  {elseif $expenses_reason->get('status') eq 'finished'}
    {#help_finance_documents_status_finished#}
  {/if}
  {if $expenses_reason->get('substatus_name')}
    <br />
    {#help_finance_documents_substatus#}{$expenses_reason->get('substatus_name')}
  {/if}
{/capture}
{/strip}
  {include file="`$theme->templatesDir`row_link_action.html" object=$expenses_reason assign='row_link'}
  {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
  {assign var='background_style' value=''}
  {assign var='background_properties' value=$expenses_reason->getBackgroundColor()}
  {if !empty($background_properties)}
    {array_push array='background_colors' new_item=$background_properties.background_color key=$background_properties.definition}
    {capture assign='background_style'} style="background-color: #{$background_properties.background_color}; color: #{$background_properties.text_color};"{/capture}
  {/if}
  <tr class="{if $background_style}t_row{else}{cycle values='t_odd,t_even'}{/if}{if $expenses_reason->get('annulled_by')} t_strike{/if}{if !$expenses_reason->get('active')} t_inactive{/if}"{$background_style}>
    <td class="t_border hright" nowrap="nowrap">
    {if $expenses_reason->get('files_count')}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=expenses_reasons&amp;expenses_reasons=attachments&amp;attachments={$expenses_reason->get('id')}">
        <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
             onmouseover="showFiles(this, '{$module}', '{$controller}', {$expenses_reason->get('id')})"
             onmouseout="mclosetime()" />
      </a>
    {/if}
    {counter name='item_counter' print=true}
    </td>
    <td class="t_border {$sort.name.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=expenses_reasons&amp;{$action_param}=view&amp;view={$expenses_reason->get('id')}">{$expenses_reason->get('name')|escape}</a></td>
    <td class="t_border {$sort.customer.isSorted}" nowrap="nowrap">
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$expenses_reason->get('customer')}" title="{#view#|escape}: {$expenses_reason->get('customer_name')|escape}">{$expenses_reason->get('customer_name')|escape|default:"&nbsp;"}</a>
    </td>
    <td class="t_border {$sort.type.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>
      {$expenses_reason->get('type_name')|escape}
    </td>
    <td class="t_border hright {$sort.total.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>
      {$expenses_reason->get('total')|escape} {$expenses_reason->get('currency')|escape}
    </td>
    <td class="t_border hright {$sort.total_with_vat.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>
      {$expenses_reason->get('total_with_vat')|escape} {$expenses_reason->get('currency')|escape}
    </td>
    <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
    {capture assign='popup_and_onclick'}
      {popup text=$expenses_reason_status|escape caption=#help_finance_documents_status#|escape width=250}{if !$expenses_reason->get('annulled_by') && $expenses_reason->checkPermissions('setstatus')} onclick="changeStatus({$expenses_reason->get('id')}, 'finance', 'expenses_reasons')" style="cursor:pointer;"{/if}
    {/capture}
    {if $expenses_reason->get('substatus_name')}
      {if $expenses_reason->get('icon_name')}
        <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_STATUSES_URL}{$expenses_reason->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
      {else}
        {if $expenses_reason->get('status') eq 'opened'}
          <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
        {elseif $expenses_reason->get('status') eq 'locked'}
          <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
        {elseif $expenses_reason->get('status') eq 'finished'}
          <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
        {/if}
      {/if}
      <span {$popup_and_onclick}>{$expenses_reason->get('substatus_name')|escape}</span>
    {else}
      {if $expenses_reason->get('status') eq 'opened'}
        <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
      {elseif $expenses_reason->get('status') eq 'locked'}
        <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
      {elseif $expenses_reason->get('status') eq 'finished'}
        <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
      {/if}
      {capture assign='status_param'}finance_documents_status_{$expenses_reason->get('status')}{/capture}
      <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
    {/if}
    </td>
    <td class="t_border {$sort.tags.isSorted}" {if $expenses_reason->getModelTags() && $expenses_reason->get('available_tags_count') gt 0 && $expenses_reason->checkPermissions('tags_view') && $expenses_reason->checkPermissions('tags_edit')} onclick="changeTags({$expenses_reason->get('id')}, 'finance', 'expenses_reasons')" style="cursor: pointer;" title="{#tags_change#}"{/if}>
      {if $expenses_reason->get('model_tags')|@count gt 0 && $expenses_reason->checkPermissions('tags_view')}
        {foreach from=$expenses_reason->get('model_tags') item='tag' name='ti'}
          <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
        {/foreach}
      {else}
        &nbsp;
      {/if}
    </td>
    <td class="hcenter" nowrap="nowrap">
      {include file=`$theme->templatesDir`single_actions_list.html object=$expenses_reason exclude='delete'}
    </td>
  </tr>
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="10">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
  <tr>
    <td class="t_footer" colspan="10"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
      {capture assign='search_link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;{$controller_param}=expenses_reasons&amp;expenses_reasons=subpanel&amp;page={/capture}
      
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        link=$search_link
        use_ajax=$use_ajax
        hide_selection_stats=true
        session_param=$session_param
      }
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
</form>