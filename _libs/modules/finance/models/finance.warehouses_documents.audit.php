<?php

class Finance_Warehouses_Documents_Audit extends Audit {
    /**
     * The module designates the section in the settings table where the auditable fields are set
     */
    public static $module = 'finance_warehouses_documents';

    /**
     * Replace some variable values with labels
     */
    public static $add_vars = array(
        'type'          => 'type_name',
        'company'       => 'company_name',
        'office'        => 'office_name',
        'warehouse'     => 'warehouse_name',
        'to_company'    => 'to_company_name',
        'to_office'     => 'to_office_name',
        'to_warehouse'  => 'to_warehouse_name',
        'customer'      => 'customer_name',
        'trademark'     => 'trademark_name',
        'project'       => 'project_name',
        'phase'         => 'phase_name',
        'department'    => 'department_name',
        'group'         => 'group_name',
        'substatus'     => 'substatus_name',
  );

    /**
     * Replace some variable values with labels
     */
    public static $translate_values = array('status', 'direction');

    /**
     * get main variables for audit
     */
    public static function getBasicAuditVars(&$registry, $module = 'finance_warehouses_documents') {

        $db = $registry['db'];
        //get table columns
        $cols = $db->MetaColumnNames(DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS, true);
        $cols = array_combine($cols, $cols);
        unset($cols['id']);
        unset($cols['user_permissions']);
        unset($cols['added']);
        unset($cols['added_by']);
        unset($cols['modified']);
        unset($cols['modified_by']);
        unset($cols['status_modified']);
        unset($cols['status_modified_by']);
        unset($cols['annulled']);
        unset($cols['annulled_by']);
        $cols['name'] = 'name';
        $cols['description'] = 'description';
        $cols['to_description'] = 'to_description';
        $cols['total_no_vat_reason_text'] = 'total_no_vat_reason_text';
        $cols['tag'] = 'tag';

        return $cols;
    }

    /**
     * prepare audit data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, &$params) {
        //get the auditable vars
        $basic_vars = self::getBasicAuditVars($registry);

        $audit_vars = array();

        switch ($params['action_type']) {
        case 'add':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($params['new_model']->isDefined($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        if (in_array($var, array('from', 'to', 'to_from', 'to_to'))) {
                            $audit_vars[$i]['field_value'] = ($params['new_model']->isDefined($var . '_name') ?
                                                              $params['new_model']->get($var . '_name') :
                                                              $params['new_model']->get($var));
                        } else {
                            $audit_vars[$i]['field_value'] = $params['new_model']->get($var);
                        }
                        $audit_vars[$i]['old_value'] = '';
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                        if (isset(self::$add_vars[$var])) {
                            $var = self::$add_vars[$var];
                            $audit_vars[$i]['field_name'] = $var;
                            $audit_vars[$i]['field_value'] = $params['new_model']->get($var);
                            $audit_vars[$i]['old_value'] = '';
                            $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                            $i++;
                        }
                    }
                }
            }
            break;
        case 'edit':
        case 'translate':
        case 'status':
        case 'multistatus':
        case 'addcorrect':
        case 'release_reservation':
        case 'finish_reservation':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($params['new_model']->get($var) != $params['old_model']->get($var) ||
                    preg_match('#^(to_)?(from|to)$#', $var) && $params['new_model']->get($var . '_name') != $params['old_model']->get($var . '_name')) {
                        $audit_vars[$i]['field_name'] = $var;
                        if (in_array($var, array('from', 'to', 'to_from', 'to_to'))) {
                            $audit_vars[$i]['field_value'] = ($params['new_model']->isDefined($var . '_name') ?
                                                              $params['new_model']->get($var . '_name') :
                                                              $params['new_model']->get($var));
                            $audit_vars[$i]['old_value'] = ($params['old_model']->isDefined($var . '_name') ?
                                                            $params['old_model']->get($var . '_name') :
                                                            $params['old_model']->get($var));
                        } else {
                            $audit_vars[$i]['field_value'] = $params['new_model']->get($var);
                            $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                        if (isset(self::$add_vars[$var])) {
                            $var = self::$add_vars[$var];
                            $audit_vars[$i]['field_name'] = $var;
                            $audit_vars[$i]['field_value'] = $params['new_model']->get($var);
                            $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                            $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                            $i++;
                        }
                    }
                }
            }
            break;
        case 'assign':
            $i = count($audit_vars);
            $basic_vars = array('owner', 'responsible', 'decision', 'observer');

            if (!empty($params['new_model']) && is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    $var_name_label = 'assign_' . $var;
                    if (!empty($params['new_model']) && $params['new_model']->isDefined('assignments_' . $var)) {
                        $new_var_array = $params['new_model']->get('assignments_' . $var);
                    } else {
                        $new_var_array = array();
                    }
                    if (!empty($params['old_model']) && $params['old_model']->isDefined('assignments_' . $var)) {
                        $old_var_array = $params['old_model']->get('assignments_' . $var);
                    } else {
                        $old_var_array = array();
                    }

                    $diff_old = array_diff_key($old_var_array, $new_var_array);
                    $diff_new = array_diff_key($new_var_array, $old_var_array);

                    if (!empty($diff_old) || !empty($diff_new)) {
                        $new_var_value = '';
                        foreach ($new_var_array as $assignee) {
                            $new_var_value .= $assignee['assigned_to_name'] . "\n";
                        }

                        $old_var_value = '';
                        foreach ($old_var_array as $assignee_old) {
                            $old_var_value .= $assignee_old['assigned_to_name'] . "\n";
                        }

                        $audit_vars[$i]['field_name'] = $var_name_label;
                        $audit_vars[$i]['field_value'] = $new_var_value;
                        $audit_vars[$i]['label'] = $new_var_value;
                        $audit_vars[$i]['old_value'] = $old_var_value;
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }
            break;
        case 'tag':
        case 'multitag':
            if (in_array('tag', $basic_vars)) {
                $diff = array_diff($params['old_model']->get('tags'), $params['new_model']->get('tags'));
                $diff2 = array_diff($params['new_model']->get('tags'), $params['old_model']->get('tags'));
                if (!empty($diff) || !empty($diff2)) {
                    $audit_vars[0]['field_name'] = 'tags';
                    $audit_vars[0]['field_value'] = serialize($params['new_model']->get('tags'));
                    $audit_vars[0]['old_value'] = serialize($params['old_model']->get('tags'));
                    $audit_vars[0]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[0]['is_array'] = 1;
                }
            }
            break;
        case 'receive_email':
        case 'email':
            $basic_vars = array('mail_from', 'mail_code', 'mail_to', 'mail_cc', 'mail_bcc', 'mail_subject', 'mail_content', 'attached_files');
            $basic_vars_array = array('mail_to', 'mail_cc', 'mail_bcc', 'attached_files');
            $i = count($audit_vars);

            foreach ($basic_vars as $var) {
                $field_value = '';
                if (in_array($var, $basic_vars_array)) {
                    $mails = $params['new_model']->get($var) ?: array();
                    $mail_names = $params['new_model']->get("{$var}_name") ?: array();
                    $field_value = array();
                    foreach ($mails as $key => $value) {
                        $field_value[] = !empty($mail_names[$key]) ? $mail_names[$key] . ' (' . $value . ')' : $value;
                    }
                    $field_value = $field_value ? serialize($field_value) : '';
                } else {
                    switch ($var) {
                        case 'mail_subject':
                            $prop = 'email_subject';
                            break;
                        case 'mail_content':
                            $prop = 'body_formated';
                            break;
                        default:
                            $prop = $var;
                            break;
                    }
                    $field_value = $params['new_model']->get($prop) ?: '';
                }
                if ($field_value) {
                    $audit_vars[$i]['field_value'] = $field_value;
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = in_array($var, $basic_vars_array);
                    $audit_vars[$i]['label'] = '';
                    $i++;
                }
            }
            break;
        case 'add_comment':
        case 'edit_comment':
            $basic_vars = array('subject', 'content', 'is_portal');
            $comment = $params['new_model']->get('comment');
            $old_comment = $params['old_model']->get('comment');
            $i = count($audit_vars);
            $is_add_action = $params['action_type'] == 'add_comment';

            foreach ($basic_vars as $var) {
                if ($is_add_action && $comment->get($var) || !$is_add_action && $old_comment->get($var) != $comment->get($var) || $var == 'is_portal' && $comment->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $comment->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    if (isset(self::$add_vars[$var])) {
                        $audit_vars[$i]['label'] = $comment->get(self::$add_vars[$var]);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get(self::$add_vars[$var]);
                        }
                    } else {
                        $audit_vars[$i]['label'] = $comment->get($var);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($var);
                        }
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        }

        $params['data'] = $audit_vars;

        return true;
    }

    /**
     * prepare audit data for view
     *
     * @return bool
     */
    public static function prepareGetData($records, $params) {
        foreach ($records as $k => $rec) {
            if ($rec['is_array']) {
                $arr = unserialize($rec['field_value']);
                if (empty($arr)) {
                    $arr = array();
                }
                if (is_array($arr)) {
                    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
                        //array is NOT multidimensional, it is safe to implode it into string
                        $records[$k]['field_value'] = implode("\n", $arr);
                    } else {
                        //array is multidimensional, usually wrong assignments array is saved
                        //it should not be displayed
                        unset($records[$k]);
                        continue;
                    }
                }

                if (!empty($rec['old_value']) &&
                    preg_match("/(a|O|s|b)\x3a[0-9]*?((\x3a((\x7b?(.+)\x7d)|(\x22(.+)\x22\x3b)))|(\x3b))/", $rec['old_value'])) {
                        $arr = unserialize($rec['old_value']);
                        if (empty($arr)) {
                            $arr = array();
                        }
                        $records[$k]['old_value'] = implode("\n", $arr);
                    }

                    if (empty($records[$k]['field_value']) && empty($records[$k]['old_value'])) {
                        unset($records[$k]);
                    }
            }
        }

        return $records;
    }

    /**
     * save model audit
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        self::prepareData($registry, $params);
        parent::saveData($registry, $params);
        if (!$registry->get('no_GT2')) {
            parent::saveGT2Data($registry, $params);
        }

        return true;
    }

    /**
     * get model audit
     *
     * @return array
     */
    public static function getData(&$registry, $params) {

        $db = $registry['db'];
        $model_name_plural = strtoupper(General::singular2plural($params['model_name']));

        $model_table_name = constant('DB_TABLE_' . $model_name_plural);
        $table_name = DB_TABLE_FINANCE_AUDIT;
        $history_table_name = DB_TABLE_FINANCE_HISTORY;
        $prefixes = array(strtolower($model_name_plural) . '_', 'finance_documents_', 'finance_', 'gt2_', 'communications_', 'comments_');
        $lang = $registry['lang'];

        $query = 'SELECT a.field_name as idx, a.*' . "\n" .
                 ' FROM ' . $table_name . ' AS a ' . "\n" .
                 ' WHERE a.parent_id=' . $params['parent_id'];
        $records = $db->GetAssoc($query);
        $basic = array();

        //create pseudo-model
        $model = new Finance_Warehouses_Document($registry, array('type' => $params['model_type']));

        //get gt2
        $gt2 = parent::getGT2Data($registry, $params);

        $layout_actions = array('add', 'edit', 'translate');
        $communications_loaded = $comments_loaded = false;

        foreach ($records as $key => $record) {
            $layout_details = null;
            if (in_array($params['action_type'], $layout_actions)) {
                if (in_array($key, array('company', 'office', 'warehouse'))) {
                    $layout_details = $model->getLayoutsDetails('warehouse_data');
                } elseif (in_array($key, array('to_company', 'to_office', 'to_warehouse'))) {
                    $layout_details = $model->getLayoutsDetails('to_warehouse_data');
                } elseif ($key == 'phase') {
                    $layout_details = $model->getLayoutsDetails('project');
                } else {
                    $layout_details = $model->getLayoutsDetails($key);
                }
                if (!isset($gt2['plain_labels'][$key]) && (empty($layout_details['view']) || empty($layout_details['id'])) && !in_array($key, array('status'))) {
                    continue;
                } elseif (in_array($key, array('total_no_vat_reason', 'total_no_vat_reason_text')) && !($record['old_value'] || $record['field_value'])) {
                    continue;
                } elseif (in_array($key, array('total_vat_rate', 'total_vat', 'total_with_vat'))) {
                    // VAT fields are always hidden for warehouse documents @see Model::getGT2Vars()
                    continue;
                }
            } elseif ($params['action_type'] == 'email' && empty($communications_loaded)) {
                $registry['translater']->loadFile(PH_MODULES_DIR . 'communications/i18n/' . $registry['lang'] . '/communications.ini');
                $communications_loaded = true;
            } elseif (in_array($params['action_type'], array('add_comment', 'edit_comment')) && empty($comments_loaded)) {
                $registry['translater']->loadFile(PH_MODULES_DIR . 'comments/i18n/' . $registry['lang'] . '/comments.ini');
                $comments_loaded = true;
            }

            $record['var_label'] = '';
            if ($key == 'active') {
                $record['var_label'] = $registry['translater']->translate('active_state');
            } elseif ($key == 'is_portal') {
                $record['var_label'] = $registry['translater']->translate('is_portal');
            } else {
                if (isset($gt2['plain_labels'][$key])) {
                    $record['var_label'] = $gt2['plain_labels'][$key];
                } elseif (!empty($layout_details) && !empty($layout_details['id']) && $layout_details['keyword'] == $key) {
                    $record['var_label'] = $layout_details['name'];
                } else {
                    foreach ($prefixes as $prefix) {
                        $var_label = $registry['translater']->translate($prefix . $record['field_name']);
                        if ($var_label) {
                            $record['var_label'] = $var_label;
                            break;
                        }
                    }
                }
            }
            if (in_array($record['field_name'], self::$translate_values)) {
                foreach ($prefixes as $prefix) {
                    $value = $registry['translater']->translate($prefix . $record['field_name'] . '_' . $record['old_value']);
                    if ($value) {
                        $record['old_value'] = $value;
                        break;
                    }
                }
                foreach ($prefixes as $prefix) {
                    $value = $registry['translater']->translate($prefix . $record['field_name'] . '_' . $record['field_value']);
                    if ($value) {
                        $record['field_value'] = $value;
                        break;
                    }
                }
            }
            if (in_array($key, self::$add_vars)) {
                continue;
            } elseif (!isset(self::$add_vars[$key])) {
                $basic[$key] = array(
                    'old_value' => $record['old_value'],
                    'field_value' => $record['field_value']
                );
            } else {
                $k = self::$add_vars[$key];
                if (isset($records[$k]['field_value'])) {
                    $basic[$key]['old_value'] = $records[$k]['old_value'];
                    $basic[$key]['field_value'] = $records[$k]['field_value'];
                } else {
                    $basic[$key]['old_value'] = $record['old_value'];
                    $basic[$key]['field_value'] = $record['field_value'];
                }
            }
            $basic[$key]['field_name'] = $record['field_name'];
            $basic[$key]['var_label'] = $record['var_label'];
            $basic[$key]['var_type'] = $record['var_type'];
            $basic[$key]['is_array'] = $record['is_array'];
            switch ($key) {
                case 'total_vat_rate':
                    if ($basic[$key]['old_value'] !== '') {
                        $basic[$key]['old_value'] .= ' %';
                    }
                    $basic[$key]['field_value'] .= ' %';
                    break;
                case 'date':
                case 'to_date':
                    $date_format = $registry['translater']->translate('date_short');
                    if ($basic[$key]['old_value'] == '0000-00-00') {
                        $basic[$key]['old_value'] = '';
                    } elseif ($basic[$key]['old_value'] !== '') {
                        $basic[$key]['old_value'] = General::strftime($date_format, $basic[$key]['old_value']);
                    }
                    $basic[$key]['field_value'] = General::strftime($date_format, $basic[$key]['field_value']);
                    break;
                case 'employees':
                    if (!empty($basic[$key]['old_value'])) {
                        $employees_ids = preg_split('#\s*,\s*#', $basic[$key]['old_value']);
                        if (is_array($employees_ids) && count($employees_ids)) {
                            $filters = array(
                                'where' => array(
                                    'c.type=' . PH_CUSTOMER_EMPLOYEE,
                                    'c.id IN (' . implode(', ', $employees_ids) . ')'),
                                'sanitize' => true);
                            $employees_found = Customers::search($registry, $filters);
                            $employee_names = array();
                            foreach ($employees_found as $employee) {
                                $employee_names[] = $employee->get('name') . ' ' . $employee->get('lastname');
                            }
                            $basic[$key]['old_value'] = implode(', ', $employee_names);
                        }
                    }
                   if (!empty($basic[$key]['field_value'])) {
                        $employees_ids = preg_split('#\s*,\s*#', $basic[$key]['field_value']);
                        if (is_array($employees_ids) && count($employees_ids)) {
                            $filters = array(
                                'where' => array(
                                    'c.type=' . PH_CUSTOMER_EMPLOYEE,
                                    'c.id IN (' . implode(', ', $employees_ids) . ')'),
                                'sanitize' => true);
                            $employees_found = Customers::search($registry, $filters);
                            $employee_names = array();
                            foreach ($employees_found as $employee) {
                                $employee_names[] = $employee->get('name') . ' ' . $employee->get('lastname');
                            }
                            $basic[$key]['field_value'] = implode(', ', $employee_names);
                        }
                    }
                    break;
                case 'is_portal':
                    if ($basic[$key]['old_value'] !== '') {
                        $basic[$key]['old_value'] = ($basic[$key]['old_value'] ?
                                                     $registry['translater']->translate('is_portal') :
                                                     $registry['translater']->translate('is_not_portal'));
                    }
                    $basic[$key]['field_value'] = ($basic[$key]['field_value'] ?
                                                   $registry['translater']->translate('is_portal') :
                                                   $registry['translater']->translate('is_not_portal'));
                    break;
                case 'active':
                    if ($basic[$key]['old_value'] !== '') {
                        $basic[$key]['old_value'] = ($basic[$key]['old_value'] ?
                                                     $registry['translater']->translate('activated') :
                                                     $registry['translater']->translate('deactivated'));
                    }
                    $basic[$key]['field_value'] = ($basic[$key]['field_value'] ?
                                                    $registry['translater']->translate('activated') :
                                                    $registry['translater']->translate('deactivated'));
                    break;
                case 'tags':
                    $basic[$key]['is_array'] = false;
                    include_once PH_MODULES_DIR . 'tags/models/tags.factory.php';
                    if ($basic[$key]['old_value']) {
                        $tag_ids =  unserialize($basic[$key]['old_value']);
                        $basic[$key]['old_value'] = '';
                        if (is_array($tag_ids) && !empty($tag_ids)) {
                            $model->set('tags', $tag_ids, true);
                            $model->getModelTagsForAudit();
                            $basic[$key]['old_value'] = implode("\n", $model->get('tag_names_for_audit'));
                            $model->unsetProperty('tags', true);
                            $model->unsetProperty('tag_names_for_audit', true);
                        }
                    }
                    if ($basic[$key]['field_value']) {
                        $tag_ids =  unserialize($basic[$key]['field_value']);
                        $basic[$key]['field_value'] = '';
                        if (is_array($tag_ids) && !empty($tag_ids)) {
                            $model->set('tags', $tag_ids, true);
                            $model->getModelTagsForAudit();
                            $basic[$key]['field_value'] = implode("\n", $model->get('tag_names_for_audit'));
                            $model->unsetProperty('tags', true);
                            $model->unsetProperty('tag_names_for_audit', true);
                        }
                    }
                    break;
                case 'annulled':
                    $date_format = $registry['translater']->translate('date_mid');
                    if ($basic[$key]['old_value'] !== '') {
                        $basic[$key]['old_value'] = $basic[$key]['old_value'] != '0000-00-00 00:00:00' ?
                                                    General::strftime($date_format, $basic[$key]['old_value']) :
                                                    '';
                    }
                    $basic[$key]['field_value'] = $basic[$key]['field_value'] != '0000-00-00 00:00:00' ?
                                                  General::strftime($date_format, $basic[$key]['field_value']) :
                                                  '';
                    break;
                case 'added_attachments':
                case 'attached_files':
                    // added attachments
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                    $basic[$key]['label'] = '';
                    $filters = array(
                        'where' => array(
                            'f.id IN (\'' . implode('\', \'', unserialize($basic[$key]['field_value'])) . '\')',
                            'f.deleted IS NOT NULL'
                        ),
                        'sanitize' => true,
                        'archive'  => !empty($params['archive'])
                    );
                    $files = Files::search($registry, $filters);
                    if ($files) {
                        foreach ($files as $file) {
                            $basic[$key]['label'] .= $file->getAsHTML() . "\n";
                        }
                    }
                    break;
            }
        }
        $basic = self::prepareGetData($basic, $params);

        if ($db->ErrorMsg()) {
            $registry['logger']->dbError('get audit', $db, $query);
            return false;
        }

        $records = array('vars' => $basic, 'gt2' => $gt2);

        return $records;
    }
}

?>
