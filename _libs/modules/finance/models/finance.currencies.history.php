<?php

class Finance_Currencies_History extends History {

    /**
     * prepare history data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, $params = array()) {
        //set action type
        self::$action_type = $params['action_type'];

        $currency = $params['new_model']->sanitize();

        switch (self::$action_type) {
            case 'add':
            case 'edit':
            default:
                $data = array (
                    'date' => $currency->get('date'),
                    'code' => $currency->get('code'),
                    'bank' => $currency->get('bank'),
                );
                break;
        }

        self::$data = $data;

        return true;
    }

    /**
     * prepare history data for view
     *
     * @return bool
     */
    public static function prepareGetData(&$registry, array $records, array $params) {
        foreach ($records as $k => $record) {
            $i18n_param = 'finance_currencies_log_';
            if ($record['user_id'] == PH_AUTOMATION_USER) {
                $i18n_param .= 'system_';
            }
            $i18n_param .= $record['action_type'];

            $log_text = $registry['translater']->translate($i18n_param);

            $date = General::strftime($registry['translater']->translate('date_short'), strtotime($record['data']['date']));

            require_once 'currency_rates.class.php';
            $bank_info = Currency_Rates::getBankInfo($record['data']['bank'], $registry['lang']);
            $bank = $bank_info['name'] . " ({$record['data']['bank']})";

            $currency = $record['data']['code'];

            $records[$k]['action_type_name'] = $registry['translater']->translate('finance_currencies_logtype_' . $record['action_type']);
            $query = 'SELECT ' . "\n" .
                 ' COUNT(a_id) AS audits ' . "\n" .
                 ' FROM ' . DB_TABLE_FINANCE_HISTORY . ' as h ' . "\n" .
                 ' LEFT JOIN ' . DB_TABLE_FINANCE_AUDIT . ' AS a' . "\n" .
                 ' ON (h.h_id=a.parent_id)' . "\n" .
                 ' WHERE h_id=' . $records[$k]['h_id'] . "\n" .
                 ' GROUP BY h_id';
            $records[$k]['audits'] = $registry['db']->getOne($query);
            //switch for additional options
            switch ($record['action_type']) {
            case 'add':
            case 'edit':
            default:
                //default options
                $records[$k]['data'] = sprintf($log_text,
                                                $record['user_name'],
                                                $currency,
                                                $date,
                                                $bank);
                break;
            }
        }

        return $records;
    }

    /**
     * save model history
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        //prepare the data before saving the history
        self::prepareData($registry, $params);

        //save history record and get the history id
        $history_id = parent::saveData($registry, $params);

        if (isset($params['old_model']) || isset($params['new_model'])) {

            //audit options
            $audit_options = array('model_name'     => self::$model->modelName,
                                   'action_type'  => self::$action_type,
                                   'parent_id'    => $history_id,
                                   'new_model'    => $params['new_model'],
                                   'old_model'    => $params['old_model']);

            //save audit
            $audit_id = Finance_Currencies_Audit::saveData($registry, $audit_options);
        }

        return $audit_id;
    }

    /**
     * get model history
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        if (!empty($params['paginate'])) {
            $params['sanitize'] = false;
            list($records, $pagination) = Model_Factory::paginatedSearch($registry, $params, 'History', 'getData');
        } else {
            $records = parent::getData($registry, $params);
        }
        $records = self::prepareGetData($registry, $records, $params);
        if (isset($pagination)) {
            $records = array($records, $pagination);
        }

        return $records;
    }
}

?>
