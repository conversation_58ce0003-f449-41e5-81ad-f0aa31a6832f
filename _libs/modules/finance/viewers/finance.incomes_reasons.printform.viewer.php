<?php

class Finance_Incomes_Reasons_Printform_Viewer extends Viewer {
    public $template = 'incomes_reasons_printform.html';

    public function prepare() {
        $this->model = $this->registry['finance_incomes_reason'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare departments
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('d.id = ' . $this->model->get('department'),
                                          'd.deleted IS NOT NULL'));
        $department = Departments::searchOne($this->registry, $filters);
        if ($department) {
            $this->data['department'] = $department;
        }

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group'),
                                              'g.deleted IS NOT NULL'));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }

        if ($this->model->get('advance')) {
            //advance invoice
            $finance_var = $this->model->get('grouping_table_2');
            $show_fields = array('article_name', 'price', 'article_description');
            foreach ($finance_var['vars'] as $key => $var) {
                if (!in_array($key, $show_fields)) {
                    $finance_var['vars'][$key]['hidden'] = '1';
                }
            }
            $this->model->set('grouping_table_2', $finance_var, true);
        }

        $this->data['gt2_total_precision'] = '%.' . $this->registry['config']->getParam('precision', 'gt2_total') . 'F';

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('finance_incomes_reasons_printform'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
