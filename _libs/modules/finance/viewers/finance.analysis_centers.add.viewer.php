<?php

class Finance_Analysis_Centers_Add_Viewer extends Viewer {
    public $template = 'analysis_centers_add.html';

    public function prepare() {
        $this->model = $this->registry['finance_analysis_center'];
        $this->data['analysis_center'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        require_once(PH_MODULES_DIR . 'finance/models/finance.analysis_types.factory.php');
        $filters = array('model_lang' => $this->model->get('model_lang'));
        $center_types = Finance_Analysis_Types::search($this->registry, $filters);

        $center_types_optgroups = array();

        $income_title = $this->i18n('finance_incomes');
        $expense_title = $this->i18n('finance_expenses');

        foreach ($center_types as $type) {
            if ($type->get('kind') == 'income' || $type->get('kind') == 'both') {
                $center_types_optgroups[$income_title][] = array('option_value' => $type->get('id'), 'label' => $type->get('name'));
            }
            if ($type->get('kind') == 'expense' || $type->get('kind') == 'both') {
                $center_types_optgroups[$expense_title][] = array('option_value' => $type->get('id'), 'label' => $type->get('name'));
            }
        }
        $this->data['analysis_center_types'] = $center_types_optgroups;

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_analysis_centers_add');
        $this->data['title'] = $title;
    }
}

?>
