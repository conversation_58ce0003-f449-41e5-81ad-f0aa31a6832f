<?php

class Finance_Warehouses_Documents_Edit_Viewer extends Viewer {
    public $template = 'warehouses_documents_edit.html';
    public $modelName = 'finance_warehouses_document';

    public function prepare() {
        $this->model = $this->getModel();

        // prepare layout index
        $this->prepareLayoutIndex();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        // GET DEPARTMENTS
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $departments = Departments::getTree($this->registry, array('sanitize' => true));
        $this->data['departments'] = $departments;

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups = Groups::getTree($this->registry, array('sanitize' => true));
        $this->data['groups'] = $groups;

        //prepare warehouses (necessary for batch data)
        $filters = array('where' => array('fwh.id = ' . $this->model->get('warehouse')),
                         'sanitize' => true);
        require_once $this->modelsDir . 'finance.warehouses.factory.php';
        $warehouse = Finance_Warehouses::searchOne($this->registry, $filters);
        $this->data['warehouses'] = array($warehouse);

        //get available currencies
        $this->data['currencies_available'] = Dropdown::getCurrencies(array($this->registry));

        if (!$this->registry['request']->isPost() || $this->model->get('status') == 'locked') {
            $this->model->getBatchesData();
        }
        if ($this->model->get('status') != 'locked') {
            $this->prepareBatchData();
        }
        switch ($this->model->get('type')) {
            case PH_FINANCE_TYPE_COMMODITIES_RESERVATION:
                $this->prepareCommoditiesReservations();
                break;
            case PH_FINANCE_TYPE_COMMODITIES_TRANSFER:
                $this->prepareCommoditiesTransfers();
                break;
            case PH_FINANCE_TYPE_WASTE:
                $this->prepareWastes();
                break;
            case PH_FINANCE_TYPE_INSPECTION:
                $this->prepareInspections();
                break;
        }

        $this->data['after_action_options'] = array(
            array('option_value' => '',
                  'label' => $this->i18n('finance_warehouses_documents_after_action_edit_unfinished'),
                  'description' => $this->i18n('finance_warehouses_documents_unfinished')));
        if ($this->registry['currentUser']->checkRights('finance_warehouses_documents', 'setstatus')) {
            if ($this->model->get('type') == PH_FINANCE_TYPE_COMMODITIES_TRANSFER) {
                if ($this->model->get('status') == 'opened') {
                    $this->data['after_action_options'][0]['description'] =
                        $this->i18n('finance_warehouses_documents_transfer_unfinished');
                    $this->data['after_action_options'][] =
                        array(
                            'option_value' => 'locked',
                            'label' => $this->i18n('addhandover_outgoing'),
                            'description' => $this->i18n('finance_warehouses_documents_transfer_locked')
                        );
                }
                $this->data['after_action_options'][] =
                    array(
                        'option_value' => 'finish',
                        'label' => $this->i18n('addhandover_incoming'),
                        'description' => $this->i18n('finance_warehouses_documents_transfer_finish')
                    );
            } else {
                $this->data['after_action_options'][] =
                    array(
                        'option_value' => ($this->model->get('type') == PH_FINANCE_TYPE_WASTE ? 'finish' : 'locked'),
                        'label' => $this->i18n('finance_warehouses_documents_after_action_finish'),
                        'description' => $this->i18n('finance_warehouses_documents_finish')
                    );
            }
        }

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareBatchData() {
        // get precision for GT2 quantity
        $gt2_quantity_precision = $this->registry['config']->getParam('precision', 'gt2_quantity');
        $gt2_quantity_prec_format = '%.' . $gt2_quantity_precision . 'F';
        $this->data['price_prec'] = $this->registry['config']->getParam('precision', 'gt2_rows');
        $this->data['price_prec'] = '%.' . $this->data['price_prec'] . 'F';

        $gt2 = $this->model->get('grouping_table_2');
        $warehouse = reset($this->data['warehouses']);
        $wid = $this->model->get('warehouse');
        $request = &$this->registry['request'];
        $articles = array();
        if ($request->isPost()) {
            //it seems that an error occurred during save
            $articles = $request->get('article_id');
        } else {
            foreach ($gt2['values'] as $key => $values) {
                if (!empty($values['article_id'])) {
                    $articles[] = $values['article_id'];
                }
            }
        }

        //we have to load batch data
        //get available batch quantities
        $customer = false;
        $reservation = false;
        switch ($this->model->get('type')) {
            case PH_FINANCE_TYPE_COMMODITIES_RESERVATION:
                $reservation = $this->model->get('id');
                break;
            case PH_FINANCE_TYPE_INSPECTION:
                $reservation = 'none';
                break;
        }
        $qParams = array(
            'nom_id' => $articles,
            'customer_id' => $customer,
            'reservation' => $reservation
        );
        $available_articles = $warehouse->getAvailableQuantity($qParams);
        $filters = array('where' => array('n.id IN ("' . implode('", "', $articles) . '")'));
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $models = Nomenclatures::search($this->registry, $filters);
        $articles = array();
        foreach ($models as $a) {
            $articles[$a->get('id')] = $a;
        }
        unset($models);
        $prec = $this->registry['config']->getSectionParams('precision');
        $existing_batches = $existing_serials = $existing_custom = array();
        $vars_settings = $this->model->getBatchVarsSettings();
        foreach ($gt2['values'] as $key => $values) {
            if (empty($values['article_id'])) {
                continue;
            }
            if (empty($values['vars_settings'])) {
                $gt2['values'][$key]['vars_settings'] = $vars_settings;
            }
            if (!empty($available_articles[$values['article_id']]['quantity'])) {
                $available = $available_articles[$values['article_id']];
                $gt2['values'][$key]['available_in'] = array($this->model->get('warehouse'));
            } else {
                $available = array('quantity' => 0, 'batch_data' => array());
            }
            $gt2['values'][$key]['available_quantity'] = sprintf($gt2_quantity_prec_format, $available['quantity']);
            switch ($this->model->get('type')) {
                case PH_FINANCE_TYPE_INSPECTION:
                    $gt2['values'][$key]['available_in'] = array($this->model->get('warehouse'));
                    $gt2['values'][$key]['current_quantity'] = sprintf($gt2_quantity_prec_format, $available['quantity']);
                    $gt2['values'][$key]['difference'] = sprintf($gt2_quantity_prec_format, $values['quantity'] - $gt2['values'][$key]['current_quantity']);
                    break;
            }

            if ($articles[$values['article_id']]->get('has_batch')) {
                $gt2['values'][$key]['has_batch'] = $articles[$values['article_id']]->get('has_batch');
                $gt2['values'][$key]['has_serial'] = $articles[$values['article_id']]->get('has_serial');
                $gt2['values'][$key]['has_expire'] = $articles[$values['article_id']]->get('has_expire');
                $gt2['values'][$key]['has_delivery_price'] = 2;
                $gt2['values'][$key]['has_batch_code'] = empty($available['quantity']) ? $articles[$values['article_id']]->get('has_batch_code') : true;
                $gt2['values'][$key]['quantity'] = 0; // JS will do the job
                if ($this->registry['request']->isPost()) {
                    $batch_isCustom = $request->get('article_' . $key . '_batch_isCustom');
                }
                $unique = $json = array();
                //create unique key for each quantity found
                foreach ($available['batch_data'] as $bd) {
                    $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'parent_id'       => $this->model->get('warehouse'),
                            'nomenclature_id' => $values['article_id'],
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    if (!isset($unique[$k])) {
                        $unique[$k] = 0;
                    }
                    $unique[$k] += $bd['quantity'];
                    //set existing batches for the article
                    if (!isset($existing_batches[$values['article_id']][$bd['batch']])) {
                        $existing_batches[$values['article_id']][$bd['batch']] = array(
                                'label' => $bd['batch_code'],
                                'option_value' => $bd['batch'],
                        );
                    }
                    if ($articles[$values['article_id']]->get('has_serial')) {
                        //set existing serials for the article
                        if (!isset($existing_serials[$values['article_id']][$bd['batch']][$bd['serial']])) {
                            $existing_serials[$values['article_id']][$bd['batch']][$bd['serial']] = array(
                                    'label' => $bd['serial'],
                                    'option_value' => $bd['serial'],
                            );
                        }
                    }
                    //set existing custom number for the article
                    if (!isset($existing_custom[$values['article_id']][$bd['batch']][$bd['custom']])) {
                        $existing_custom[$values['article_id']][$bd['batch']][$bd['custom']] = array(
                                'label' => $bd['custom'],
                                'option_value' => $bd['custom'],
                        );
                        if (!$bd['custom']) {
                            $existing_custom[$values['article_id']][$bd['batch']][$bd['custom']]['label'] = $this->i18n('finance_warehouses_documents_no_custom_batch_number');
                        }
                    }

                    //prepare data and dependencies
                    if (empty($json[$bd['batch']])) {
                        $json[$bd['batch']] = $bd;
                        if (!$bd['serial']) {
                            $bd['serial'] = '';
                        } else {
                            $json[$bd['batch']]['serial'] = array($json[$bd['batch']]['serial'] => array('label' => $json[$bd['batch']]['serial'], 'option_value' => $json[$bd['batch']]['serial']));
                        }
                        if ($bd['custom']) {
                            $json[$bd['batch']]['custom'] = array($json[$bd['batch']]['custom'] => array('label' => $json[$bd['batch']]['custom'], 'option_value' => $json[$bd['batch']]['custom']));
                        } else {
                            $json[$bd['batch']]['custom'] = array('' => array('label' => $this->i18n('finance_warehouses_documents_no_custom_batch_number'), 'option_value' => ''));
                            $bd['custom'] = '';
                        }
                        $json[$bd['batch']]['expire_formatted'] = General::strftime('%d.%m.%Y', $json[$bd['batch']]['expire']);
                        $json[$bd['batch']]['available_quantity'] = array($bd['serial'] . '_' . $bd['custom'] => $json[$bd['batch']]['quantity']);
                        $json[$bd['batch']]['custom_serials'][$bd['custom']] = array($bd['serial']);
                        $json[$bd['batch']]['serial_customs'][$bd['serial']] = array($bd['custom']);
                        unset($json[$bd['batch']]['quantity']);
                    } else {
                        if (!$bd['serial']) {
                            $bd['serial'] = '';
                        }
                        if ($bd['serial'] && empty($json[$bd['batch']]['serial'][$bd['serial']])) {
                            $json[$bd['batch']]['serial'][$bd['serial']] = array('label' => $bd['serial'], 'option_value' => $bd['serial']);
                        }
                        if ($bd['custom'] && empty($json[$bd['batch']]['custom'][$bd['custom']])) {
                            $json[$bd['batch']]['custom'][$bd['custom']] = array('label' => $bd['custom'], 'option_value' => $bd['custom']);
                        } elseif (!$bd['custom'] && empty($json[$bd['batch']]['custom'][''])) {
                            $json[$bd['batch']]['custom'] = array('' => array('label' => $this->i18n('finance_warehouses_documents_no_custom_batch_number'), 'option_value' => ''));
                            $bd['custom'] = '';
                        }
                        $json[$bd['batch']]['available_quantity'][$bd['serial'] . '_' . $bd['custom']] = $bd['quantity'];
                        if (empty($json[$bd['batch']]['custom_serials'][$bd['custom']])) {
                            $json[$bd['batch']]['custom_serials'][$bd['custom']] = array();
                        }
                        if (!in_array($bd['serial'], $json[$bd['batch']]['custom_serials'][$bd['custom']])) {
                            $json[$bd['batch']]['custom_serials'][$bd['custom']][] = $bd['serial'];
                        }
                        if (empty($json[$bd['batch']]['serial_customs'][$bd['serial']])) {
                            $json[$bd['batch']]['serial_customs'][$bd['serial']] = array();
                        }
                        if (!in_array($bd['custom'], $json[$bd['batch']]['serial_customs'][$bd['serial']])) {
                            $json[$bd['batch']]['serial_customs'][$bd['serial']][] = $bd['custom'];
                        }
                    }
                }

                //prepare batch options for the current article
                if (!empty($gt2['values'][$key]['batch_data'])) {
                    foreach ($gt2['values'][$key]['batch_data'] as $idx => $bd) {
                        $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $wid,
                                'nomenclature_id' => $values['article_id'],
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        if (isset($unique[$k])) {
                            $gt2['values'][$key]['batch_data'][$idx]['available_quantity'] = $unique[$k];
                        } else {
                            $gt2['values'][$key]['batch_data'][$idx]['available_quantity'] = 0;
                        }
                        if (!empty($json[$bd['batch']])) {
                            $gt2['values'][$key]['batch_data'][$idx]['batch_data_json_object'] = $json[$bd['batch']];
                        }
                        if (!empty($batch_isCustom[$idx])) {
                            //set custom code for the new batch
                            $gt2['values'][$key]['batch_data'][$idx]['batch'] = $gt2['values'][$key]['batch_data'][$idx]['batch_code'];
                        } elseif (!isset($existing_batches[$values['article_id']][$bd['batch']])) {
                            if ($request->isPost()) {
                                if ($this->model->get('type') == PH_FINANCE_TYPE_INSPECTION) {
                                    $existing_batches[$values['article_id']][$bd['batch']] = array(
                                        'label' => $bd['batch_code'],
                                        'option_value' => $bd['batch'],
                                        'just_added' => true,
                                    );
                                } else {
                                    $gt2['values'][$key]['batch_data'][$idx]['batch'] = '';
                                }
                            } else {
                                $existing_batches[$values['article_id']][$bd['batch']] = array(
                                    'label' => $bd['batch_code'],
                                    'option_value' => $bd['batch'],
                                    'just_added' => true,
                                );
                            }
                        }
                    }
                } else {
                    $gt2['values'][$key]['batch_data'] = array();
                }
            }
        }
        $this->data['existing_batches'] = $existing_batches;
        $this->data['existing_serials'] = $existing_serials;
        $this->data['existing_custom'] = $existing_custom;
        $this->model->set('grouping_table_2', $gt2, true);
    }

    /**
     * Additional data preparation for Inspection
     */
    public function prepareInspections() {
        $wid = $this->model->get('warehouse');
        $gt2 = $this->model->get('grouping_table_2');
        $options = $gt2['vars']['article_name']['autocomplete']['fill_options'];
        foreach ($options as $key => $val) {
            if (preg_match('#^\$quantity\s*\=\>#', $val)) {
                $options[$key] = preg_replace('#\=\>.*#', '=> ', $options[$key]);
            }
            if (preg_match('#^\$(\w+)\s*=>#', $val, $matches) && array_key_exists($matches[1], $gt2['vars']) && array_key_exists('custom_class', $gt2['vars'][$matches[1]])) {
                $gt2['vars'][$matches[1]]['custom_class'] = preg_replace('#(^|\s+)copy_values(\s+|$)#', ' ', $gt2['vars'][$matches[1]]['custom_class']);
            }
        }

        $gt2['vars']['article_name']['autocomplete']['fill_options'] = array_values($options);
        $gt2['vars']['article_name']['autocomplete']['unique'] = 'article_id';
        $gt2['show_select_buttons'] = 'nomenclatures';
        //set some special things as we will need to get batch data for the batch articles
        if (empty($gt2['vars']['article_name']['autocomplete']['execute_after'])) {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow';
        } else {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow(autocomplete, data);' .
                $gt2['vars']['article_name']['autocomplete']['execute_after'];
        }
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$has_batch => <has_batch>';
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$available_quantity => <warehouse' . $wid . '_available>';
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$current_quantity => <warehouse' . $wid . '_available>';
        $gt2['vars']['article_name']['autocomplete']['filters']['<get_all_quantities>'] = $wid;
        $gt2['vars']['article_name']['autocomplete']['filters']['<subtype>'] = 'commodity';

        $vars = array();

        foreach ($gt2['vars'] as $key => $var) {
            if ($key == 'quantity') {
                $vars['current_quantity'] = array (
                    'id' => 'current_quantity',
                    'name' => 'current_quantity',
                    'type' => 'text',
                    'hidden' => !$this->registry['currentUser']->checkRights('finance_warehouses_documents', 'setstatus'),
                    'readonly' => 1,
                    'width' => '50',
                    'label' => $this->i18n('finance_warehouses_documents_current_quantity'),
                    'text_align' => 'right',
                    'js_filter' => 'insertOnlyFloats',
                );
            }
            $vars[$key] = $var;
            if ($key == 'available_quantity' && !$vars[$key]['hidden']) {
                $vars[$key]['hidden'] = !$this->registry['currentUser']->checkRights('finance_warehouses_documents', 'setstatus');
            }
            if ($key == 'quantity') {
                $vars['difference'] = array (
                    'id' => 'difference',
                    'name' => 'difference',
                    'type' => 'text',
                    'hidden' => !$this->registry['currentUser']->checkRights('finance_warehouses_documents', 'setstatus'),
                    'readonly' => 1,
                    'width' => '50',
                    'label' => $this->i18n('finance_warehouses_documents_difference'),
                    'text_align' => 'right',
                    'js_filter' => 'insertOnlyFloats',
                );
                $gt2['last_visible_column'] = 'difference';
            } elseif (!$vars[$key]['hidden']) {
                $gt2['last_visible_column'] = $var['name'];
            }
        }
        $gt2['vars'] = $vars;

        $this->model->set('grouping_table_2', $gt2, true);

        // prepare responsible employees autocompleter and data
        $autocomplete_employee = array (
            'filters' => array('type' => PH_CUSTOMER_EMPLOYEE . ''), //convert the id to string
            'type' => 'customers',
            'url' => sprintf('%s?%s=customers&customers=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param']),
            'clear' => 1
        );
        $this->data['autocomplete_employee'] = $autocomplete_employee;

        $employees = $this->model->get('employees') ?: array();
        if ($this->registry['request']->isPost()) {
            // clear empty strings
            $employees = implode(',', array_filter($employees));
        }

        if ($employees) {
            $employees = '(' . $employees . ')';

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('where' => array('c.id IN ' . $employees),
                             'sort' => array('c.id IN ' . $employees),
                             'sanitize' => true);
            $records = Customers::search($this->registry, $filters);

            $employees = array();
            foreach ($records as $record) {
                $employees[$record->get('id')] = array('id' => $record->get('id'),
                                                       'code' => $record->get('code'),
                                                       'name' => $record->get('name') . ($record->get('lastname') ? ' ' . $record->get('lastname') : '')
                                                 );
            }
        }
        $this->model->set('employees', $employees, true);
    }

    /**
     * Additional data preparation for Commodities transfer
     */
    public function prepareCommoditiesTransfers() {

        if ($this->model->get('status') == 'locked') {
            $layouts_details = $this->model->get('layouts_details');
            foreach ($layouts_details as $keyname => $layout) {
                if (strpos($keyname, 'to_') !== 0) {
                    $layouts_details[$keyname]['edit'] = 0;
                }
            }
            $this->model->set('layouts_details', $layouts_details, true);
        }

        // responsible employees of 'from' warehouse
        $warehouse = reset($this->data['warehouses']);
        $employees_options = $warehouse ? $warehouse->getEmployees('DROPDOWN') : array();
        $found_from = false;
        foreach ($employees_options as $eo) {
            if ($eo['option_value'] == $this->model->get('from')) {
                $found_from = true;
                break;
            }
        }
        $model_from = $this->model->get('from');
        $employees_options = array_filter($employees_options,
            function ($a) use ($model_from) { return $a['active_option'] || $a['option_value'] == $model_from; });
        if (!$found_from && ($this->model->get('from') || $this->model->get('from_name'))) {
            if (!$this->model->get('from')) {
                $this->model->set('from', $this->model->get('from_name'), true);
            }
            array_unshift($employees_options, array(
                'label' => $this->model->get('from_name') ?:$this->model->get('from'),
                'active_option' => 1,
                'option_value' => $this->model->get('from')
            ));
        }
        $this->data['employees_options'] = $employees_options;

        // options for 'to' warehouse
        require_once $this->modelsDir . 'finance.dropdown.php';
        $params = array(0 => $this->registry,
                        'active' => 1);
        $this->data['to_warehouses_data'] = Finance_Dropdown::getWarehouseData($params);

        // responsible employees of 'to' warehouse
        if ($this->model->get('to_warehouse_data') && preg_match('#^\d+_\d+_(\d+)$#', $this->model->get('to_warehouse_data'), $to_warehouse)) {
            $to_warehouse = Finance_Warehouses::searchOne($this->registry, array('where' => array('fwh.id=\'' . $to_warehouse[1] . '\'')));
            $to_employees_options = $to_warehouse ? $to_warehouse->getEmployees('DROPDOWN') : array();
            unset($to_warehouse);
            $found_to_to = false;
            foreach ($to_employees_options as $eo) {
                if ($eo['option_value'] == $this->model->get('to_to')) {
                    $found_to_to = true;
                    break;
                }
            }
            $model_to_to = $this->model->get('to_to');
            $to_employees_options = array_filter($to_employees_options,
                function ($a) use ($model_to_to) { return $a['active_option'] || $a['option_value'] == $model_to_to; });
            if (!$found_to_to && ($this->model->get('to_to') || $this->model->get('to_to_name'))) {
                if (!$this->model->get('to_to')) {
                    $this->model->set('to_to', $this->model->get('to_to_name'), true);
                }
                array_unshift($to_employees_options, array(
                    'label' => $this->model->get('to_to_name') ?:$this->model->get('to_to'),
                    'active_option' => 1,
                    'option_value' => $this->model->get('to_to')
                ));
            }
            $this->data['to_employees_options'] = $to_employees_options;
        }

        // all employees to choose from for 'to' and 'to_from' fields
        require_once PH_MVC_DIR . 'dropdown.class.php';
        $employees = Dropdown::getEmployees(array($this->registry));
        $found_to = false;
        $found_to_from = false;
        foreach ($employees as $employee) {
            if ($employee['option_value'] == $this->model->get('to')) {
                $found_to = true;
            }
            if ($employee['option_value'] == $this->model->get('to_from')) {
                $found_to_from = true;
            }
        }
        if (!$found_to && ($this->model->get('to') || $this->model->get('to_name'))) {
            if (!$this->model->get('to')) {
                $this->model->set('to', $this->model->get('to_name'), true);
            }
            array_unshift($employees, array(
                'label' => $this->model->get('to_name')?:$this->model->get('to'),
                'option_value' => $this->model->get('to')
            ));
        }
        if (!$found_to_from && ($this->model->get('to_from') || $this->model->get('to_from_name'))) {
            if (!$this->model->get('to_from')) {
                $this->model->set('to_from', $this->model->get('to_from_name'), true);
            }
            if ($this->model->get('to_from') != $this->model->get('to')) {
                array_unshift($employees, array(
                    'label' => $this->model->get('to_from_name')?:$this->model->get('to_from'),
                    'option_value' => $this->model->get('to_from')
                ));
            }
        }
        $this->data['employees'] = $employees;

        $gt2 = $this->model->get('grouping_table_2');
        $new_vars = array();
        $last_visible_column = '';

        foreach ($gt2['vars'] as $var_name => $var_info) {
            if ($var_name == 'quantity') {
                $var_info['custom_class'] = 'quantity commodities_transfer_quantity';
                $var_info['cell_class'] = 'quantity commodities_transfer_quantity';
            }
            $new_vars[$var_name] = $var_info;
            if (!$var_info['hidden']) {
                $last_visible_column = $var_name;
            }
        }
        //allow articles to be added only once
        $new_vars['article_name']['autocomplete']['unique'] = 'article_id';
        //set some special things as we will need to get batch data for the batch articles
        if (empty($new_vars['article_name']['autocomplete']['execute_after'])) {
            $new_vars['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow';
        } else {
            $new_vars['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow(autocomplete, data);' . $new_vars['article_name']['autocomplete']['execute_after'];
        }
        $new_vars['article_name']['autocomplete']['fill_options'][] = '$has_batch => <has_batch>';

        $wid = $this->model->get('warehouse');
        $new_vars['article_name']['autocomplete']['filters']['<get_available_quantities>'] = $wid;
        $new_vars['article_name']['autocomplete']['filters']['<subtype>'] = 'commodity';
        $new_vars['article_name']['autocomplete']['fill_options'][] =
            '$available_quantity => <warehouse' . $wid . '_available>';
        if ($this->model->get('status') == 'opened' && !empty($new_vars['available_quantity']) && $new_vars['available_quantity']['hidden']) {
            $var = array('hidden' => 0, 'readonly' => 1) + $new_vars['available_quantity'];
            unset($new_vars['available_quantity']);
            General::injectInArray(array('available_quantity' => $var), $new_vars, 'after', 'quantity');
            if (array_search('available_quantity', array_keys($new_vars)) > array_search($last_visible_column, array_keys($new_vars))) {
                $last_visible_column = 'available_quantity';
            }
        }
        foreach ($new_vars['article_name']['autocomplete']['fill_options'] as $key => $val) {
            if (preg_match('#^\$(\w+)\s*=>#', $val, $matches) && array_key_exists($matches[1], $new_vars) && array_key_exists('custom_class', $new_vars[$matches[1]])) {
                $new_vars[$matches[1]]['custom_class'] = preg_replace('#(^|\s+)copy_values(\s+|$)#', ' ', $new_vars[$matches[1]]['custom_class']);
            }
        }
        $gt2['vars'] = $new_vars;
        $gt2['last_visible_column'] = $last_visible_column;

        $this->model->set('grouping_table_2', $gt2, true);
    }

    /**
     * Additional data preparation for Waste record
     */
    public function prepareWastes() {

        // prepare responsible employees autocompleter and data
        $autocomplete_employee = array (
            'filters' => array('type' => PH_CUSTOMER_EMPLOYEE . ''), //convert the id to string
            'type' => 'customers',
            'url' => sprintf('%s?%s=customers&customers=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param']),
            'clear' => 1
        );
        $this->data['autocomplete_employee'] = $autocomplete_employee;

        $employees = $this->model->get('employees') ?: array();
        if ($this->registry['request']->isPost()) {
            // clear empty strings
            $employees = implode(',', array_filter($employees));
        }

        if ($employees) {
            $employees = '(' . $employees . ')';

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('where' => array('c.id IN ' . $employees),
                             'sort' => array('c.id IN ' . $employees),
                             'sanitize' => true);
            $records = Customers::search($this->registry, $filters);

            $employees = array();
            foreach ($records as $record) {
                $employees[$record->get('id')] = array('id' => $record->get('id'),
                                                       'code' => $record->get('code'),
                                                       'name' => $record->get('name') . (($record->get('lastname')) ? ' ' . $record->get('lastname') : '')
                                                 );
            }
        }
        $this->model->set('employees', $employees, true);

        $gt2 = $this->model->get('grouping_table_2');
        //set some special things as we will need to get batch data for the batch articles
        if (empty($gt2['vars']['article_name']['autocomplete']['execute_after'])) {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow';
        } else {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow(autocomplete, data);' . $gt2['vars']['article_name']['autocomplete']['execute_after'];
        }
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$has_batch => <has_batch>';
        $wid = $this->model->get('warehouse');
        $gt2['vars']['article_name']['autocomplete']['filters']['<get_available_quantities>'] = $wid;
        $gt2['vars']['article_name']['autocomplete']['filters']['<subtype>'] = 'commodity';
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$available_quantity => <warehouse' . $wid . '_available>';
        if (!empty($gt2['vars']['available_quantity']) && $gt2['vars']['available_quantity']['hidden']) {
            $var = array('hidden' => 0, 'readonly' => 1) + $gt2['vars']['available_quantity'];
            unset($gt2['vars']['available_quantity']);
            General::injectInArray(array('available_quantity' => $var), $gt2['vars'], 'after', 'quantity');
            if (array_search('available_quantity', array_keys($gt2['vars'])) > array_search($gt2['last_visible_column'], array_keys($gt2['vars']))) {
                $gt2['last_visible_column'] = 'available_quantity';
            }
        }
        $this->model->set('grouping_table_2', $gt2, true);
    }

    /**
     * Additional data preparation for Commodities reservation
     */
    public function prepareCommoditiesReservations() {

        //prepare customer
        if ($customer_id = $this->model->get('customer')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array(
                'sanitize' => true,
                'model_lang' => $this->model->get('model_lang'),
                'where' => array(
                    'n.deleted IS NOT NULL',
                    'n.id = ' . $trademark_id
                ),
                //flag to search in customers trademarks
                'session_param' => 'filter_trademark_nomenclature'
            );
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        if ($this->model->get('project')) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('p.deleted IS NOT NULL',
                                              'p.id = ' . $this->model->get('project')));
            $project = Projects::searchOne($this->registry, $filters);
            $phases = array();
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);

                if ($project->get('stages')) {
                    $project->getIncludedStages();
                    foreach ($project->get('stages_info') as $status => $stages_status) {
                        foreach ($stages_status as $stg_id => $stage) {
                            $phases[] = array(
                                'label'        => $stage->get('name'),
                                'option_value' => $stage->get('id'));

                            if ($stage->get('id') == $this->model->get('phase')) {
                                $this->model->set('phase_name', $stage->get('name'), true);
                            }
                        }
                    }
                }
                $this->data['phases'] = $phases;
            }
        }

        $gt2 = $this->model->get('grouping_table_2');
        //set some special things as we will need to get batch data for the batch articles
        if (empty($gt2['vars']['article_name']['autocomplete']['execute_after'])) {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow';
        } else {
            $gt2['vars']['article_name']['autocomplete']['execute_after'] = 'addBatchDataRow(autocomplete, data);' . $gt2['vars']['article_name']['autocomplete']['execute_after'];
        }
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] = '$has_batch => <has_batch>';

        $wid = $this->model->get('warehouse');

        //set additional fill options for the autocompleter
        //e.g. the quantities for the warehouses have to be filled
        $gt2['vars']['article_name']['autocomplete']['fill_options'][] =
            '$available_quantity => <warehouse' . $wid . '_available>';
        $gt2['vars']['article_name']['autocomplete']['filters']['<get_available_quantities>'] = $wid;
        $gt2['vars']['article_name']['autocomplete']['filters']['<subtype>'] = 'commodity';
        if (!empty($gt2['vars']['available_quantity']) && $gt2['vars']['available_quantity']['hidden']) {
            $var = array('hidden' => 0, 'readonly' => 1) + $gt2['vars']['available_quantity'];
            unset($gt2['vars']['available_quantity']);
            General::injectInArray(array('available_quantity' => $var), $gt2['vars'], 'after', 'quantity');
            if (array_search('available_quantity', array_keys($gt2['vars'])) > array_search($gt2['last_visible_column'], array_keys($gt2['vars']))) {
                $gt2['last_visible_column'] = 'available_quantity';
            }
        }

        $this->model->set('grouping_table_2', $gt2, true);

        $reservationDoc = new Finance_Warehouses_Document(
            $this->registry,
            array('type' => PH_FINANCE_TYPE_COMMODITIES_RESERVATION)
        );
        $this->data['default_release_date'] = $reservationDoc->getDefaultReleaseDate();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('finance_warehouses_documents_edit'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
