<?php

class Finance_Warehouses_List_Viewer extends Viewer {
    public $template = 'warehouses_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.warehouses.factory.php';

        $filters = Finance_Warehouses::saveSearchParams($this->registry);

        list($warehouses, $pagination) = Finance_Warehouses::pagedSearch($this->registry, $filters);
        $this->models = $this->data['warehouses'] = $warehouses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_warehouses');
        $this->data['title'] = $title;
    }
}

?>
