<?php

class Finance_Warehouses_Documents_Generate_Viewer extends Viewer {
    public $template = 'warehouses_documents_generate.html';

    public function prepare() {
        if ($this->registry['request']->get('submit_button') == 'preview') {
            $this->data['preview_content'] = $this->registry->get('preview_content');
            $this->setFrameset('frameset_blank.html');
            $this->template = 'warehouses_documents_generate_preview.html';
        } else {
            $this->model = $this->registry['finance_warehouses_document'];
            $this->data['finance_warehouses_document'] = $this->model;

            //set submit link
            $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                $this->registry['controller_param'], $this->controller,
                                $this->registry['action_param'], $this->action,
                                $this->action, $this->model->get('id'));
            $this->data['submitLink'] = $this->submitLink;

            //assign specified pattern to the renderer
            $this->data['pattern'] = $this->registry['pattern'];

            if ($this->model->get('revision')) {
                $this->data['current_revision'] = $this->model->get('revision');
            } else {
                $this->data['current_revision'] = new Model($this->registry);
            }

            $this->prepareTranslations();

            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('finance_warehouses_documents_generate'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
