<?php

class Finance_Payments_Add_Viewer extends Viewer {
    public $template = 'payments_add.html';

    public function prepare() {
        $this->model = $this->registry['finance_payment'];

        // prepare layout index
        $this->prepareLayoutIndex();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups               = Groups::getTree($this->registry);
        $this->data['groups'] = $groups;

        //prepare companies, offices, cashboxes/bank accounts
        require_once $this->modelsDir . 'finance.dropdown.php';
        $params = array($this->registry,
                        'lang' => $this->model->get('model_lang'),
                        'active' => 1);
        if ($this->model->get('type') == 'PKO' || $this->model->get('type') == 'RKO') {
            $params['payment_type'] = 'cash';
            $params['payment_direction'] = $this->model->get('type') == 'PKO' ? 'incomes' : 'expenses';
        } elseif ($this->model->get('type') == 'BP' || $this->model->get('type') == 'PN') {
            $params['payment_type'] = 'bank';
            $params['payment_direction'] = $this->model->get('type') == 'BP' ? 'incomes' : 'expenses';
        }
        $companies_data = Finance_Dropdown::getCompaniesData($params);
        $this->data['companies_data'] = $companies_data;

        // Set the default company data
        if (!$this->model->get('company_data') && !empty($params['payment_type'])) {
            $this->model->set('company_data', $this->registry['currentUser']->getDefaultCompanyData($params['payment_type'], 'payments', $companies_data), true);
        }

        //prepare customer
        $customer_id = 0;
        if ($this->model->get('customer')) {
            $customer_id = $this->model->get('customer');
        }

        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer', $customer_id, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array(
                'sanitize' => true,
                'model_lang' => $this->model->get('model_lang'),
                'where' => array(
                    'n.deleted IS NOT NULL',
                    'n.id = ' . $trademark_id
                ),
                //flag to search in customers trademarks
                'session_param' => 'filter_trademark_nomenclature'
            );
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        if ($this->model->get('project')) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('p.deleted IS NOT NULL',
                                              'p.id = ' . $this->model->get('project')));
            $project = Projects::searchOne($this->registry, $filters);
            $phases = array();
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);

                if ($project->get('stages')) {
                    $project->getIncludedStages();
                    foreach ($project->get('stages_info') as $status => $stages_status) {
                        foreach ($stages_status as $stg_id => $stage) {
                            $phases[] = array(
                                'label'        => $stage->get('name'),
                                'option_value' => $stage->get('id'));

                            if ($stage->get('id') == $this->model->get('phase')) {
                                $this->model->set('phase_name', $stage->get('name'), true);
                            }
                        }
                    }
                }
                if (!$this->model->get('phase')) {
                    $this->model->set('phase', $project->get('stage'), true);
                    $this->model->set('phase_name', $project->get('stage_name'), true);
                }
                $this->data['phases'] = $phases;
            }
        }

        //prepare employee
        $employee_id = 0;
        if ($this->model->get('employee')) {
            $employee_id = $this->model->get('employee');
        }
        if ($employee_id) {
            require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
            $filters = array('sanitize' => true,
                             'where' => array('c.type = ' . PH_CUSTOMER_EMPLOYEE, 'c.id = ' . $employee_id));
            $employee = Customers::searchOne($this->registry, $filters);
            if ($employee) {
                $this->model->set('employee_code', $employee->get('code'), true);
                $this->model->set('employee_name', $employee->get('name') . ' ' . $employee->get('lastname'), true);
                $this->model->set('employee', $employee_id, true);
            } else {
                $this->model->set('employee', '', true);
            }
        }
        $this->data['autocomplete_employee_filters'] = array('<type>' => (string)PH_CUSTOMER_EMPLOYEE);

        require_once $this->modelsDir . 'finance.currencies.factory.php';
        $currency = Finance_Currencies::getAvailableCurrencies($this->registry, array('active' => 1));
        $this->data['currency'] = $currency;

        if (!$this->registry['request']->isPost()) {
            foreach ($currency as $code => $data) {
                if ($data['is_main']) {
                    $this->model->set('currency', $code, true);
                    break;
                }
            }
        }

        $this->data['after_action_options'] = array(
            array('option_value' => 'added',
                  'label' => $this->i18n('finance_payments_after_action_add_unfinished'),
                  'description' => $this->i18n('help_finance_payments_after_action_unfinished',
                                               array('action' => $this->i18n('finance_payments_action_add')))),
            array('option_value' => 'finished',
                  'label' => $this->i18n('finance_payments_after_action_finish',
                                         array('action' => $this->i18n('finance_payments_after_action_add_unfinished'))),
                  'description' => $this->i18n('help_finance_payments_after_action_finish',
                                               array('action' => $this->i18n('finance_payments_action_add')))));

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('finance_payments_add'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
