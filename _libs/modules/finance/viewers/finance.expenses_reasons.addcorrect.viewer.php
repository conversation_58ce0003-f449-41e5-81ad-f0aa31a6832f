<?php

class Finance_Expenses_Reasons_AddCorrect_Viewer extends Viewer {
    public $template = 'expenses_reasons_edit.html';

    public function prepare() {
        $this->model = $this->registry['finance_expenses_reason'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //get type of the parent document
        $filters = array('where' => array('fdt.id = ' . PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON),
                         'sanitize' => true);
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $this->data['type'] = $type;

        // GET DEPARTMENTS
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $departments = Departments::getTree($this->registry);
        $this->data['departments'] = $departments;

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups = Groups::getTree($this->registry);
        $this->data['groups'] = $groups;

        //prepare companies, offices, cashboxes/bank accounts
        // Prepare the payment type filter
        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $document_type_filters = array(
            'where'    => array('`fdt`.`id` = \'' . $this->model->get('type') . '\''),
            'sanitize' => true);
        $document_type = Finance_Documents_Types::searchOne($this->registry, $document_type_filters);
        // If the both payment types are selected into this finance document type
        //   and there is no payment amount or invoiced amount
        $this->model->getPaidAmount();
        $this->model->getInvoicedAmount();
        $paid_amount     = $this->model->get('paid_amount');
        $invoices_amount = $this->model->get('invoices_amount');
        if ($document_type && preg_match('/.+_.+/', $document_type->get('payment_way')) && $paid_amount == 0 && $invoices_amount == 0) {
            // No filter for payment type
            $payment_type = false;
        } else {
            // Show only the payment type that is selected into the model
            $payment_type = $this->model->get('payment_type');
        }
        require_once $this->modelsDir . 'finance.dropdown.php';
        $params = array($this->registry,
                        'lang'              => $this->model->get('model_lang'),
                        'company_id'        => $this->model->get('company'),
                        'office_id'         => $this->model->get('office'),
                        'payment_type'      => $payment_type,
                        'payment_direction' => 'expenses',
                        'active'            => 1);
        $companies_data = Finance_Dropdown::getCompaniesData($params);
        $this->data['companies_data'] = $companies_data;

        //prepare customer
        $customer_id = 0;
        if ($this->model->get('customer')) {
            $customer_id = $this->model->get('customer');
        }

        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer', $customer_id, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
            }
        }

        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array(
                'sanitize' => true,
                'model_lang' => $this->model->get('model_lang'),
                'where' => array(
                    'n.deleted IS NOT NULL',
                    'n.id = ' . $trademark_id
                ),
                //flag to search in customers trademarks
                'session_param' => 'filter_trademark_nomenclature'
            );
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        if ($this->model->get('project')) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('p.deleted IS NOT NULL',
                                              'p.id = ' . $this->model->get('project')));
            $project = Projects::searchOne($this->registry, $filters);
            $phases = array();
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);

                if ($project->get('stages')) {
                    $project->getIncludedStages();
                    foreach ($project->get('stages_info') as $status => $stages_status) {
                        foreach ($stages_status as $stg_id => $stage) {
                            if ($stage->get('finished') == '0000-00-00 00:00:00') {
                                $phases[] = array(
                                    'label' => $stage->get('name'),
                                    'option_value' => $stage->get('id'));
                            }
                            if ($stage->get('id') == $this->model->get('phase')) {
                                $this->model->set('phase_name', $stage->get('name'), true);
                            }
                        }
                    }
                }
                $this->data['phases'] = $phases;
            }
        }

        //prepare employee
        $employee_id = 0;
        if ($this->model->get('employee1')) {
            $employee_id = $this->model->get('employee1');
        }
        if ($employee_id) {
            require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
            $filters = array(
                'sanitize' => true,
                'where' => array(
                    'c.type = ' . PH_CUSTOMER_EMPLOYEE,
                    'c.id = ' . $employee_id
                )
            );
            $employee = Customers::searchOne($this->registry, $filters);
            if ($employee) {
                $this->model->set('employee1_code', $employee->get('code'), true);
                $this->model->set('employee1_name', $employee->get('name') . ' ' . $employee->get('lastname'), true);
                $this->model->set('employee1', $employee_id, true);
            } else {
                $this->model->set('employee1', '', true);
            }
        }
        $this->data['autocomplete_employee_filters'] = array('<type>' => (string)PH_CUSTOMER_EMPLOYEE);

        $gt2 = $this->model->get('grouping_table_2');

        //duplicate fields for price, quantity, surplus, and discount
        //as we have to know if one of the fields is changed and we must lock the other one
        //This is to prevent several changes in one row as we don't have
        //extreme math logic in the software
        $duplicate = array('quantity', $gt2['calculated_price'], 'discount_value', 'discount_percentage', 'surplus_value', 'surplus_percentage');
        foreach ($duplicate as $k => $d) {
            $gt2['vars'][$d . '_duplicate'] = $gt2['vars'][$d];
            $gt2['vars'][$d . '_duplicate']['hidden'] = 1;
            $gt2['vars'][$d . '_duplicate']['name'] = $d . '_duplicate';
            unset($gt2['vars'][$d . '_duplicate']['custom_class']);
            $gt2['vars'][$d]['js_methods']['onchange'] = 'preventCorrectionColisions(this)';
        }

        // if the 'calculated_price' field is hidden and after the last visible column,
        // set it as last_visible_column (display the +/- buttons in it)
        if ($gt2['vars'][$gt2['calculated_price']]['hidden'] &&
        $gt2['vars'][$gt2['last_visible_column']]['position'] < $gt2['vars'][$gt2['calculated_price']]['position']) {
            $gt2['last_visible_column'] = $gt2['calculated_price'];
        }
        $gt2['vars'][$gt2['calculated_price']]['hidden'] = 0;
        $gt2['vars'][$gt2['calculated_price']]['readonly'] = 0;

        $i = 0;
        $gt2['rows_readonly'] = array();
        $gt2['delimeter_start'] = true;
        $request = &$this->registry['request'];
        foreach ($gt2['values'] as $key => $val) {
            if (empty($val['article_id'])) {
                unset($gt2['values'][$key]);
                continue;
            }

            //get control values for duplicated fields
            if ($request->isPost()) {
                //from the request(after error)
                foreach ($duplicate as $d) {
                    $v = $request->get($d . '_duplicate');
                    $gt2['values'][$key][$d . '_duplicate'] = $v[$key];
                }
            } else {
                //from the original values
                foreach ($duplicate as $d) {
                    $gt2['values'][$key][$d . '_duplicate'] = $val[$d];
                }
            }

            if (in_array($key, $gt2['rows'])) {
                $i++;
                //mark all current rows as readonly
                $gt2['rows_readonly'][] = $i;
            }
        }

        $gt2['last_editable_row_index'] = $i;
        if ($this->model->get('advance')) {
            //advanced document - disable adding/deleting rows
            $gt2['hide_multiple_rows_buttons'] = true;
            $gt2['allow_readonly_delete'] = false;
            //set fields that will not be readonly for the readonly rows
            $gt2['fields_not_readonly'] = array($gt2['calculated_price']);
        } else {
            //add one empty row in the beginning
            //IMPORTANT!!! Do not change this to use array_unshift
            //we need all the keys as is
            $gt2['values'] = $gt2['values'] + array(array());
            //we want to be able to delete readonly rows
            $gt2['allow_readonly_delete'] = true;

            //set fields that will not be readonly for the readonly rows
            $gt2['fields_not_readonly'] = array('quantity', $gt2['calculated_price']);
        }
        if (!$this->model->get('date_of_payment') || !$this->registry['request']->isPost()) {
            //calculate date_of_payment
            $dop = array(
                'point' => 'start',
                'period_start' => $this->model->get('issue_date') ? $this->model->get('issue_date') : date('Y-m-d'),
                'count' => $type->get('default_date_of_payment_count'),
                'period_type' => $type->get('default_date_of_payment_period_type'),
                'direction' => 'after'
            );

            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $dop = Calendars_Calendar::calcDateBySettings($this->registry, $dop);
            $this->model->set('date_of_payment', $dop, true);
        }

        $this->model->set('grouping_table_2', $gt2, true);

        // prepare layout index (use reason type to get layouts)
        $this->prepareLayoutIndex();

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_expenses_reasons_addcorrect');
        $this->data['title'] = $title;
    }
}

?>
