<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='sections_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$tags_section->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_tags'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {foreach from=$tags_optgroups key='optgroup_name' item='optgroup'}
              <div class="t_caption2_title" style="padding: 5px 0;">{$optgroup_name|escape|default:"&nbsp;"}:</div>
              {foreach from=$optgroup item='option'}
                {if $option.option_value eq $option.value}
                  {if (isset($option.active_option) && $option.active_option == 0)}<span class="inactive_option" title="{#inactive_option#}">*{/if}
                  {if trim($option.label)}{$option.label|escape|default:'&nbsp;'}{else}<img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" />{/if}<br />
                  {if (isset($option.active_option) && $option.active_option == 0)}</span>{/if}
                {/if}
              {/foreach}
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_tag_limit'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$tags_section->get('tag_limit')|escape|default:0}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_place'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$tags_section->get('place')|escape|default:0}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$tags_section->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$tags_section}
</div>
