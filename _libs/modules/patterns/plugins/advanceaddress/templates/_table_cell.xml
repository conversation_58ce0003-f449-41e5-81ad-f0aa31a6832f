<w:tc>
    <w:tcPr>
        <w:vAlign w:val="{$cell->getVerticalAlign()}"/>
        {if $cell->getColspan()}
            <w:gridSpan w:val="{$cell->getColspan()}"/>
        {/if}
        {if !is_null($cell->getWidth())}
            <w:tcW w:w="{$cell->getWidth()}" w:type="dxa"/>
        {/if}
        {if $cell->getRowspan() gt 1}
            {$cell->getRowspanTailIncrement()}
            {if $cell->getRowspanTail() eq 1}<w:vMerge w:val="restart"/>{/if}
            {if $cell->getRowspanTail() gt 1}<w:vMerge w:val="continue"/>{/if}
        {/if}
        {if $cell->getBackgroundColor()}
            <w:shd w:val="clear" w:color="auto" w:fill="{$cell->getBackgroundColor()}"/>
        {/if}
    </w:tcPr>
    {if $cell->getRowspanTail() gt 1}
    <w:p/>
    {else}

    {if $cell->isPain()}
        {$cell->getContent()}
    {/if}
    <w:p>
        <w:pPr>
            <w:spacing w:after="0"/>
            {if $cell->getTextAlign()}<w:jc w:val="{$cell->getTextAlign()}"/>{/if}
            {* Do not split across pages *}
            {if !$cell->canSplit()}<w:keepNext/>{/if}
        </w:pPr>
        <w:r>
            <w:rPr>
                {if $cell->isBold()}<w:b/>{/if}
                {if $cell->getFontsize()}<w:sz w:val="{$cell->getFontsize()}"/>{/if}
            </w:rPr>{if !$cell->isPain()}
            <w:t>{$cell->getContent()}</w:t>{/if}
        </w:r>
    </w:p>
    {/if}
</w:tc>
