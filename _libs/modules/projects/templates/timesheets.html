<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="nopadding">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_info_header.html}
      </table>
    </td>
  </tr>
  <tr>
    <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="projects_timesheets_switch"><div class="switch_{if $smarty.cookies.projects_timesheets_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$title|escape}</div></td>
  </tr>
  <tr id="projects_timesheets"{if $smarty.cookies.projects_timesheets_box eq 'off'} style="display: none"{/if}>
    <td class="nopadding">
      <div id="panel_{$timesheets_session_param}">
        {if ($project->get('system_task_id'))}
          {include file=_timesheets_panel.html model_id=$project->get('system_task_id')}
        {else}
          <table cellpadding="3" cellspacing="3" border="0">
            <tr>
              <td valign="top" style="padding-left: 0; margin-left: 0; border-collapse: collapse; color:#FF0000;">
                {#projects_no_system_task#|escape}
              </td>
            </tr>
          </table>
        {/if}
      </div>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
</div>