<?php

class Projects_Timesheets_Viewer extends Viewer {
    public $template = 'timesheets.html';

    public function prepare() {

        //load tasks' lang files
        $i18n_files[] = sprintf('%s%s%s/%s',
            PH_MODULES_DIR,
            'tasks/i18n/',
            $this->registry['lang'],
            'tasks.ini');
        $this->loadCustomI18NFiles($i18n_files);

        //suffix for the session param
        $suffix = $this->module . '_' . $this->model->get('id');

        $session_param = 'timesheets_ajax_' . $suffix;

        //sets the parent module to mark that the ajax is called from projects module
        $this->data['parent_module'] = $this->module;

        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
        $system_task_id = Projects::getSystemTask($this->registry, $this->model->get('id'));

        $this->model->set('system_task_id', $system_task_id, true);

        //prepare the info for the system task
        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
        $tasks_filter = array ('where' => array('t.id=' . $system_task_id,
                                                't.type=' . PH_TASK_SYSTEM_TYPE),
                               'sanitize' => true);
        $this->data['task'] = Tasks::searchOne($this->registry, $tasks_filter);

        //search the timesheets for the current system task
        require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheets.factory.php';
        $filters = Tasks_Timesheets::saveSearchParams(
                    $this->registry,
                    array(
                        'where' => array('tt.task_id = ' . $system_task_id),
                        'sanitize' => true),
                    $session_param);
        list($timesheets, $pagination) = Tasks_Timesheets::pagedSearch($this->registry, $filters);

        $this->data['timesheets'] = $timesheets;
        $this->data['pagination'] = $pagination;
        $this->data['timesheets_use_ajax'] = true;
        $this->data['timesheets_session_param'] = $session_param;
        $sort_base_link = sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'], 'tasks',
            $this->registry['controller_param'], 'timesheets',
            'timesheets', 'list',
            'model_id', $system_task_id,
            'parent_module', $this->data['parent_module']);

        $this->getSortables(array('module' => 'tasks', 'controller' => 'timesheets'));

        $this->data['timesheets_sort'] = $this->prepareAjaxSort($filters, $session_param, $session_param, $sort_base_link, true);

        require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheetsconfigurators.factory.php';
        $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                array('where' => array('tc.added_by=' . $this->registry['currentUser']->get('id'),
                                       'tc.model_type=\'' . $this->model->get('type') . '\'',
                                       'tc.model=\'' . strtolower($this->model->modelName) . '\''),
                      'model_lang' => $this->registry['lang']));
        $configTimesheetPatterns = array();
        foreach ($timesheetsConfigurators as $timesheetConfigurator) {
            $configTimesheetPatterns[] = array('label' => $timesheetConfigurator->get('name'),
                                               'option_value' => $timesheetConfigurator->get('id'));
        }
        $this->data['configTimesheetPatterns'] = $configTimesheetPatterns;

        $timesheet = Tasks_Timesheets::buildModel($this->registry);

        //get activities
        $this->data['activities'] = Dropdown::getTasksTimesheetsActivities(array($this->registry));
        if ($this->data['activities'] && !$timesheet->get('activity')) {
            $timesheet->set('activity', $this->data['activities'][0]['option_value'], true);
        }

        // load data from saved configuration into timesheet
        if ($this->registry['request']->get('configurator', 'get')) {
            $timesheet->loadFromConfig($this->registry['request']->get('configurator', 'get'),
                                       strtolower($this->model->modelName),
                                       $this->model->get('type'));
        }

        //default period type is 'dates'
        $period_type = $this->registry['request']->get('period_type', 'get') ?: 'dates';
        if ($this->registry['request']->get('period_type', 'get') || !$timesheet->get('period_type')) {
            $timesheet->set('period_type', $period_type, true);
        }
        if ($this->registry['request']->get('startperiod', 'get')) {
            $timesheet->set('startperiod_dates', $this->registry['request']->get('startperiod', 'get'), true);
            $timesheet->set('startperiod_period', $this->registry['request']->get('startperiod', 'get'), true);
        }
        if ($this->registry['request']->get('endperiod', 'get')) {
            $timesheet->set('endperiod_dates', $this->registry['request']->get('endperiod', 'get'), true);
            $timesheet->set('endperiod_period', $this->registry['request']->get('endperiod', 'get'), true);
        }
        if ($this->registry['request']->get('subject', 'get') == 'timesheet_activity') {
            foreach ($this->data['activities'] as $act_option) {
                if ($act_option['option_value'] == $timesheet->get('activity')) {
                    $timesheet->set('subject', $act_option['label'], true);
                    break;
                }
            }
        }

        /* $resources = array('human' => $this->i18n('tasks_timesheets_human'),
                           'machine' => $this->i18n('tasks_timesheets_machine'),
                           'equipment' => $this->i18n('tasks_timesheets_equipment'));
        $this->data['resources'] = $resources; */

        // prepare assignments of parent model
        $this->data['user_id'] = $timesheet->getParentAssignments($this->model);

        //prepare offices
        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'sanitize' => true);
        $offices = Offices::search($this->registry, $filters);

        $_options_offices = array();
        foreach($offices as $office) {
            $_options_offices[] = array(
                'active_option' => $office->get('active'),
                'label' => $office->get('name'),
                'option_value' => $office->get('id'));
        }
        $this->data['offices'] = $_options_offices;

        $this->data['timesheet'] = $timesheet->sanitize();

        // data for management of saved configurations
        $this->data['model_type'] = $this->model->get('type');
        $this->data['model_name'] = strtolower($this->model->modelName);
        $this->data['parent_model_id'] = $this->model->get('id');

        $this->prepareTranslations();
        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('projects_timesheets'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
