<?php
require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
require_once PH_MODULES_DIR . 'projects/models/projects.audit.php';

class Projects_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Project';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Projects';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'phases',
        'clone', 'export', 'timesheets',
        'create', 'documents', 'tasks', 'events', 'generate', 'attachments',
        'setstatus', 'assign', 'tag', 'remind',
        'relatives', 'history', 'comments', 'emails', 'minitasks', 'communications',
        'manage_outlooks', 'print', 'printlist'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'timesheets', 'addtimesheet',
        'relatives', 'history', 'communications'
    );

    /**
     * Action definitions for the up right menu
     */
    public $actionDefinitionsUpRight = array(
        'print', 'manage_outlooks', 'printlist'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate', 'phases',
        'assign', 'attachments', 'relatives'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_select',
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'p.type';

    /**
     * Actions where side panels for model can be displayed
     */
    public static $actionsSidePanel = array('view', 'edit');

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'audit':
            $this->_audit();
            break;
        case 'add':
            $this->_add();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assign':
            $this->_getAssignments();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'addquick':
            $this->_addQuick();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
        case 'managevars':
            $this->_edit();
            break;
        case 'phases':
            $this->_phases();
            break;
        case 'view':
        case 'viewvars':
            $this->_view();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'history':
            $this->_history();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'timesheets':
            $this->_timesheets();
            break;
        case 'calculate':
            $this->_calculate();
            break;
        case 'print':
            $this->_print();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'generate':
            $this->_generate();
            break;
        case 'filter':
            $this->_filter();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'ajax_select':
            $this->_select();
            break;
        case 'ajax_watch':
            $this->_manageStopWatch();
            break;
        case 'search':
            $this->_search();
            break;
        case 'create':
            $this->_create();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'remind':
            $this->_remind();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'franky':
            $this->_franky();
            break;
        case 'savegroupvar':
            $this->_saveGroupVar();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'calculatedeadlines':
            $this->_calculateDeadlines();
            break;
        case 'ajax_show_customers_info':
            $this->_showCustomersInfo();
            break;
        case 'ajax_show_last_records':
            $this->_showLastRecords();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'revision':
            $this->_revision();
            break;
        case 'createsystasks':
            $this->_createSystemTasks();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'ajax_phases':
            $this->_getPhases();
            break;
        case 'ajax_sidepanel':
            $this->_sidePanel();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Redirect to specified module at ADD section and auto complete customer data
     */
    private function _create() {
        $request = &$this->registry['request'];

        $module = $request->get('operation');
        switch ($module) {
            case 'documents':
                $add_link = sprintf('operation=add&type=%d&customer=%d&project=%d',
                                        $request->get('document_type'), $request->get('create_from_customer_id'), $request->get('create_from_project_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'tasks':
                $add_link = sprintf('type=%d&customer=%d&project=%d',
                                        $request->get('task_type'), $request->get('create_from_customer_id'), $request->get('create_from_project_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'events':
                $add_link = sprintf('type=%d&customer=%d&project=%d',
                                    $request->get('event_type'), $request->get('create_from_customer_id'), $request->get('create_from_project_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'minitasks':
                $add_link = sprintf('communications=%d&communication_type=minitasks&operation=add#communications_container',
                                    $request->get('create_from_project_id'));
                $this->redirect($this->module, 'communications', $add_link);
                break;
        }
        return true;
    }

    /**
     * Attaches files to the project
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $this->registry->set('getAssignments', true, true);
        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);
        if ($project) {
            $project->getAttachments();
        }

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $modified_files = array();

            $erred_modified_files = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $project->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);

                        //explain the failed upload with more details
                        foreach (FilesLib::$_errors as $err) {
                            $this->registry['messages']->setError($err);
                        }
                        FilesLib::$_errors = array();
                    }

                    $modified_files[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files)) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $project->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setError($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }

                    $added_files[$idx] = $params;
                }
            }


            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $filters = array('where' => array('p.id = ' . $project->get('id')),
                                 'model_lang' => $project->get('model_lang'));
                $project_attached_files = Projects::searchOne($this->registry, $filters);
                $project_attached_files->getAttachments();
                $project_attached_files->sanitize();

                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'add_attachments', 'new_model' => $project_attached_files, 'old_model' => $project));
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files)) {
                $this->actionCompleted = true;
            }

        }

        $this->registry['added_files'] = $added_files;

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            if (! $project->get('registry')) {
                $project->unsanitize();
            }

            $project->getAttachments();
            $project->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated project file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            if (is_numeric($request->get('file'))) {
                $file_id = $request->get('file');
            } else {
                $file_id = General::decrypt($request->get('file'), '_' . $this->action . '_', 'xtea');
            }

            $filters = array('where' => array('f.id = ' . $file_id),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                    }
                    break;
                case 'delfile':
                    // get the files info needed for the audit
                    $old_project = clone $project;
                    $old_project->getAttachments();

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('p.id = ' . $project->get('id')),
                                         'model_lang' => $project->get('model_lang'));
                        $project_del_files = Projects::searchOne($this->registry, $filters);
                        $project_del_files->getAttachments();
                        $project_del_files->sanitize();

                        Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'del_attachments', 'new_model' => $project_del_files, 'old_model' => $old_project));
                        $this->registry['messages']->setMessage($this->i18n('message_projects_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_projects_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    // get the files info needed for the audit
                    $old_project = clone $project;
                    $old_project->getAttachments();

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('p.id = ' . $project->get('id')),
                                         'model_lang' => $project->get('model_lang')
                                        );
                        $project_del_files = Projects::searchOne($this->registry, $filters);
                        $project_del_files->getAttachments();
                        $project_del_files->sanitize();

                        Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'del_attachments', 'new_model' => $project_del_files, 'old_model' => $old_project));

                        $this->registry['messages']->setMessage($this->i18n('message_projects_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_projects_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * assign project
     */
    protected function _assign() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (empty($project)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        //assign from tab
        if ($request->isPost()) {
            if (!empty($project)) {
                $this->old_model = clone $project;
                $this->old_model->sanitize();

                $project->set('assignments_type', $request->get('assignments_type'), true);
                $project->set('departments_assignments', $request->get('departments_assignments') ?: array(), true);
                $project->set('users_assignments', $request->get('users_assignments') ?: array(), true);

                if ($project->updateAssignments()) {
                    $this->registry['messages']->setMessage($this->i18n('message_projects_assign_success', array($project->getModelTypeName())), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    $filters = array('where' => array('p.id = ' . $request->get('id')),
                                     'model_lang' => $request->get('model_lang'));
                    $new_project = Projects::searchOne($this->registry, $filters);

                    Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'assign', 'new_model' => $new_project, 'old_model' => $this->old_model));
                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_projects_assign_failed', array($project->getModelTypeName())));
                }
            }
        // modifying assignments from lightbox
        } elseif ($request->get('a_type')) {
            if (!empty($project)) {
                $this->old_model = clone $project;
                $this->old_model->sanitize();

                $id = $request->get($this->action);

                $project->set('assignments_type', $request->get('assignments_type'), true);
                $project->set('departments_assignments', $request->get('departments_assignments') ?: array(), true);
                $project->set('users_assignments', $request->get('users_assignments') ?: array(), true);

                if ($project->updateAssignments()) {
                    $this->registry['messages']->setMessage($this->i18n('message_projects_assign_success', array($project->getModelTypeName())), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    $filters = array('where' => array('p.id = ' . $id),
                                     'model_lang' => $request->get('model_lang'));
                    $new_project = Projects::searchOne($this->registry, $filters);

                    Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'assign', 'new_model' => $new_project, 'old_model' => $this->old_model));
                    $this->actionCompleted = true;
                    if (!$this->registry->isRegistered('project')) {
                        $this->registry->set('project', $project->sanitize());
                    }

                    //manually set custom after action so that the navigation is redirected to previous action or list mode
                    $this->registry['messages']->insertInSession($this->registry);

                    if (!empty($_SERVER['HTTP_REFERER'])) {
                        preg_match('/&projects=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
                    }
                    if (isset($matches[1])) {
                        $after_action = $matches[1];
                    } else {
                        $after_action = 'list';
                    }
                    //set after action to view if not having permission
                    if (!$project->checkPermissions($after_action)) {
                        $after_action = 'list';
                    }

                    $request->set('after_action', $after_action, 'get', true);
                    if (!empty($_SERVER['HTTP_REFERER'])) {
                        $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
                    }
                    $this->registry->set('exit_after', true, true);

                    return true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_projects_assign_failed', array($project->getModelTypeName())), '', -1);
                }
            }
        }

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            $project->getAssignments();
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Prepares a type of assignment for model
     */
    public function _getAssignments() {
        $request = &$this->registry['request'];

        //get the requested model ID and assignment type
        $id = $request->get('id');
        $a_type = $request->get('a_type');

        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);
            //$project->getAssignments();
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        //prepare assignments array
        $assignments_type = $project->get('assignments_type');
/*
        if (!$this->registry['request']->isPost()) {
            $departments_assignments = @array_keys($this->model->get('departments_assignments'));
            $users_assignments = @array_keys($this->model->get('users_assignments'));
        } else {*/
            $departments_assignments = @array_keys($project->get('departments_assignments'));
            $users_assignments = @array_keys($project->get('users_assignments'));
//        }

        //prepare users array
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $project->get('lang'),
                         'where' => array('u.hidden = 0', 'u.active = 1'),
                         'sort' => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'));
        $users_obj = Users::search($this->registry, $filters);

        $users_optgroups = array('normal_users' => array(), 'portal_users' => array());
        $m_value = array();
        foreach ($users_obj as $obj) {
            if (is_array($users_assignments) && in_array($obj->get('id'), $users_assignments)) {
                $m_value[] = $obj->get('id');
            }

            $optgroup_label = ($obj->get('is_portal')) ? 'portal_users' : 'normal_users';
            $users_optgroups[$optgroup_label][] = array(
                    'label'        => $obj->get('firstname') . ' ' . $obj->get('lastname'),
                    'option_value' => $obj->get('id'),
                    'value'        => (is_array($users_assignments) && in_array($obj->get('id'), $users_assignments)) ? $obj->get('id') : '');
        }

        if (!empty($users_optgroups['normal_users'])) {
            usort($users_optgroups['normal_users'], array('parent', 'sortAssignments'));
        }
        if (!empty($users_optgroups['portal_users'])) {
            usort($users_optgroups['portal_users'], array('parent', 'sortAssignments'));
        }
        $users = array (
            'name'     => 'users_assignments',
            'type'     => 'checkbox_group',
            'label'    => $this->i18n('projects_assignments'),
            'help'     => $this->i18n('help_projects_assignments'),
            'optgroups'  => $users_optgroups,
            'controll_all' => true,
            'value'    => $m_value);

        //prepare departments array
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $filters = array('model_lang' => $project->get('lang'));
        $departments_obj = Departments::getTree($this->registry, $filters);

        $departments_options = array();
        $m_value = array();
        foreach ($departments_obj as $obj) {
            $departments_options[] = array(
                'level'        => $obj->get('level'),
                'label'        => $obj->get('name'),
                'option_value' => $obj->get('id'),
                'active'       => $obj->isActivated(),
                'deleted'      => $obj->isDeleted(),
                'value'        => (isset($departments_assignments[$obj->get('id')]) ? $obj->get('id') : ''));
            if (@$assignments_type == 'Departments' && is_array($departments_assignments) && in_array($obj->get('id'), $departments_assignments)) {
                $m_value[] = $obj->get('id');
            }
        }
        $departments = array (
            'name'     => 'departments_assignments',
            'type'     => 'checkbox_tree',
            'label'    => $this->i18n('projects_assignments'),
            'help'     => $this->i18n('projects_assignments'),
            'options'  => $departments_options,
            'controll_all' => true,
            'value'    => $m_value);

        $this->registry['include_tree'] = true;

        $setassign['options'] = array('label' => $this->i18n('projects_assign_btn'));
        $setassign['a_type'] = $a_type;
        $setassign['model_id'] = $project->get('id');
        $setassign['module'] = 'projects';
        $setassign['action'] = 'assign';
        $setassign['module_param'] = $this->registry['module_param'];
        $setassign['show_form'] = 1;
        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'projects/templates/_action_assign.html');
        $this->viewer->data['project'] = $project;
        $this->viewer->data['available_action'] = $setassign;
        $this->viewer->data['assignments_type'] = $assignments_type;
        $this->viewer->data['departments_assignments'] = $departments;
        $this->viewer->data['users'] = $users;
        $this->viewer->display();
        exit;
    }

    /**
     * Relatives of the document
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('p.id = ' . $id));
        if ($model_lang = $request->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $project = Projects::searchOne($this->registry, $filters);
        if (!empty($project)) {
            if ($request->isPost()) {
                $project->set('referers', $request->get('referers'), true);
                if ($project->updateRelatives()) {
                    $this->registry['messages']->setMessage($this->i18n('message_relatives_success'), '', -2);
                    $this->registry['messages']->insertInSession($this->registry);

                    //the model was successfully saved set action as completed
                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_relatives_failed'), '', -1);
                }
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            $project->getParents();
            $project->getAssignments();

            //get the relatives tree
            $this->registry->set('relatives_tree', $project->getRelativesTree());

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of the project
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'p.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $project = Projects::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($project && $this->checkAccessOwnership($project, false)) {
                if (!$this->registry->isRegistered('project')) {
                    $this->registry->set('project', $project->sanitize());
                }

                require_once $this->viewersDir . 'projects.history.viewer.php';
                $viewer = new Projects_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Communication concerning the document (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $this->registry->set('getAssignments', true, true);
        $filters = array('where' => array('p.id = ' . $id ));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $project = Projects::searchOne($this->registry, $filters);
        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Timesheets for the projects
     */
    private function _timesheets() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('p.id = ' . $id));
        $this->registry->set('getAssignments', true, true);
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $project = Projects::searchOne($this->registry, $filters);
        if (!empty($project)) {
            if (!$project->checkPermissions('viewtimesheets')) {
                $this->redirect($this->module, 'view', 'view=' . $project->get('id'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST?$request->get('id'):$request->get('print'));
        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            if (!$request->get('pattern')) {
                //show error no such model
                $this->registry['messages']->setError($this->i18n('error_print_no_default_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $request->get('pattern')),
                             'sanitize' => true,
                             'model_lang' => $request->get('model_lang'));
            $pattern = Patterns::searchOne($this->registry, $filters);

            if (!empty($pattern)) {
                $patterns_vars = $project->getPatternsVars();
                $project->extender = new Extender();
                $project->extender->model_lang = $project->get('model_lang');
                $project->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $project->extender->add($key, $value);
                }

                if ($pattern->get('force_generate')) {
                    //generate and save file and get its id
                    $this->old_model = clone $project;

                    $result = false;
                    if ($pattern->get('not_regenerate_finished_record') && $project->get('status')=='finished') {
                        $previous_generated_file = $project->getLastGeneratedFile($pattern);

                        if ($previous_generated_file) {
                            $result = $previous_generated_file->get('id');
                        }
                    }

                    if (!$result) {
                        if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                            $result = $project->generateXLS();
                        } elseif (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                            $result = $project->generateDOCX();
                        } else {
                            $result = $project->generatePDF();
                        }
                    }

                    if ($result) {
                        $project->set('file_id', $result, true);
                        if (!$this->registry->isRegistered('project')) {
                            $this->registry->set('project', $project->sanitize());
                        }

                        //save history
                        require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
                        Projects_History::saveData($this->registry, array('model' => $project,
                                                                          'action_type' => 'print',
                                                                          'pattern' => $pattern->get('id'),
                                                                          'generated_file' => $result));

                        // show the file
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                        $file_id = $request->get('file');
                        $filters = array('where'    => array('f.id = ' . $result),
                                         'sanitize' => true,
                                         'archive'  => $project->get('archived_by'));
                        $file = Files::searchOne($this->registry, $filters);

                        $file->viewFile();
                        exit;
                    }
                } else {
                    Projects_History::saveData($this->registry, array('model' => $project,
                                                                      'action_type' => 'print',
                                                                      'pattern' => $pattern->get('id'),
                                                                      'generated_file' => false));
                    $project->unsanitize();
                    if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $project->generateXLS('browser_mode');
                    } elseif (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $project->generateDOCX('browser_mode');
                    } else {
                        //generate file to the browser window
                        $result = $project->generatePDF('browser_mode');
                    }
                }
            } else {
                $result = false;
            }

            if ($result) {
                //the content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_projects_print_document'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $project->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     *                  or crypted string containing from_id and to_id
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&projects=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Projects::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $projects = array();
        if ($ids) {
            $filters = array('where' => array('p.id IN (' . implode(', ', $ids) . ')'),
                                              'model_lang' => $this->registry->get('lang'),
                                              'sanitize' => true);
            $projects = Projects::search($this->registry, $filters);
        }

        //if no models
        if (empty($projects) || count($ids) != count($projects)) {
            $this->registry['messages']->setError($this->i18n('error_no_projects_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $projects[0]->get('type');
        foreach ($projects as $project) {
            $type_i = $project->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'projects.types.factory.php';
        $filters = array('where' => array('pt.id = ' . $type),
                         'sanitize' => true);
        $doc_type = Projects_Types::searchOne($this->registry, $filters);
        $type_name_plural = $doc_type && $doc_type->get('name_plural') ? $doc_type->get('name_plural') : $this->i18n('projects');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_projects_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_projects_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                                          'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_projects_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_projects_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Projects::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_projects_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _generate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST?$request->get('id'):$request->get('generate'));
        $filters = array('where'      => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (!empty($project)) {
            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_projects_generate_document'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_projects_generate_invalid_pattern'));

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $project->get('id'));
            }

            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.active = 1'),
                                              'model_lang' => $request->get('model_lang'),
                                              'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_projects_generate_document'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_projects_generate_invalid_pattern'));

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $project->get('id'));
            }

            //get all generated files for the selected pattern
            $files = $project->getGeneratedFiles(array('pattern_id' => $pattern_id));

            //get file details
            $revision_id = $request->get('revision');
            if ($revision_id && $files) {
                foreach ($files as $file) {
                    if ($file->get('id') == $revision_id) {
                        //prepare selected revision details
                        $project->set('revision', $file, true);
                        break;
                    }
                }
            }

            //get revision details
            if (!empty($files)) {
                $project->set('revisions', $files, true);
            }
        }

        if ($request->isPost()) {
            if (!empty($project)) {
                $patterns_vars = $project->getPatternsVars();
                $project->extender = new Extender();
                $project->extender->model_lang = $project->get('model_lang');
                $project->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $project->extender->add($key, $value);
                }
                switch ($pattern->get('format')) {
                    case 'xls':
                    case 'xlsx':
                        if ($file_id = $project->generateXLS()) {
                            $this->registry['messages']->setMessage($this->i18n('message_projects_generate_success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            //save history
                            require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
                            Projects_History::saveData($this->registry, array('model' => $project,
                                                                              'action_type' => 'generate',
                                                                              'pattern' => $pattern->get('id'),
                                                                              'generated_file' => $file_id));

                            $this->actionCompleted = true;
                            $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                        } else {
                            $this->registry['messages']->setError($this->i18n('error_projects_generate_document'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);
                        }
                        break;
                    case 'docx':
                    case 'docx2pdf':
                        $browser_mode = $request->get('submit_button') == 'preview';
                        if ($file_id = $project->generateDOCX($browser_mode)) {
                            $this->registry['messages']->setMessage($this->i18n('message_projects_generate_success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            //save history
                            require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
                            Projects_History::saveData($this->registry, array('model' => $project,
                                                                               'action_type' => 'generate',
                                                                               'pattern' => $pattern->get('id'),
                                                                               'generated_file' => $file_id));

                            $this->actionCompleted = true;
                            $this->redirect($this->module, 'attachments', 'attachments=' . $project->get('id'));
                        } else {
                            $this->registry['messages']->setError($this->i18n('error_projects_generate_document'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            $this->redirect($this->module, 'view', array('view' => $project->get('id')));
                        }
                        break;
                    case 'csv':
                        // TO DO add option fields and also create a pattern which indicates the header columns
                        break;
                    case 'pdf':
                    default:
                        $this->old_model = clone $project;
                        if ($file_id = $project->generatePDF()) {
                            if ($request->get('submit_button') != 'preview') {
                                $this->registry['messages']->setMessage($this->i18n('message_projects_generate_success'), '', -1);
                                $this->registry['messages']->insertInSession($this->registry);

                                //save history
                                require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
                                Projects_History::saveData($this->registry, array('model' => $project,
                                                                                  'action_type' => 'generate',
                                                                                  'pattern' => $pattern->get('id'),
                                                                                  'generated_file' => $file_id));

                                $project->set('file_id', $file_id, true);
                                $this->actionCompleted = true;
                            }
                        } else {
                            $this->registry['messages']->setError($this->i18n('error_projects_generate_document'), '', -1);
                        }
                        break;
                }
            }
        }

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            //register pattern to the registry
            if ($pattern) {
                $this->registry->set('pattern', $pattern->sanitize());
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        // make sure action in registry is 'add'
        $this->setAction('add');

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            require_once PH_MODULES_DIR . 'projects/models/projects.types.factory.php';
            $filters = array('where' => array('pt.id = ' . $type_id,
                                              'pt.active = 1'),
                             'sanitize' => true);
            $type = Projects_Types::searchOne($this->registry, $filters);
        }

        $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'add') : false;

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('project');
            $this->registry['messages']->setError($this->i18n('error_projects_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $project = Projects::buildModel($this->registry);

            //sets the properties of the type to the current model
            $project->set('generate_system_task', $type->get('generate_system_task'), true);
            $project->set('available_planned_budget', $type->get('planned_budget'), true);
            $project->set('available_working_hours', $type->get('working_hours'), true);
            $project->set('type_name', $type->get('name'), true);

            // build a blank model, just set the type to get the additional variables
            $old_project = new Project($this->registry, array('type' => $project->get('type')));
            // get additional vars and values:
            // get old vars before post
            $this->registry->set('get_old_vars', true, true);
            $old_project->getVars();

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $project->unsetVars();
            $project->getVars();

            // check transition
            $old_project->new_model = $project;
            $trans = $this->checkTransition($old_project);
            unset($old_project->new_model);

            if ($trans && $project->save()) {

                $this->old_model = $old_project->sanitize();

                $filters = array('where' => array('p.id = ' . $project->get('id')),
                                 'model_lang' => $project->get('model_lang'));
                $new_project = Projects::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_project->getVars();

                $audit_parent = Projects_History::saveData($this->registry,
                                                           array('model' => $project,
                                                                 'action_type' => 'add',
                                                                 'new_model' => $new_project,
                                                                 'old_model' => $this->old_model));

                //update assignments if any
                if ($request->get('departments_assignments') || $request->get('users_assignments')) {
                    //get the project from previously saved object
                    $project = $new_project;
                    $project->set('assignments_type', $request->get('assignments_type'), true);
                    if ($request->get('departments_assignments')) {
                        $project->set('departments_assignments', $request->get('departments_assignments'), true);
                    }
                    if ($request->get('users_assignments')) {
                        $project->set('users_assignments', $request->get('users_assignments'), true);
                    }
                    $this->old_model->set('assignments_type', $request->get('assignments_type'), true);
                    $this->old_model->set('departments_assignments', array(), true);
                    $this->old_model->set('users_assignments', array(), true);

                    if ($project->updateAssignments()) {
                        $this->registry['messages']->setMessage($this->i18n('message_projects_assign_success', array($new_project->getModelTypeName())), '', 0);

                        $filters = array('where' => array('p.id = ' . $project->get('id')),
                                                          'model_lang' => $project->get('model_lang'));
                        $assigned_project = Projects::searchOne($this->registry, $filters);

                        Projects_History::saveData($this->registry,
                                                   array('model' => $project,
                                                         'action_type' => 'assign',
                                                         'new_model' => $assigned_project,
                                                         'old_model' => $this->old_model));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_projects_assign_failed', array($new_project->getModelTypeName())));
                    }
                }

                if ($project->get('create_from_customer') && ($project->get('customer') == $project->get('create_from_customer'))) {
                    // write history for the customer
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                    $filters_customer = array('where' => array('c.id = ' . $project->get('customer')),
                                              'model_lang' => $request->get('model_lang'));
                    $customer = Customers::searchOne($this->registry, $filters_customer);

                    $empty_customer_model = Customers::buildModel($this->registry);
                    $empty_customer_model->sanitize();

                    $customer->set('created_project_code', $new_project->get('code'), true);
                    $customer->set('created_project_name', $new_project->get('name'), true);

                    Customers_History::saveData($this->registry, array('model'        => $customer,
                                                                       'new_model'    => $customer,
                                                                       'old_model'    => $empty_customer_model,
                                                                       'action_type'  => 'create_project',
                                                                       'project_type' => $new_project->get('type_name')));
                }

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_projects_add_success', array($new_project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //get the project from previously saved object
                $project = $new_project;
            } else {
                if ($request->get('update_relatives') && $request['referers']) {
                    $project->getParentNames($request['referers']);
                } else {
                    $project->set('referers', '', true);
                }

                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_projects_add_failed', array($project->getModelTypeName())), '', -1);
            }
        } else {
            //create empty project model
            $project = Projects::buildModel($this->registry);

            if (!$project->get('model_id')) {
                $project->set('model_id', time(), true);
            }

            //set default values from the project type
            $project->set('group', $type->getDefaultGroup(), true);
            $project->set('generate_system_task', $type->get('generate_system_task'), true);
            $project->set('available_planned_budget', $type->get('planned_budget'), true);
            $project->set('available_working_hours', $type->get('working_hours'), true);
            $project->set('type_name', $type->get('name'), true);
            $project->set('priority', 'medium', true);
            // Set the default value for the name of this project type
            $project->set('name', $type->get('default_name'), true);
            if ($project->get('customer') && $project->get('customer_create')) {
                // if customer is set in the GET string then the project has been created from customer
                $project->set('create_from_customer', $project->get('customer'), true);
            }
            //set default value of the manager
            if (!$project->get('manager') && $this->registry['currentUser']->get('id')) {
                $project->set('manager', $this->registry['currentUser']->get('id'), true);
            }
        }

        if (!empty($project)) {

            if (!$this->actionCompleted) {
                // get additional variables
                $project->getVarsForTemplate();
            }

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('project', $project->sanitize());
        }

        return true;
    }

    /**
     * Add a single model
     */
    private function _addQuick() {
        $request = &$this->registry['request'];

        $autocomplete_params = array();
        if ($request->get('autocomplete_filter')) {
            if ($request->get('addquick_type')) {
                $autocomplete_params['addquick_type'] = $request->get('addquick_type');
            }
        } else if ($this->registry['session']->get($request['uniqid'], 'autocomplete_params')) {
            $autocomplete_params = $this->registry['session']->get($request['uniqid'], 'autocomplete_params');
        } else if ($request->get('autocomplete_params')) {
            $autocomplete_params = json_decode($request->get('autocomplete_params'), true);
        }

        require_once($this->modelsDir . 'projects.types.factory.php');

        $filters = array('where' => array('pt.active=1'),
                         'sort' => array('pti18n.name ASC'),
                         'sanitize' => true);
        if (isset($autocomplete_params['addquick_type'])) {
            $filters['where'][] = 'pt.id IN (' . implode(',', $autocomplete_params['addquick_type']) . ')';
        }
        $projectsTypes = Projects_Types::search($this->registry, $filters);

        foreach ($projectsTypes as $key => $type) {
            if (! $this->checkActionPermissions($this->module . $type->get('id'), 'add')) {
                unset($projectsTypes[$key]);
            }
        }

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if ($type_id) {
            foreach ($projectsTypes as $key => $prj_type) {
                if ($key == 0) {
                    $type = $prj_type;
                }
                if ($prj_type->get('id') == $type_id) {
                    $type = $prj_type;
                }
            }
        } else {
            $type = reset($projectsTypes);
        }
        $this->registry->set('current_type', $type, true);
        $this->registry->set('projects_types', $projectsTypes, true);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $project = Projects::buildModel($this->registry);
            $project->set('generate_system_task', $type->get('generate_system_task'), true);
            $project->set('available_planned_budget', $type->get('planned_budget'), true);
            $project->set('available_working_hours', $type->get('working_hours'), true);
            $project->set('type_name', $type->get('name'), true);

            // build a blank model, just set the type to get the additional variables
            $old_project = new Project($this->registry, array('type' => $project->get('type')));
            // get additional vars and values:
            // get old vars before post
            $this->registry->set('get_old_vars', true, true);
            $old_project->getVars();

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $project->unsetVars();
            $project->getVars();

            // check transition
            $old_project->new_model = $project;
            $trans = $this->checkTransition($old_project);
            unset($old_project->new_model);

            if ($trans && $project->save()) {

                $this->old_model = $old_project->sanitize();

                $filters = array('where' => array('p.id = ' . $project->get('id')),
                                 'model_lang' => $project->get('model_lang'));
                $new_project = Projects::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_project->getVars();

                $audit_parent = Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'add', 'new_model' => $new_project, 'old_model' => $this->old_model));

                //update assignments if any
                if ($request->get('departments_assignments') || $request->get('users_assignments')) {
                    //get the project from previously saved object
                    $project = $new_project;
                    $project->set('assignments_type', $request->get('assignments_type'), true);
                    if ($request->get('departments_assignments')) {
                        $project->set('departments_assignments', $request->get('departments_assignments'), true);
                    }
                    if ($request->get('users_assignments')) {
                        $project->set('users_assignments', $request->get('users_assignments'), true);
                    }
                    $this->old_model->set('assignments_type', $request->get('assignments_type'), true);
                    $this->old_model->set('departments_assignments', array(), true);
                    $this->old_model->set('users_assignments', array(), true);

                    if ($project->updateAssignments()) {
                        $this->registry['messages']->setMessage($this->i18n('message_projects_assign_success', array($new_project->getModelTypeName())), '', 0);

                        $filters = array('where' => array('p.id = ' . $project->get('id')),
                                                          'model_lang' => $project->get('model_lang'));
                        $assigned_project = Projects::searchOne($this->registry, $filters);

                        Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'assign', 'new_model' => $assigned_project, 'old_model' => $this->old_model));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_projects_assign_failed', array($new_project->getModelTypeName())));
                    }
                }

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_projects_add_success', array($new_project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->registry['session']->remove($request['uniqid'], 'autocomplete_params');

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //get the project from previously saved object
                $project = $new_project;
            } else {
                if ($request->get('update_relatives') && $request['referers']) {
                    $project->getParentNames($request['referers']);
                } else {
                    $project->set('referers', '', true);
                }

                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_projects_add_failed', array($project->getModelTypeName())), '', -1);
            }
        }

        if (!$this->actionCompleted) {
            //create an empty project model
            if (!$request->isPost()) {
                $project = Projects::buildModel($this->registry);
                if (!$project->get('model_id')) {
                    $project->set('model_id', time(), true);
                }
            }

            if ($type) {
                //set default values from the project type
                if (!$request->isPost()) {
                    $project->set('group', $type->getDefaultGroup(), true);
                    // Set the default value for the name of this project type
                    $project->set('name', $type->get('default_name'), true);
                    $project->set('priority', 'medium', true);
                    //set default value of the manager
                    if (!$project->get('manager') && $this->registry['currentUser']->get('id')) {
                        $project->set('manager', $this->registry['currentUser']->get('id'), true);
                    }
                }

                $project->set('type', $type->get('id'), true);
                $project->set('type_name', $type->get('name'), true);
                $project->set('generate_system_task', $type->get('generate_system_task'), true);
                $project->set('available_planned_budget', $type->get('planned_budget'), true);
                $project->set('available_working_hours', $type->get('working_hours'), true);

                //prepare additional variables
                $project->getVarsForTemplate();
            }
        }

        if (!empty($project)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('project', $project->sanitize());
        }

        return true;
    }

    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // make sure action in registry is 'edit'
        $this->setAction('edit');

        $this->registry->set('getAssignments', true, true);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $project = Projects::buildModel($this->registry);
            // get old model from db
            $filters = array('where' => array('p.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_project = Projects::searchOne($this->registry, $filters);
            $old_props = array('status', 'generate_system_task', 'type_name',
                               'added', 'added_by', 'user_permissions', 'requires_completed_minitasks');
            foreach ($old_props as $prop) {
                $project->set($prop, $old_project->get($prop), true);
            }

            // get additional vars and values:
            // get old vars before post
            $this->registry->set('get_old_vars', true, true);
            $old_project->getVars();

            // get post vars
            $this->registry->set('get_old_vars', false, true);
            $project->unsetVars();
            $project->getVars();

            // check transition
            $old_project->new_model = $project;
            $trans = $this->checkTransition($old_project);
            unset($old_project->new_model);

            if ($trans && $project->save()) {
                $this->old_model = $old_project->sanitize();

                $filters = array('where' => array('p.id = ' . $project->get('id')),
                                 'model_lang' => $project->get('model_lang'));
                $new_project = Projects::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_project->getVars();

                $audit_parent = Projects_History::saveData($this->registry,
                                                           array('model' => $new_project,
                                                                 'action_type' => 'edit',
                                                                 'new_model' => $new_project,
                                                                 'old_model' => $old_project));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_projects_edit_success', array($new_project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //if the status of the project is 'closed' the system task for it is set to 'finished'
                if (($project->get('status') == 'closed_success') || ($project->get('status') == 'closed_failed')) {
                    $project->checkCompleteSystemTask();
                }

                //get the project from previously saved object
                $project = $new_project;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_projects_edit_failed', array($old_project->getModelTypeName())), '', -1);

                if ($request->get('update_relatives')) {
                    if ($request['referers']) {
                        $project->getParentNames($request['referers']);
                    } else {
                        $project->set('referers', '', true);
                    }
                } else {
                    $project->getParents();
                }
                $project->getAssignments();
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('p.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);

            if ($project) {
                //check access and ownership of the model
                $this->checkAccessOwnership($project);

                $project->getAssignments();
                $project->getParents();
            }
        }

        if (!empty($project)) {
            if (!$this->actionCompleted) {
                // get additional variables
                $project->getVarsForTemplate();
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Clone model
     */
    private function _clone() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        if (empty($project)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        } else {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            //check transition of the model
            $this->checkTransition($project, true);

            //clone
            $project->getVars();

            //the source model vars are stored in orig_vars,
            //to be used later when copying the additional vars
            $project->set('orig_vars', $project->get('vars'), true);

            //clear the model additional vars, because they are copied later
            $project->unsetVars();

            //current model lang
            $model_lang = $project->get('model_lang');
            //get translations of source model
            $langs = $project->getTranslations();
            // then remove them from model
            $project->unsetProperty('translations', true);

            //clear the id and set the source (original) id
            $project->set('id', null, true);
            $project->set('num', null, true);
            $project->set('origin_id', $id, true);
            $project->set('origin_code', $project->get('code'), true);
            $project->set('origin_name', $project->get('name'), true);

            //variable used to store the relatives type
            $project->set('clone_transform', 'cloned', true);

            // Check if the relation should be skipped
            if ($request->isRequested('skip_relatives')) {
                $project->set('skip_relatives', $request->get('skip_relatives'), true);
            }

            //do the clone
            if ($project->cloneModel()) {
                foreach ($langs as $t_lang) {
                    //copy other translations
                    if ($model_lang != $t_lang) {
                        $filters = array('where' => array('p.id = ' . $id,
                                                          'p.active = 1'),
                                         'model_lang' => $t_lang);
                        $t_project = Projects::searchOne($this->registry, $filters);
                        if (!empty($t_project)) {
                            $t_project->getVars();
                            $t_project->set('orig_vars', $t_project->get('vars'), true);
                            $t_project->unsetVars();
                            $t_project->set('id', $project->get('id'), true);
                            $t_project->slashesEscape();
                            $t_project->updateI18N();
                            $t_project->slashesStrip();
                            $t_project->copyVars($t_lang);
                        }
                    }
                }
                if ($project->slashesEscaped) {
                    $project->slashesStrip();
                }

                $filters = array('where' => array('p.id = ' . $project->get('id')),
                                 'model_lang' => $project->get('model_lang'));
                $new_project = Projects::searchOne($this->registry, $filters);
                $new_project->getVars();
                // data for audit
                $new_project->set('origin_full_num', $project->get('origin_code'), true);
                $new_project->set('origin_name', $project->get('origin_name'), true);

                $old_project = new Project($this->registry, array('type' => $project->get('type')));
                $old_project->getVars();
                $old_project->sanitize();

                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'clone', 'new_model' => $new_project, 'old_model' => $old_project));

                //clone is successful
                //redirect view cloned model
                $this->registry['messages']->setMessage($this->i18n('message_projects_clone_success', array($project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $project->get('id'));
            } else {
                //unsuccessful clone
                //redirect view parent model
                $this->registry['messages']->setError($this->i18n('error_projects_clone_failed', array($project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $id);
            }
        }
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        $this->registry->set('getAssignments', true, true);
        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            // get the complete model from db (in the new model_lang)
            $filters = array('where' => array('p.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);
            $project->getVars();

            $old_project = clone $project;
            $this->old_model = $old_project->sanitize();

            // set data from request into model
            foreach ($request->getAll() AS $k => $v) {
                $project->set($k, $v, true);
            }

            if ($project->save()) {
                $filters = array('where' => array('p.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_project = Projects::searchOne($this->registry, $filters);
                $new_project->getVars();

                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'translate', 'new_model' => $new_project, 'old_model' => $old_project));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_projects_translate_success', array($new_project->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_projects_translate_failed', array($old_project->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('p.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);

            if ($project) {
                //check access and ownership of the model
                $this->checkAccessOwnership($project);
            }
        }

        if (!empty($project)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('project', $project->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // make sure action in registry is 'view'
        $this->setAction('view');

        $filters = array('where' => array('p.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $this->registry->set('getAssignments', true, true);
        $project = Projects::searchOne($this->registry, $filters);

        if (!empty($project)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($project);

            $project->getVarsForTemplate();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $project->getParents();
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view/edit model's phases
     */
    private function _phases() {
        require_once PH_MODULES_DIR . 'projects/models/projects.stages.model.php';
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $this->registry->set('getAssignments', true, true);

        //check if details are submitted via POST
        if ($request->isPost()) {
            $project = Projects::buildModel($this->registry);
            $filters = array('where'      => array('p.id = ' . $project->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_project = Projects::searchOne($this->registry, $filters);

            //merge post with db model data
            $old_params = $old_project->getAll();
            $new_params = $project->getAll();

            if (isset($new_params['stages'])) {
                unset($new_params['stages']);
            }

            $params = array_merge($old_params, $new_params);
            $project = new Project($this->registry, $params);

            $project->getStages();
            $project->processPostStages();

            if ($project->valid && $project->updateStages()) {
                // action type for the history message
                $action_types = array();
                if ($request->get('stages_action')) {
                    if ($request->get('stages_action') == 'complete_stage_info') {
                        if ($request->get('stage_date_finish')) {
                            $action_types[] = 'finish_stage';
                        }
                        if ($request->get('start_stage')) {
                            $action_types[] = 'start_stage';
                        }
                    } elseif ($request->get('stages_action') == 'complete_stage_activities') {
                        if ($project->get('activities_history_completed')) {
                            //show message 'message_projects_edit_success'
                            $this->registry['messages']->setMessage($this->i18n('message_projects_stage_activities_finish_success'), '', -1);
                        }
                        $this->registry['messages']->insertInSession($this->registry);
                    }
                } else {
                    if ($request->get('start_stage')) {
                        $action_types[] = 'start_stages';
                    } else {
                        $action_types[] = 'stages';
                    }
                }

                if (!empty($action_types)) {
                    $new_project = Projects::searchOne($this->registry, array('where' => array('p.id = ' . $project->get('id'))));

                    // write history for all the taken actions
                    foreach ($action_types as $action_type) {
                        Projects_History::saveData($this->registry, array('model' => $new_project, 'action_type' => $action_type));
                    }

                    // prepare history if the status has been changed
                    if ($project->get('old_project_status') != $project->get('new_project_status')) {
                        $audit_parent = Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'status', 'new_model' => $new_project, 'old_model' => $old_project));

                        //send notification
                        $this->sendStatusNotification($new_project, $audit_parent);
                    }

                    $this->registry['messages']->setMessage($this->i18n('message_projects_stages_edit_success'), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);
                }

                $this->actionCompleted = true;
            } else {
                $this->registry['messages']->setError($this->i18n('error_projects_stages_edit_failed', array($project->getModelTypeName())), '', -1);
            }
        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where'      => array('p.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);
            if (!empty($project)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($project);
            }
            $project->getStages();
            if ($project->get('started_project')) {
                $project->getIncludedStages();
            }
            $project->processDBStages();
        }

        if (!empty($project)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Calculates deadlines of stages
     */
    private function _calculateDeadlines() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $project = Projects::buildModel($this->registry);

        $project = Projects::buildModel($this->registry);
        $filters = array('where'      => array('p.id = ' . $project->get('id')),
                         'model_lang' => $request->get('model_lang'));
        $old_project = Projects::searchOne($this->registry, $filters);
        $old_project->getAssignments();

        //merge post with db model data
        $old_params = $old_project->getAll();
        $new_params = $project->getAll();

        $params = array_merge($old_params, $new_params);
        $project = new Project($this->registry, $params);

        $project->getStages();
        $template = '';
        if ($request->get('revision')) {
            $project->set('perform_revision', true, true);
            $project->processDBStages();
            $project->processPostStages();
            $project->processRevisionAdditionalStages();
            $template = '_revision_stages_list.html';
        } else {
            $project->processPostStages();
            $template = '_stages_prepare_project.html';
        }

        // prepare the viewer and all the required data
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->template = $template;

        $viewer->data['project'] = $project;

        // prepare responsibles
        $responsibles = array();
        if ($project->get('manager')) {
            $responsibles[] = array(
                'label'         => $project->get('manager_name'),
                'option_value'  => $project->get('manager')
            );
        }
        if ($project->get('users_assignments')) {
            foreach ($project->get('users_assignments') as $user) {
                if ($user['assigned_to'] != $project->get('manager')) {
                    $responsibles[] = array(
                        'label'         => $user['assigned_to_name'],
                        'option_value'  => $user['assigned_to']
                    );
                }
            }
        }
        $viewer->data['responsibles'] = $responsibles;
        $viewer->data['phases_choose_responsible'] = $this->registry['config']->getParamFromDB('projects', 'phases_choose_responsible');

        exit($viewer->fetch());
    }

    /**
     * revision project's phases
     */
    private function _revision() {
        require_once PH_MODULES_DIR . 'projects/models/projects.stages.model.php';
        $request = &$this->registry['request'];

        $this->registry->set('getAssignments', true, true);
        //get the requested model ID
        $id = $request->get($this->action, 'get');
        //check if details are submitted via POST

        if ($request->isPost()) {
            $project = Projects::buildModel($this->registry);
            $filters = array('where'      => array('p.id = ' . $project->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_project = Projects::searchOne($this->registry, $filters);

            //merge post with db model data
            $old_params = $old_project->getAll();
            $new_params = $project->getAll();

            if (isset($new_params['stages'])) {
                unset($new_params['stages']);
            }

            $params = array_merge($old_params, $new_params);
            $project = new Project($this->registry, $params);

            // set flag to mark that revision is in progress
            $project->set('perform_revision', true, true);

            $project->getStages();
            $project->processDBStages();
            $project->processPostStages();

            if ($project->valid && $project->updateRevisionedStages()) {
                //successfully saved revision stages
                $this->registry['session']->remove('projects_revision', 'after_action');
                $this->afterAction = 'phases';
                $this->actionCompleted = true;

                // save into the history
                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'revision_project'));

                //show message for successful action
                $this->registry['messages']->setMessage($this->i18n('message_projects_stage_revisioned'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->registry['messages']->setError($this->i18n('error_projects_stage_revisioned_failed', array($project->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('p.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);

            if (!empty($project)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($project);
            }

            $project->getStages();
            $project->processDBStages();
        }

        if (!empty($project)) {
            $project->processRevisionAdditionalStages();
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('project')) {
                $this->registry->set('project', $project->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_project'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Get customer's info
     */
    private function _showCustomersInfo() {
        $request = &$this->registry['request'];

        $customerInfoViewer = new Viewer($this->registry);

        $i18n_files[] = PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini';
        $customerInfoViewer->loadCustomI18NFiles($i18n_files);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customer  = array('sanitize' => true,
                                       'model_lang' => $request->get('model_lang'),
                                       'where' => array('c.id = ' . $customer_id,
                                                        'c.subtype = \'normal\''));
            $customer = Customers::searchOne($this->registry, $filters_customer);

            if ($customer) {
                if ($customer->get('main_branch_id')) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                    $filters_branches = array('sanitize' => true,
                                              'model_lang' => $request->get('model_lang'),
                                              'where' => array('c.id = ' . $customer->get('main_branch_id'),
                                                               'c.subtype = \'branch\''));
                    $customer_main_branch = Customers_Branches::searchOne($this->registry, $filters_branches);
                    $customer->set('branch_address', ($customer_main_branch ? $customer_main_branch->get('address') : ''), true);
                } else {
                    $customer->set('branch_address', '', true);
                }

                $customerInfoViewer->data['customers_info'] = $customer;
            } else {
                $customerInfoViewer->data['hide_side_panel'] = 1;
            }
        } else {
            $customerInfoViewer->data['hide_side_panel'] = 1;
        }

        $customerInfoViewer->setFrameset('_customers_info_side_panel.html');
        $customerInfoViewer->display();
        exit;
    }

    /**
     * Get last five records having same customer and type as current model
     */
    private function _showLastRecords() {
        $request = &$this->registry['request'];

        $lastRecordsViewer = new Viewer($this->registry);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            $filters_records = array('where' => array('p.customer = ' . $customer_id,
                                                      'p.type = \'' . $request->get('model_type') . '\''),
                                     'sort' => array('p.added DESC'),
                                     'limit' => 5,
                                     'sanitize' => true,
                                     'check_module_permissions' => 'projects');

            $last_records = Projects::search($this->registry, $filters_records);
            // get files count to display "paper clip"
            foreach ($last_records as $rec) {
                $rec->getFilesCount();
            }

            $lastRecordsViewer->data['last_records'] = $last_records;
        } else {
            $lastRecordsViewer->data['hide_side_panel'] = 1;
        }

        $lastRecordsViewer->setFrameset(PH_MODULES_DIR . 'projects/templates/_last_records_side_panel.html');
        $lastRecordsViewer->display();
        exit;
    }

    /**
     * Set status of model
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $old_project = Projects::searchOne($this->registry, array('where' => array('p.id = ' . $id)));
        $this->old_model = clone $old_project;
        $this->old_model->sanitize();
        $request->set('id', $id, 'all', true);
        $project = Projects::buildModel($this->registry);
        //set new model to check transition
        $old_project->new_model = $project;
        $this->checkTransition($old_project, true);

        //get referer's action
        $matches = array();
        preg_match('/&projects=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        if ($project->setStatus()) {
            //show message 'message_projects_status_success'
            $this->registry['messages']->setMessage($this->i18n('message_projects_status_success', array($old_project->getModelTypeName())), '', -2);

            $comment = '';
            if ($request->get('comment')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::buildModel($this->registry);

                $comment->set('content', $request->get('comment'), true);
                $comment->set('subject', $this->i18n('projects_status_change_comment'), true);
                $comment->set('model', 'Project', true);
                $comment->set('model_id', $id, true);
                $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                $comment->set('skip_send_email', true, true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    $comment->slashesStrip();

                    //show corresponding message
                    $this->registry['messages']->setMessage($this->i18n('message_projects_comments_add_success'), '', -1);
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->registry['messages']->setError($this->i18n('error_comments_add_failed'), '', -1);
                }
            }

            //if the status of the project is 'finished', the system task for it is set to 'finished'
            if ($project->get('status') == 'finished') {
                $project->checkCompleteSystemTask();
            }

            $new_project = Projects::searchOne($this->registry,
                                               array('where' => array('p.id = ' . $id, 'p.status IS NOT NULL')));

            // set the comment into new model to be used for notification or automations
            $new_project->set('comment', $comment, true);

            $this->model = clone $new_project;
            $this->model->sanitize();

            $audit_parent = Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => 'status', 'new_model' => $new_project, 'old_model' => $old_project));

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_project);
            }

            //send notification
            $this->sendStatusNotification($new_project, $audit_parent);

            //set after action to view if have not permission
            if (!$new_project->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_projects_status_failed', array($old_project->getModelTypeName())), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            header("Location: " . $_SERVER['HTTP_REFERER']);
            exit;
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            $this->registry->set('exit_after', true, true);
        }

        return true;
    }

    /**
     * status of models
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');
        $filters = array('where' => array('p.id = ' . $request->get('id')),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $project = Projects::searchOne($this->registry, $filters);

        $setstatus['options'] = array('label' => $this->i18n('projects_status_btn'), 'form_method' => 'post');
        $setstatus['model_id'] = $project->get('id');
        $setstatus['module'] = 'projects';
        $setstatus['action'] = 'setstatus';
        $setstatus['module_param'] = $this->registry['module_param'];
        $setstatus['show_form'] = 1;
        $setstatus['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'projects/templates/_action_status.html');
        $this->viewer->data['project'] = $project;
        $this->viewer->data['available_action'] = $setstatus;
        $this->viewer->data['hide_status_label'] = true;
        $this->viewer->display();
        exit;
    }

    /**
     * get project phases with ajax
     */
    private function _getPhases() {
        $request = &$this->registry['request'];
        $p_id = $request->get('p_id');
        $records = array();
        if ($p_id) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $project = Projects::searchOne($this->registry, array('where' => array('p.id = ' . $p_id)));
            if ($project) {
                $params = array ($this->registry, 'self' => $project);
                $records = Dropdown::getUnfinishedPhases($params);
            }
        }

        print json_encode($records);
    }

    /**
     * change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('p.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $projects = Projects::search($this->registry, $filters);

        //if no projects or checked deleted projects
        if (empty($projects) || count($ids) != count($projects)) {
            $this->registry['messages']->setError($this->i18n('error_no_projects_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_project = $projects[0];

        $type = ($first_project->get('type'));
        foreach ($projects as $project) {
            $type_i = $project->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once PH_MODULES_DIR . 'projects/models/projects.types.factory.php';
        $filters = array('where' => array('pt.id = ' . $type,
                                          'pt.deleted IS NOT NULL'),
                         'sanitize' => true);
        $type = Projects_Types::searchOne($this->registry, $filters);
        $type_name_plural = $type && $type->get('name_plural') ? $type->get('name_plural') : $this->i18n('projects');

        $result = Projects::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_projects_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $type_name_plural)), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_projects_change_status_not_all', array($type_name_plural)));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_projects_multistatus_failed', array($type_name_plural)), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Remind
     */
    private function _remind() {
        $request = &$this->registry['request'];

        //get the requested model ID
        if ($request->get('id')) {
            $id = $request->get('id');
        } else {
            $id = $request->get($this->action);
        }

        //get referer's action
        preg_match('/&projects=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $filters = array('where' => array('p.id = ' . $id), 'model_lang' => $request->get('model_lang'));
        $project = Projects::searchOne($this->registry, $filters);

        $saved_reminder = false;
        //check if details are submitted via POST
        if ($request->get('reminder_date')) {

            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';

            //get reminder type
            $filters = array('where' => array('et.keyword=\'reminder\''), 'sanitize' => true);
            //get the event type reminder
            $event_type = Events_Types::searchOne($this->registry, $filters);
            $duration = $event_type->getDefaultDuration();

            if ($event_type) {
                if ($request->get('reminder_event_id')) {
                    //edit existing event
                    $filters = array('where' => array('e.id = ' . $request->get('reminder_event_id')));
                    $event = Events::searchOne($this->registry, $filters);
                    $event->set('name', sprintf($this->i18n('projects_reminder_event_name'), $project->get('name')), true);
                    $event->set('description', $request->get('custom_message'), true);
                    $event->set('event_start', $request->get('reminder_date'), true);
                    $event->set('duration', $duration, true);
                    $event->set('event_end', date('Y-m-d H:i:00', strtotime($request->get('reminder_date')) + 60*$duration), true);
                    if ($event->save()) {

                    }
                } else {
                    //create new event
                    $event = Events::buildModel($this->registry);
                    $event->set('id', null, true);
                    $event->set('name', sprintf($this->i18n('projects_reminder_event_name'), $project->get('name')), true);
                    $event->set('description', $request->get('custom_message'), true);
                    $event->set('event_start', $request->get('reminder_date'), true);
                    $event->set('duration', $duration, true);
                    $event->set('event_end', date('Y-m-d H:i:00', strtotime($request->get('reminder_date')) + 60*$duration), true);
                    $event->set('type', $event_type->get('id'), true);
                    $event->set('availability', 'available', true);
                    $event->set('customer', $project->get('customer'), true);
                    $event->set('branch', $project->get('branch'), true);
                    $event->set('contact_person', $project->get('contact_person'), true);
                    $event->set('trademark', $project->get('trademark'), true);
                    $event->set('project', $project->get('id'), true);

                    if ($event->save()) {
                        $record['link_to'] = $project->get('id');
                        $record['origin'] = 'project';
                        $record['link_type'] = 'parent';
                        $event->updateRelatives($record);
                    }
                }
            }
            if ($event && $event->get('id')) {
                //save reminder
                $request->set('selected_panel', 'date', true);
                if ($event->remind()) {
                    $saved_reminder = true;
                }
            }
        }

        if ($saved_reminder) {
            if ($request->get('reminder_event_id')) {
                $this->registry['messages']->setMessage($this->i18n('message_projects_reminder_edit_success'), '', -1);
            } else {
                $this->registry['messages']->setMessage($this->i18n('message_projects_reminder_add_success'), '', -1);
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_projects_reminder_failed'), '', -1);
        }
        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        $this->actionCompleted = true;
        if (!$this->registry->isRegistered('project')) {
            $this->registry->set('project', $project->sanitize());
        }

        return true;
    }

    /**
     * filter for references
     */
    private function _filter() {

        $this->viewer = $this->getViewer();

        $autocomplete_filter = $this->registry['request']->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->_select();
                $this->viewer->data['autocomplete_filters'] = $filters;
            }
        }
        $this->viewer->setFrameset('frameset_pop.html');

        return true;
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Projects::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            foreach ($ids as $id) {
                $project = new Project($this->registry);
                $project->set('id', $id, true);
                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => $this->action));
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete projects
        $result = Projects::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));

            foreach ($ids as $id) {
                $project = new Project($this->registry);
                $project->set('id', $id, true);
                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => $this->action));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Projects::restore($this->registry, $ids);

        if ($result) {
            //restore successful

            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
            foreach ($ids as $id) {
                $project = new Project($this->registry);
                $project->set('id', $id, true);
                Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => $this->action));
            }
        } else {
            //restore failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;
        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Projects::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Manage Frankenstein configurator (obsolete).
     * Also manage (load/save/delete) saved configurations for a configurator.
     */
    private function _franky() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('p.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);
        }
        if (empty($project)) {
            $project = Projects::buildModel($this->registry);
            $project->set('id', $request->get('id', 'get'), true);
            if ($request->get('type')) {
                $project->set('type', $request->get('type'), true);
            } elseif ($request->get('edit_id')) {
                $type = Projects::getConfiguratorModelType($this->registry, $request->get('edit_id'));
                $project->set('type', $type, true);
            }
        }

        $project->getVarsForTemplate(false);
        $this->registry->set('project', $project->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/franky.viewer.php';
        $this->viewer = new Franky_Viewer($this->registry);
        $this->viewer->model = $project;

        $configurator = Configurators::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configData'] =
                $configurator->getConfigForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator->config_delete($request->get('del_id'));
            } else {
                if ($request->get('id', 'get')) {
                    $configurator->saveFranky();
                } else {
                    $configurator->save();
                }
            }
            if (($request->get('del_id') || $request->get('config_id')) && !$request->get('id', 'get')) {
                $this->viewer->data['configPatterns'] =
                    $configurator->getConfigPatterns(
                        array(
                            'model' => $project->modelName,
                            'model_type' => $project->get('type'),
                            'model_id' => 0,
                            'config_num' => $request->get('config_num')
                        ));
            }

            if ($request->get('id', 'get')) {
                $project->getVarsForTemplate(false);
                $this->registry->set('project', $project->sanitize(), true);
            }
        }

        return true;
    }

    /**
     * Manage (load/save/delete) saved configurations for a grouping table
     */
    private function _saveGroupVar() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('p.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $project = Projects::searchOne($this->registry, $filters);
        }
        if (empty($project)) {
            if ($request->get('type')) {
                $project = new Project($this->registry);
                $project->set('id', $request->get('id', 'get'), true);
                $project->set('type', $request->get('type'), true);
            } else {
                exit;
            }
        }

        $project->getVarsForTemplate(false);
        $this->registry->set('project', $project->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/savegroupvar.viewer.php';
        $this->viewer = new SaveGroupVar_Viewer($this->registry);
        $this->viewer->model = $project;

        $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configGroupData'] =
                $configurator_group->getConfigGroupForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator_group->configGroupDelete($request->get('del_id'));
            } else {
                $configurator_group->save();
            }
            $this->viewer->data['configGroupPatterns'] =
                $configurator_group->getConfigGroupPatterns(
                    array(
                        'model' => $project->modelName,
                        'model_type' => $project->get('type'),
                        'config_group_num' => $request->get('group_num')
                    ));
        }

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();
        if ($this->model) {
            //check if the project has stages defined
            if (!$this->model->checkStages()) {
                //remove phases
                unset($this->actionDefinitions[array_search('phases', $this->actionDefinitions)]);
                if ($this->model->get('finalized_project')) {
                    unset($this->actionDefinitions[array_search('setstatus', $this->actionDefinitions)]);
                }
            } else {
                //remove status
                unset($this->actionDefinitions[array_search('setstatus', $this->actionDefinitions)]);
            }
        }

        if ($this->action == 'filter') {
            $this->actionDefinitions = array($this->action);
            $this->afterActionDefinitions = array();
        }

        $actions = parent::getActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                $actions['list']['options']['previous_list']['img'] = 'list';
                $actions['list']['options']['previous_list']['label'] = $this->i18n('previous_list');
                $actions['list']['options']['previous_list']['url'] = $actions['list']['url'];
                if (isset($actions['search'])) {
                    $actions['list']['options']['previous_search']['img'] = 'search';
                    $actions['list']['options']['previous_search']['label'] = $this->i18n('previous_search');
                    $actions['list']['options']['previous_search']['url'] = $actions['search']['url'];
                }

                $actions['list']['options']['all_projects']['img'] = 'projects';
                $actions['list']['options']['all_projects']['label'] = $this->i18n('projects_all_projects');
                $actions['list']['options']['all_projects']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';

                $actions['list']['url'] = $actions['list']['url'] . sprintf('&amp;type=%d&amp;type_section=', $this->model->get('type'));
            } else {
                $actions['list']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';
            }
        }

        if (isset($actions['add']) || isset($actions['printlist'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'pt.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'pt.type_section="' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_project')) {
                $custom_filters = $this->registry['session']->get($this->action . '_project');
            }

            if (!empty($custom_filters)) {
                // shows if there is a type defined and if so doesn't add the type section filter
                $type_defined = false;
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#p.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'pt.id="' . $custom_filters['values'][$key] . '"';
                            if ($type_defined) {
                                $found++;
                            } else {
                                $type_defined = true;
                                $found = 1;
                            }
                        } else if (preg_match('#pt.type_section#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            if (! $type_defined) {
                                $customize = 'pt.type_section="' . $custom_filters['values'][$key] . '"';
                                $found++;
                            }
                        }
                    }
                }
            }
        }

        if (isset($actions['add'])) {
            $actions['add']['ajax_no'] = 1;
            require_once($this->modelsDir . 'projects.types.factory.php');
            $filters = array('model_lang' => $this->registry['lang'],
                             'where' => array('pt.active = 1'),
                             'sort' => array('pti18n.name ASC'),
                             'satitize' => true);

            // if there is a model the only available options for add and multiadd
            // will be the options for the current type or the current section
            if ($found == 1 && $customize) {
                $filters['where'][] = $customize;
            }

            $projectTypes = Projects_Types::search($this->registry, $filters);
            $options = array();
            $types_to_add = array();
            foreach ($projectTypes as $type) {
                if ($this->checkActionPermissions($this->module . $type->get('id'), 'add')) {
                    $options[] = array('label' => $type->get('name'), 'option_value' => $type->get('id'));
                    $types_to_add[] = $type->get('id');
                }
            }
            if (! empty($options)) {
                $request_type = ($this->registry['request']->get('type')) ? $this->registry['request']->get('type') : '';
                $add_options = array (
                    array (
                        'custom_id' => 'type_',
                        'name' => 'type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('projects_type'),
                        'help' => $this->i18n('projects_add_legend'),
                        'options' => $options,
                        'value' => $request_type
                    )
                );
                $actions['add']['options'] = $add_options;

                // check if the current list is for only one project type and if it so
                // automatically loads that type in the Add screen
                $selected_type = '';
                if (isset($add_options[0]) && !empty($request_type) && in_array($request_type, $types_to_add)) {
                    $selected_type = $request_type;
                } else if (count($options) == 1) {
                    $first_project_type = reset($options);
                    $selected_type = $first_project_type['option_value'];
                }

                // change the link if necessary
                if ($selected_type) {
                    $actions['add']['url'] .= '&amp;type=' . $selected_type;
                    $actions['add']['options'] = '';
                    unset($actions['adds']['ajax_no']);
                    unset($actions['adds']['template']);
                }
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
        }

        if (isset($actions['clone'])) {
            require_once($this->modelsDir . 'projects.types.factory.php');
            $filters = array('where' => array('pt.id = ' . $this->model->get('type'),
                                              'pt.active = 1'),
                             'sanitize' => true);
            $type = Projects_Types::searchOne($this->registry, $filters);
            if (empty($type)) {
                unset($actions['clone']);
            } else {
                $actions['clone']['confirm'] = 'confirm_clone';
            }
        }

        //remind action
        if ($this->model && $this->model->get('id') && isset($actions['remind']) && $this->checkActionPermissions('events' . PH_REMINDER_EVENT_TYPE, 'add')) {

            $reminderTypes = array(
                'toaster' => $this->i18n('projects_reminder_toaster'),
                'email' => $this->i18n('projects_reminder_email'),
                'both' => $this->i18n('projects_reminder_both')
            );

            $reminder = $this->registry['currentUser']->getReminderModel(array('model_name' => 'project',
                                                                               'model_id' => $this->model->get('id')));
            $actions['remind']['options'] = array('label' => $this->i18n('remind'));
            $actions['remind']['ajax_no'] = 1;
            $actions['remind']['template'] = '_action_remind.html';
            $actions['remind']['model_id'] = $this->model->get('id');
            $actions['remind']['options']['reminderTypes'] = $reminderTypes;
            $actions['remind']['options']['reminder'] = $reminder;
        } else {
            unset($actions['remind']);
        }

        //timesheets action
        if (isset($actions['timesheets']) && $this->model && $this->model->get('id') && $this->model->checkPermissions('viewtimesheets')) {
            if ($this->model->checkPermissions('addtimesheet')) {
                require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheetsconfigurators.factory.php';
                $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                        array('where' => array('tc.added_by=' . $this->registry['currentUser']->get('id'),
                                               'tc.model_type=\'' . $this->model->get('type') . '\'',
                                               'tc.model=\'' . strtolower($this->model->modelName) . '\''),
                              'model_lang' => $this->registry['lang'],
                              'sanitize' => true));
                $configTimesheetPatterns = array();
                if (!$timesheetsConfigurators) {
                    $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                            array('where' => array('tc.added_by=' . $this->registry['currentUser']->get('id'),
                                                   'tc.model=\'all\''),
                                  'sanitize' => true));
                }
                if (!$timesheetsConfigurators) {
                    $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                            array('where' => array('tc.added_by=0',
                                                   'tc.model=\'all\''),
                                  'sanitize' => true));
                }
                foreach ($timesheetsConfigurators as $timesheetConfigurator) {
                    $configTimesheetPatterns[] = array(
                        'label' => $timesheetConfigurator->get('name'),
                        'option_value' => $timesheetConfigurator->get('id'),
                        'img' => 'addtimesheet',
                        'url' => $actions['timesheets']['url'] . '&amp;configurator=' . $timesheetConfigurator->get('id') . '#add_timesheet');
                }
                if ($configTimesheetPatterns) {
                    General::injectInArray(array('addtimesheet' => array()), $actions, 'after', 'timesheets');
                    $actions['addtimesheet']['action'] = 'addtimesheet';
                    $actions['addtimesheet']['name'] = 'addtimesheet';
                    $actions['addtimesheet']['label'] = $this->i18n('addtimesheet');
                    $actions['addtimesheet']['url'] = $configTimesheetPatterns[0]['url'];
                    if (count($configTimesheetPatterns) > 1) {
                        $actions['addtimesheet']['ajax_no'] = 1;
                        $actions['addtimesheet']['drop_menu'] = true;
                        $actions['addtimesheet']['hide_label'] = true;
                        $actions['addtimesheet']['options'] = $configTimesheetPatterns;
                    }
                }
            }
        } else {
            unset($actions['timesheets']);
        }

        if ($this->model && $this->model->get('id') && isset($actions['setstatus'])) {
            $actions['setstatus']['options'] = array('label' => $this->i18n('projects_status_btn'), 'form_method' => 'post');
            $actions['setstatus']['ajax_no'] = 1;
            $actions['setstatus']['template'] = PH_MODULES_DIR . 'projects/templates/_action_status.html';
            $actions['setstatus']['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
            $actions['setstatus']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['setstatus']);
        }

        if (isset($actions['generate']) || isset($actions['print'])) {
            //get all generate/print patterns for this type
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->model->modelName . '\'',
                    'p.model_type = \'' . $this->model->get('type') . '\'',
                    'p.active = 1',
                    'p.list = 0'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns = Patterns::search($this->registry, $filters_patterns);

        }

        if (isset($actions['generate'])) {
            if (empty($patterns)) {
                //remove generate action, the project type does not define the types to generate to
                unset($actions['generate']);
            } else {
                $_options_patterns = array();
                foreach ($patterns as $pattern) {
                    $_options_patterns[] = array(
                        'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                        'option_value' => $pattern->get('id'));
                }
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern_',
                        'name' => 'pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('projects_pattern'),
                        'help' => $this->i18n('projects_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                        $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
                $actions['generate']['ajax_no'] = 1;
            }
        }

        if (isset($actions['print'])) {
            $patterns_options = array();

            if ($this->model->get('type')) {
                //get all generate/print patterns for this type
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                $filters_patterns = array(
                    'where' => array(
                        'p.model = \'' . $this->model->modelName . '\'',
                        'p.model_type = \'' . $this->model->get('type') . '\'',
                        'p.active = 1',
                        'p.list = 0'
                    ),
                    'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                    'model_lang' => $this->registry['lang'],
                    'sanitize' => true
                );
                if ($this->registry['currentUser']->get('is_portal')) {
                    $filters_patterns['where'][] = 'p.is_portal = 1';
                }
                $patterns = Patterns::search($this->registry, $filters_patterns);

                $filters_type = array(
                    'where' => array(
                        'pt.id = ' . $this->model->get('type')
                    ),
                    'model_lang' => $this->getModelLang(),
                    'sanitize' => true
                );
                $project_type = Projects_Types::searchOne($this->registry, $filters_type);

                //get the id of the default document type print template
                $default_pattern_id = 0;
                if ($project_type && $project_type->get('default_pattern')) {
                    $default_pattern_id = $project_type->get('default_pattern');
                }

                $available_patterns = array();
                foreach ($patterns as $pattern) {
                    $available_patterns[] = $pattern->get('id');
                    $patterns_options[] = array(
                        'id'        => $pattern->get('id'),
                        'label'     => $pattern->get('name'),
                        'img'       => $pattern->getIcon(),
                        'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                        'target'    => '_blank',
                    );
                }
            }

            if (empty($patterns_options)) {
                unset($actions['print']);
            } else {
                if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                } elseif (count($available_patterns) == 1) {
                    // sets the first pattern in the list as default and assigns a link to the direct print
                    list($first_pattern) = $patterns_options;
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                } else {
                    $actions['print']['url'] = '#';
                }
                $actions['print']['drop_menu'] = true;
                $actions['print']['no_tab'] = true;
                $actions['print']['label'] = '';
                $actions['print']['target'] = '_blank';
                $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['print']['img'] .= '_plus';
                }
                $actions['print']['options'] = $patterns_options;
            }
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^pt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^pt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments']) || isset($actions['minitasks'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('projects_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
                if (isset($actions['minitasks'])) {
                    $actions['communications']['options']['minitasks']['img'] = 'minitasks';
                    $actions['communications']['options']['minitasks']['label'] = $actions['minitasks']['label'];
                    $actions['communications']['options']['minitasks']['url'] = $actions['communications']['url'] . '&amp;communication_type=minitasks';
                    // do not unset yet, action is used in "Create" as well
                    //unset($actions['minitasks']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if ($this->model && $this->model->get('id') && isset($actions['create'])) {
            $actions['create']['label'] = $this->i18n('create');
            $actions['create']['ajax_no'] = 1;
            $actions['create']['template'] = PH_MODULES_DIR . 'projects/templates/_action_create.html';

            if (isset($actions['documents']) && $this->checkActionPermissions('documents', 'add')) {

                $direction_labels[PH_DOCUMENTS_INCOMING] = $this->i18n('projects_documents_incoming');
                $direction_labels[PH_DOCUMENTS_OUTGOING] = $this->i18n('projects_documents_outgoing');
                $direction_labels[PH_DOCUMENTS_INTERNAL] = $this->i18n('projects_documents_internal');

                require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                $filters = array('where' => array('dt.active = 1',
                                                  'dt.inheritance = 0'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('dti18n.name ASC'));
                $types_all = Documents_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('documents' . $type->get('id'), 'add')) {
                        $_options[$direction_labels[$type->get('direction')]][] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    $first_key = array_keys($_options);
                    $first_key = reset($first_key);
                    //prepare documents options
                    $actions['documents']['options'] = array (
                        array (
                            'custom_id'     => 'document_type__',
                            'name'          => 'document_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('projects_document_type'),
                            'help'          => $this->i18n('projects_document_type'),
                            'optgroups'     => $_options,
                            'value'         => $this->registry['request']->get('document_type') ?: $_options[$first_key][0]['option_value']
                        )
                    );
                    $actions['documents']['label'] = $this->i18n('projects_document');
                }
            }

            if (!empty($actions['documents']['options'])) {
                $actions['create']['options']['documents'] = $actions['documents'];
            }
            unset ($actions['documents']);

            if (isset($actions['tasks']) && $this->checkActionPermissions('tasks', 'add')) {
                require_once PH_MODULES_DIR . 'tasks/models/tasks.types.factory.php';
                $filters = array('where' => array('tt.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('tti18n.name ASC'));
                $types_all = Tasks_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('tasks' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare task options
                    $actions['tasks']['options'] = array (
                        array (
                            'custom_id'     => 'task_type__',
                            'name'          => 'task_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('projects_task_type'),
                            'help'          => $this->i18n('projects_task_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('task_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['tasks']['label'] = $this->i18n('projects_task');
                }
            }

            if (!empty($actions['tasks']['options'])) {
                $actions['create']['options']['tasks'] = $actions['tasks'];
            }
            unset ($actions['tasks']);

            if (isset($actions['events']) && $this->checkActionPermissions('events', 'add')) {
                require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
                $filters = array('where' => array('et.keyword NOT IN (\'reminder\', \'plannedtime\')', 'et.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('eti18n.name ASC'));
                $types_all = Events_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('events' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare event options
                    $actions['events']['options'] = array (
                        array (
                            'custom_id'     => 'event_type__',
                            'name'          => 'event_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('projects_event_type'),
                            'help'          => $this->i18n('projects_event_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('event_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['events']['label'] = $this->i18n('projects_event');
                }
            }

            if (!empty($actions['events']['options'])) {
                $actions['create']['options']['events'] = $actions['events'];
            }
            unset ($actions['events']);

            if (isset($actions['minitasks'])) {
                if ($this->registry['currentUser']->checkRights('minitasks', 'add') &&
                    !($this->model->get('requires_completed_minitasks') && $this->model->get('status') == 'finished' &&
                    $this->model->get('finalized_project'))) {
                    $actions['minitasks']['label'] = $this->i18n('projects_minitask');
                    $actions['create']['options']['minitasks'] = $actions['minitasks'];
                }
                unset ($actions['minitasks']);
            }

            if (!$actions['create']['options']) {
                unset ($actions['create']);
            } else {
                // GET CUSTOMER's ID
                $actions['create']['customer'] = $this->model->get('customer');
                // GET PROJECT's ID
                $actions['create']['project'] = $this->model->get('id');
            }
        } else {
            unset ($actions['create']);

            if (isset($actions['documents'])) {
                unset($actions['documents']);
            }
            if (isset($actions['tasks'])) {
                unset($actions['tasks']);
            }
            if (isset($actions['events'])) {
                unset($actions['events']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // checks current action and sets the alternative action for view/edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } else if ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit or view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } else if (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && ! empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['export'])) {
                unset ($actions['export']);
            }
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
        }
        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        //get the after actions
        $actions = parent::getAfterActions();

        //prepare add options types
        require_once($this->modelsDir . 'projects.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sort' => array('pti18n.name ASC'),
                         'sanitize' => true,
                         'where' => array('pt.active = 1'));
        $projectTypes = Projects_Types::search($this->registry, $filters);

        $_options_add = array();
        foreach ($projectTypes as $type) {
            $type_permition = $this->checkActionPermissions($this->module.$type->get('id'), 'add');
            if ($type_permition) {
                $_options_add[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
            }
        }

        if (isset($actions['add']) && !empty($_options_add)) {
            //prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'type____',
                    'name' => 'aa1_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('projects_type'),
                    'help' => $this->i18n('projects_add_legend'),
                    'options' => $_options_add,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : ''),
            );
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Send status change notification to assignees
     *
     * @param Project $new_project - project object
     * @param int $audit_parent - id of history record
     */
    public function sendStatusNotification($new_project, $audit_parent) {
        $template = 'project_status';

        if (!$new_project->shouldSendEmail($template)) {
            return true;
        }

        $new_project->getAssignments();
        $users_assignments = $new_project->get('users_assignments');
        $users_ids_array = array();
        foreach ($users_assignments as $user_assignment) {
            $users_ids_array[] = $user_assignment['assigned_to'];
        }
        $users_ids_array[] = $new_project->get('manager');

        $records = array();
        if (!empty($users_ids_array)) {
            $query = 'SELECT ' . "\n" .
                     '  u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname, u2.id,' . "\n" .
                     '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name, u2.is_portal ' . "\n" .
                     'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                     '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_project->get('model_lang') . '")' . "\n" .
                     'WHERE u2.id IN (' . implode(',', $users_ids_array) . ') AND u2.active=1 AND u2.deleted_by=0';
            $records = $this->registry['db']->GetAll($query);
        }

        if (empty($records)) {
            return;
        }

        $not_users = Users::getUsersNoSend($this->registry, $template);

        //prepare audit data
        $this->registry['request']->set('audit', $audit_parent, 'all', true);
        require_once PH_MODULES_DIR . 'projects/viewers/projects.audit.viewer.php';
        $configViewer = new Projects_Audit_Viewer($this->registry);
        $configViewer->templatesDir = $this->registry['theme']->templatesDir;
        $configViewer->template = '_audit_email.html';

        //prepare the title for the audit table
        $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_project->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_project->get('modified'))));
        $configViewer->data['audit_title'] = $audit_title;
        $configViewer->prepare();
        $audit = $configViewer->data['audit'];
        $statuses = array();
        foreach ($audit['vars'] as $var) {
            $statuses[$var['field_name']] = $var['label'];
            $statuses['old_' . $var['field_name']] = $var['old_value'];
        }
        $audit_data = $configViewer->fetch();
        // CHECK if there is audit data and compare the settings for each user from personal settings table
        $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
        $users_send_always = Users::getUsersSendSettings($this->registry, 'document_edit_send_when_audit', 'emails');

        $project_view_url = sprintf('%s/index.php?%s=projects&projects=view&view=%d',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], $new_project->get('id'));
        $add_comment_url = sprintf('%s/index.php?%s=projects&projects=communications&communications=%d&communication_type=comments#comments_add_form',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], $new_project->get('id'));

        $sent_to = array();
        foreach ($records as $record) {
            //check if the assignee want to receive email
            if ($record['assignment_email']) {
                $send = false;
                if (in_array($record['id'], $not_users)
                || $record['id'] == $this->registry['currentUser']->get('id')) {
                    //the user does not want to receive notifications when the project is edited
                    //or the user is current user
                    continue;
                }

                // force sending of comment if no status has been changed but comment is added
                $force_send_comment_email = false;
                if ($new_project->get('comment') && !($record['is_portal'] && !$new_project->get('comment')->get('is_portal'))) {
                    $force_send_comment_email = true;
                }

                if (empty($audit_data) && in_array($record['id'], $users_send_always)) {
                    $send = true;
                } else if (!empty($audit_data) || $force_send_comment_email) {
                    $send = true;
                }

                if ($send) {
                    $mailer = new Mailer($this->registry, $template, $new_project);
                    $mailer->placeholder->add('project_name', $new_project->get('name'));
                    $mailer->placeholder->add('project_code', $new_project->get('code'));
                    $mailer->placeholder->add('project_num', $new_project->get('num'));
                    $mailer->placeholder->add('project_type', $new_project->get('type_name'));
                    $mailer->placeholder->add('project_added_by', $new_project->get('added_by_name'));
                    $mailer->placeholder->add('project_view_url', $project_view_url);
                    $mailer->placeholder->add('project_add_comment_url', $add_comment_url);
                    $mailer->placeholder->add('customer_name', $new_project->get('customer_name'));
                    $mailer->placeholder->add('status', $this->i18n('projects_status'));
                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                    $mailer->placeholder->add('user_name', $record['assignment_name']);
                    $mailer->placeholder->add('last_audit', $audit_data);
                    $mailer->placeholder->add('modified_by_name', $new_project->get('modified_by_name'));

                    $comment = $new_project->get('comment');
                    if ($comment && $comment->get('content') && !($record['is_portal'] && !$comment->get('is_portal'))) {
                        $mailer->placeholder->add('model_id', $comment->get('id'));
                        $mailer->placeholder->add('model', 'Comment');
                        $mailer->template['model_name'] = 'Comment';
                        $mailer->template['model_id'] = $comment->get('id');
                        $mailer->template['system_flag'] = 0;
                        $mailer->placeholder->add('project_comment', nl2br($comment->get('content')));
                    } else {
                        $mailer->template['model_name'] = $new_project->modelName;
                        $mailer->template['model_id'] = $new_project->get('id');
                    }

                    if (empty($statuses['status'])) {
                        $mailer->placeholder->add('old_status', $this->i18n('projects_status_' . $new_project->get('status')));
                        $mailer->placeholder->add('status', $this->i18n('projects_status_' . $new_project->get('status')));
                    } else {
                        $mailer->placeholder->add('old_status', $statuses['old_status']);
                        $mailer->placeholder->add('status', $statuses['status']);
                    }
                    //send email
                    $result = $mailer->send();
                    if (!@in_array($record['assignment_email'], $result['erred'])) {
                        $sent_to[] = $record['assignment_name'];
                    } else {
                    }
                }
            }
        }
        if (count($sent_to)) {
            $notify_for = $this->i18n('projects_' . $template . '_notify', array($new_project->getModelTypeName()));
            if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
            } else {
                $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Selects certain items by specified parameter.
     * This method prints unordered list and exists
     */
    public function _select($autocomplete = array()) {
        $request = &$this->registry['request'];

        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            $search_fields = array('<code>', '<name>');
        }
        if (!is_array($search_fields)) {
            $search_fields = array($search_fields);
        }
        $i18n_columns = array_keys($this->registry['db']->MetaColumns(DB_TABLE_PROJECTS_I18N, false));
        $main_alias = Projects::getAlias('projects', 'projects');
        foreach ($search_fields as $idx => $field) {
            $field = preg_replace('#<|>#', '', $field);
            if (preg_match('#^a__*#', $field)) {
                //search by additional variable
                $search_fields[$idx] = $field;
            } else {
                //search by main field
                $alias = $main_alias;
                if (in_array(strtoupper($field), $i18n_columns)) {
                    //search by main field in i18n table
                    $alias .= 'i18n';
                }
                $search_fields[$idx] = $alias . '.' . $field;
            }
        }

        //prepare sort if is requested
        $sort = array();
        if (!$r_sort = $request->get('sort')) {
            $r_sort = array('<name>');
        }
        foreach ($r_sort as $field) {
            if (preg_match_all('#<([^>]*)>#i', $field, $matches)) {
                $replacements = [];
                foreach ($matches[1] as $key => $fieldName) {
                    if (preg_match('#^a__*#', $fieldName)) {
                        //sort by additional variable
                        $replacements[$matches[0][$key]] = $fieldName;
                    } else {
                        //sort by main field
                        $alias = $main_alias;
                        if (in_array(strtoupper($fieldName), $i18n_columns)) {
                            //sort by main field in i18n table
                            $alias .= 'i18n';
                        }
                        $replacements[$matches[0][$key]] = $alias . '.' . $fieldName;
                    }
                }
                $updatedSort = $field;
                foreach ($replacements as $replaceTarget => $replaceWith) {
                    $updatedSort = preg_replace('#' . $replaceTarget . '#', $replaceWith, $updatedSort);
                }

                if (!preg_match('#\s+(ASC|DESC)#i', $updatedSort)) {
                    $updatedSort .= ' ASC';
                }
                $sort[] = $updatedSort;
            }

        }

        $additional_where = array();
        if ($req_filters = $request->get('filters')) {
            foreach ($req_filters as $filter => $value) {
                $alias = $main_alias . '.';
                $field = preg_replace('#<|>#', '', $filter);
                // escape value for the SQL search query
                $value = General::slashesEscape($value);

                switch ($filter) {
                    case '<tag>':
                        $alias = 'tags.';
                        $field = 'tag_id';
                        break;
                    default:
                        if (preg_match('#^a__*#', $field)) {
                            //search by additional variable
                            $alias = '';
                        } elseif (in_array(strtoupper($field), $i18n_columns)) {
                            //search by main field in i18n table
                            $alias = $main_alias . 'i18n.';
                        }
                        break;
                }

                if (preg_match('#^\s*(!?=)(.*)#', $value, $matches)) {
                    // search expression for a single value
                    $additional_where[] =
                        sprintf('%s%s %s \'%s\' AND',
                                $alias, $field, trim($matches[1]), trim($matches[2]));
                    continue;
                } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $value, $matches)) {
                    // search expression for multiple values
                    $negative_search = preg_match('#not#i', $matches[1]);
                    $compare_operator = $negative_search ? '!=' : '=';
                    $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                    $count_or_clauses = count($amatches);
                    foreach ($amatches as $idx => $amatch) {
                        $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                        $additional_where[] =
                            sprintf('%s%s %s \'%s\' %s',
                                    $alias, $field, $compare_operator, $amatch, $logical_operator);
                    }
                    continue;
                }

                $vals = preg_split('#\s*,\s*#', $value);
                if (count($vals) > 1) {
                    $count_or_clauses = count($vals);
                    foreach ($vals as $idx => $val) {
                        $clause = $alias . $field . ' = \'' . $val . '\'';
                        if ($idx < $count_or_clauses - 1) {
                            $clause .= ' OR';
                        } else {
                            $clause .= ' AND';
                        }
                        $additional_where[] = $clause;
                    }
                } else {
                    $additional_where[] = $alias . $field . ' = \'' . $vals[0] . '\' AND';
                }
            }
        } else {
            //set default filter to skip projects with status finished
            //this default filter is applied only for add and edit actions
            $action = '';
            //get referers action
            if (!empty($_SERVER['HTTP_REFERER'])) {
                preg_match_all('#.*\?|\&([^=]*)=([^&]*)#', str_replace('?', '&', $_SERVER['HTTP_REFERER']), $matches);
            }
            if ($request->get('field') == 'values_autocomplete' && $request->get('row')) {
                // autocompleter is called from field in advanced search in search/filter action
                // do not add filter by status
                $action = 'search';
            } elseif (!empty($matches[1]) && !empty($matches[2])) {
                $get_array = array_combine($matches[1], $matches[2]);
                $controller = (isset($get_array[Router::CONTROLLER_PARAM])) ? $get_array[Router::CONTROLLER_PARAM] : (isset($get_array[Router::MODULE_PARAM]) ? $get_array[Router::MODULE_PARAM] : '');
                $action = @$get_array[$controller];
            } elseif ($request->get('scope')) {
                // if autocompleter is in dashlet/lightbox with no specific
                // filters, we assume action is adding of a new record
                $action = 'add';
            }

            if (preg_match('#add|edit|managevars#', $action)) {
                //skip the projects with status finished only for actions for add and edit
                $additional_where[] = 'p.status!=\'finished\' AND';
            }
        }

        //prepare suggestions format
        if (!$suggestions_format = $request->get('suggestions')) {
            $suggestions_format = '[<code>] <name>';
        }

        $s_field = $request->get('field');
        //prepare fill option definitions
        if (!$fill_options = $request->get('fill_options')) {
            //we must be in the basic vars
            //so get the autocomplete field
            $fill_options = array(
                '$' . $s_field . ' => [<code>] <name>',
                '$' . preg_replace('#_autocomplete$#', '', $s_field) . '_oldvalue' . ' => [<code>] <name>');
            if (preg_match('#_autocomplete$#', $s_field)) {
                $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . ' => <id>';
            }
        } else {
            $fill_oldvalue = '[<code>] <name>';
            foreach ($fill_options as $fill_option) {
                if (preg_match('#^\$' . $s_field . '\s*=\>#', $fill_option)) {
                    @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                }
            }
            $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . '_oldvalue' . ' => ' . $fill_oldvalue;
        }

        $autocomplete = array('search' => $search_fields,
                              'sort' => $sort,
                              'suggestions_format' => $suggestions_format,
                              'fill_options' => $fill_options,
                              'additional_where' => $additional_where,
                              'type' => 'projects'
                        );

        if ($request->get('autocomplete_filter')) {
            $filters = parent::_select($autocomplete);
            return $filters;
        }
        parent::_select($autocomplete);

        exit;
    }

    /**
     * Creates system tasks for projects without created system tasks
     */
    public function _createSystemTasks() {

        //get projects whose types require creation of system tasks
        $this->registry->set('getAssignments', true, true);
        $filters = array('where' => array('pt.generate_system_task = 1', 'pt.active = 1'),
                        'sanitize' => false);
        $projects = Projects::search($this->registry, $filters);

        $this->loadI18NFiles(PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini');

        foreach ($projects as $project) {
            //check if the project has a system task already
            $system_task_id = Projects::getSystemTask($this->registry, $project->get('id'));
            if (!$system_task_id) {
                $res = $project->createSystemTask();
            }
        }
        exit;
    }
}

?>
