<?php

class Dashlets_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Dashlet';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Dashlets';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'view', 'edit', 'translate'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'getoptions',
        'dashlet',
        'custom_action',
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'dashlet':
            $this->_loadPlugin();
            break;
        case 'custom_action':
            $this->_customPluginAction();
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }

        $this->_checkForPlugin();
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {

        $request = &$this->registry['request'];
        $dashlet = $this->registry->get('dashlet');

        //check if we have a model
        if ($request->isPost()) {

            $dashlet = Dashlets::buildModel($this->registry);

            // If this dashlet is for a report
            if ($dashlet->get('module') == 'reports') {
                // Get the report filters
                $dashlet->getCustomReportFilters($dashlet->get('controller'));
            }

            if ($dashlet->save()) {
                //show corresponding message
                if ($dashlet->get('id')) {
                    $this->registry['messages']->setMessage($this->i18n('message_dashlets_add_success'), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    //the model was successfully saved set action as completed
                    $this->actionCompleted = true;
                } else {
                    //the add process failed
                    $this->registry['messages']->setError($this->i18n('error_dashlets_add_failed'), '', -2);
                }
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_dashlets_add_failed'), '', -1);
            }

            // unset plugin model
            if (isset($dashlet->plugin)) {
                unset($dashlet->plugin);
            }
        } elseif ($request->get('module_name')) {
            $user_groups = $this->registry['currentUser']->getGroups();
            @list($module,$controller) = explode('_', $request->get('module_name'), 2);
            if (!$controller) {
                $controller = $module;
            }
            $params = array('module_name' => $request->get('module_name'),
                            'added_by' => $this->registry['currentUser']->get('id'),
                            'group' => (!empty($user_groups[0]) ? $user_groups[0] : 0));
            $dashlet = new Dashlet($this->registry, $params);

            // If this dashlet is for a report
            if ($module == 'reports') {
                // Get the report filters
                $dashlet->getCustomReportFilters($controller);
            }

            //check access
            $this->checkAccessOwnership($dashlet);
        } else {
            $this->registry['messages']->setError($this->i18n('error_dashlets_module_not_selected'), 'module_name', -1);
        }

        if (!empty($dashlet)) {
            $this->registry->set('include_calendar', true, true);
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('dashlet', $dashlet->sanitize(), true);
        }

        return true;
    }

    /**
     * Edits of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $dashlet = Dashlets::buildModel($this->registry);

            // If this dashlet is for a report
            if ($dashlet->get('module') == 'reports') {
                // Get the report filters
                $dashlet->getCustomReportFilters($dashlet->get('controller'));
            }
            if ($dashlet->save()) {
                //show message 'message_dashlets_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_dashlets_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_dashlets_edit_failed'), '', -1);
            }

            // unset plugin model
            if (isset($dashlet->plugin)) {
                unset($dashlet->plugin);
            }
        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('d.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));

            $dashlet = Dashlets::searchOne($this->registry, $filters);

            // If this dashlet is for a report
            if ($dashlet && $dashlet->get('module') == 'reports') {
                // Get the report filters
                $dashlet->getCustomReportFilters($dashlet->get('controller'));
            }

            if (!empty($dashlet)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($dashlet);
            }
        }

        if (!empty($dashlet)) {
            //register the model,
            //so that it could be used further by the viewer
            $this->registry->set('include_calendar', true, true);
            if (!$this->registry->isRegistered('dashlet')) {
                $this->registry->set('dashlet',  $dashlet->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_dashlet'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('d.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $dashlet = Dashlets::searchOne($this->registry, $filters);

        if (!empty($dashlet)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($dashlet);

            // If this dashlet is for a report
            if ($dashlet->get('module') == 'reports') {
                // Get the report filters
                $dashlet->getCustomReportFilters($dashlet->get('controller'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('dashlet')) {
                $this->registry->set('dashlet', $dashlet->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_dashlet'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        $prepared_for_deletion = array();
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete dashlets
        $result = Dashlets::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        $prepared_for_deletion = array();
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete dashlets
        $result = Dashlets::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            $actions['add']['options'] = 1;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions(array($action_name));

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {

            require_once $this->modelsDir . 'dashlets.dropdown.php';
            $options = Dashlets_Dropdown::getDashletsModules(array($this->registry));
            $add_options = array();

            //prepare add options
            if (isset($options['contain_optgroups']) && $options['contain_optgroups']) {
                unset($options['contain_optgroups']);

                $add_options[] = array (
                    'custom_id' => 'module_name',
                    'name' => 'module_name',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('dashlet_for'),
                    'help' => $this->i18n('dashlet_for_legend'),
                    'optgroups' => $options
                );
            } else {
                $add_options[] = array (
                    'custom_id' => 'module_name',
                    'name' => 'module_name',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('dashlet_for'),
                    'help' => $this->i18n('dashlet_for_legend'),
                    'options' => $options
                );
            }
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }
        return $actions;
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Dashlets::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $dashlet = Dashlets::buildModel($this->registry);

            if ($dashlet->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_dashlets_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_dashlets_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('d.id = ' . $id), 'model_lang' => $request->get('model_lang'));
            $dashlet = Dashlets::searchOne($this->registry, $filters);

            if (!empty($dashlet)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($dashlet);
            }
        }

        if (!empty($dashlet)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('dashlet', $dashlet->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_dashlet'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Check if the requested dashlet is for a plugin and load its template for the current action
     */
    private function _checkForPlugin() {
        $dashlet = $this->registry['dashlet'];
        if (!$dashlet) {
            return;
        }
        if (in_array($this->action, array('add', 'edit', 'view')) &&
          (preg_match('#^plugin_#', $dashlet->get('module_name')) || $dashlet->get('module') == 'plugin') &&
          !$this->actionCompleted) {
            if ($dashlet->get('module_name')) {
                @list($module, $controller) = explode('_', $dashlet->get('module_name'), 2);
            } else {
                $module = $dashlet->get('module');
                $controller = $dashlet->get('controller');
            }
            $viewer = PH_MODULES_DIR . 'dashlets/plugins/' . $controller . '/viewers/custom.' . $this->action. '.viewer.php';
            //if viewer does not exist redirect to the default action
            if (!file_exists($viewer)) {
                $this->registry['messages']->setError($this->i18n('error_missing_plugin_file'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, $this->defaultAction);
            }

            require_once($viewer);
            //$viewer = preg_replace('#(^(.))|(_(.))#e', "strtoupper('$1').strtoupper('$3')", $controller);
            //$viewer .= '_' . ucfirst($this->action) . '_Viewer';
            $viewer = 'Custom_' . ucfirst($this->action) . '_Viewer';
            $viewer = new $viewer($this->registry, true);

            if (!file_exists($viewer->templatesDir . $viewer->template)) {
                $this->registry['messages']->setError($this->i18n('error_missing_plugin_file'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, $this->defaultAction);
            }

            $viewer->data['available_actions'] = $this->getActions();
            $viewer->prepare();
            $viewer->display();
            exit;
        }
    }

    /**
     * load plugin(custom) dashlet
     */
    private function _loadPlugin() {

        $plugin = $this->registry['request']->get('plugin');
        if (!$plugin) {
            exit($this->i18n('error_missing_plugin_file'));
        }

        $filters = array('where' => array('d.id = ' . $this->registry['request']->get('dashlet'),
                                          'd.active = 1'),
                         'sanitize' => true);
        $dashlet = Dashlets::searchOne($this->registry, $filters);
        if (!$dashlet) {
            exit($this->i18n('error_no_such_dashlet'));
        }

        $this->registry->set('dashlet', $dashlet);

        $viewer = PH_MODULES_DIR . 'dashlets/plugins/' . $plugin . '/viewers/custom.dashlet.viewer.php';
        // if viewer does not exist, send error message
        if (!file_exists($viewer)) {
            exit($this->i18n('error_missing_plugin_file'));
        }

        require_once($viewer);
        $viewer = 'Custom_Dashlet_Viewer';
        $viewer = new $viewer($this->registry);

        if (!file_exists($viewer->templatesDir . $viewer->template)) {
            exit($this->i18n('error_missing_plugin_file'));
        }

        $viewer->prepare();
        $viewer->display();
        exit;
    }

    /**
     * load custom plugin action
     */
    private function _customPluginAction() {
        //check the name of the plugin
        $plugin = $this->registry['request']->get('plugin');
        $custom_plugin_action = $this->registry['request']->get('custom_plugin_action');

        if (!$plugin || !$custom_plugin_action) {
            exit($this->i18n('error_missing_plugin_file'));
        }

        if (!$this->registry['request']->get('custom_call', 'post')) {
            //check the dashlet in the database
            $filters = array('where' => array('d.id = \'' . $this->registry['request']->get('dashlet') . '\'',
                                              'd.active = 1'),
                             'sanitize' => true);
            $dashlet = Dashlets::searchOne($this->registry, $filters);

            if (!$this->registry['request']->get('force')) {
                if (!$dashlet) {
                    exit($this->i18n('error_no_such_dashlet'));
                }

                // get the current dashlet properties to get settings
                $this->registry->set('dashlet', $dashlet, true);
            }
        }
        $filter_dashlet_plugin = array('get_one' => $plugin);
        $dashlet_plugin = Dashlets::getPlugins($this->registry, $filter_dashlet_plugin);
        $dashlet_settings = isset($dashlet_plugin[$plugin]) ? $dashlet_plugin[$plugin]['settings'] : array();

        // first check for class name that contains plugin name
        $custom_model_name = str_replace(' ', '_', ucwords(str_replace('_', ' ', $plugin))) . '_Custom_Model';

        if (!class_exists($custom_model_name)) {
            //check for custom model
            $custom_model_file = PH_MODULES_DIR . 'dashlets/plugins/' . $plugin . '/models/custom.model.php';
            $custom_model_name = 'Custom_Model';

            // if plugin model file does not exist, return error message
            if (!file_exists($custom_model_file)) {
                exit($this->i18n('error_missing_plugin_file'));
            } else {
                require_once($custom_model_file);
            }
        }

        //load plugin files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'dashlets/plugins/' . $plugin . '/i18n/' . $this->registry->get('lang'), false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }

        $custom_model = new $custom_model_name($dashlet_settings);

        // if method does not exist in model, return error message
        if (!method_exists($custom_model, $custom_plugin_action)) {
            exit($this->i18n('error_missing_plugin_file'));
        }

        $action_result = $custom_model->$custom_plugin_action();
        $this->actionCompleted = true;
        // check if we have model and old model for the plugin in the registry
        if ($this->registry->isRegistered('dashlet_plugin_model')) {
            $this->model = $this->registry->get('dashlet_plugin_model');
        }
        if ($this->registry->isRegistered('dashlet_plugin_old_model')) {
            $this->old_model = $this->registry->get('dashlet_plugin_old_model');
        }
        $this->registry->set('ajax_result', $action_result, true);
    }
}

?>
