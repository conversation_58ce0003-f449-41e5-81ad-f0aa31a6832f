<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template = 'dashlet.html';
        $this->pluginName = 'asp_finish_contract';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $registry = &$this->registry;

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');
        $plugin  = $dashlet->get('controller');

        $ac_hardcoded = 'autocomplete := contracts' . "\n" .
                        'autocomplete_filter := <subtype> => contract' . "\n" .
                        'autocomplete_filter := <status> => closed' . "\n" .
                        'autocomplete_filter := <annulled_by> => 0' . "\n" .
                        'autocomplete_filter := <deleted_by> => 0' . "\n" .
                        'autocomplete_filter := <formula_status> => ready' . "\n" .
                        'autocomplete_fill_options := $contract_finish => <id>' . "\n" .
                        'autocomplete_fill_options := $contract_start_info => <date_start>' . "\n" .
                        'autocomplete_buttons_hide := search_add' . "\n" .
                        'autocomplete_execute_after := $(\'contract_start_info\').onchange();getContractObligations($$(\'#content_dashlet_' . $dashlet->get('id') . ' #contract_finish\')[0].value, $$(\'#content_dashlet_' . $dashlet->get('id') . ' #contract_end_date\')[0].value);' . "\n" ;

        $filters = $dashlet->get('filters');
        if (empty($filters['autocomplete_settings'])) {
            $filters['autocomplete_settings'] = '';
        }
        $ac_source = $ac_hardcoded . $filters['autocomplete_settings'];
        $dashlet->unsanitize();
        $ac_source = $dashlet->processAutocompleteSource($ac_source);
        $dashlet->sanitize();
        $this->data['autocomplete'] = $ac_source;
        $this->data['scripts_url'] = $this->scriptsUrl . 'custom.js';
        $this->data['dashlet'] = $dashlet;

        $query = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . "\n" .
                 'WHERE parent_name = "termination_grounds" ORDER BY position';
        $this->data['finish_reasons'] = $this->registry['db']->GetAll($query);

        $dashlet_allowed = true;
        if (!empty($filters['allowed_users']) &&
            is_array($filters['allowed_users']) &&
            //$registry['currentUser']->get('role') != PH_ROLES_ADMIN &&
            !in_array($registry['currentUser']->get('id'), $filters['allowed_users'])) {
            $dashlet_allowed = false;
        }
        $this->data['dashlet_allowed'] = $dashlet_allowed;
    }
}

?>
