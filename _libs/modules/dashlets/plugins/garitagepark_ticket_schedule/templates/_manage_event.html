{**
  * MANAGE EVENT
  *}
<form name="manage_event_form" class="garitagepark_ticket_schedule_form" method="post" action="" onsubmit="return false;">
  <input type="hidden" name="ticket" id="ticket" value="{$document->get('id')|default:''}" />
  <input type="hidden" name="id" id="id" value="{$event->get('id')|default:''}" />
  <input type="hidden" name="name" id="name" value="{$event->get('name')|escape|default:''}" />
  <input type="hidden" name="customer" id="customer" value="{$event->get('customer')|default:''}" />
  <input type="hidden" name="customer_name" id="customer_name" value="{$event->get('customer_name')|escape|default:''}" />
  <input type="hidden" name="location" id="location" value="{$event->get('location')|escape|default:''}" />
  <input type="hidden" name="description" id="description" value="{$event->get('description')|escape|default:''}" />
  <table class="t_table t_layout_table" style="margin: auto; width: auto;">
    {* info header from events module *}
    {include file="`$smarty.const.PH_MODULES_DIR`/events/templates/_info_header.html" 
            templatesDir="`$smarty.const.PH_MODULES_DIR`/events/templates/"
    }
    {array assign='datetime_js_methods'
            onblur="Garitagepark_Ticket_Schedule.onChangeDatetime(this);"
            disallow_date_before=$smarty.now|date_format:#date_iso_short#
            disallow_date_after=$document->get('deadline')|default:''
    }
    <tr>
      <td class="labelbox">
        {#plugin_scheduled_time#|escape}:
      </td>
      <td class="required">{#required#}</td>
      <td>
        <a id="error_event_start" class="floatl"></a>
        {include file="input_datetime.html"
            standalone=true
            name='event_start'
            value=$event->get('event_start')|default:''
            width='100'
            label=#events_start_date#
            js_methods=$datetime_js_methods
        }
        <span class="floatl" style="padding: 0 5px;">
          {#to#|mb_lower}
        </span>
        <a id="error_event_end" class="floatl"></a>
        {include file="input_datetime.html"
            standalone=true
            name='event_end'
            value=$event->get('event_end')|default:''
            width='100'
            label=#events_event_end#
            js_methods=$datetime_js_methods
        }
      </td>
    </tr>
    <tr>
      <td class="labelbox">
        {#plugin_participants#|escape}:
      </td>
      <td class="unrequired">&nbsp;</td>
      <td class="nowrap">
        {include file="input_autocompleter.html"
            standalone=true
            name='participants'
            autocomplete=$participants_autocomplete
            autocomplete_var_type='basic'
            width='400'
            label=#autocomplete_search_users#
            show_placeholder='label'
        }
      </td>
    </tr>
    <tr class="row_assign">
      <td colspan="3">
        <a id="error_participants"></a>
        {include file=`$templatesDir`_assign_participants.html
            user_intervals=$event->get('user_intervals')
            availability_events=$event->get('availability_events')
            before_width=$event->get('before_width')
            event_width=$event->get('event_width')
            end_width=$event->get('end_width')
            event_hours=$event->get('event_hours')
            start_hour=$event->get('start_hour')
        }
      </td>
    </tr>
    <tr>
      <td colspan="3">
        &nbsp;
      </td>
    </tr>
    <tr>
      <td colspan="3" class="hright">
        {include file=`$theme->templatesDir`cancel_button.html}
        <button type="button" name="saveButton1" class="button" onclick="Garitagepark_Ticket_Schedule.save(this.form);">{#save#|escape}</button>
        {if $document->get('edit')}
        <button type="button" name="btn_manage_ticket" class="button" onclick="Garitagepark_Ticket_Schedule.manageTicket(event, {ldelim}id: {$event->get('ticket')|default:0}{rdelim});">{#plugin_manage_ticket#|escape}</button>
        {/if}
        <button type="button" name="btn_view_ticket" class="button" onclick="Garitagepark_Ticket_Schedule.viewTicket(event, {ldelim}id: {$event->get('ticket')|default:0}{rdelim});">{#plugin_view_ticket#|escape}</button>
      </td>
    </tr>
  </table>
</form>
