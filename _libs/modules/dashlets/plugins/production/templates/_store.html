<div class="t_section_title">
  <h1 class="hcenter">{#plugin_store_title#|escape} <span class="green">{$document->get('date')|date_format:#date_short#}, {$document->getPlainVarValue('workplace_name')|escape}, {$document->getPlainVarValue('workshift_name')|escape} ({#plugin_production_schedule_short#|escape} {$document->getPlainVarValue('production_schedule_name')|escape} {$document->getPlainVarValue('product_name')|escape})</span></h1>
</div>
<div class="section_container nopadding" style="max-width: 1000px;">
  <form action="">
    <input type="hidden" name="id" id="id" value="{$document->get('id')}" />
    {include file=`$theme->templatesDir`_gt2_edit.html
             model=$reason
    }
    <br class="clear" />
    <br class="clear" />
    <div class="hright section_container full_width">
      <button class="button" type="button" onclick="production.load({ldelim}action: 'home'{rdelim});">{#back#|mb_upper|escape}</button>
      <button class="button green" type="button" onclick="production.load({ldelim}form: this.form, action: 'store', complex: 1{rdelim});">{#confirm#|mb_upper|escape}</button>
    </div>
    {include file=`$templatesDir`_messages_content.html}
    <script type="text/javascript" defer="defer">
      production.processLoadMaterials({$articles_serial});
    </script>
  </form>
</div>