<form name="plugin_personal_settings" action="" method="post">
  <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table t_table1">
    {array assign='layout' id='plugin_settings' edit=1 view=1 visible=1 system=0 keyword="plugin_settings" cookie='on' name=#settings#}
    {array assign='layouts_vars' plugin_settings=$plugin_fields}
    <tr>
      <td colspan="3" class="nopadding">
        <div class="t_section_title t_caption3_title">
          {$layout.name|escape}
          <img src="{$theme->imagesUrl}small/delete.png" onclick="return production.settings();" class="pointer floatr" alt="{#close#|escape}" title="{#close#|escape}" />
        </div>
      </td>
    </tr>
    {include file=`$smarty.const.PH_MODULES_DIR`documents/templates/_manage_vars.html}
    <tr>
      <td colspan="3">
        <button type="button" class="button floatr" onclick="production.load({ldelim}action: 'settings', form: this.form, target: this.up('.settings_container'), complex: 1{rdelim});">{#save#|escape}</button>
      </td>
    </tr>
  </table>
</form>
