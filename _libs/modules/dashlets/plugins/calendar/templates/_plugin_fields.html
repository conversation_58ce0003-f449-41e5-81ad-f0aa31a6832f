{assign var='custom_filters' value=$dashlet->get('filters')}
{if !$custom_filters.period}
  {assign var='value' value='month'}
{else}
  {assign var='value' value=$custom_filters.period}
{/if}
{if !$custom_filters.participation}
  {assign var='value2' value='both'}
{else}
  {assign var='value2' value=$custom_filters.participation}
{/if}
<table cellpadding="0" cellspacing="0" border="0" class="t_table" id="custom_fields">
  <tr>
    <td class="labelbox">
      {help label_content=#plugin_period# text_content=''}
    </td>
    <td class="required">{#required#}</td>
    <td>
    {if $action ne 'view'}
      {include file=`$theme->templatesDir`input_radio.html
               options=$periods
               value=$value
               name='period'
               standalone=true
      }
    {else}
      {$periods.$value.label}
    {/if}
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      {help label_content=#plugin_participation# text_content=''}
    </td>
    <td class="required">{#required#}</td>
    <td>
    {if $action ne 'view'}
      {include file=`$theme->templatesDir`input_radio.html
          options=$participations
          value=$value2
          name='participation'
          standalone=true
      }
    {else}
      {$participations.$value2.label}
    {/if}
    </td>
  </tr>
</table>
