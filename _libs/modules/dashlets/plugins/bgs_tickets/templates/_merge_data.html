{if $merge_enabled}
<div class="t_top_border content_block">
    <h1 class="hcenter">{#plugin_related_tickets#|escape}</h1>
    <div class="" style="margin: 0 auto; padding: 5px;">
        {if $relatives}
        <div class="relatives" style="margin: 10px 0;">
            {foreach from=$relatives item='relative'}
            <div class="relative" data-id="{$relative.id}">{$relative.full_num|numerate:$relative.direction}</div>
            {/foreach}
        </div>
        {/if}
        {if $merge_autocomplete}
        <label class="labelbox" for="{$merge_autocomplete.custom_id}">{#plugin_merge_to#}</label>
        {include file=`$theme->templatesDir`input_autocompleter.html
            name=$merge_autocomplete.name
            custom_id=$merge_autocomplete.custom_id
            autocomplete=$merge_autocomplete.autocomplete
            value=$merge_autocomplete.value
            width=160
            standalone=true
            index=null
        }
        <input type="button" value="OK" class="button merge" data-merge-to-field="{$merge_autocomplete.autocomplete.id_var}" />
        {/if}
    </div>
</div>
{/if}
