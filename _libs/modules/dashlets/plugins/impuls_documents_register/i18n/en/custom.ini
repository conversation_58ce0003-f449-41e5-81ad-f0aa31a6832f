#LABELS FOR ALL PLUGINS
dashlets_dashlet_for = Dashlet for
plugin_name = Name
plugin_description = Description
dashlets_default = Add by default
plugin_full_width = Full screen width
help_dashlets_default = Specifies whether the dashlet to be added by default

help_dashlets_dashlet_for = Module or report dashlet is for
help_plugin_full_width = If selected, dashlet will be displayed in full screen width

plugin_records_per_page = Records per page

#PLUGIN SPECIFIC LABELS
plugin_impuls_add_date = Added date
plugin_impuls_add_date_from = from
plugin_impuls_add_date_to = to
plugin_impuls_executor = Executor
plugin_impuls_document_num = Document num
plugin_impuls_key_word = Key word
plugin_impuls_customer = Customer
plugin_impuls_project = Project/Location
plugin_document_type = Document type

plugin_document_type_documents = Documents
plugin_document_type_finance_documents = Financial documents

plugin_impuls_status_opened = Opened
plugin_impuls_status_locked = Locked
plugin_impuls_status_finished = Finished
plugin_impuls_status_closed = Closed
plugin_impuls_payment_status_paid = paid
plugin_impuls_payment_status_unpaid = unpaid
plugin_impuls_payment_status_partial = partial

plugin_impuls_document_num = Document num
plugin_impuls_date = Date
plugin_impuls_customer_num = Custom num
plugin_impuls_document_type = Type
plugin_impuls_name = Name
plugin_impuls_description = Description
plugin_impuls_total_value = Total value
plugin_impuls_total_value_with_vat = Total value (VAT)
plugin_impuls_deadline = Deadline
plugin_impuls_status = Status
plugin_impuls_payment_status = Payment status
plugin_impuls_tags = Tags