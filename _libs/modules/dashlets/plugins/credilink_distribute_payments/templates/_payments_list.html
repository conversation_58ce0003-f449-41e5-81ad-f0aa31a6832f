{if $payments}
  <div style="margin-bottom: 3px;"><i>* {#plugin_credilink_note_distribution#|escape}</i></div>
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border" style="vertical-align: middle;"><div style="width: 200px;">{#plugin_credilink_customer#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 120px;">{#plugin_credilink_payment_sum#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 120px;">{#plugin_credilink_payment_undistributed_sum#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 70px;">{#plugin_credilink_payment_date#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 150px;">{#plugin_credilink_payment_reason#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 200px;">{#plugin_credilink_documents#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 120px;">{#plugin_credilink_document_obligations_org_currency#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 120px;">{#plugin_credilink_document_obligations_pay_currency#|escape}</div></td>
      <td style="vertical-align: middle;"><div style="">&nbsp;</div></td>
    </tr>
    {foreach from=$payments item=payment name=pay}
      {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
      {foreach from=$payment.obligations item=obligation name=obl}
        <tr class="{$current_row_class}">
          {if $smarty.foreach.obl.first}
            <td class="t_border vmiddle" rowspan="{$payment.rowspan}">
              <a target="_blank" href="{$payment.customer_link}">{$payment.customer_name|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
              {$payment.amount|string_format:"%.2f"|default:"0.00"} {$payment.currency|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
              {$payment.not_distributed_amount|string_format:"%.2f"|default:"0.00"} {$payment.currency|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
              {$payment.issue_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border vmiddle" rowspan="{$payment.rowspan}">
              {$payment.reason|escape|nl2br|default:"&nbsp;"}
            </td>
          {/if}
          <td class="t_border">
            <a target="_blank" href="{$obligation.link}">
              {$obligation.visible_name|escape|default:"&nbsp;"}
            </a>
          </td>
          <td class="t_border hright">
            {$obligation.obligation_original|string_format:"%.2f"|default:"0.00"} {$obligation.obligation_original_currency|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright">
            {$obligation.obligation_converted|string_format:"%.2f"|default:"0.00"} {$obligation.obligation_converted_currency|escape|default:"&nbsp;"}
          </td>
          {if $smarty.foreach.obl.first}
            <td rowspan="{$payment.rowspan}" class="vmiddle" style="padding: 3px;">
              {if $payment.distributable}
                <img src="{$theme->imagesUrl}go.png" onclick="loadDistributionForm('{$payment.id}')" style="cursor: pointer;" alt="{#plugin_credilink_distribute_payment#|escape}" title="{#plugin_credilink_distribute_payment#}" />
              {else}
                &nbsp;
              {/if}
            </td>
          {/if}
        </tr>
      {foreachelse}
        <tr class="{$current_row_class}">
          <td class="t_border vmiddle" rowspan="{$payment.rowspan}">
            {$payment.customer_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
            {$payment.amount|string_format:"%.2f"|default:"0.00"} {$payment.currency|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
            {$payment.not_distributed_amount|string_format:"%.2f"|default:"0.00"} {$payment.currency|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$payment.rowspan}">
            {$payment.issue_date|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$payment.rowspan}">
            {$payment.reason|escape|nl2br|default:"&nbsp;"}
          </td>
          <td colspan="4" class="hcenter">
            <span style="font-style: italic; color: #949599; width: 100%;">{#plugin_credilink_no_incomes_reasons_to_distribute_to#|escape}</span>
          </td>
        </tr>
      {/foreach}
    {/foreach}
  </table>
{else}
  <span style="color: red">{#plugin_credilink_no_payments_to_distribute#|escape}</span>
{/if}
