if (env.current_lang == 'bg') {
    i18n['messages']['error_browser_version'] = 'Версията на браузъра Ви не позволява коректна работа с инфо панела.<br />Моля, обновете браузъра си или отворете приложението с друг браузър!';
} else {
    // for any other language messages will be in English
    i18n['messages']['error_browser_version'] = 'Your browser version does not allow correct work with the dashlet. Please, update your browser or open the application with another browser!';
}

/**
 * Check browser version, otherwise GT2 calculations will not work
 *
 * @param {number} d_id - dashlet identifier
 */
checkBrowserVersion = function(d_id) {
    var content_dashet = $('content_dashlet_' + d_id);
    if (typeof (gt2calc) == 'undefined' && content_dashet) {
        // IE8 or older
        content_dashet.innerHTML = '<div class="error hcenter" style="margin: 10px;">' + i18n['messages']['error_browser_version'] + '<div>';
    }
    return true;
};

/**
 * Loads data for leaving form added today (if such exists) from cookie
 * holding patient id
 */
loadLeavingFormFromCookie = function() {
    var scope = $('content_dashlet_' + $('rlf_dashlet_id').value);
    if (Cookie.get('rlf_patient') && scope) {
        scope.select('#rlf_patient')[0].value = Cookie.get('rlf_patient');
        Cookie.erase('rlf_patient');
        refreshAutocompleteItems(window['params_' + scope.select('#rlf_patient_autocomplete')[0].getAttribute('uniqid')]);
    }
};

/**
 * Loads data for leaving form added today (if such exists) when patient is selected
 *
 * @param {Object} autocomplete - autocompleter settings
 * @param {Object} data - autocompleter returned data
 */
loadLeavingForm = function(autocomplete, data) {
    var dashlet_id = $('rlf_dashlet_id').value;
    // selected patient
    var patient = 0;
    for (var s in data) {
        if (s == '$rlf_patient') {
            patient = data[s];
            break;
        };
    }

    // clear messages panel
    updateMessagesPanel('');

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=dashlets' +
        '&dashlets=custom_action' +
        '&plugin=reprobiomed_leaving_form' +
        '&dashlet=' + dashlet_id +
        '&custom_plugin_action=loadLeavingForm' +
        '&patient=' + patient +
        '&current_document=' + $('rlf_id').value;
    var opt = {
        asynchronous: false,
        method:       'get',
        onSuccess:    function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages = '';
            if (result.errors) {
                messages += '<ul class="error">\n';
                for (var err in result.errors) {
                    if (typeof result.errors[err] == 'string') {
                        messages += '<li>' + result.errors[err] + '</li>\n';
                    }
                }
                messages += '</ul>';
            } else {
                toggleButtonActive($('rlf_saveButton'), 1);
            }
            updateMessagesPanel(messages);

            if (typeof result.id != 'undefined' && $('rlf_id')) {
                $('rlf_id').value = result.id;
            }
            if (result.gt2) {
                var container = $('rlf_gt2_cell');
                if (container) {
                    container.innerHTML = result.gt2;
                    // load and execute scripts
                    var scripts = container.getElementsByTagName('script');
                    for (s in scripts) {
                        ajaxLoadJS(scripts[s]);
                    }
                }
            } else {
                // if gt2 is not reloaded, delete all rows containing tests
                $$('#content_dashlet_' + dashlet_id + ' .free_field4').each(function(el) {
                    if (el.value && el.up('tr').style.display != 'none') {
                        hideField('var_group_0', el.id.replace(/^.*_(\d+)$/, '$1'));
                    }
                });
            }
            if (typeof result.doctor != 'undefined') {
                var doctor = $('rlf_doctor');
                for (var i = 0; i < doctor.options.length; i++) {
                    if (doctor.options[i].value == result.doctor) {
                        doctor.options[i].selected = true;
                        if (doctor.attributes.onchange) {
                            doctor.onchange();
                        }
                        break;
                    }
                }
            }
            if (result.payment_type) {
                var payment_type = $('rlf_payment_type');
                for (var i = 0; i < payment_type.options.length; i++) {
                    if (payment_type.options[i].value == result.payment_type) {
                        payment_type.options[i].selected = true;
                        break;
                    }
                }
            }
            if (result.visit_type) {
                var visit_type = $('rlf_visit_type');
                for (var i = 0; i < visit_type.options.length; i++) {
                    if (visit_type.options[i].value == result.visit_type) {
                        visit_type.options[i].selected = true;
                        break;
                    }
                }
            }
            if (result.button_label && $('rlf_saveButton')) {
                $('rlf_saveButton').innerHTML = result.button_label;
            }
            if ($('rlf_selectButton')) {
                if (result.has_recommended_tests) {
                    removeClass($('rlf_selectButton'), 'hidden');
                } else {
                    addClass($('rlf_selectButton'), 'hidden');
                }
            }
            if (typeof result.notes != 'undefined' && $('rlf_notes')) {
                $('rlf_notes').value = result.notes;
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    new Ajax.Request(url, opt);
};

/**
 * Add/edit leaving form
 *
 * @param {Object} btn - button that triggered action
 */
saveLeavingForm = function(btn) {
    var dashlet_id = $('rlf_dashlet_id').value;

    Effect.Center('loading');
    Effect.Appear('loading');
    toggleButtonActive(btn, 0);

    var url = env.base_url + '?' + env.module_param + '=dashlets' +
        '&dashlets=custom_action' +
        '&plugin=reprobiomed_leaving_form' +
        '&dashlet=' + dashlet_id +
        '&custom_plugin_action=saveLeavingForm';
    var opt = {
        asynchronous: false,
        method:       'post',
        parameters:   Form.serialize(btn.form),
        onSuccess:    function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages = '';
            if (result.errors) {
                toggleButtonActive(btn, 1);
                messages += '<ul class="error">\n';
                for (var err in result.errors) {
                    if (typeof result.errors[err] == 'string') {
                        messages += '<li>' + result.errors[err] + '</li>\n';
                    }
                }
                messages += '</ul>';
                updateMessagesPanel(messages);
            } else if (result.messages) {
                var container = $('content_dashlet_' + dashlet_id);
                //container.innerHTML = '';
                addClass(container, 'dashlet_loader');

                messages += '<ul class="message">\n';
                for (var msg in result.messages) {
                    if (typeof result.messages[msg] == 'string') {
                        messages += '<li>' + result.messages[msg] + '</li>\n';
                    }
                }
                messages += '</ul>';

                add_messages = function () {
                    if (!container.className.match(/dashlet_loader/)) {
                        updateMessagesPanel(messages);

                        var scripts = container.getElementsByTagName('script');
                        for (var j = 0; j<scripts.length; j++) {
                            ajaxLoadJS(scripts[j]);
                        }

                        clearTimeout(dl_timer);
                    } else {
                        dl_timer = setTimeout(add_messages, 500);
                    }
                };

                dl_timer = setTimeout(add_messages, 500);
                dashletsLoad(container, 'plugin', 'reprobiomed_leaving_form', dashlet_id);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    new Ajax.Request(url, opt);
};

/**
 * Loads lightbox with recommended tests for patient
 *
 * @param {Object} btn - button that triggered action
 */
loadRecommendedTests = function(btn) {
    // selected patient
    var patient = 0;
    if ($('rlf_patient') != null) {
        patient = parseInt($('rlf_patient').value);
    }
    if (!patient || isNaN(patient)) {
        addClass(btn, 'hidden');
        return;
    }
    toggleButtonActive(btn, 0);
    toggleButtonActive($('rlf_saveButton'), 0);

    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_id = $('rlf_dashlet_id').value;
    var url = env.base_url + '?' + env.module_param + '=dashlets' +
        '&dashlets=custom_action' +
        '&plugin=reprobiomed_leaving_form' +
        '&dashlet=' + dashlet_id +
        '&custom_plugin_action=loadRecommendedTests';

    var params = {
        patient: patient,
        'selected_rows[]': []
    };
    $$('#content_dashlet_' + dashlet_id + ' .free_field4').each(function(el) {
        if (el.value && !el.className.match(/input_inactive/) && el.up('tr').style.display != 'none') {
            params['selected_rows[]'].push(el.value);
        }
    });

    var opt = {
        method: 'get',
        parameters: params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages = '';
            if (result.errors) {
                messages += '<ul class="error">\n';
                for (var err in result.errors) {
                    if (typeof result.errors[err] == 'string') {
                        messages += '<li>' + result.errors[err] + '</li>\n';
                    }
                }
                messages += '</ul>';
            }
            updateMessagesPanel(messages);
            if (result.gt2) {
                lb = new lightbox({
                    content: result.gt2,
                    title: result.title || '',
                    width: 850,
                    height: ''
                });
                lb.activate();
            }
            toggleButtonActive(btn, 1);
            toggleButtonActive($('rlf_saveButton'), 1);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    new Ajax.Request(url, opt);
};

/**
 * Process recommended tests marked as confirmed or denied
 */
processRecommmendedTests = function() {

    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_id = $('rlf_dashlet_id').value;
    var tests_confirmed = {}, tests_denied = [];

    // process radios in GT2 in lightbox
    $$('.lb_content table.grouping_table2 input[type="radio"]').each(function(el) {
        if (el.checked && el.className.match(/confirmed|denied/)) {
            var row = el.name.replace(/^.*\[(\d+)\]$/, '$1');
            if (el.className.match('confirmed')) {
                tests_confirmed[row] = {
                    category: el.up('tr').select('.free_field3')[0].value,
                    article_id: el.up('tr').select('.article_id')[0].value
                };
            } else {
                tests_denied.push(row);
            }
        }
    });

    if (lb && lb.active) {
        lb.deactivate();
    }

    // load confirmed tests into GT2 in dashlet
    var tbl = $$('#content_dashlet_' + dashlet_id + ' #var_group_0')[0], found;
    for (var row in tests_confirmed) {
        found = false;
        tbl.select('.free_field4').each(function(el) {
            if (el.value == row && el.up('tr').style.display != 'none') {
                if (el.className.match(/input_inactive/)) {
                    disableField(tbl.id, el.id.replace(/^(.*_)(\d+)$/, '$2'));
                }
                found = true;
                throw $break;
            }
        });
        if (found) {
            continue;
        }
        addField(tbl.id);
        var last_row = tbl.rows[$('gt2_delimeter').rowIndex - 1];
        last_row.select('.free_field3')[0].value = tests_confirmed[row].category;
        toggleUndefined(last_row.select('.free_field3')[0]);
        last_row.select('.article_id')[0].value = tests_confirmed[row].article_id;
        last_row.select('.free_field4')[0].value = row;
        // add temporary class name that field was just refreshed and should not be cleared in updateRow
        addClass(last_row.select('.free_field4')[0], 'refreshed');
        refreshAutocompleteItems(window['params_' + last_row.select('.article_name')[0].getAttribute('uniqid')]);
    }

    // update denied tests immediately
    if (tests_denied.length) {
        var messages_panel = $('messages_panel_' + dashlet_id);
        var params = {
            link: env.base_url + '?' + env.module_param + '=dashlets' +
                '&dashlets=custom_action' +
                '&plugin=reprobiomed_leaving_form' +
                '&dashlet=' + dashlet_id +
                '&custom_plugin_action=updateTests' +
                '&rows=' + tests_denied.join(',') +
                '&patient=' + $('rlf_patient').value,
            target: messages_panel,
            execute_after: function() {
                if (messages_panel.innerHTML) {
                    messages_panel.parentNode.style.display = '';
                    new Effect.ScrollTo(messages_panel);
                } else {
                    messages_panel.parentNode.style.display = 'none';
                }
            }
        };
        ajaxUpdater(params);
    }

    Effect.Fade('loading');
};

/**
 * Change all radios in recommended tests table to selected master option
 */
selectRecommendedAll = function(value) {
    $$('.lb_content table.grouping_table2 input[type="radio"]').each(function(el) {
        if (el.value == value) {
            el.checked = true;
        }
    });
};

/**
 * Updates quantity field according to type of nomenclature in all GT2 rows
 * when table is loaded
 */
processLoadGT2 = function() {
    var dashlet_id = $('rlf_dashlet_id').value;

    var gt2_qty = $$('#content_dashlet_' + dashlet_id + ' .grouping_table2 .quantity');
    for (var i = 0; i < gt2_qty.length; i++) {
        var article_type = $(gt2_qty[i].id.replace(/^quantity/, 'article_type'));
        if (article_type != null && article_type.value == 10) {
            gt2_qty[i].readOnly = true;
            addClass(gt2_qty[i], 'readonly');
            // add this class so that preventCorrectionColisions does not
            // unlock field
            addClass(gt2_qty[i], 'system_readonly');
        }
    }

    return true;
};

/**
 * Updates quantity, discount_percentage and free_field4 in GT2 row according
 * to type of selected nomenclature and selected option for visit type dropdown
 *
 * @param {Object} autocomplete - autocompleter settings
 * @param {Object} data - autocompleter returned data
 */
updateRow = function(autocomplete, data) {
    // row (group table row number)
    var row = '';
    if (autocomplete.field && autocomplete.field.match(/.*_\d+$/)) {
        row = autocomplete.field.replace(/.*(_\d+)$/, '$1');
    }
    // nomenclature type
    var type = 0;
    for (var s in data) {
        if (s == '$article_type') {
            type = data[s];
            break;
        };
    }
    var quantity = $('quantity' + row);
    if (quantity != null) {
        if (type == 10) {
            quantity.readOnly = true;
            addClass(quantity, 'readonly');
            // add this class so that preventCorrectionColisions does not
            // unlock field
            addClass(quantity, 'system_readonly');
        } else {
            quantity.readOnly = false;
            removeClass(quantity, 'readonly');
        }
    }
    updateDiscount($('rlf_visit_type'), $('discount_percentage' + row));

    // and then remove the class because of permitNegativeValues
    // oh, the insanity!
    removeClass(quantity, 'system_readonly');

    // field holds value of related row for laboratory testing, otherwise it
    // should be empty
    var related_row = $('free_field4' + row);
    if (related_row != null) {
        if (related_row.className.match(/refreshed/)) {
            removeClass(related_row, 'refreshed');
        } else {
            related_row.value = '';
        }
    }
};

/**
 * Change discount_percentage to 0 or 100 according to selected option of visit_type
 *
 * @param {Object} visit_type - visit type dropdown
 * @param {Object} discount_percentage - discount percentage field in a GT2 row
 * @return {Boolean}
 */
updateDiscount = function(visit_type, discount_percentage) {
    // if discount_percentage field is readonly, it was probably locked by
    // preventCorrectionColisions, so do not change its value
    if (discount_percentage != null && visit_type != null && !discount_percentage.readOnly) {
        if (!visit_type[visit_type.selectedIndex].className.match('free')) {
            discount_percentage.value = '100';
        } else {
            discount_percentage.value = '0';
        }
        // for preventCorrectionColisions
        if (typeof discount_percentage.onchange == 'function') {
            discount_percentage.onchange();
        }
        // for gt2calc
        if (typeof discount_percentage.onkeyup == 'function') {
            discount_percentage.onkeyup();
        }
    }
    return true;
};

/**
 * Change value of all discount_percentage fields to 0 or 100 when selected option
 * of visit_type is changed
 *
 * @param {Object} visit_type - visit type dropdown
 * @return {Boolean}
 */
updateAllDiscounts = function(visit_type) {
    var discount_fields = $$('#reprobiomed_leaving_form .gt2edit .discount_percentage');
    for (var i = 0; i < discount_fields.length; i++) {
        updateDiscount(visit_type, discount_fields[i]);
    }
    return true;
};

/**
 * Update content and visiblity of messages panel of dashlet
 *
 * @param {string} content - content to display
 * @return {Boolean}
 */
updateMessagesPanel = function(content) {
    var dashlet_id = $('rlf_dashlet_id').value;
    var messages_panel = $('messages_panel_' + dashlet_id);
    if (messages_panel) {
        messages_panel.innerHTML = content;
        messages_panel.parentNode.style.display = (content ? '' : 'none');
        if (content) {
            new Effect.ScrollTo(messages_panel);
        }
    }
    return true;
};

/**
 * Enable/disable specified button
 *
 * @param {Object} btn - button
 * @param {Boolean} enabled - if new state is enabled or disabled
 * @return {Boolean}
 */
toggleButtonActive = function(btn, enabled) {
    if (btn) {
        btn.disabled = !enabled;
        if (enabled) {
            removeClass(btn, 'inactive');
        } else {
            addClass(btn, 'inactive');
        }
    }
    return true;
};

/**
 * Position lightbox for suggested tests and its contents
 */
positionLightbox = function() {
    // if table is too long, make it fit the screen
    $$('.lb_content #gt2_container').each(function(el) {
        var max_height = Math.round(window.innerHeight ? window.innerHeight*0.9 : window.screen.availHeight*0.8);
        el.style.maxHeight = max_height + 'px';
        el.style.overflowX = 'hidden';
        el.style.overflowY = 'auto';
        // try to position lightbox vertically in the middle of the visible part of the window
        if (window.pageYOffset && el.offsetHeight >= max_height) {
            $('lightbox').style.top = (window.pageYOffset) + 'px';//$('lightbox').offsetTop +
        }
    });

};

