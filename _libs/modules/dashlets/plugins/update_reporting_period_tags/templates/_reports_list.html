<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_assesser_report#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_customer#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_user_bank#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_purpose#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_price#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_present_date#|escape}</div></td>
    <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#plugin_change_tags_reported_for_period#|escape}</div></td>
  </tr>
  {foreach from=$reports_list item=result name=results}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="t_border">
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}" target="_blank">{$result.full_num|default:"&nbsp;"}</a>
      </td>
      <td class="t_border">
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer_id}" target="_blank">{$result.customer_name|default:"&nbsp;"}</a>
      </td>
      <td class="t_border">
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.user_bank_id}" target="_blank">{$result.user_bank_name|default:"&nbsp;"}</a>
      </td>
      <td class="t_border">
        {$result.purpose_name|default:"&nbsp;"}
      </td>
      <td class="t_border hright">
        {$result.total|string_format:"%.2f"|default:"0.00"} {$result.currency|escape}
      </td>
      <td class="t_border">
        {$result.deadline|date_format:#date_mid#|escape|default:"&nbsp;"}
      </td>
      <td nowrap="nowrap" style="cursor: pointer;" onclick="{if $result.tags}clearReportTags('{$result.id}', '{$dashlet_plugin}', '{$dashlet_id}');{else}{if $choose_tags}loadTagsOptions('{$result.id}', '{$dashlet_plugin}', '{$dashlet_id}'){else}if (confirm('{$current_month_tags_message|escape:'javascript'}')) setMonthTags('{$result.id}', '{$dashlet_plugin}', '{$dashlet_id}', false);{/if}{/if}">
        {$result.tags|default:"&nbsp;"}
      </td>
    </tr>
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="7">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="7"></td>
  </tr>
</table>