{* Look the link bellow to know why DEFER is used *}
{* http://msdn.microsoft.com/en-us/library/ms533897.aspx *}
{assign var='custom_filters' value=$dashlet->get('filters')}
<script type="text/javascript">
  env.modelName = 'Contract';
</script>
<form method="post" id="correct_policy" onsubmit="return false" name="correct_policy" action="{$smarty.server.PHP_SELF}?{$module_param}=dashlets&amp;dashlets=custom_action&amp;custom_plugin_action=search_contract_data">
  <script defer="defer" src="{$scripts_url}?{$system_options.build}"></script>
  <script defer="defer" src="{$scripts_gt2_url}?{$system_options.build}"></script>
  <input id="correct_policy_dashlet_plugin" name="correct_policy_dashlet_plugin" value="{$dashlet_plugin}" type="hidden" />
  <input id="correct_policy_dashlet_id" name="correct_policy_dashlet_id" value="{$dashlet_id}" type="hidden" />
  <table border="0" cellpadding="5" cellspacing="0" style="width: 100%; padding-top: 5px; background-color: #F5F5F5;">
    <tr>
      <td class="labelbox" style="width: 80px !important;"><strong>{#plugin_correct_policy_contract#}:</strong></td>
      <td style="width: 300px;">
        {include file=`$theme->templatesDir`input_autocompleter.html
                 name='contract_ac'
                 width='250'
                 autocomplete=$contract_ac
                 autocomplete_var_type='basic'
                 standalone=true}
      </td>
      <td class="labelbox" style="width: 170px !important;"><strong>{#plugin_correct_policy_type_payment#}:</strong></td>
      <td>
        {include file=`$theme->templatesDir`input_dropdown.html
                 label=#plugin_correct_policy_type_payment#
                 help=#plugin_correct_policy_type_payment#
                 name='correct_policy_payment_type'
                 onchange='searchContractData()'
                 custom_id='correct_policy_payment_type'
                 width='250'
                 standalone=true}
      </td>
    </tr>
  </table>
</form>
<div id="full_contract_data" style="background-color: #F5F5F5;"></div>