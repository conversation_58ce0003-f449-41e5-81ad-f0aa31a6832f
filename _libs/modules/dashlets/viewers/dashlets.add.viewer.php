<?php

class Dashlets_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {

        $this->prepareTitleBar();

        require_once $this->modelsDir . 'dashlets.factory.php';

        if (!$this->registry->get('dashlet')) {
            require_once $this->modelsDir . 'dashlets.dropdown.php';
            $options = Dashlets_Dropdown::getDashletsModules(array($this->registry));

            //prepare add options
            if (isset($options['contain_optgroups']) && $options['contain_optgroups']) {
                unset($options['contain_optgroups']);
                $add_options= array ('optgroups' => $options);
            } else {
                $add_options= array ('options' => $options);
            }

            $this->data['modules'] = $add_options;
        } else {
            //prepare the data for the template

            $registry = &$this->registry;
            $request = &$registry['request'];

            $this->model     = $this->registry->get('dashlet');
            $session_filters = $this->model->get('filters');
            unset($this->model->properties['filters']);
            $this->data['dashlet'] = $this->model;

            //prepare group tree
            require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
            $this->data['groups'] = Groups::getTree($registry);

            //get the module and controller for the dashlet
            @list($module, $controller) = explode('_', $this->model->get('module_name'), 2);

            $params = array('module' => $module, 'controller' => $controller);
            if ($module == 'reports') {
                // Get the report
                require_once(PH_MODULES_DIR . 'reports/models/reports.factory.php');
                $report_filters = array('name' => $controller,
                                        'sanitize' => true);
                $report = Reports::getReports($this->registry, $report_filters);

                // Set the i18n name of the model
                $this->data['module_name_i18n'] = $report[0]->get('name');

                // suggest dashlet title
                if (!$this->model->get('name')) {
                    $this->model->set('name', $report[0]->get('name'), true);
                }

                // Get the custom filters and the custom report filters model from the properties of the dashlet
                $defineCustomFilters = $this->model->get('define_custom_filters');
                $filters             = $this->model->get('defined_custom_filters');
                unset($this->model->properties['define_custom_filters']);
                unset($this->model->properties['defined_custom_filters']);

                $report_type          = array('name' => $controller, 'source' => 'request');
                $already_used_filters = array();
                foreach ($filters as $key => $value) {
                    if (isset ($session_filters[$key])) {
                        $filters[$key]['value'] = $session_filters[$key];
                        if (isset ($session_filters[$key . '_name']) && isset ($session_filters[$key . '_name'])) {
                            $filters[$key]['value1'] = $session_filters[$key . '_code'];
                            $filters[$key]['value2'] = $session_filters[$key . '_name'];
                        } elseif (isset ($session_filters[$key . '_period']) && isset ($session_filters[$key . '_period_type'])) {
                            $filters[$key]['value1'] = $session_filters[$key . '_period'];
                            $filters[$key]['value2'] = $session_filters[$key . '_period_type'];
                        }
                    } else {
                        $filters[$key]['value'] = '';
                    }
                }

                if (!empty($defineCustomFilters) && method_exists($defineCustomFilters, 'processDependentFilters')) {
                    $filters = $defineCustomFilters->processDependentFilters($filters);
                }

                //separate the reports settings from the other filters
                $reports_settings = array();
                $already_used_filters = array();
                foreach ($filters as $key => $value) {
                    if (isset($value['setting']) && $value['setting']) {
                        if (! in_array($key, $already_used_filters)) {
                            if (isset($filters[$key . '_options'])) {
                                $value['additional_options'] = $filters[$key . '_options'];
                                $already_used_filters[] = $key . '_options';
                                unset($filters[$key . '_options']);
                            }
                            $reports_settings[] = $value;
                            unset($filters[$key]);
                        }
                    }
                }

                $this->data['report_filters']   = $filters;
                $this->data['reports_settings'] = $reports_settings;
                $this->data['report_type']      = $controller;

                // Prepare the display setting of the report dashlet
                $report_display_options = array(
                    array(
                        'label' => $this->i18n('dashlets_report_display_option_fully_functional'),
                        'option_value' => 'fully_functional',
                    ),
                    array(
                        'label'        => $this->i18n('dashlets_report_display_option_filters'),
                        'option_value' => 'filters'
                    )
                );
                if (file_exists(PH_MODULES_DIR . 'reports/plugins/' . $report[0]->get('type') . '/dashlet.html')) {
                    $report_display_options[] = array('label'        => $this->i18n('dashlets_report_display_option_results'),
                                                      'option_value' => 'results');
                } else {
                    // If no dashlet.html template then don't show the report filters into the dashlet settings
                    $report_display_options[] = array('label'        => $this->i18n('dashlets_report_display_option_results'),
                                                      'option_value' => 'results',
                                                      'disabled'     => true);
                    $this->data['hide_report_results'] = true;
                }
                $this->data['report_display_options'] = $report_display_options;
            } else {
                // set temporary routing parameters while processing saved search params
                $real_action = $this->registry['action'];

                $this->registry->set('action', 'getoptions', true);

                //get search definitions for selected module and controller from the data base
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');

                $controller_name = $module . ($controller && $controller != $module ? '_' . $controller : '');
                $controller_name = implode('_', array_map('ucfirst', explode('_', $controller_name))) . '_Controller';

                // use reflection to get static property of class
                require_once PH_MODULES_DIR . $module . '/controllers/' . $module . ($controller && $controller != $module ? '.' . $controller : '') . '.controller.php';
                $reflection_class = new ReflectionClass($controller_name);
                $prop = 'searchAdditionalVarsSwitch';
                $static_props = $reflection_class->getStaticProperties();
                $switch_additional = array_key_exists($prop, $static_props) !== false ? $static_props[$prop] : '';
                unset($reflection_class);

                // extract values of model types and names of all other search fields from session filters
                $params = $params + Filters::extractSearchFields($session_filters);

                //prepare advanced search definitions
                $registry['translater']->loadFile(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . ($module != $controller ? '_' . $controller : '') . '.ini');
                if ($module != $controller) {
                    $i18n_files = FilesLib::readDir(PH_MODULES_DIR . $module . '/i18n/' . $this->model->get('model_lang') . '/');
                    foreach ($i18n_files as $i18n_file) {
                        $registry['translater']->loadFile(PH_MODULES_DIR . $module . '/i18n/' . $this->model->get('model_lang') . '/' . $i18n_file);
                    }
                }
                list($advanced_search, $system_fields, $saved_filters) = Filters::getAdvancedSearchDefinitions($registry, $params);

                //prepare the basic sort definitions for optgroup
                $system_fields['sort'] = array('basic_vars' => $system_fields['sort']);

                //get additional variables if session filters contain model types and module has additional vars
                $additional_search = $columns_additional = array();
                if (!empty($params['model_types']) && $switch_additional) {
                    $request->set('model_types', $params['model_types'], 'all', true);
                    $additional_search = Filters::getAdditionalSearchDefs($registry, $params);

                    $additional_sortables = Filters::getAdditionalSortDefs($registry, array('module' => $module));
                    if ($additional_sortables) {
                        $system_fields['sort']['additional_vars'] = $additional_sortables;
                    }

                    // Get all common additional columns for the selected model types
                    $columns_additional = Dashlets::getAdditionalColumns($registry, General::plural2singular($module), $params['model_types'], true);

                    $request->remove('model_types');
                }

                if (!empty($system_fields['sort']['basic_vars'])) {
                    uasort($system_fields['sort']['basic_vars'], array('Filters', 'searchSort'));
                }
                if (!empty($system_fields['sort']['additional_vars'])) {
                    uasort($system_fields['sort']['additional_vars'], array('Filters', 'searchSort'));
                }

                //get columns that can be visible in the table with the results
                $columns_basic = Dashlets::getModuleFields($this->registry, $params);
                $prefix = $module . '_';
                if ($module != $controller) {
                    $prefix .= $controller . '_';
                }
                foreach ($columns_basic as $key => $field) {
                    $label = $registry['translater']->translate($prefix . $key);
                    if (!$label && $module == 'finance') {
                        $label = $registry['translater']->translate('finance_' . $key);
                    }
                    if (!$label) {
                        $label = $registry['translater']->translate($key);
                    }
                    $columns_basic[$key] = array('label'        => $label,
                                                 'option_value' => $key);
                }
                $columns_basic = array_values($columns_basic);
                uasort($columns_basic, array('Filters', 'searchSort'));

                // restore routing parameters
                $this->registry->set('action', $real_action, true);

                if ($module == $controller) {
                    $this->data['module_name_i18n'] = $registry['translater']->getParam('' , 'menu_' . $module);
                } else {
                    $this->data['module_name_i18n'] = $registry['translater']->getParam('' , 'menu_' . $module . '_' . $controller);
                }

                // If there are no additional columns
                if (empty($columns_additional)) {
                    // Prepare only the basic columns as options for the columns dropdown
                    $this->data['columns_options']   = $columns_basic;
                } else {
                    // Prepare the basic and the additional columns as optgroups for the columns dropdown
                    $this->data['columns_optgroups'] = array('basic_vars'      => $columns_basic,
                                                             'additional_vars' => $columns_additional);
                }

                $this->data['search_fields']['basic_vars']      = $advanced_search;
                $this->data['advanced_search_options']          = json_encode($advanced_search);
                $this->data['system_fields']                    = $system_fields;
                $this->data['switch_additional']                = $switch_additional;
                $this->data['search_fields']['additional_vars'] = $additional_search;
                $this->data['additional_search_options']        = json_encode($additional_search);
                $this->data['saved_filters']                    = $saved_filters;
            }

            $this->data['params']          = $params;
            if (!isset($session_filters['search_fields']) ||
                empty($session_filters['search_fields']) ||
                count(array_filter($session_filters['search_fields'])) == 0) {
                //remove the empty filters as to display the filter panel
                $session_filters = array();
            }
            $this->data['session_filters'] = $session_filters;
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets_add');
        $this->data['title'] = $title;
    }
}

?>
