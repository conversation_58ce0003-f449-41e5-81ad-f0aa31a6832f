<?php

require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';

/**
 * Crontabs model class
 */
Class Projects_Crontab extends Model {
    public $modelName = 'Projects_Crontab';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $this->logger = new Logger($this->modelName,
        'crontab/' . $params['action']. '_' .date('Y_m').'_cron' . '.log');

        $start_text = "\n======================\n"." (".date('Y-m-d H:i:s').")";
        $this->logger->info($start_text);

        $this->log_pattern = "projects:(%s); user:%s; email:%s; about:%s";
        $this->err_log_pattern = "projects:(%s); user:%s; !!!email:%s; about:%s";

        //set lang
        $this->lang = $this->registry['lang'];
        $this->registry->set('currentUser',Users::searchOne($this->registry,array('where'=>array('u.id=' . PH_AUTOMATION_USER, 'u.hidden=1'))), true);
    }

   /**
    * crontabLastSent() - get last sent info DB
    *
    * @param array $params - arrays to filter
    * @return array $list - array with results
    */
    public function lastSent($params = array()) {

        $where = array();

        $where[] = 'ct.recipient_type="user"';

        if (!empty($params['recipient_id'])) {
            $where[] = 'ct.recipient_id in ('.implode(',',$params['recipient_id']).')';
        }

        if (!empty($params['model_id'])) {
            $where[] = 'ct.model_id in ('.implode(',',$params['model_id']).')';
        }

        if (!empty($params['model'])) {
            $where[] = sprintf('ct.model="%s"',$params['model']);
        }

        if (!empty($params['group'])) {
            $group = $params['group'];
        }

        $query = 'SELECT ct.*,MAX(sent) as lsent FROM ' . DB_TABLE_CRONTAB .' as ct ';
        $query .= (!empty($where)) ? ' WHERE ' . implode(' AND ', $where) : '';
        $query .= ' group by recipient_id, model_id'.(!empty($group) ? ', '.$group : '');

        $last_sent = $this->registry['db']->GetAll($query);
        if (empty($last_sent)) {
            $last_sent = array();
        }
        $list = array();
        foreach ($last_sent as $snt) {
            $index = (!empty($group) ? $snt[$group] : '').'^'.$snt['model_id'].'^'.$snt['recipient_id'];
            $list[$index] = $snt['lsent'];
        }
        return $list;
    }

   /**
    * getExpiredPhases() - get expired phases from DB
    *
    * @param array $filters - arrays to filter
    * @return array with results
    */
    function getExpiredPhases($filters = array()) {

        $sql['select'] = 'SELECT p.id as idx, sh.stage_current_responsible as charge_person, ' . "\n" .
                         " IF(sh.started > '0000-00-00 00:00:00' AND
                        sh.finished = '0000-00-00 00:00:00' AND sh.stage_deadline > 0 AND
                        sh.stage_deadline < NOW(), 1, 0) as expired_time, " . "\n" .
                         '  si18n.name as expired_stage_name, s.status as stage_status, ' . "\n" .
                         '  p.*, pi18n.*, '.
                         '  u1.email as added_by_email, u1.active as added_by_active, ' . "\n" .
                         '  u2.email as charge_person_email, u2.active as charge_person_active, ' . "\n" .
                         '  u3.email as manager_email, u3.active as manager_active, ' . "\n" .
                         '  CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as charge_person_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as manager_name ' . "\n";

        $sql['from'] = 'FROM ' . DB_TABLE_STAGES_HISTORY . ' AS sh' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_STAGES . ' AS s' . "\n" .
                       '  ON (s.id=sh.stage_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS si18n' . "\n" .
                       '  ON (s.id=si18n.parent_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                       '  ON (sh.model_id=p.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (p.id=pi18n.parent_id AND pi18n.lang="' . $this->lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (p.customer=ci18n.parent_id AND ci18n.lang="' . $this->lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS . ' AS u1' . "\n" .
                       '  ON (p.added_by=u1.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (p.added_by=ui18n1.parent_id AND ui18n1.lang="' . $this->lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS . ' AS u2' . "\n" .
                       '  ON (sh.stage_current_responsible=u2.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (sh.stage_current_responsible=ui18n2.parent_id AND ui18n2.lang="' . $this->lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS . ' AS u3' . "\n" .
                       '  ON (p.manager=u3.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (p.manager=ui18n3.parent_id AND ui18n3.lang="' . $this->lang . '")';

        $where[] = "p.active = 1 AND p.deleted = 0";
        $where[] = 's.parent_id = 0';
        $where[] = 's.model = "Project"';
        $sql['where'] = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";
        $sql['having'] = 'HAVING expired_time = 1';

        $query = implode("\n", $sql);
        $records = $this->registry['db']->GetAssoc($query);

        return $records;
    }

    /**
     * expiredPhases
     *
     * @return bool - result of the operation
     */
    public function expiredPhases() {
        $template = 'crontab_expired_stages';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $db = $this->registry['db'];

        //get user whom not send emails
        $not_users = Users::getUsersNoSend($this->registry, $template);
        //get projects
        $expired_projects = $this->getExpiredPhases();
        $doc_id = array();
        //get projects ids
        $users = array();
        foreach ($expired_projects as $expired_project) {
            $doc_id[] = $expired_project['id'];
            if ($expired_project['added_by_email'] && $expired_project['added_by_active']==1 &&
                !isset($users[$expired_project['added_by']]['projects'][$expired_project['id']])) {
                $users[$expired_project['added_by']]['projects'][$expired_project['id']] = 'stage_added_by';
                $users[$expired_project['added_by']]['email'] = $expired_project['added_by_email'];
                $users[$expired_project['added_by']]['name'] = $expired_project['added_by_name'];
            }

            if ($expired_project['charge_person_email'] && $expired_project['charge_person_active']==1 &&
                !isset($users[$expired_project['charge_person']]['projects'][$expired_project['id']])) {
                $users[$expired_project['charge_person']]['projects'][$expired_project['id']] = 'stage_charge_in';
                $users[$expired_project['charge_person']]['email'] = $expired_project['charge_person_email'];
                $users[$expired_project['charge_person']]['name'] = $expired_project['charge_person_name'];
            }

            if ($expired_project['manager_email'] && $expired_project['manager_active']==1 &&
                !isset($users[$expired_project['manager']]['projects'][$expired_project['id']])) {
                $users[$expired_project['manager']]['projects'][$expired_project['id']] = 'stage_group_manager';
                $users[$expired_project['manager']]['email'] = $expired_project['manager_email'];
                $users[$expired_project['manager']]['name'] = $expired_project['manager_name'];
            }
        }

        //get last sent info
        $sent = $this->lastSent(array('model_id' => $doc_id, 'model'=>'project'));
        $db->StartTrans();

        $mailer = new Mailer($this->registry);
        foreach ($users as $user_id => $user) {
            if (!in_array($user_id,$not_users)) {
                $mailer->setTemplate($template);
                $this->logData($expired_projects, $user_id, $user, $sent, $mailer);
            }
        }

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Log data in file and DB
     *
     * @return bool - result of the operation
     */
    public function logData(&$expired_projects, &$user_id, &$user, &$sent, &$mailer) {

        $db = $this->registry['db'];
        $projects = array();
        foreach ($user['projects'] as $model_id => $about) {
            $k = '^'.$model_id.'^'. $user_id;
            if ((isset($sent[$k]) && (time()-strtotime($sent[$k]) > $this->registry['config']->getParam('crontab', 'send_interval')*3600))
                ||(!isset($sent[$k]) && $user['email'] != '')) {
                $project_view_url = sprintf('%s/index.php?%s=projects&projects=view&view=%d',
                                            $this->registry['config']->getParam('crontab', 'base_host'),
                                            $this->registry['module_param'], $model_id);
                $project_info = array(
                    'name' => $expired_projects[$model_id]['name'],
                    'customer_name' => $expired_projects[$model_id]['customer_name'],
                    'url' => $project_view_url
                );
                if (isset($expired_projects[$model_id]['expired_stage_name'])) {
                    $project_info['expired_stage_name'] = $expired_projects[$model_id]['expired_stage_name'];
                }
                $projects[] = $project_info;
            } else {
                unset($user['projects'][$model_id]);
            }
        }

        if ($projects) {
            $cViewer = new Viewer($this->registry);
            $cViewer->setFrameset('frameset_blank.html');
            $cViewer->template = 'projects.html';
            $cViewer->data['projects'] = $projects;
            $projects_info = $cViewer->fetch();
            $mailer->placeholder->add('projects_info', $projects_info);
            $mailer->placeholder->add('to_email', $user['email']);
            $mailer->placeholder->add('user_name', $user['name']);
            //echo "<br /> $about:".$user['email'];
            $result = $mailer->send();
            if (!@in_array($user['email'], $result['erred'])) {
                $set['recipient_id'] = $user_id;
                $set['model'] = "'project'";
                $set['sent'] = "now()";
                foreach ($user['projects'] as $model_id => $about) {
                    $set['model_id'] = $model_id;
                    $set['about'] = "'$about'";
                    $insert[] = implode(',' , $set);
                    $models_id[] = $model_id;
                }
                $query = "INSERT INTO " . DB_TABLE_CRONTAB .
                         " (recipient_id, model, sent, model_id, about) VALUES (" . implode("),( ", $insert) . ")";
                $db->Execute($query);
                $this->logger->info(sprintf($this->log_pattern, implode(',',$models_id),
                    $user['name'], $user['email'], $about));
                return true;
            } else {
                foreach ($user['projects'] as $model_id => $about) {
                    $models_id[] = $model_id;
                }
                $this->logger->error(sprintf($this->err_log_pattern, implode(',',$models_id),
                    $user['name'], $user['email'], $about));
                return false;
            }
        }
    }
}

?>
