<?php

class Index_Backend_Viewer extends Viewer {
    public $template = 'backend.html';

    public function prepare() {
        $menu = $this->data['menu'];
        $backend_modules = array();
        if (isset($menu['settings'])) {
            foreach($menu['settings']['options'] as $name => $item) {
                if ($item['name'] != '|' && $name != 'index_backend') {
                    $backend_modules[$name] = $item;
                }
            }
            $this->data['backend_modules'] = $backend_modules;
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('index_backend');
    }
}

?>
