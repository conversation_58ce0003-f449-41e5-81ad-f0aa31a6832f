<?php

trait AdvanceAddressUpdateNamesCitMunDisQrtStr {
    private $key_words_source = array('cities','municipalities','districts','quarters','streets');
    private $cities_names = array();
    private $municipalities_names = array();
    private $districts_names = array();
    private $quarters_names = array();
    private $streets_names = array();

    private $msg_format = array(
        'success' => array(
            'cities'         => '<div style="color: green;">Cities have been successfully updated in %s with IDs: %s</div>',
            'municipalities' => '<div style="color: green;">Municipalities have been successfully updated in %s with IDs: %s</div>',
            'districts'      => '<div style="color: green;">Districts have been successfully updated in %s with IDs: %s</div>',
            'quarters'       => '<div style="color: green;">Quarters have been successfully updated in %s with IDs: %s</div>',
            'streets'        => '<div style="color: green;">Streets have been successfully updated in %s with IDs: %s</div>',
            'bb'             => '<div style="color: green;">BB successfully updated in %s with IDs: %s</div>'
        ),
        'errors' => array(
            'cities'         => '<div style="color: darkred;">Cities update failed for %s with IDs: %s</div>',
            'municipalities' => '<div style="color: darkred;">Municipality update failed for %s with IDs: %s</div>',
            'districts'      => '<div style="color: darkred;">District update failed for %s with IDs: %s</div>',
            'quarters'       => '<div style="color: darkred;">Quarters update failed for %s with IDs: %s</div>',
            'streets'        => '<div style="color: darkred;">Streets update failed for %s with IDs: %s</div>',
            'bb'             => '<div style="color: darkred;">BB update failed for %s with IDs: %s</div>'
        )
    );

    private $messages = array(
        'success' => array(
            'cities' => array(),
            'municipalities' => array(),
            'districts' => array(),
            'quarters' => array(),
            'streets' => array(),
            'bb' => array(
                'documents' => array()
            ),
        ),
        'errors' => array(
            'cities' => array(),
            'municipalities' => array(),
            'districts' => array(),
            'quarters' => array(),
            'streets' => array(),
            'bb' => array(
                'documents' => array()
            ),
        )
    );

    private $bb_vars_names = array(
        'cities'         => array(),
        'municipalities' => array(),
        'districts'      => array(),
        'streets'        => array(),
        'quarters'       => array()
    );

    /**
     * Bullet which will fix the names of quarters, municipalities, cities, quarters and streets
     */
    public function updateNamesCitMunDisQtrStr() {
        set_time_limit(0);

        // get the names of the source nomenclatures
        foreach ($this->key_words_source as $key_word) {
            $this->getAANomNames($key_word);
        }

        $this->registry['db']->StartTrans();
        $this->updateAACitMunDisRelatedRecords();

        // process errors
        foreach ($this->messages as $msg_type => $msg_sources) {
            foreach ($msg_sources as $model_source => $model_targets) {
                foreach ($model_targets as $model_trgt => $model_ids) {
                    if (empty($model_ids)) {
                        continue;
                    }
                    if (!isset($this->msg_format[$msg_type][$model_source])) {
                        continue;
                    }
                    echo(sprintf($this->msg_format[$msg_type][$model_source], $model_trgt, implode(', ', $model_ids)));
                    echo('<div style="color: black;">====================</div>');
                }
            }
        }
        $result = $this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return !$result;
    }

    /**
     * Function to get the current names of required nomenclatures
     */
    public function getAANomNames($key_word='') {
        $setting_type_name = sprintf('nom_%s_type', $key_word);
        $setting_priority_var_name = sprintf('%s_update_search', $key_word);
        $property_var_name = sprintf('%s_names', $key_word);
        if (empty($this->settings[$setting_type_name])) {
            return;
        }

        $priority_records = array();
        if (!empty($this->settings[$setting_priority_var_name])) {
            $priority_records = array_filter(preg_split('#\s*,\s*#', $this->settings[$setting_priority_var_name]));
        }

        // get the names of the nomenclatures
        $sql = 'SELECT n.id, ni18n.name' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (n.deleted_by=0 AND n.active=1 AND n.type="' . $this->settings[$setting_type_name] . '"
                     AND ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '"
                     ' . (!empty($priority_records) ? ' AND n.id IN ("' . implode('","', $priority_records) . '")' : '') . ')' . "\n";
        $this->$property_var_name = $this->registry['db']->GetAssoc($sql);
    }

    /**
     * Function to process the related noms and update every pointed related model
     */
    public function updateAACitMunDisRelatedRecords() {
        // check which of the main noms have information for them
        $nomenclatures_related_key_names = array('quarter', 'street', 'market_information', 'prp_analog', 'pp_analog');
        foreach ($nomenclatures_related_key_names as $nom_key_name) {
            $type_setting_nm = sprintf('nom_type_%s', $nom_key_name);
            if (empty($this->settings[$type_setting_nm])) {
                continue;
            }
            $this->updateAARelatedRecords($nom_key_name, 'nomenclatures');
        }
        // check for documents to update
        $type_setting_nm = sprintf('doc_type_%s', 'report_one');

        if (!empty($this->settings[$type_setting_nm])) {
            $this->updateAARelatedRecords('report_one', 'documents');
        }

        if (!empty($this->settings['doc_type_report'])) {
            $this->updateAARelatedBBRecords();
        }
    }

    /**
     * Function to process the related noms and update every pointed related model
     */
    public function updateAARelatedRecords($key_word_upd, $model_name) {
        $rec_type_setting = sprintf('%s_type_%s', substr($model_name, 0, 3), $key_word_upd);
        $rel_main_table = defined(sprintf('DB_TABLE_%s', strtoupper($model_name))) ? constant(sprintf('DB_TABLE_%s', strtoupper($model_name))) : '';
        $rel_cstm_table = defined(sprintf('DB_TABLE_%s_CSTM', strtoupper($model_name))) ? constant(sprintf('DB_TABLE_%s_CSTM', strtoupper($model_name))) : '';
        if (empty($this->settings[$rec_type_setting]) ||
            !$rel_main_table ||
            !$rel_cstm_table) {
            return;
        }
        foreach ($this->key_words_source as $key_wrd_src) {
            $source_data_property = $key_wrd_src . '_names';
            if (empty($this->$source_data_property)) {
                // no information for this property
                continue;
            }
            $key_wrd_src_setting_id = sprintf('%s_var_%s_id', $key_word_upd, $key_wrd_src);
            $key_wrd_src_setting_nm = sprintf('%s_var_%s_name', $key_word_upd, $key_wrd_src);

            if (empty($this->settings[$key_wrd_src_setting_id]) || empty($this->settings[$key_wrd_src_setting_nm])) {
                // no set settings for the needed vars, so skip them
                continue;
            }

            // get the additional var which marks a street as duplicate
            // get the additional vars for the required model
            $vars_rel_model = array($this->settings[$key_wrd_src_setting_id], $this->settings[$key_wrd_src_setting_nm]);
            $sql = 'SELECT `name`, `id`, `multilang` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="' . ucfirst(General::plural2singular($model_name)) . '" AND `model_type`="' . $this->settings[$rec_type_setting] . '" AND `name` IN ("' . implode('","', $vars_rel_model) . '")';
            $vars_rel_model = $this->registry['db']->GetAssoc($sql);

            // get the related records which have to be updated
            $sql = 'SELECT t.id as `model_id`, t_cstm_nm.value as `name`, t_cstm_id.value as `id`' . "\n" .
                   'FROM ' . $rel_main_table . ' AS t' . "\n" .
                   'INNER JOIN ' . $rel_cstm_table . ' AS t_cstm_id' . "\n" .
                   ' ON (t.type="' . $this->settings[$rec_type_setting] . '" AND t.deleted_by=0 AND t_cstm_id.var_id ="' . $vars_rel_model[$this->settings[$key_wrd_src_setting_id]]['id'] . '" AND t_cstm_id.model_id=t.id AND t_cstm_id.value IN ("' . implode('","', array_keys($this->$source_data_property)) . '"))' . "\n" .
                   'INNER JOIN ' . $rel_cstm_table . ' AS t_cstm_nm' . "\n" .
                   ' ON (t_cstm_nm.var_id ="' . $vars_rel_model[$this->settings[$key_wrd_src_setting_nm]]['id'] . '" AND t_cstm_nm.model_id=t.id AND t_cstm_nm.lang="' . ($vars_rel_model[$this->settings[$key_wrd_src_setting_nm]]['multilang'] ? $this->registry['db'] : "") . '")' . "\n";
            $models_to_check = $this->registry['db']->GetAssoc($sql);

            // check the names and compare them with the new ones
            foreach ($models_to_check as $mdl_id => $mdl_data) {
                if ($mdl_data['name'] == $this->{$source_data_property}[$mdl_data['id']]) {
                    // this does not need update
                    continue;
                }

                //update the date for the current authority
                $query = 'UPDATE ' . $rel_cstm_table . "\n" .
                         'SET `value` = "' . General::slashesEscape($this->{$source_data_property}[$mdl_data['id']]) . '", `modified`= NOW(), `modified_by`="' . PH_AUTOMATION_USER . '"' . "\n" .
                         'WHERE `model_id` = '. $mdl_id . "\n" .
                         '  AND `var_id` = ' . $vars_rel_model[$this->settings[$key_wrd_src_setting_nm]]['id'] . "\n" .
                         '  AND (`lang`="" OR `lang`="' . $this->registry['lang'] . '")';

                $msg_key = 'errors';
                if ($this->registry['db']->Execute($query)) {
                    $msg_key = 'success';
                }
                if (!isset($this->messages[$msg_key][$key_wrd_src][$model_name])) {
                    $this->messages[$msg_key][$key_wrd_src][$model_name] = array();
                }
                $this->messages[$msg_key][$key_wrd_src][$model_name][] = $mdl_id;
            }
        }
    }

    /**
     * Method which will update the BBs in documents
     */
    public function updateAARelatedBBRecords() {
        // get the BB vars for all documents of the required type which are not empty
        $sql = 'SELECT bb.* FROM ' . DB_TABLE_BB . ' AS bb ' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (bb.`model_id`=d.`id` AND d.`deleted_by`=0 AND bb.`model`="Document" AND bb.`model_type`="' . $this->settings['doc_type_report'] . '" AND bb.`params` NOT LIKE "a:0:{}")' . "\n";
        $all_bbs = $this->registry['db']->GetAll($sql);

        $this->getBBVarsNames();

        // group the bbs by parent model
        $models_to_change = array();
        foreach ($all_bbs as $bb) {
            $bb_data = @unserialize($bb['params']);
            if (empty($bb_data)) {
                continue;
            }

            // define the vars which contains the needed data
            foreach ($this->key_words_source as $key_wrd_src) {
                $vars_ids_list = !empty($this->bb_vars_names[$key_wrd_src]['id']) ? $this->bb_vars_names[$key_wrd_src]['id'] : array();
                $vars_names_list = !empty($this->bb_vars_names[$key_wrd_src]['name']) ? $this->bb_vars_names[$key_wrd_src]['name'] : array();
                $id_var = array_intersect(array_keys($bb_data), $vars_ids_list);
                $id_var = reset($id_var);
                $nm_var = array_intersect(array_keys($bb_data), $vars_names_list);
                $nm_var = reset($nm_var);

                // if the BB is already set for change, get the changed version
                $temp_bb = $bb_data;
                if (isset($models_to_change[$bb['model_id']][$bb['id']])) {
                    $temp_bb = $models_to_change[$bb['model_id']][$bb['id']];
                }

                if (!isset($temp_bb[$id_var]) ||
                    !isset($this->{$key_wrd_src . '_names'}[$temp_bb[$id_var]]) ||
                    !isset($temp_bb[$nm_var]) ||
                    $this->{$key_wrd_src . '_names'}[$temp_bb[$id_var]] == $temp_bb[$nm_var]
                ) {
                    continue;
                }

                $temp_bb[$nm_var] = $this->{$key_wrd_src . '_names'}[$temp_bb[$id_var]];
                if (!isset($models_to_change[$bb['model_id']])) {
                    $models_to_change[$bb['model_id']] = array();
                }
                $models_to_change[$bb['model_id']][$bb['id']] = $temp_bb;
            }
        }

        // goes through all the models and performs the updates on the BBs
        foreach ($models_to_change as $model_id => $bb_updated) {
            $error = array();

            $this->registry['db']->StartTrans();

            foreach ($bb_updated as $bb_upd_id => $bb_upd) {
                $query = 'UPDATE ' . DB_TABLE_BB . "\n" .
                         'SET `params` = "' . General::slashesEscape(serialize($bb_upd)) . '" WHERE `id` = ' . $bb_upd_id;
                if (!$this->registry['db']->Execute($query)) {
                    $error[] = $bb_upd_id;
                }
            }

            if ($error) {
                $this->messages['errors']['bb']['documents'][] = $model_id . ' (BBs: ' . implode(',', $error) .')';
                $this->registry['db']->FailTrans();
            } else {
                $this->messages['success']['bb']['documents'][] = $model_id;
            }

            $this->registry['db']->CompleteTrans();
        }

        return true;
    }

    /**
     * Function to prepare the vars info for the BB
     *
     * @return void
     */
    private function getBBVarsNames(): void {
        foreach ($this->key_words_source as $key_wrd_src) {
            if (!isset($this->bb_vars_names[$key_wrd_src]) ||
                empty($this->settings['bb_var_' . $key_wrd_src . '_id']) ||
                empty($this->settings['bb_var_' . $key_wrd_src . '_name'])) {
                continue;
            }
            $this->bb_vars_names[$key_wrd_src]['id'] = array_filter(preg_split('#\s*,\s*#', $this->settings['bb_var_' . $key_wrd_src . '_id']));
            $this->bb_vars_names[$key_wrd_src]['name'] = array_filter(preg_split('#\s*,\s*#', $this->settings['bb_var_' . $key_wrd_src . '_name']));
        }
    }
}
