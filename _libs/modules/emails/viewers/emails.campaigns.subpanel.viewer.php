<?php

class Emails_Campaigns_Subpanel_Viewer extends Viewer {
    public $template = '_statistics_subpanel.html';

    public function prepare() {
        require_once $this->modelsDir . 'emails.campaigns.factory.php';
        require_once $this->modelsDir . 'emails.campaigns_statistics_details.factory.php';

        //prefix for the session param
        $session_param_prefix = 'statistics_subpanel_ajax_';

        $filters = Emails_Campaigns_Statistics_Details::saveSearchParams($this->registry, array(), $session_param_prefix);

        $stat_type = $this->registry['request']->get('stat_type');
        $conf_id = $this->registry['request']->get('subpanel');
        $page = $this->registry['request']->get('page');
        if ($page) {
            $filters['page'] = $page;
        }
        $filters['conf_id'] = $conf_id;
        $filters['stat_type'] = $stat_type;

        $emails_campaign = array(
            'where' => array('ecd.id = ' . $conf_id),
            'model_lang' => $this->registry['lang'],
            'sanitize' => true
        );
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $emails_campaign);

        $filters['status'] = $emails_campaign ? $emails_campaign->get('status') : '';

        // set custom sortables
        $this->sortables = array(
            'name' => 'name',
            'email' => 'email',
            'sent' => 'sent'
        );

        if (!empty($filters['sort'])) {
            foreach ($filters['sort'] as $idx => $sort_param) {
                $sort_param = trim(preg_replace('#\s+(ASC|DESC)#i', '', $sort_param));
                if (!in_array($sort_param, $this->sortables)) {
                    unset($filters['sort'][$idx]);
                }
            }
            $filters['sort'] = array_values($filters['sort']);
        }

        list($this->data['recipients'], $this->data['pagination']) = Emails_Campaigns_Statistics_Details::pagedSearch($this->registry, $filters);

        // form custom sort base url
        $sort_base = sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            'subpanel', $conf_id,
                            'stat_type', $stat_type);

        //prepare sort array for the listing
        $this->prepareAjaxSort($filters, $session_param_prefix, 'statistics_subpanel', $sort_base);

        $this->data['conf_id'] = $conf_id;
        $this->data['stat_type'] = $stat_type;

        $legend = '';
        if (!empty($emails_campaign)) {
            $emails_campaign->getConfigurations();
            foreach ($emails_campaign->get('conf_ids') as $idx => $value) {
                if ($value == $conf_id) {
                    $targetlist_names = $emails_campaign->get('targetlist_names');
                    $template_names = $emails_campaign->get('template_names');
                    $legend = sprintf($this->i18n('emails_campaigns_recipients_legend'), $targetlist_names[$idx], $template_names[$idx]);
                }
            }
        }

        if ($stat_type) {
            $this->data['emails_campaigns_recipients_legend'] = $legend . $this->i18n('emails_campaigns_recipients_' . $stat_type);
        }

        $this->setFrameset('frameset_blank.html');
    }
}

?>
