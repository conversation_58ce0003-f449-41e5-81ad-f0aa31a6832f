<?php

/**
 * Custom export of invoices to Microinvest
 */
Class Custom_Export extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Export';

    /**
     * The global registry
     */
    public static $registry;

    /**
     * Log data
     */
    public static $logData = array();

    /**
     * Settings
     */
    public static $settings = array();

    /**
     * Translates custom labels defined in the i18n of the plugin
     */
    public static function i18n($param) {
        return self::$registry['translater']->translate($param);
    }

    /**
     * Function to do the export of the invoices
     *
     * @param object $registry - the main registry
     */
    public static function export(&$registry) {
        self::$registry = &$registry;

        //prepare some of the parameters
        if (!defined('EXPORT_ENCODING')) {
            define('EXPORT_ENCODING', 'windows-1251');
        }
        if (!defined('EXPORT_DELIMITER')) {
            switch ($registry['request']->get('separator')) {
                case 'semicolon':
                    define('EXPORT_DELIMITER', ";");
                    break;
                case 'comma':
                    define('EXPORT_DELIMITER', ",");
                    break;
                case 'tab':
                    define('EXPORT_DELIMITER', "\t");
                    break;
                default:
                    define('EXPORT_DELIMITER', "|");
                    break;
            }
        }
        if (!defined('EXPORT_NEW_LINE')) {
            define('EXPORT_NEW_LINE', "\r\n");
        }
        if (!defined('EXPORT_SORT')) {
            define('EXPORT_SORT', "id DESC");
        }

        //get the file name
        $file_name = 'import.txt';

        //set flag to get tags for current model
        $registry->set('getTags',       true, true);
        $registry->set('prepareModels', true, true);

        //include the factory
        require_once PH_MODULES_DIR . 'finance/models/finance.' . $registry['controller'] . '.factory.php';

        //construct the factory name
        $factory_name = str_replace(' ', '_', ucwords(str_replace('_', ' ', 'finance_' . $registry['controller'])));

        // Get the previous list filters
        $prev_filters = $factory_name::saveSearchParams(
            $registry,
            array(),
            (preg_match('#^(search|list)_.*#', $registry['request']->get('session_param')) ?
                preg_replace('#^(search|list)_.*#', '$1', $registry['request']->get('session_param')) :
                'list') . '_'
        );

        // get available filter for the current action or selected items from the request
        if ($registry['request']->get('items')) {
            //get the alias
            $alias = $factory_name::getAlias($registry['module'], $registry['controller'], $registry['action']);

            /*
             * Prevent from exporting of records which the user has no permissions for
             */
            // Get the ids of all records using the previous list filters
            $all_items = $factory_name::getIds($registry, $prev_filters);
            // Get the ids of all selected items which exists into the $all_items array
            $items = array_intersect($registry['request']->get('items'), $all_items);

            $filters = array(
                'where' => array($alias . '.id IN (' . implode(', ', $items) . ')'),
                'sort' => array($alias . '.' . EXPORT_SORT)
            );
        } else {
            //prepare filters
            $filters = $prev_filters;
        }

        //we don't need to check again for the permissions for types
        //as the IDs are already filtered through these filters
        $filters['skip_permissions_check'] = true;

        // get all the records for the defined filters
        $records = $factory_name::search($registry, $filters);

        if ($registry['controller'] == 'payments') {
            //preform export content preparation
            $content = self::_formatExportPaymentsData($records);
        } else {
            //preform export content preparation
            $content = self::_formatExportFinancialData($records);
        }

//         $content_files = array_keys($content);
//         if ($registry['controller'] == 'payments' && empty($content[$content_files[0]])) {
//             return array('error' => array(self::i18n('error_no_invoices_to_export')));
//         } elseif ($registry['controller'] != 'payments' && (empty($content[$content_files[0]]) || empty($content[$content_files[1]]))) {
//             return array('error' => array(self::i18n('error_no_invoices_to_export')));
//         } else {
            //save the log
            Exports::exportLog($registry, array(
                                        'file_name' => $file_name,
                                        'log'       => self::$logData
                        ));
//         }

        //clean the output before sending the content to the browser
        ob_clean();

        //send it to the user
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header("Content-type: text/plain; charset=" . EXPORT_ENCODING);
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        //convert content to the enconding
        if (EXPORT_ENCODING != 'UTF-8') {
            $content = iconv("UTF-8", EXPORT_ENCODING . '//TRANSLIT', $content);
        }

        //send the content to the browser
        print $content;
    }

    /**
     * Prepares the content of the exported file for financial documents
     *
     * @param array $records   - the list of all invoice/debit notices/credite notices/annulments models
     * @return string - the prepared plain text
     */
    private static function _formatExportFinancialData($records) {
        $content = array();

        $customers = array();
        foreach ($records as $record) {
            if (!$record->get('num') || $record->get('status') != 'finished') {
                //do not export not issued invoices
                continue;
            }

            //define the currency rate
            $currency = $record->get('currency');
            if ($currency == 'BGN') {
                //the currency rate is 1
                $rate = 1;
            } else {
                //get currency rate for the day the invoice was issued
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $rate = Finance_Currencies::getRate(self::$registry, $currency, 'BGN', $record->get('issue_date'));
            }


            //the record type is defined for import documentation
            $record_type = '';
            switch ($record->get('type')) {
                case PH_FINANCE_TYPE_INVOICE:
                case PH_FINANCE_TYPE_PRO_INVOICE:
                    $record_type = self::i18n('invoice');
                    break;
                case PH_FINANCE_TYPE_CREDIT_NOTICE:
                    $record_type = self::i18n('credit');
                    break;
                case PH_FINANCE_TYPE_DEBIT_NOTICE:
                    $record_type = self::i18n('debit');
                    break;
            }

            //get customer data
            if ($record->get('customer') && !isset($customers[$record->get('customer')])) {
                // Get the customer data if not taken yet
                $query = 'SELECT c.is_company, ' . "\n" .
                         '  c.eik, ' . "\n" .
                         '  c.in_dds, ' . "\n" .
                         '  c.ucn, ci18n.mol, ci18n.address,' . "\n" .
                         '  IF(c.is_company!="", ci18n.company_name, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname))) AS customer_name,' . "\n" .
                         '  IF(c.is_company!="", ci18n.registration_address, ci18n.address_by_personal_id) AS company_address' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n ' . "\n" .
                         '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $record->get('model_lang') . '")' . "\n" .
                         'WHERE c.id=' . $record->get('customer');
                $customer_data = self::$registry['db']->GetRow($query);

                $customers[$record->get('customer')] = $customer_data;
            } elseif (isset($customers[$record->get('customer')])) {
                // If the customer data is already taken, then just use it
                $customer_data = $customers[$record->get('customer')];
            } else {
                // If there is no customer data then set an empty one
                $customer_data = array(
                    'code'                  => '',
                    'code_deliverer'        => '',
                    'is_company'            => '',
                    'eik'                   => '',
                    'in_dds'                => '',
                    'ucn'                   => '',
                    'mol'                   => '',
                    'address'               => '',
                    'customer_name'         => '',
                );
            }

            // define percents
            $payment_type = 0;
            if ($record->get('total_vat_rate') == 0) {
                $payment_type = 21;
            } elseif ($record->get('total_vat_rate') == 7) {
                $payment_type = 20;
            } else {
                $payment_type = 16;
            }

            // needs to check the included nomenclatures to define the value for the first column
            self::$registry->set('get_old_vars', true, true);
            $record->getVars();
            self::$registry->set('get_old_vars', false, true);

            $included_nomenclatures = array();
            $included_nomenclatures_data = array();
            $gt2_table = array();
            foreach ($record->get('vars') as $var) {
                if ($var['type'] == 'gt2') {
                    foreach ($var['values'] as $row_id => $row_values) {
                        if (!empty($row_values['article_id'])) {
                            $included_nomenclatures_data[] = array(
                                'article' => $row_values['article_id'],
                                'value'   => $row_values['subtotal_with_discount']
                            );
                            if (!in_array($row_values['article_id'], $included_nomenclatures)) {
                                $included_nomenclatures[] = $row_values['article_id'];
                            }
                        }
                    }
                    break;
                }
            }

            // check for advance nomenclatures
            $first_column_value = '';
            foreach ($included_nomenclatures_data as $inc_nom) {
                if ($inc_nom['article'] == ADVANCE_NOMENCLATURE && $inc_nom['value']>0) {
                    $first_column_value = POSITIVE_ADVANCE_VALUE;

                    // The first column value is already set so stop the loop
                    break;
                }
            }

            if ($first_column_value == '' && !empty($included_nomenclatures)) {
                // check the nomenclatures
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                $filters_noms = array(
                    'where'    => array(
                        'n.id IN (' . implode(',', $included_nomenclatures) . ')'
                    ),
                    'sanitize' => true
                );

                $current_model_nomeclatures = Nomenclatures::search(self::$registry, $filters_noms);

                $goods_others = 0;
                $services = 0;
                foreach ($current_model_nomeclatures as $curr_nom) {
                    if ($curr_nom->get('subtype') == 'commodity' || $curr_nom->get('subtype') == 'other') {
                        $goods_others++;
                    } elseif ($curr_nom->get('subtype') == 'service') {
                        $services++;
                    }
                }

                if ($goods_others > 0) {
                    $first_column_value = 2;
                } elseif ($services == count($included_nomenclatures)) {
                    $first_column_value = 5;
                }
            }

            // If there is still no value for the first column
            if ($first_column_value == '') {
                // Then set the default value: 2
                $first_column_value = 2;
            }

            //the invoice, credit, debit plain values data
            $data = array(
                $first_column_value,                                                                       //  1: POSITIVE_ADVANCE_VALUE (when there is an advance with positive value), 2 (when there are any goods or others) or 5 (when all nomenclatures are services)
                General::strftime('%d.%m.%Y', $record->get('issue_date')),                                 //  2: date of the document
                $record->get('num'),                                                                       //  3: the number if the invoice according to invoices counter
                $record_type,                                                                              //  4: document type "Ф-ра" - invoice, "ДИ" debit notice, "КИ" - credit notice
                ($record->get('annulled_by')) ? 0 : $record->get('total_with_vat'),                        //  5: total with VAT
                $payment_type,                                                                             //  6: payment_type
                $customer_data['customer_name'],                                                           //  7: customer name (company_name or name lastname for persons)
                ($customer_data['is_company'] ? $customer_data['mol'] :  $customer_data['customer_name']), //  8: customer contact person (mol)
                '',                                                                                        //  9: city (it should be empty)
                preg_replace('#(\n|\r)#', ' ', trim($customer_data['company_address'])),                   // 10: company address
                ($customer_data['is_company'] ? $customer_data['in_dds'] :  ''),                           // 11: in_dds
                ($customer_data['is_company'] ? $customer_data['eik'] :  $customer_data['ucn']),           // 12: eik / ucn
                '',                                                                                        // 13: not used
                preg_replace('#(\n|\r)#', ' ', $record->get('name')),                                      // 14: document name
                '',                                                                                        // 15: not used
                ($record->get('annulled_by')) ? 0 : $record->get('total_vat'),                             // 16: VAT
            );
            $content[] = implode(EXPORT_DELIMITER, $data);

            //apply this invoice to the log
            switch ($records[0]->get('type')) {
            case PH_FINANCE_TYPE_INVOICE:
            case PH_FINANCE_TYPE_PRO_INVOICE:
                $exported_type = 'invoices';
                break;
            case PH_FINANCE_TYPE_CREDIT_NOTICE:
                $exported_type = 'credit_notices';
                break;
            case PH_FINANCE_TYPE_DEBIT_NOTICE:
                $exported_type = 'debit_notices';
                break;
            }
            self::$logData[$exported_type][] = $record->get('id');

            // Tag this record
            if (defined('EXPORT_TAGS') && EXPORT_TAGS) {
                // Get the tags from the export settings (the tags that should be set)
                $set_tags = preg_split('#\s*,\s*#', EXPORT_TAGS);
                // Get the current model tags
                $model_tags = $record->get('tags');
                // Get the new tags that should be set
                $tags = array_unique(array_merge($set_tags, $model_tags));

                // Set tags only if not already tagged
                if (count($model_tags) != count($tags)) {
                    // Make a copy of the current model
                    $old_invoice = clone $record;
                    // Prepare the model
                    $record->unsanitize();
                    // Set the new tags
                    $record->set('tags', $tags, true);
                    // call addTags method directly to just add the export tags (skip validation)
                    if ($record->addTags($set_tags)) {
                        // Write history
                        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                        $audit_parent = Finance_Incomes_Reasons_History::saveData(self::$registry, array('model' => $record, 'action_type' => 'tag', 'new_model' => $record, 'old_model' => $old_invoice));
                    }
                }
            }
        }

        $content = implode(EXPORT_NEW_LINE, $content) . EXPORT_NEW_LINE;
        return $content;
    }

    /**
     * Prepares the content of the exported file for payments and their distrubution
     *
     * @param array $records - the list of all payment models
     * @return string - the prepared plain text
     */
    private static function _formatExportPaymentsData($records) {
        $content = array();

        $customers = array();
        $containers_ = array();
        foreach ($records as $record) {
            if (!$record->get('num') || $record->get('status') != 'finished') {
                //do not export not finished documents
                continue;
            }

            //get customer data
            if ($record->get('customer') && !isset($customers[$record->get('customer')])) {
                // Get the customer data if not taken yet
                $query = 'SELECT c.is_company, ' . "\n" .
                         '  c.eik, ' . "\n" .
                         '  c.in_dds, ' . "\n" .
                         '  c.ucn, ci18n.mol, ci18n.address,' . "\n" .
                         '  IF(c.is_company!="", ci18n.company_name, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname))) AS customer_name,' . "\n" .
                         '  IF(c.is_company!="", ci18n.registration_address, ci18n.address_by_personal_id) AS company_address' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n ' . "\n" .
                         '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $record->get('model_lang') . '")' . "\n" .
                         'WHERE c.id=' . $record->get('customer');
                $customer_data = self::$registry['db']->GetRow($query);

                $customers[$record->get('customer')] = $customer_data;
            } elseif (isset($customers[$record->get('customer')])) {
                $customer_data = $customers[$record->get('customer')];
            } else {
                $customer_data = array(
                    'code' => '',
                    'code_deliverer' => '',
                    'is_company' => '',
                    'eik' => '',
                    'in_dds' => '',
                    'ucn' => '',
                    'mol' => '',
                    'address' => '',
                );
            }

            //get relatives
            $record->getRelatives();

            if ($record->get('type') == 'RKO' || $record->get('type') == 'PN') {
                $invoices = $record->get('paid_reasons');
            } else {
                $invoices = $record->get('paid_invoices');
            }

            //ToDo: what to do if no related records?

            foreach ($invoices as $invoice_id => $invoice) {

                //the record type is defined in the Microinvest import documentation
                $record_type = '';
                switch ($invoice['type']) {
                    case PH_FINANCE_TYPE_INVOICE:
                    case PH_FINANCE_TYPE_PRO_INVOICE:
                        $record_type = self::i18n('invoice');
                        break;
                    case PH_FINANCE_TYPE_CREDIT_NOTICE:
                        $record_type = self::i18n('credit');
                        break;
                    case PH_FINANCE_TYPE_DEBIT_NOTICE:
                        $record_type = self::i18n('debit');
                        break;
                }
                // payment document data
                $data = array(
                    10,                                                                                   //  1: "2" stands for documents, "10" for payments
                    General::strftime('%d.%m.%Y', $record->get('issue_date')),                            //  2: payment date
                    $record->get('num'),                                                                  //  3: payment number
                    //ToDo: what should be record type if document is related to more than one financial document
                    $record_type,                                                                         //  4: document type "Ф-ра" - invoice, "ДИ" debit notice, "КИ" - credit notice
                    $invoice['paid_amount'],                                                              //  5: total paid amount
                    //ToDo: what is this?????
                    8,                                                                                    //  6: always 8?????
                    $customer_data['customer_name'],                                                      //  7: customer name (company_name or name lastname for persons)
                    $customer_data['mol'],                                                                //  8: customer contact person (mol)
                    '',                                                                                   //  9: city (it should be empty)
                    preg_replace('#(\n|\r)#', ' ', trim($customer_data['company_address'])),              // 10: company address
                    $customer_data['in_dds'],                                                             // 11: in_dds
                    $customer_data['eik'],                                                                // 12: eik
                    '',                                                                                   // 13: not used
                    //ToDo: payment name???
                    $record->get('name'),                                                                 // 14: document name
                    '',                                                                                   // 15: not used
                    //ToDo: VAT??? 0???
                    0,                                                                                    // 16: VAT??? should be 0
                );

                $content[] = implode(EXPORT_DELIMITER, $data);
            }

            //apply this invoice to the log
            self::$logData['payments'][] = $record->get('id');

            //tag the exported payments
           if (defined('EXPORT_TAGS') && EXPORT_TAGS) {
               //tag the invoice
               $set_tags = preg_split('#\s*,\s*#', EXPORT_TAGS);
               $model_tags = $record->get('tags');

               $tags = array_unique(array_merge($set_tags, $model_tags));

               //set tags only if not already tagged
               if (count($model_tags) != count($tags)) {
                   $old_payment = clone $record;
                   $record->unsanitize();
                   //set the new tags
                   $record->set('tags', $tags, true);
                   // call addTags method directly to just add the export tags (skip validation)
                   if ($record->addTags($set_tags)) {
                       //save history
                       require_once(PH_MODULES_DIR . 'finance/models/finance.payments.history.php');
                       $audit_parent = Finance_Payments_History::saveData(self::$registry, array('model' => $record, 'action_type' => 'tag', 'new_model' => $record, 'old_model' => $old_payment));
                   }
               }
               unset($record);
           }
        }

        $content = implode(EXPORT_NEW_LINE, $content) . EXPORT_NEW_LINE;

        return $content;
    }
}

?>
