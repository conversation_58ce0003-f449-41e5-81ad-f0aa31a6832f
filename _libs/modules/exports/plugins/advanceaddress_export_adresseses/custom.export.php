<?php

/**
 * Custom export
 */
Class Custom_Export extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Export';

    /**
     * The global registry
     */
    public static $registry;

    /**
     * Log data
     */
    public static $logData = array();

    /**
     * Settings
     */
    public static $settings = array();

    /**
     * Export row template array
     */
    public static $template_array = array(
        'id' => '',
        'num' => '',
        'date' => '',
        'customer' => '',
        'assignor' => '',
        'office' => '',
        'media' => '',
        'deadline' => '',
        'system_made_by' => '',
        'rating_type' => '',
        'assessment_purpose' => '',
        'award_date' => '',
        'order_date' => '',
        'send_doc_date' => '',
        'work_term' => '',
        'loan_applicant_name' => '',
        'bank_name' => '',
        'bb' => '',
        'bb_object_kind' => '',
        'bb_object_kind_id' => '',
        'bb_object_type' => '',
        'bb_object_type_id' => '',
        'bb_type_object' => '',
        'bb_type_name_object' => '',
        'bb_object_identifier' => '',
        'bb_city_id' => '',
        'bb_city' => '',
        'bb_address' => '',
        'bb_address_id' => '',
        'bb_address_number' => '',
        'bb_quarter' => '',
        'bb_quarter_id' => '',
        'bb_postcode' => '',
        'bb_municipality_id' => '',
        'bb_municipality' => '',
        'bb_area_id' => '',
        'bb_area' => '',
        'bb_xpos' => '',
        'bb_ypos' => '',
        'bb_market_value' => '',
        'bb_price_square_meter' => '',
        'bb_plain_address' => '',
        'reply_city' => '',
        'reply_region' => '',
        'reply_quarter' => '',
        'reply_street' => '',
        'reply_number' => '',
        'reply_x' => '',
        'reply_y' => '',
        'reply_address_id' => ''
    );

    /**
     * BB vars list
     */
    public static $bb_vars_list = array(
        'city' => 'CITY',
        'city_id' => 'CITY_ID',
        'object_kind' => 'OBJECT_KIND',
        'object_kind_id' => 'OBJECT_KIND_ID',
        'object_type' => 'OBJECT_TYPE',
        'object_type_id' => 'OBJECT_TYPE_ID',
        'type_object' => 'TYPE_OBJECT',
        'type_name_object' => 'TYPE_NAME_OBJECT',
        'object_identifier' => 'OBJECT_IDENTIFIER',
        'address' => 'ADDRESS',
        'address_id' => 'ADDRESS_ID',
        'address_number' => 'ADDRESS_NUMBER',
        'quarter' => 'QUARTER',
        'quarter_id' => 'QUARTER_ID',
        'postcode' => 'POSTCODE',
        'municipality_id' => 'MUNICIPALITY_ID',
        'municipality' => 'MUNICIPALITY',
        'area' => 'AREA',
        'area_id' => 'AREA_ID',
        'xpos' => 'XPOS',
        'ypos' => 'YPOS',
        'market_value' => 'MARKET_VALUE',
        'price_square_meter' => 'PRICE_SQUARE_METER'
    );

    /**
     * Data to export
     */
    public static $data = array();

    /**
     * Translates custom labels defined in the i18n of the plugin
     */
    public static function i18n($param) {
        return self::$registry['translater']->translate($param);
    }

    /**
     * Function to do the export of the invoices
     *
     * @param object $registry - the main registry
     */
    public static function export(&$registry) {
        set_time_limit(0);
        self::$registry = &$registry;

        $items = self::_getRecords();
        if (empty($items)) {
            return array('error' => array(self::i18n('error_no_models_to_export')));
        }

        //prepare some of the parameters
        if (!defined('EXPORT_ENCODING')) {
            define('EXPORT_ENCODING', 'UTF-8');
        }
        if (!defined('EXPORT_DELIMITER')) {
            switch ($registry['request']->get('separator')) {
                case 'semicolon':
                    define('EXPORT_DELIMITER', ";");
                    break;
                case 'comma':
                    define('EXPORT_DELIMITER', ",");
                    break;
                case 'tab':
                    define('EXPORT_DELIMITER', "\t");
                    break;
                default:
                    define('EXPORT_DELIMITER', ";");
                    break;
            }
        }
        if (!defined('EXPORT_NEW_LINE')) {
            define('EXPORT_NEW_LINE', "\r\n");
        }
        if (!defined('EXPORT_QUANTIFIER')) {
            define('EXPORT_QUANTIFIER', '"');
        }

        // Process the vars and take the list f names for each BB var
        foreach (self::$bb_vars_list as $bb_var_alias => $bb_var_setting) {
            if (defined($bb_var_setting) && constant($bb_var_setting)) {
                self::$bb_vars_list[$bb_var_alias] = preg_split('/\s*,\s*/', constant($bb_var_setting));
            } else {
                self::$bb_vars_list[$bb_var_alias] = array();
            }
        }

        // prepare the title row
        $title_row = array();
        foreach (self::$template_array as $key => $key_val) {
            $title_row[$key] = self::i18n('export_addresses_' . $key);
        }
        self::$data[] = $title_row;

        /*
         * process the data by models in partitions
         */
        $single_partition = 10;
        $offset = 0;
        $total_results = count($items);
        do {
            $current_part = array_slice($items, $offset, $single_partition);
            self::processModels($current_part);
            $offset += $single_partition;
        } while ($offset<$total_results);

        //preform export content preparation
        $content = self::_formatExportData();

        //get the file name and prepare it
        $file_name = $registry['request']->get('file_name') . '.csv';

        if (empty($content)) {
            return array('error' => array(self::i18n('error_no_models_to_export')));
        } else {
            //save the log
            Exports::exportLog($registry, array(
                                        'file_name' => $file_name,
                                        'log'       => self::$logData
                        ));
        }

        //clean the output before sending the content to the browser
        ob_clean();

        //send it to the user
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header("Content-type: text/plain; charset=" . EXPORT_ENCODING);
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        //convert content to the enconding
        if (EXPORT_ENCODING != 'UTF-8') {
            $content = iconv("UTF-8", EXPORT_ENCODING . '//TRANSLIT', $content);
        }

        //send the content to the browser
        print $content;
    }

    /**
     * Prepares general data (GT2 rows)
     */
    private static function _getRecords() {
        //construct the factory name
        $factory_name = 'Documents';

        // Get the previous list filters
        $prev_filters = $factory_name::saveSearchParams(
            self::$registry,
            array(),
            (preg_match('#^(search|list)_.*#', self::$registry['request']->get('session_param')) ?
                preg_replace('#^(search|list)_.*#', '$1', self::$registry['request']->get('session_param')) :
                'list') . '_'
        );

        // get available filter for the current action or selected items from the request
        if (self::$registry['request']->get('items')) {
            /*
             * Prevent from exporting of records which the user has no permissions for
             */
            // Get the ids of all records using the previous list filters
            $all_items = $factory_name::getIds(self::$registry, $prev_filters);
            // Get the ids of all selected items which exists into the $all_items array
            $items = array_intersect(self::$registry['request']->get('items'), $all_items);
        } else {
            //prepare filters
            $items = self::$registry['request']->get('items');
        }

        return $items;
    }

    /**
     * Get the data from selected models
     */
    private static function processModels($ids) {
        $filters = array(
            'where'                  => array('d.id IN (' . implode(',', $ids) . ')'),
            'model_lang'             => self::$registry['lang'],
            'skip_assignments'       => true,
            'skip_permissions_check' => true,
            'sanitize'               => true,

        );

        $documents = Documents::search(self::$registry, $filters);

        foreach ($documents as $doc) {
            $doc->unsanitize();
            $current_doc_array = self::$template_array;

            $current_doc_array['id'] = $doc->get('id');
            $current_doc_array['num'] = $doc->get('full_num');
            $current_doc_array['date'] = General::strftime(self::i18n('date_short'), $doc->get('date'));
            $current_doc_array['customer'] = $doc->get('customer_name');
            $current_doc_array['office'] = $doc->get('office_name');
            $current_doc_array['media'] = $doc->get('media_name');
            $current_doc_array['deadline'] = General::strftime(self::i18n('date_short'), $doc->get('deadline'));
            $current_doc_array['assignor'] = $doc->getVarValue(ASSIGNOR);
            $current_doc_array['system_made_by'] = $doc->getVarValue(SYSTEM_MADE_BY);
            $current_doc_array['rating_type'] = $doc->getVarValue(RATING);
            $current_doc_array['assessment_purpose'] = $doc->getVarValue(ASSESSMENT_PURPOSE);
            $current_doc_array['award_date'] = $doc->getVarValue(AWARD_DATE);
            $current_doc_array['order_date'] = $doc->getVarValue(ORDER_DATE);
            $current_doc_array['send_doc_date'] = $doc->getVarValue(SEND_DOC_DATE);
            $current_doc_array['work_term'] = $doc->getVarValue(WORK_TERM);
            $current_doc_array['loan_applicant_name'] = $doc->getVarValue(LOAN_APPLICANT);
            $current_doc_array['bank_name'] = $doc->getVarValue(BANK);

            $bb = $doc->getBB(array('model_id' => $doc->get('id')));
            foreach ($bb as $bb_r) {
                $address_elements = array(
                    'city' => '',
                    'quarter' => '',
                    'address' => '',
                    'address_number' => '',
                );
                $result_row = $current_doc_array;
                $result_row['bb'] = $bb_r['id'];

                foreach ($bb_r['params'] as $param => $param_value) {
                    foreach (self::$bb_vars_list as $bb_rel_var => $bb_related_vars) {
                        if (in_array($param, $bb_related_vars)) {
                            $result_row['bb_' . $bb_rel_var] = $param_value;
                            if (array_key_exists($bb_rel_var, $address_elements)) {
                                $address_elements[$bb_rel_var] = $param_value;
                            }
                            break;
                        }
                    }
                }
                $address_elements = array_filter($address_elements);
                $result_row['bb_plain_address'] = implode(', ', $address_elements);

                self::$data[] = $result_row;
            }
            $doc->sanitize();
        }
    }

     /**
     * Prepares the content of the exported file for the documents
     *
     * @return string - the prepared plain text
     */
    private static function _formatExportData() {
        $content = array();
        $content_data = array();

        foreach (self::$data as $rec_dat) {
            // create temp file in the memory
            $f = fopen('php://memory', 'w+');
            fputs($f, implode(EXPORT_DELIMITER, $rec_dat)."\n");
            rewind($f);
            $content_data[] = stream_get_contents($f);
            fclose($f);

            if (!empty($rec_dat['id'])) {
                self::$logData[] = $rec_dat['id'];
            }
        }

        //apply the new lines
        $content = implode('', $content_data);

        return $content;
    }

}

?>
