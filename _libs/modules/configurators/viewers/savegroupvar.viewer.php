<?php

class SaveGroupVar_Viewer extends Viewer {
    public $filters = array();

    public function prepare() {
        $this->data['model_id'] = $this->model->get('id') ?: $this->model->get('model_id');
        $request = &$this->registry['request'];

        if ($request->get('del_id') || $request->get('config_group_id')) {
            //deleted or saved configurator group
            $this->template = '_configurator_group_panel.html';
            $this->data['var']['grouping'] = $request->get('group_num');
            $this->data['config_group_id'] = $request->get('config_group_id');
        } else {
            //edit configurator group
            $this->template = '_configurator_group_edit.html';

            $group_num = $this->registry['configGroupData']['group_num'];
            if (!isset($group_num)) {
                $group_num = $request->get('group_num');
            }
            if (empty($group_num)) {
                $group_num = $this->registry['group_num'];
            }
            $config_group_vars = $this->registry['configGroupData']['params'];

            $vars = $this->model->get('vars');
            foreach ($vars as $var) {
                //find grouping var
                if ($var['type'] == 'grouping' && $var['grouping'] == $group_num) {
                    $group_var = $var;
                    $group_var['values'] = array();
                    // nums should start after current saved ones in db (if any),
                    // so that they are submitted as new rows
                    $max_num = (!empty($var['max_num']) ? $var['max_num'] : 0);
                    //set var values
                    foreach ($config_group_vars as $var_name => $var_value) {
                        if (in_array($var_name, $group_var['names'])) {
                            $key = array_search($var_name, $group_var['names']);
                            $new_num = $max_num;
                            if (is_array($var_value)) {
                                foreach ($var_value as $row_num => $val) {
                                    $group_var['values'][++$new_num][$key] = $val;
                                }
                            }
                        }
                    }
                    $this->data['var'] = $group_var;
                    break;
                }
            }
        }

        $this->setFrameset($this->template);
    }
}

?>
