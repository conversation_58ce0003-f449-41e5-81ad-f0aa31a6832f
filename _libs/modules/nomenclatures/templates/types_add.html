<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="nomenclatures" action="{$submitLink}" method="post" enctype="multipart/form-data">
<input type="hidden" name="id" id="id" value="{$nomenclatures_type->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$nomenclatures_type->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_basic_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='types_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox" name="name" id="name" value="{$nomenclatures_type->get('name')|escape}" title="{#nomenclatures_types_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name_plural"><label for="name_plural"{if $messages->getErrors('name_plural')} class="error"{/if}>{help label='types_name_plural'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox" name="name_plural" id="name_plural" value="{$nomenclatures_type->get('name_plural')|escape}" title="{#nomenclatures_types_name_plural#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_subtype"><label for="subtype"{if $messages->getErrors('subtype')} class="error"{/if}>{help label='types_subtype'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="radio" name="subtype" id="subtype_commodity" value="commodity"{if $nomenclatures_type->get('subtype') eq 'commodity'} checked="checked"{/if} onchange="if (this.checked) $('batch_options').style.display='';" /><label for="subtype_commodity">{#nomenclatures_types_subtype_commodity#}</label>
            <input type="radio" name="subtype" id="subtype_service" value="service"{if $nomenclatures_type->get('subtype') eq 'service'} checked="checked"{/if} onchange="if (this.checked) $('batch_options').style.display='none';" /><label for="subtype_service">{#nomenclatures_types_subtype_service#}</label>
            <input type="radio" name="subtype" id="subtype_other" value="other"{if $nomenclatures_type->get('subtype') eq 'other' || !$nomenclatures_type->get('subtype')} checked="checked"{/if} onchange="if (this.checked) $('batch_options').style.display='none';" /><label for="subtype_other">{#nomenclatures_types_subtype_other#}</label>
          </td>
        </tr>
        <tr id="batch_options"{if $nomenclatures_type->get('subtype') ne 'commodity'} style="display: none;"{/if}>
          <td class="labelbox">{help label='batch_options'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {capture assign=clickf}if (this.checked && !$('default_has_batch').checked) $('default_has_batch').checked = true;{/capture}
            {capture assign=clickf1}if (!this.checked) {ldelim}$('default_has_serial').checked = false;$('default_has_expire').checked = false;$('default_has_batch_code').checked = false;{rdelim}{/capture}
            {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclatures_type->get('default_has_batch') label=#nomenclatures_has_batch# name=default_has_batch onclick=$clickf1}
            {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclatures_type->get('default_has_serial') label=#nomenclatures_has_serial# name=default_has_serial onclick=$clickf}
            {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclatures_type->get('default_has_expire') label=#nomenclatures_has_expire# name=default_has_expire onclick=$clickf}
            {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclatures_type->get('default_has_batch_code') label=#nomenclatures_has_batch_code# name=default_has_batch_code onclick=$clickf}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_type_section"><label for="type_section"{if $messages->getErrors('type_section')} class="error"{/if}>{help label='type_section'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <select class="selbox" name="type_section" id="type_section" title="{#nomenclatures_type_section#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
            {foreach from=$type_section item='sections'}
              <option value="{$sections->get('id')}"{if $sections->get('id') eq $nomenclatures_type->get('type_section')} selected="selected"{/if}>{$sections->get('name')}</option>
            {/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='types_description'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
             <textarea class="areabox" name="description" id="description" title="{#nomenclatures_types_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$nomenclatures_type->get('description')}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_icon"><label for="icon_file"{if $messages->getErrors('icon')} class="error"{/if}>{help label='types_icon_file'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="file" name="icon_file" id="icon_file" class="filebox" />
          </td>
        </tr>
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_counter_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_counter"><label for="counter"{if $messages->getErrors('counter')} class="error"{/if}>{help label='types_counter'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="hidden" name="counter_prev" id="counter_prev" value="{$nomenclatures_type->get('counter')|escape}" />
            <select class="selbox{if !$nomenclatures_type->get('counter')} undefined{/if}" name="counter" id="counter" title="{#nomenclatures_types_counter#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); typeSelectCounter(this);" onkeyup="typeSelectCounter(this);">
              <option value="" class="undefined">[{#please_select#|escape}]</option>
              <option value="add_counter" class="undefined"{if $nomenclatures_type->get('counter') == 'add_counter'} selected="selected"{/if}>[{#nomenclatures_types_add_counter#|mb_upper|escape}]</option>
            {foreach from=$counters item='counter'}
              <option value="{$counter->get('id')}"{if $counter->get('id') eq $nomenclatures_type->get('counter')} selected="selected"{/if}>{$counter->get('name')|escape}</option>
            {/foreach}
            </select>
          </td>
        </tr>
        <tr class="section_add_counter{if $nomenclatures_type->get('counter') ne 'add_counter'} hidden{/if}">
          <td class="labelbox"><a name="error_counter_name"><label for="counter_name"{if $messages->getErrors('counter_name')} class="error"{/if}>{help label='counters_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox doubled" name="counter_name" id="counter_name" value="{$nomenclatures_type->get('counter_name')|escape}" title="{#nomenclatures_counters_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr class="section_add_counter{if $nomenclatures_type->get('counter') ne 'add_counter'} hidden{/if}">
          <td class="labelbox"><a name="error_formula"><label for="formula"{if $messages->getErrors('formula')} class="error"{/if}>{help label='counters_formula'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {include file=`$templatesDir`_counters_formula.html nomenclatures_counter=$nomenclatures_type->get('counter_model')}
          </td>
        </tr>
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_additional_settings_of_fields#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_validate_unique"><label for="validate_unique"{if $messages->getErrors('validate_unique')} class="error"{/if}>{help label='types_validate_unique'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <span style="position: absolute; left: 450px;{if $validate_unique_options|@count gt 10} padding-top: 13px;{/if}">
              <input type="checkbox" name="validate_unique_current_year" id="validate_unique_current_year" value="1"{if $nomenclatures_type->get('validate_unique_current_year')} checked="checked"{/if} title="{#nomenclatures_types_validate_unique_current_year#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" /><label for="validate_unique_current_year">{help label='types_validate_unique_current_year' label_sufix=''}</label>
            </span>
            {include file=`$theme->templatesDir`input_checkbox_group.html
                     name='validate_unique'
                     label=#nomenclatures_types_validate_unique#
                     options=$validate_unique_options
                     value=$nomenclatures_type->get('validate_unique')
                     standalone=true
            }
          </td>
        </tr>
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_default_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_auto_code"><label for="auto_code"{if $messages->getErrors('auto_code')} class="error"{/if}>{help label='types_auto_code'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <a name="error_auto_code_leading_zeros"><label for="auto_code_leading_zeros"{if $messages->getErrors('auto_code_leading_zeros')} class="error"{/if}>{help label='types_auto_code_leading_zeros'}</label></a>
            <select class="selbox" style="width: 40px;" name="auto_code_leading_zeros" id="auto_code_leading_zeros" title="{#nomenclatures_types_auto_code_leading_zeros#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
            {section name='i' loop=7 start=3}
              <option value="{$smarty.section.i.index}"{if ($smarty.section.i.index eq $nomenclatures_type->get('auto_code_leading_zeros')) || (!$nomenclatures_type->get('auto_code_leading_zeros') && $smarty.section.i.index eq 4)} selected="selected"{/if}>{$smarty.section.i.index}</option>
            {/section}
            </select>
            <a name="error_auto_code_suffix"><label for="auto_code_suffix"{if $messages->getErrors('auto_code_suffix')} class="error"{/if}>{help label='types_auto_code_suffix'}</label></a>
            <input type="text" class="txtbox" style="width: 40px;" name="auto_code_suffix" id="auto_code_suffix" value="{$nomenclatures_type->get('auto_code_suffix')|escape}" title="{#nomenclatures_types_auto_code_suffix#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_default_group"><label for="default_group"{if $messages->getErrors('default_group')} class="error"{/if}>{help label='types_group'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <select class="selbox{if !$nomenclatures_type->get('default_group')} undefined{/if}" name="default_group" id="default_group" title="{#nomenclatures_types_group#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
              <option value="" class="undefined">[{#please_select#|escape}]</option>
              <option value="[default_user_group]"{if $nomenclatures_type->get('default_group') eq '[default_user_group]'} selected="selected"{/if}>{#nomenclatures_types_default_user_group#}</option>
            {foreach from=$groups item='item'}
              {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $nomenclatures_type->get('default_group')}
              <option value="{$item->get('id')}"{if $item->get('id') eq $nomenclatures_type->get('default_group')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$item->get('name')|indent:$item->get('level'):"-"}</option>
              {/if}
            {/foreach}
            </select>
          </td>
        </tr>
{include file=`$templatesDir`_categories.html from_type=1}
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$nomenclatures_type}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
