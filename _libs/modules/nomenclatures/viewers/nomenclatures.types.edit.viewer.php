<?php

class Nomenclatures_Types_Edit_Viewer extends Viewer {
    public $template = 'types_edit.html';

    public function prepare() {
        $this->model = $this->registry['nomenclatures_type'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        // layouts search URL
        $this->data['layouts_search_url'] =
            sprintf('%s?%s=layouts&amp;layouts=search&amp;search_layout=1&amp;search_module=layouts&amp;search_controller=&amp;search_fields[0]=%s&amp;compare_options[0]=%%3D+\'%%25s\'&amp;values[0]=%s_%s&amp;sort[0]=l.place&amp;sort[1]=l.active&amp;order[0]=ASC&amp;order[1]=DESC&amp;display=100',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'],
                    'l.model4', ucfirst(General::plural2singular($this->module)), $this->model->get('id'));

        //prepare sections
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.sections.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('ns.active = 1'),
                         'sanitize' => true);
        $this->data['type_section'] = Nomenclatures_Sections::search($this->registry, $filters);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare counters
        require_once $this->modelsDir . 'nomenclatures.counters.factory.php';
        $filters = array('sanitize' => true);
        $this->data['counters'] = Nomenclatures_Counters::search($this->registry, $filters);

        //prepare categories tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = array_values(Nomenclatures_Categories::getTree($this->registry, array('sanitize' => true)));

        //get type categories
        $this->data['cats'] = $this->model->getCategories();

        $this->registry['include_tree'] = true;

        // get all available validate unique fields
        $validate_unique_fields = $this->registry['config']->getParamAsArray($this->module, 'validate_unique_fields');
        $validate_unique_options = array();
        foreach ($validate_unique_fields as $field) {
            $validate_unique_options[] = array(
                'label' => $this->i18n($this->module . '_' . $field),
                'option_value' => 'unique_' . $field,
                'active_option' => true
            );
        }
        $this->data['validate_unique_options'] = $validate_unique_options;

        //get unique fields of current type
        if (!$this->model->get('validate_unique') && !$this->registry['request']->isPost()) {
            $validate_unique = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('id'));
            $current_year = array_search('current_year', $validate_unique);
            if ($current_year !== false) {
                $this->model->set('validate_unique_current_year', true, true);
                unset($validate_unique[$current_year]);
            }
            $this->model->set('validate_unique', array_values($validate_unique), true);
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures_types_edit');
        $this->data['title'] = $title;
    }
}

?>
