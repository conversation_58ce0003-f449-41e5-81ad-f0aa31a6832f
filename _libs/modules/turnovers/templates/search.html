<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=turnovers&amp;turnovers=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
        <table cellspacing="0" cellpadding="0" border="0" id="turnovers_table" class="t_table t_list">
          <tr>
            <td class="t_caption t_border"><div class="t_caption_title"></div></td>
            <td class="t_caption t_border {$sort.customer.class}"><div class="t_caption_title" onclick="{$sort.customer.link}">{#turnovers_customer#}</div></td>
            <td class="t_caption t_border {$sort.trademark.class}"><div class="t_caption_title" onclick="{$sort.trademark.link}">{#trademark#}</div></td>
            <td class="t_caption t_border {$sort.day.class}"><div class="t_caption_title" onclick="{$sort.day.link}">{#date#}</div></td>
            <td class="t_caption t_border {$sort.total.class}"><div class="t_caption_title" onclick="{$sort.total.link}">{#turnovers_without_vat#} ({$currency})</div></td>
            <td class="t_caption t_border {$sort.total_with_vat.class}"><div class="t_caption_title" onclick="{$sort.total_with_vat.link}">{#turnovers_with_vat#} ({$currency})</div></td>
            <td class="t_caption">&nbsp;</td>
          </tr>
          {foreach from=$turnovers item=turnover}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border">
            {if ($period eq 'day' && $turnover.confirmed) || ($period eq 'month' && $turnover.confirmed_days && $turnover.month_days eq $turnover.confirmed_days) || ($period eq 'year' && $turnover.confirmed_days && $turnover.year_days eq $turnover.confirmed_days)}
            <img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" />{else}&nbsp;{/if}</td>
            <td class="t_border">{$turnover.customer_name|escape}</td>
            <td class="t_border">{$turnover.trademark_name|escape}</td>
            <td class="t_border">{if $period eq 'day'}{$turnover.date|date_format:#date_short#}{elseif $period eq 'month'}{$turnover.date|date_format:"%Y %B"|escape}{elseif $period eq 'year'}{$turnover.date}{/if}</td>
            <td class="t_border pricebox_view">{$turnover.total|escape|string_format:"%.2f"}</td>
            <td class="t_border pricebox_view">{$turnover.total_with_vat|escape|string_format:"%.2f"}</td>
            <td><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=input&amp;period={$period}&amp;customer={$turnover.customer}&amp;trademark={$turnover.trademark}&amp;{if $period eq 'day'}day_from={$turnover.date}&amp;day_to={$turnover.date}{elseif $period eq 'month'}month_from={$turnover.date}&amp;month_to={$turnover.date}{elseif $period eq 'year'}year={$turnover.date}{/if}"><img src="{$theme->imagesUrl}edit.png" width="16" height="16" border="0" alt="{#edit#|escape}" /></a></td>
          </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="7">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="7"></td>
          </tr>
        </table>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
