<?php

class Reports_Index_Viewer extends Viewer {
    public $template = 'index.html';

    public function prepare() {

        $current_report_details = '';
        //check for a generated report and sets its name
        if (isset($this->registry['report_type']['name'])) {
            $current_report_type = $this->registry['report_type']['name'];
        } else {
            $current_report_type = '';
        }

        //define filters
        $filters = Reports::saveSearchParams($this->registry, $this->registry->get('filters_values'), 'reports_' . $current_report_type . '_');
        list($reports_results, $pagination) = Reports::pagedSearch($this->registry, $filters);

        if (!empty($filters['report_type'])) {
            $current_report_type = $filters['report_type'];
            $filters_details['name'] = $filters['report_type'];
            $filters_details['sanitize'] = true;
            $current_report_full_details = Reports::getReports($this->registry, $filters_details);
            if (!empty($current_report_full_details)) {
                $current_report_details = $current_report_full_details[0];
            }
        }

        if ($current_report_type) {
            $rfilters = array('name' => $current_report_type);
            $this->model = Reports::getReports($this->registry, $rfilters);
            $this->model = Reports::sanitizeModels($this->model);
            if (isset($this->model[0])) {
                $this->model = $this->model[0];
            }
        }

        //includes multiaction buttons if required
        $this->data['include'] = array();
        if (!empty($reports_results['include_multiedit'])) {
            $this->data['include'][] = 'multiedit';
            unset($reports_results['include_multiedit']);
        }
        //includes a button for creation of a new model (document etc.)
        if (!empty($reports_results['additional_options']['include_create_model'])) {
            $this->data['include'][] = 'create_model';
            $this->data['btn_create_model'] =
                !empty($reports_results['additional_options']['btn_create_model']) ?
                $reports_results['additional_options']['btn_create_model'] :
                $this->i18n('create');
            unset($reports_results['additional_options']['include_create_model']);
        }
        //includes a button for cloning multiple records from results
        if (!empty($reports_results['additional_options']['include_clone_selected'])) {
            $this->data['include'][] = 'clone_selected';
            $this->data['btn_clone_selected'] = $reports_results['additional_options']['btn_clone_selected'];
            unset($reports_results['additional_options']['include_clone_selected']);
        }
        if (!empty($reports_results['additional_options']['include_assign_selected'])) {
            $this->data['include'][] = 'assign_selected';
            unset($reports_results['additional_options']['include_assign_selected']);
        }

        // Hide the standard export button, if the flag dont_show_export_button is set to true
        if (!empty($reports_results['additional_options']['dont_show_export_button'])) {
            $this->data['dont_show_export_button'] = true;
            unset($reports_results['additional_options']['dont_show_export_button']);
        }

        // exclude the outer form if necessary
        if (!empty($reports_results['additional_options']['exclude_outter_form'])) {
            $this->data['exclude_outter_form'] = $reports_results['additional_options']['exclude_outter_form'];
            unset($reports_results['additional_options']['exclude_outter_form']);
        }

        if (defined('ALLOWS_EMAIL_SENDING') && ALLOWS_EMAIL_SENDING && isset($this->model) &&
           (defined('DISABLE_EMAIL_SENDING') && !DISABLE_EMAIL_SENDING || !defined('DISABLE_EMAIL_SENDING'))) {
            $this->data['include'][] = 'sendemail';

            // get email templates for the current model and type OR for current report
            if (defined('EMAIL_ORIGIN') && EMAIL_ORIGIN != 'self' || !defined('EMAIL_ORIGIN')) {
                $filts = array('where' => array('e.model = \'' . CREATE_MODEL . '\'',
                                                'e.model_type IN(' . CREATE_TYPE . ')'),
                                                'e.name = \'\'',
                               'sanitize' => true);
            } else {
                $filts = array('where' => array('e.model = \'Report\'',
                                                'e.model_type = \'' . $this->model->get('id') . '\'',
                                                'e.name = \'\''),
                               'sanitize' => true);
                if (defined('FEEDBACK_EMAIL')) {
                    $filts['where'][] = 'e.id != \'' . FEEDBACK_EMAIL . '\'';
                }
            }
            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';

            // email sending is implicitly enabled based on general settings above
            // unless it is explicitly disabled with optional setting below
            $send_email = !(defined('SEND_EMAIL') && !SEND_EMAIL);
            if ($send_email) {
                $mail_templates = Emails::search($this->registry, $filts);

                $mail_body = '';
                if (!empty($mail_templates[0])) {
                    $mail_body = $mail_templates[0]->get('body');
                    $this->data['email_subject'] = $mail_templates[0]->get('subject');
                }

                //prepare the fck editor
                $editor = new Editor($this->registry, 'body', array('toolbar' => 'Preview', 'height' => '150px'));
                $editor->setContent($mail_body);
                $this->data['editor_content'] = $editor->create();

                foreach ($mail_templates as $key => $value) {
                    $mail_templates[$key] = array('label' => $value->get('subject'),
                                                  'option_value' => $value->get('id'));
                }
                $this->data['email_templates'] = $mail_templates;
            }
            $this->data['send_email'] = $send_email;

            $send_sms = false;
            if (defined('SEND_SMS') && SEND_SMS) {
                $send_sms = true;

                array_pop($filts['where']);
                $filts['where'][] = 'e.name = "sms"';

                $sms_templates = Emails::search($this->registry, $filts);

                $sms_body = '';
                if (!empty($sms_templates[0])) {
                    $sms_body = $sms_templates[0]->get('body');
                }

                //prepare the fck editor
                $editor2 = new Editor($this->registry, 'sms_body', array('toolbar' => 'Preview', 'height' => '150px'));
                $editor2->setContent($sms_body);
                $this->data['editor2_content'] = $editor2->create();

                foreach ($sms_templates as $key => $value) {
                    $sms_templates[$key] = array('label' => $value->get('subject'),
                                                 'option_value' => $value->get('id'));
                }
                $this->data['sms_templates'] = $sms_templates;
            }
            $this->data['send_sms'] = $send_sms;

            $this->data['mail_settings_title'] = $this->i18n('reports_mail_settings_title');

            if (defined('ALLOWS_FILES_GENERATION') && ALLOWS_FILES_GENERATION) {
                //get file patterns for the current model and type
                $this->data['include'][] = 'generate_files';
                if (defined('FILES_ORIGIN') && FILES_ORIGIN != 'self' || !defined('FILES_ORIGIN')) {
                    $filts = array('where' => array('p.for_printform = 0',
                                                    'p.model = \'' . CREATE_MODEL . '\'',
                                                    'p.model_type IN (' . CREATE_TYPE . ')',
                                                    'p.active = 1',
                                                    'p.list = 0'),
                                   'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                   'sanitize' => true);
                } else {
                    $filts = array('where' => array('p.for_printform = 0',
                                                    'p.model = \'Report\'',
                                                    'p.model_type = \'' . $this->model->get('id') . '\'',
                                                    'p.active = 1',
                                                    'p.list = 0'),
                                   'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                   'sanitize' => true);
                }
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                $patterns = Patterns::search($this->registry, $filts);
                foreach ($patterns as $key => $value) {
                    $patterns[$key] = array('label' => $value->get('name'),
                                            'option_value' => 'pattern_' . $value->get('id'));
                }
                $this->data['patterns'] = $patterns;
            }
        }

        // defines if the frozen table headers will be used
        $this->data['freeze_table_headers'] = (defined('FREEZE_TABLE_HEADERS') ? FREEZE_TABLE_HEADERS : 0);

        //report's filters
        $current_filters = $this->registry->get('report_filters');
        foreach ($current_filters as $key => $value) {
            if (array_key_exists($key, $filters)) {
                $current_filters[$key]['value'] = $filters[$key];
            }
        }

        $this->registry->set('report_filters', $current_filters, true);
        $this->data['templatesDirPlugin'] = PH_MODULES_DIR . "reports/plugins/";
        $this->data['templatesUrlPlugin'] = PH_MODULES_URL . "reports/plugins/";
        $this->data['pagination'] = $pagination;

        // check if custom template for filters is set
        if ($this->registry->get('custom_filters_template') && $current_report_type) {
            $custom_filters_template_full_path = sprintf("%s%s/%s", $this->data['templatesDirPlugin'], $current_report_type, $this->registry->get('custom_filters_template'));
            if (file_exists($custom_filters_template_full_path))  {
                $this->data['custom_filters_template'] = $custom_filters_template_full_path;
            }
        }

        //report's additional options
        if (isset($reports_results['additional_options'])) {
            $this->data['reports_additional_options'] = $reports_results['additional_options'];
            unset($reports_results['additional_options']);
        }

        // reports which the user can generate
        $permitted_reports = array();

        $filters_reports = array('sanitize' => true);
        $this->data['reports_results'] = $reports_results;
        $reports = Reports::getReports($this->registry, $filters_reports);
        $reports_optgroups = array();
        foreach ($reports as $key => $report) {
            if (!$report->checkRights()) {
                unset($reports[$key]);
            } else {
                $permitted_reports[] = $report->get('type');
                if (!$report->get('visible') && ($report->get('type') != $current_report_type)) {
                    unset($reports[$key]);
                } else {
                    $report_configurations = $report->get('configurations');
                    //prepare a dropdown with all reports
                    //grouped by section name and ordered by position
                    //all the reports that do not have section name are pushed into OTHERS
                    //remove the numbering in the name of the section
                    $section = ($report->get('section')) ? preg_replace('#^[0-9]*\. *(.*)$#', '$1', $report->get('section')) : $this->i18n('reports_section_other');
                    $reports_optgroups[$section][] = array(
                        'label'        => $report->get('name'),
                        'option_value' => $report->get('type'),
                        'class_name'   => (!empty($report_configurations['skip_session_filters']) ? 'skip_session_filters' : ''),
                    );
                }
            }
        }

        if (!empty($reports)) {
            $this->data['reports_types'] = $reports;
            //put section other to the bottom of the list
            if (array_key_exists($this->i18n('reports_section_other'), $reports_optgroups)) {
                if (count($reports_optgroups) == 1) {
                    //there are only OTHERS, do not show option groups
                    $reports_options = $reports_optgroups[$this->i18n('reports_section_other')];
                } else {
                    //put section other to the bottom of the list
                    General::relocateElementInArray($this->i18n('reports_section_other'), $reports_optgroups, 'last');
                }
            }
            //prepare a dropdown with all reports
            //grouped by section name and ordered by position
            $reports_dropdown = array (
                'name'               => 'report_type',
                'type'               => 'dropdown',
                'label'              => $this->i18n('reports_text_title'),
                'help'               => $this->i18n('help_reports_text_title'),
                'value'              => ($this->model) ? $this->model->get('type') : '',
                'first_option_label' => $this->i18n('reports_select_option'),
                'onchange'           => "changeReport(this)");
            if (isset($reports_options)) {
                //there are only OTHERS, do not show option groups
                $reports_dropdown['options'] = $reports_options;
            } else {
                $reports_dropdown['optgroups'] = $reports_optgroups;
            }
            $this->data['reports_dropdown'] = $reports_dropdown;
        }
        if (!empty($filters['report_type']) && in_array($filters['report_type'], $permitted_reports)) {
            $this->data['report_type'] = $filters['report_type'];
            //added lang files for the selected report if file exists
            $lang_file = sprintf('%s%s%s%s%s%s',
                PH_MODULES_DIR,
                'reports/plugins/',
                $filters['report_type'],
                '/i18n/',
                $this->registry['lang'],
                '/reports.ini');
            if (file_exists($lang_file)) {
                $i18n_files[] = $lang_file;
            }
            $loadReportI18nFromDb = true;
        }
        if ($this->registry->isRegistered('report_filters')) {
            $reports_settings = array();

            //separate the reports settings from the other filters
            $final_filters = $this->registry->get('report_filters');
            $already_used_filters = array();
            foreach ($final_filters as $key => $value) {
                if (isset($value['setting']) && $value['setting']) {
                    if (! in_array($key, $already_used_filters)) {
                        if (isset($final_filters[$key . '_options'])) {
                            $value['additional_options'] = $final_filters[$key . '_options'];
                            $already_used_filters[] = $key . '_options';
                            unset($final_filters[$key . '_options']);
                        }
                        $reports_settings[] = $value;
                        unset($final_filters[$key]);
                    }
                }
            }

            $this->data['report_filters'] = $final_filters;
            $this->data['reports_settings'] = $reports_settings;
            if ($current_report_details) {
                $this->data['reports_description'] = $current_report_details->get('description');
            }
        }
        // custom report without input filters that will be executed when report type is selected
        if ($this->registry->isRegistered('hide_generate_button')) {
            $this->data['hide_generate_button'] = $this->registry->get('hide_generate_button');
        }
        // custom report without export button or custom export button
        if ($this->registry->isRegistered('hide_export_button')) {
            $this->data['hide_export_button'] = $this->registry->get('hide_export_button');
        }
        // hide the report selection panel
        if ($this->registry->isRegistered('hide_report_selection')) {
            $this->data['hide_report_selection'] = $this->registry->get('hide_report_selection');
        }
        // hide the filters panel
        if ($this->registry->isRegistered('hide_filters_panel')) {
            $this->data['hide_filters_panel'] = $this->registry->get('hide_filters_panel');
        }
        if ($this->registry->isRegistered('hide_filters')) {
            $this->data['hide_filters'] = $this->registry->get('hide_filters');
        }
        if ($this->registry->isRegistered('generated_report')) {
            $this->data['generated_report'] = $this->registry->get('generated_report');
            if ($current_report_details) {
                $this->data['export_permission'] =
                    $this->registry['currentUser']->checkRights($this->module, 'export') &&
                    $this->registry['currentUser']->checkRights($this->module . $current_report_details->get('id'), 'export');
            }
        }

        // check available patterns for export
        $available_patterns = array();
        if ($this->model) {
            $export_template_file = sprintf('%s%s/%s/%s/%s',
                PH_MODULES_DIR,
                'reports',
                'plugins',
                $this->model->get('type'),
                'export_report.html');

            if (file_exists($export_template_file) && !(defined('HIDE_EXPORT_DEFAULT') && HIDE_EXPORT_DEFAULT)) {
                $available_patterns[] = array(
                    'id'            => '',
                    'format'        => 'xls',
                    'name'          => $this->i18n('report_export_default'),
                    'description'   => $this->i18n('report_export_default_description')
                );
            }

            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $patterns_filters = array('where'        => array('p.for_printform = 0',
                                                              'p.model = "Report"',
                                                              'p.model_type = "' . $this->model->get('id') . '"',
                                                              'p.active = 1',
                                                              'p.list = 0'),
                                      'sort'         => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                      'model_lang'   => $this->registry['lang'],
                                      'sanitize'     => true);
            $patterns = Patterns::search($this->registry, $patterns_filters);

            foreach ($patterns as $pattern) {
                $available_patterns[] = array(
                    'id'            => $pattern->get('id'),
                    'format'        => $pattern->get('format'),
                    'name'          => sprintf('%s (%s)', $pattern->get('name'), $pattern->get('format')),
                    'description'   => (($pattern->get('format')=='xls') ? $this->i18n('report_export_default_description') : $pattern->get('description'))
                );
            }
        }
        $this->data['available_patterns'] = $available_patterns;

        //load lang files
        $i18n_files[] = sprintf('%s%s%s/%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $this->registry['lang'],
            'documents.ini');
        if ($this->registry->isRegistered('custom_report_i18n_files')) {
            $custom_report_i18n_files = $this->registry->get('custom_report_i18n_files');
            if (!is_array($custom_report_i18n_files)) {
                $custom_report_i18n_files = array($custom_report_i18n_files);
            }
            $i18n_files = array_merge($i18n_files, $custom_report_i18n_files);
        }

        $this->loadCustomI18NFiles($i18n_files);

        // Load report translations from DB
        if (!empty($loadReportI18nFromDb)) {
            $this->loadI18nFromDb($this->module, [$filters['report_type']]);
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('reports');

        $navbarlink[] = array('href' => $this->registry['module_param'] . '=' . $this->module . '&amp;report_type=', 'text' => $title);
        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}
