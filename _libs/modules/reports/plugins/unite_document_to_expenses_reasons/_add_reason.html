<form action="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;report_type={$selected_report}&amp;reports=create_model" onsubmit="return validateExpenseData(this);" method="post" name="reasons_form" style="color: #666666; font-family: Verdana,Arial,Helvetica,sans-serif; font-size: 11px;">
  {$help_message|escape}<br /><br />
  {foreach from=$types_to_add name=tr item=type_reason}
    <input type="radio" name="reason_type" id="reason_type_{$smarty.foreach.tr.iteration}" value="{$type_reason.option_value}"{if $smarty.foreach.tr.first} checked="checked"{/if}><label for="reason_type_{$smarty.foreach.tr.iteration}"> {$type_reason.label}</label><br />
  {/foreach}
  <br />

  <label for="new_reason_company">{#reports_company#} {#required#}</label><br />
  <select name="new_reason_company" id="new_reason_company" class="selbox" title="{#reports_company#}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
    {foreach from=$companies_list name=cl item=company}
      <option value="{$company->get('id')|escape}" class=""{if $smarty.foreach.cl.first} selected=""selected"{/if}>{$company->get('name')|escape}</option>
    {/foreach}
  </select>
  <br />
  <br />

  <label for="currency_reason">{#reports_currency#} {#required#}</label><br />
  <select name="currency_reason" id="currency_reason" class="selbox" title="{#reports_currency#}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
    <option value="" class="undefined">[{#please_select#|escape}]</option>
    {foreach from=$currencies name=curr item=currency}
      <option value="{$currency.option_value|escape}" class="">{$currency.label|escape}</option>
    {/foreach}
  </select>
  <br />
  <br />

  <input type="hidden" name="completed_quantities_data" value="{$completed_quantities_data|escape}" />
  <input type="hidden" name="table_session_param" value="" />
  {strip}
  <button type="submit" name="addReason" class="button">{#add#|escape}</button>
  <input type="button" class="button" onclick="lb.deactivate();" value="{#cancel#}" />
  {/strip}
</form>
