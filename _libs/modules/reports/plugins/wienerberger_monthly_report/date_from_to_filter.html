<tr>
  <td class="labelbox">
    <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help label_content=$filter_settings.label text_content=$filter_settings.help}</label>
  </td>
  {if $filter_settings.required}<td class='required'>*</td>{else}<td>&nbsp;</td>{/if}
  <td nowrap="nowrap" style="vertical-align: top;">
      {include file="input_dropdown.html"
      standalone=true
      width=$filter_settings.width
      name=$filter_settings.name
      custom_id=$filter_settings.custom_id
      label=$filter_settings.label
      required=$filter_settings.required
      options=$filter_settings.options
      help=$filter_settings.help
      value=$filter_settings.value|default:''}
    <span style="vertical-align: top; padding-left: 5px;">
      {include file="input_dropdown.html"
        standalone=true
        width=$filter_settings.additional_filter.width
        name=$filter_settings.additional_filter.name
        custom_id=$filter_settings.additional_filter.custom_id
        required=$filter_settings.additional_filter.required
        options=$filter_settings.additional_filter.options
        label=$filter_settings.additional_filter.label
        help=$filter_settings.additional_filter.help
        value=$filter_settings.additional_filter.value|default:''}
    </span>
  </td>
</tr>