<?php
    Class Bdz_Find_Employee Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            if (!empty($filters['structure'])) {
                // defines available departments by default
                $structures_list = explode(',', STRUCTURES_LIST);

                // get available structures - their names will be used later
                $structures_sql = 'SELECT dep.id as idx, depi18n.name as label' . "\n" .
                                  'FROM ' . DB_TABLE_DEPARTMENTS . ' AS dep' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                                  ' ON (dep.id=depi18n.parent_id AND depi18n.lang="' . $registry['lang'] . '")' . "\n" .
                                  'WHERE dep.active=1 AND dep.deleted_by=0 AND dep.id IN ("' . implode('","', $structures_list) . '")' . "\n" .
                                  'ORDER BY depi18n.name ASC';

                $available_structures = $registry['db']->getAssoc($structures_sql);

                $structures_subdepartments_relations = array();

                // include departments class
                require_once PH_MODULES_DIR . "departments/models/departments.factory.php";

                // goes through all the structures and finds their subdepartments
                foreach ($available_structures as $avb_id => $avb_str) {
                    $current_child_subdepartments = Departments::getTreeDescendantsIds($registry, $avb_id);
                    foreach ($current_child_subdepartments as $dep_id) {
                        $structures_subdepartments_relations[$dep_id] = $avb_id;
                    }
                }

                $full_departments_list = array();
                if (!empty($filters['department']) || !empty($filters['structure'])) {
                    if (!empty($filters['department'])) {
                        $current_selected_department = $filters['department'];
                    } else {
                        $current_selected_department = $filters['structure'];
                    }

                    $full_departments_list = Departments::getTreeDescendantsIds($registry, $current_selected_department);
                } else {
                    $full_departments_list = array_keys($structures_subdepartments_relations);
                }

                // get customers assigned to this department
                $sql_employees = array();
                $sql_employees['select'] = 'SELECT c.id, CONCAT (ci18n.name, " ", ci18n.lastname) AS name, ci18n.position, c.department, depi18n.name as department_name, "" as structure_name' . "\n";

                $sql_employees['from']   = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                           '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                                           '  ON (c.department=depi18n.parent_id AND depi18n.lang="' . $model_lang . '")' . "\n";

                $where = array();
                $where[] = 'c.active=1';
                $where[] = 'c.deleted_by=0';
                $where[] = 'c.type=' . PH_CUSTOMER_EMPLOYEE;
                $where[] = 'c.department IN ("' . implode('","', $full_departments_list) . '")';
                if (!empty($filters['position'])) {
                    $clauses = array();
                    $all_words = array();
                    $all_words = explode(" ", $filters['position']);
                    foreach ($all_words as $word) {
                        if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                            $clauses[] = '(LOWER(ci18n.position) REGEXP "[[:<:]]' . mb_strtolower($word, mb_detect_encoding($word)) . '[[:>:]]")';
                        }
                    }

                    if (!empty($clauses)) {
                        $where[] = '(' . implode(' OR ', $clauses) . ')';
                    }
                }

                $sql_employees['where'] = 'WHERE ' . implode(' AND ', $where);

                $sql_employees['order'] = ' ORDER BY CONCAT (ci18n.name, " ", ci18n.lastname) ASC' . "\n";

                //search basic details with current lang parameters
                $query = implode("\n", $sql_employees);
                $final_results = $registry['db']->GetAll($query);

                foreach ($final_results as $fr_key => $fr) {
                    if (!empty($structures_subdepartments_relations[$fr['department']]) && !empty($available_structures[$structures_subdepartments_relations[$fr['department']]])) {
                        $final_results[$fr_key]['structure_name'] = $available_structures[$structures_subdepartments_relations[$fr['department']]];
                    }
                    $final_results[$fr_key]['encoded_data'] = base64_encode(json_encode($final_results[$fr_key]));
                }
                $final_results['additional_options']['error'] = false;
            } else {
                $final_results['additional_options']['error'] = $registry['translater']->translate('error_reports_complete_required_fields');
            }

            $final_results['additional_options']['exclude_outter_form'] = true;

            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                //no pagination required return only the models
                $results = $final_results;
            }

            return $results;
        }

        public static function sortingResultsByEmployeeName($a, $b) {
            return ($a['name'] > $b['name']) ? 1 : -1;
        }
    }
?>