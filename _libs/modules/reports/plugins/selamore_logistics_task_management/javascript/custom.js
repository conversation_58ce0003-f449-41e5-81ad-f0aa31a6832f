let report;

let filtersScreens = {
    'trading_company':               ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'point_of_sale':                 ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'logistics_specialist':          ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'ls_full_num':                   ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'ls_status':                     ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'include_closed_specifications': ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'period_specification_from':     [''],
    'customer':                      ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'product_category':              ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'product_in_specification':      ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'supplier_type':                 ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'supplier':                      ['create_orders_to_supplier', 'planning_and_informing_supplier', 'tlr'],
    'period_os_from':                [''],
    'os_full_num':                   ['planning_and_informing_supplier', 'tlr'],
    'os_status':                     ['planning_and_informing_supplier', 'tlr'],
    'include_closed_orders':         ['planning_and_informing_supplier', 'tlr'],
    'country':                       ['tlr'],
    'allocation_status':             ['tlr'],
    'requested_week':                ['create_orders_to_supplier'],
    'confirmed_week':                ['planning_and_informing_supplier', 'tlr'],
};
let screensFilters = {};
function getScreenFilters(currentScreen) {
    if (Object.keys(screensFilters).length === 0) {
        for (const filter in filtersScreens) {
            filtersScreens[filter].forEach((screen) => {
                if (!screensFilters[screen]) {
                    screensFilters[screen] = [];
                }
                screensFilters[screen].push(filter);
            });
        }
    }

    if (screensFilters.hasOwnProperty(currentScreen)) {
        return screensFilters[currentScreen];
    } else {
        return [];
    }
}

function getReportForValue() {
    const reportForOption = document.querySelector('[name="report_for"]:checked');
    if (!reportForOption) {
        return '';
    }
    return reportForOption.value;
}

function loadResultsTable() {
    const gridContainer = document.querySelector('#ej2_grid_selamore_logistics_task_management');
    if (!gridContainer) {
        return;
    }
    const reportFor = getReportForValue();
    let reportParams = {
        reportFor: reportFor,
        i18n: i18n,
        gridContainer: gridContainer,
        gridParams: JSON.parse(gridContainer.getAttribute('data-grid-params')),
        precision: document.querySelector('#precision').value,
    };
    if (gridContainer.hasAttribute('data-grid-rowspan')) {
        reportParams.gridRowspan = JSON.parse(gridContainer.getAttribute('data-grid-rowspan'));
    }
    reportParams.editAllowed = (typeof gridContainer.dataset.editAllowed !== 'undefined' && gridContainer.dataset.editAllowed === 'allowed');

    switch (reportFor) {
        case 'create_orders_to_supplier':
            report = new SelamoreLogisticsTaskManagementForCreateOs(reportParams);
            break;
        case 'planning_and_informing_supplier':
            report = new SelamoreLogisticsTaskManagementForCreateARP(reportParams);
            break;
        case 'tlr':
            report = new SelamoreLogisticsTaskManagementForCreateTlr(reportParams);
            break;
    }
    if (report) {
        report.loadGrid();
    }
}

let tradingCompaniesOffices = {};
function prepareTradingCompaniesOffices() {
    fetch(`${env.base_url}?${env.module_param}=reports&report_type=selamore_logistics_task_management&reports=get_trading_companies_offices`, {
        headers: {
            'Accept': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        tradingCompaniesOffices = data;
        updateFilterPointOfSale();
    }).catch(error => {
        // TODO: on HTTP ERROR
    });
}

function updateFilterPointOfSale() {
    let availableTradingCompaniesOfficesIds = {};
    const tradingCompanyElements = document.querySelectorAll('input[name^="trading_company["]');
    if (tradingCompanyElements.length > 0) {
        tradingCompanyElements.forEach((tradingCompany) => {
            const tradingCompanyId = tradingCompany.value;
            if (!tradingCompanyId) {
                return;
            }
            const tradingCompanyOffices = tradingCompaniesOffices[tradingCompanyId];
            for (const office in tradingCompanyOffices) {
                availableTradingCompaniesOfficesIds[office] = office;
            }
        });
    }
    availableTradingCompaniesOfficesIds = Object.values(availableTradingCompaniesOfficesIds);
    const pointOfSaleOptionElements = document.querySelectorAll('select[name^="point_of_sale["] option');
    if (pointOfSaleOptionElements.length > 0) {
        pointOfSaleOptionElements.forEach((option) => {
            // Skip show/hide option "Please select"
            if (option.value === '') {
                return;
            }
            if (!availableTradingCompaniesOfficesIds.length || availableTradingCompaniesOfficesIds.includes(option.value)) {
                option.show();
            } else {
                option.hide();
                const select = option.parentElement;
                const selectValue = select.value;
                option.selected = false;
                if (selectValue === option.value) {
                    toggleUndefined(select);
                }
            }
        });
    }
}

function clearFilter(container) {
    const ac = container.querySelector('input.autocompletebox');
    if (ac) {
        clearAutocompleteItems(getAutocompleteParams(ac), true);
        return;
    }
    const select = container.querySelector('select');
    if (select) {
        select.value = '';
        toggleUndefined(select);
        return;
    }
    const checkbox = container.querySelector('input[type="checkbox"]');
    if (checkbox) {
        checkbox.checked = false;
        return;
    }
    const dates = container.querySelectorAll('input[type="text"].datebox');
    if (dates.length) {
        dates.forEach((date) => {
            clearDateField(date);
        });
        return;
    }
    const texts = container.querySelectorAll('input[type="text"]');
    if (texts.length) {
        texts.forEach((text) => {
            text.value = '';
        });
    }
}

function removeMultiFilterAdditionalRows(filterRow) {
    const multipleFilterSubTable = filterRow.querySelector('table.t_grouping_table');
    if (!multipleFilterSubTable) {
        return;
    }
    const subTableId = multipleFilterSubTable.getAttribute('id');
    if (!subTableId) {
        return;
    }
    const subTableRows = multipleFilterSubTable.querySelectorAll(`tr[id^="${subTableId}_"]`);
    if (!subTableRows.length) {
        return;
    }
    subTableRows.forEach((subTableRow) => {
        if (subTableRow.getAttribute('id') === `${subTableId}_1`) {
            return;
        }
        subTableRow.remove();
    });
    const minusButton = filterRow.querySelector(`#${subTableId}_minusButton`);
    if (!minusButton) {
        return;
    }
    minusButton.classList.add('disabled');
}

function clearHiddenFilters() {
    document.querySelectorAll('tr.hidden_filter').forEach((filterRow) => {
        removeMultiFilterAdditionalRows(filterRow);
        clearFilter(filterRow);
    });
}

function showHideFilters() {
    const reportFor = getReportForValue();

    const screenFilters = getScreenFilters(reportFor);
    for (const filterName in filtersScreens) {
        const filterLabel = document.querySelector(`label[for="${filterName}"]`);
        if (!filterLabel) {
            continue;
        }

        const filterRow = filterLabel.closest('tr');
        if (!filterRow) {
            continue;
        }

        if (screenFilters.includes(filterName)) {
            filterRow.classList.remove('hidden_filter');
        } else {
            filterRow.classList.add('hidden_filter');
        }
    }
}

Event.observe(window, 'load', function () {
    showHideFilters();

    document.querySelector('form#reports_generated').addEventListener('submit', () => {
        clearHiddenFilters();
    })

    loadResultsTable();

    const tradingCompanyMinusButton = document.querySelector('#table_trading_company_minusButton');
    if (tradingCompanyMinusButton) {
        tradingCompanyMinusButton.addEventListener('click', () => {
            updateFilterPointOfSale();
        });
    }
    prepareTradingCompaniesOffices();
});
