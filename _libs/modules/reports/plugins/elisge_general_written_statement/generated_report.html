<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table cellpadding="0" cellspacing="0" class="t_table bordered_cell">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_document_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_article_code#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_article_description#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_depot_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_articles_measure#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_amount_article#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_pcs_boxes#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_pcs_box#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_palet_num#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_total_kg#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td class="t_border hright" nowrap="nowrap" width="25" rowspan="{$result.doc_rows}">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border" rowspan="{$result.doc_rows}">
              {$result.customer|escape|default:"&nbsp;"}
            </td>
            {foreach from=$result.documents item=document name=doc}
              {if !$smarty.foreach.doc.first}
                <tr>
              {/if}
              <td class="t_border" nowrap="nowrap" rowspan="{$document.rows}">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$document.id}">{$document.full_num|numerate:$document.direction|default:"&nbsp;"}</a>
              </td>
                {foreach from=$document.info item=nfo name=protocol_info}
                  {if !$smarty.foreach.protocol_info.first}
                    <tr>
                  {/if}
                  <td class="t_border">
                    {$nfo.articles_code|default:"-"}
                  </td>
                  <td class="t_border">
                    {$nfo.articles_name|escape|default:"&nbsp;"}
                  </td>
                  {if $smarty.foreach.protocol_info.first}
                    <td class="t_border" rowspan="{$document.rows}">
                      {$document.depot_num|default:"&nbsp;"}
                    </td>
                  {/if}
                  <td class="t_border">
                    {$nfo.articles_measure|escape|default:"&nbsp;"}
                  </td>
                  <td class="t_border hright">
                    {$nfo.amount_article|escape|default:"0"}
                  </td>
                  <td class="t_border hright">
                    {$nfo.pcs_boxes|escape|default:"0"}
                  </td>
                  <td class="t_border hright">
                    {$nfo.pcs_box|escape|default:"0"}
                  </td>
                  <td class="t_border hright">
                    {$nfo.palet_num|escape|default:"&nbsp;"}
                  </td>
                  {if $smarty.foreach.protocol_info.first}
                    <td class="hright" rowspan="{$document.rows}">
                      {$document.total_kg|default:"&nbsp;"}
                    </td>
                  {/if}
                  {if !$smarty.foreach.protocol_info.last}
                    </tr>
                  {/if}
                {/foreach}
              {if !$smarty.foreach.doc.last}
                </tr>
              {/if}
            {/foreach}
        {foreachelse}
          <tr>
            <td class="error" colspan="12">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        {if $reports_additional_options.total_kilograms}
          <tr class="t_even">
            <td class="hright" colspan="11"><strong>{#reports_total_kg#|escape}:</strong></td>
            <td class="hright"><strong>{$reports_additional_options.total_kilograms|default:"0"}</strong></td>
          </tr>
        {/if}
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>