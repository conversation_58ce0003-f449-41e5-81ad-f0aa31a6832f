<?php

Class Advance_Report_By_Banks Extends Reports {

    public static function buildQuery(&$registry, $filters = array()) {
        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $final_results = array();
        $totals = array(
            'rate_total' => 0,
            'vat' => 0,
            'rate_total_with_vat' => 0,
        );

        if (!empty($filters['month']) && !empty($filters['year']) && !empty($filters['user_bank'])) {

            // take the active, not deleted report documents and their relatives (if such exists)
            $sql_reports = 'SELECT (IF (rd.id IS NOT NULL, rd.id, d.id))' . "\n" .
                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                ' ON (dr.parent_model_name="Document" AND dr.link_to_model_name="Document" AND dr.origin="cloned" AND dr.link_to=d.id)' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS rd' . "\n" .
                ' ON (dr.parent_id=rd.id AND rd.active=1 AND rd.deleted_by=0 AND rd.type="' . DOCUMENT_REPORT . '")' . "\n" .
                'WHERE d.type="' . DOCUMENT_REPORT . '" AND d.active=1 AND d.deleted_by=0' . "\n";
            $reports_list = $registry['db']->GetCol($sql_reports);

            if (!empty($reports_list)) {
                $current_report_additional_vars = array(ROLE_USER_ID, ROLE_TYPE, BANK_ID, PERIOD_DATE, VIEWING_DATE, MARKET_VALUE, LIQUIDATION_VALUE, RATING_TYPE, RATE_OBJECTS_TOTAL);
                $bb_additional_vars_prefixes = array(OBJECT_TYPE_PREFIX, CITY_PREFIX, ADDRESS_PREFIX, QUARTER_PREFIX, STREET_NUM_PREFIX, LAND_AREA_PREFIX, BUILD_AREA_PREFIX, REAL_BUILD_AREA_PREFIX, PRICE_PER_SQ_M_PREFIX, DEGREE_COMPLETION_PREFIX);

                $searched_var_names = array();
                $searched_var_names[] = 'fm.type="config"';
                $searched_var_names[] = 'fm.name IN ("' . implode('","', $current_report_additional_vars) . '")';
                foreach ($bb_additional_vars_prefixes as $bb_var) {
                    $searched_var_names[] = 'fm.name LIKE "' . $bb_var . '%"';
                }

                //sql to take the ids of the needed additional vars
                $sql_for_document_add_vars = 'SELECT fm.id, fm.name, fm.type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND (' . implode(' OR ', $searched_var_names) . ') AND fm.model_type="' . DOCUMENT_REPORT . '"';
                $document_add_vars = $registry['db']->GetAll($sql_for_document_add_vars);

                $object_type_var_names = array();
                $city_var_names = array();
                $address_var_names = array();
                $quarter_var_names = array();
                $str_num_var_names = array();
                $land_area_var_names = array();
                $build_area_var_names = array();
                $real_build_area_var_names = array();
                $price_per_sq_m_var_names = array();
                $degree_completion_var_names = array();
                $congifurators_var_ids = array();
                $role_user_id_var_id = '';
                $role_type_var_id = '';
                $bank_id_var_id = '';
                $period_var_id = '';
                $viewing_date_var_id = '';
                $market_value_var_id = '';
                $liquidation_value_var_id = '';
                $rating_type_var_id = '';
                $rate_objects_total_var_id = '';


                foreach ($document_add_vars as $doc_var) {
                    if (preg_match('#^' . OBJECT_TYPE_PREFIX . '.*$#', $doc_var['name'])) {
                        $object_type_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . CITY_REGEXP . '$#', $doc_var['name'])) {
                        $city_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . ADDRESS_REGEXP . '$#', $doc_var['name'])) {
                        $address_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . QUARTER_REGEXP . '$#', $doc_var['name'])) {
                        $quarter_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . STREET_NUM_REGEXP . '$#', $doc_var['name'])) {
                        $str_num_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . LAND_AREA_PREFIX . '.*$#', $doc_var['name'])) {
                        $land_area_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . BUILD_AREA_PREFIX . '.*$#', $doc_var['name'])) {
                        $build_area_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . REAL_BUILD_AREA_PREFIX . '.*$#', $doc_var['name'])) {
                        $real_build_area_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . PRICE_PER_SQ_M_PREFIX . '.*$#', $doc_var['name'])) {
                        $price_per_sq_m_var_names[] = $doc_var['name'];
                    } elseif (preg_match('#^' . DEGREE_COMPLETION_PREFIX . '.*$#', $doc_var['name'])) {
                        $degree_completion_var_names[] = $doc_var['name'];
                    } elseif ($doc_var['name'] == ROLE_USER_ID) {
                        $role_user_id_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == ROLE_TYPE) {
                        $role_type_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == BANK_ID) {
                        $bank_id_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == PERIOD_DATE) {
                        $period_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == VIEWING_DATE) {
                        $viewing_date_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == MARKET_VALUE) {
                        $market_value_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == LIQUIDATION_VALUE) {
                        $liquidation_value_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == RATING_TYPE) {
                        $rating_type_var_id = $doc_var['id'];
                    } elseif ($doc_var['name'] == RATE_OBJECTS_TOTAL) {
                        $rate_objects_total_var_id = $doc_var['id'];
                    } elseif ($doc_var['type'] == 'config') {
                        $congifurators_var_ids[] = $doc_var['id'];
                    }
                }

                // Get the documents data
                $sql_data['select'] = 'SELECT d.id, tagi18n1.name as month, tagi18n2.name as year, d.full_num, d.date, d.deadline, d.customer, d.status, ds.name as substatus, CONCAT(ci18n_customer.name, \' \', ci18n_customer.lastname) as customer_name, ' . "\n" .
                    '  IF(c.is_company="1", c.eik, c.ucn) as customer_num, d_cstm_roles.value as role_id, d_cstm_role_user.value as role_user, ' . "\n" .
                    '  CONCAT(ci18n_role_user_name.name, \' \', ci18n_role_user_name.lastname) as role_user_name, d_cstm_bank.value as bank, ' . "\n" .
                    '  d_cstm_period.value as period, d_cstm_view_date.value as view_date, d_cstm_market_value.value as market_value, ' . "\n" .
                    '  d_cstm_rating_type.value as rating_type, d_cstm_rating_type_name.name as rating_type_name, ' . "\n" .
                    '  d_cstm_liquidation_value.value as liquidation_value, d_cstm_rate_total.value as rate_total, bb.params as bb_data, bb.id as bb_id' . "\n";

                $sql_data['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                    'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t1' . "\n" .
                    '  ON (t1.model=\'Document\' AND d.id=t1.model_id AND t1.tag_id IN (' . implode(',', $filters['month']) . ') )' . "\n" .
                    'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t2' . "\n" .
                    '  ON (t2.model=\'Document\' AND d.id=t2.model_id AND t2.tag_id IN (' . implode(',', $filters['year']) . ') )' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS tagi18n1' . "\n" .
                    '  ON (t1.tag_id=tagi18n1.parent_id AND tagi18n1.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS tagi18n2' . "\n" .
                    '  ON (t2.tag_id=tagi18n2.parent_id AND tagi18n2.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                    '  ON (d.substatus=ds.id AND ds.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                    '  ON (c.id=d.customer)' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_customer' . "\n" .
                    '  ON (ci18n_customer.parent_id=d.customer AND ci18n_customer.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_roles' . "\n" .
                    '  ON (d_cstm_roles.model_id=d.id AND d_cstm_roles.var_id="' . $role_type_var_id . '" AND d_cstm_roles.value="' . ROLE_RATED_BY . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_role_user' . "\n" .
                    '  ON (d_cstm_role_user.model_id=d.id AND d_cstm_role_user.var_id="' . $role_user_id_var_id . '" AND d_cstm_role_user.num=d_cstm_roles.num AND d_cstm_roles.value IS NOT NULL AND d_cstm_roles.value!="" AND d_cstm_roles.value!="0")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_role_user_name' . "\n" .
                    '  ON (ci18n_role_user_name.parent_id=d_cstm_role_user.value AND ci18n_role_user_name.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rating_type' . "\n" .
                    '  ON (d_cstm_rating_type.model_id=d.id AND d_cstm_rating_type.var_id="' . $rating_type_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS d_cstm_rating_type_name' . "\n" .
                    '  ON (d_cstm_rating_type_name.parent_id=d_cstm_rating_type.value AND d_cstm_rating_type_name.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_bank' . "\n" .
                    '  ON (d_cstm_bank.model_id=d.id AND d_cstm_bank.var_id="' . $bank_id_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_period' . "\n" .
                    '  ON (d_cstm_period.model_id=d.id AND d_cstm_period.var_id="' . $period_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_view_date' . "\n" .
                    '  ON (d_cstm_view_date.model_id=d.id AND d_cstm_view_date.var_id="' . $viewing_date_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_market_value' . "\n" .
                    '  ON (d_cstm_market_value.model_id=d.id AND d_cstm_market_value.var_id="' . $market_value_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_liquidation_value' . "\n" .
                    '  ON (d_cstm_liquidation_value.model_id=d.id AND d_cstm_liquidation_value.var_id="' . $liquidation_value_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rate_total' . "\n" .
                    '  ON (d_cstm_rate_total.model_id=d.id AND d_cstm_rate_total.var_id="' . $rate_objects_total_var_id . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_BB . ' AS bb' . "\n" .
                    '  ON (bb.model="Document" AND bb.model_type="' . DOCUMENT_REPORT . '" AND bb.model_id=d.id AND bb.meta_id IN (' . implode(',', $congifurators_var_ids) . '))' . "\n";

                $where = array();
                $where[] = 'd.id IN ("' . implode('","', $reports_list) . '")';
                if (!empty($filters['user_bank'])) {
                    $where[] = '(d_cstm_bank.value IN ("' . implode('","', $filters['user_bank']) . '") OR d.customer IN ("' . implode('","', $filters['user_bank']) . '"))';
                }
                $sql_data['where'] = 'WHERE ' . implode(' AND ', $where);
                $sql_data['order'] = 'ORDER BY `year`, FIND_IN_SET(tagi18n1.parent_id, "' . MONTH_TAGS . '")';

                $query_doc_main_data = implode("\n", $sql_data);

                $docs_main_data = $registry['db']->GetAll($query_doc_main_data);

                // get the conversion rate between EUR and BGN
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $bgn_eur_conversion_rate = Finance_Currencies::getRate($registry, 'BGN', 'EUR');

                $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

                // prepare the final data

                $nomenclature_ids = array();
                foreach ($docs_main_data as $doc_res) {
                    if (!isset($final_results[$doc_res['id']])) {
                        $final_results[$doc_res['id']] = array(
                            'id' => $doc_res['id'],
                            'full_num' => $doc_res['full_num'],
                            'authorization_letter_id' => '',
                            'authorization_letter_num' => '',
                            'date' => $doc_res['date'],
                            'deadline' => $doc_res['deadline'],
                            'month' => $doc_res['month'],
                            'year' => $doc_res['year'],
                            'client' => $doc_res['customer'],
                            'client_name' => $doc_res['customer_name'],
                            'eik_ucn' => $doc_res['customer_num'],
                            'assessors' => array(),
                            'rated_objects' => array(),
                            'rating_type' => $doc_res['rating_type'],
                            'rating_type_name' => $doc_res['rating_type_name'],
                            'status' => $registry['translater']->translate('documents_status_' . $doc_res['status']) . ($doc_res['substatus'] ? ': ' . $doc_res['substatus'] : ''),
                            'date_assign' => $doc_res['period'],
                            'date_view' => $doc_res['view_date'],
                            'date_complete' => $doc_res['deadline'],
                            'date_rate' => $doc_res['date'],
                            'market_value' => (float) $doc_res['market_value'] * (float) $bgn_eur_conversion_rate,
                            'liquidation_value' => (( (float) $doc_res['liquidation_value'] * (float) $doc_res['market_value']) / 100) * (float) $bgn_eur_conversion_rate,
                            'rate_total' => $doc_res['rate_total'],
                            'vat' => (((float) $doc_res['rate_total'] * 20) / 100),
                            'rate_total_with_vat' => (((float) $doc_res['rate_total'] * 20) / 100) + (float) $doc_res['rate_total'],
                            'rowspan' => 1
                        );
                    }

                    if ($doc_res['role_user']) {
                        $final_results[$doc_res['id']]['assessors'][$doc_res['role_user']] = $doc_res['role_user_name'];
                    }

                    $bb_data = unserialize($doc_res['bb_data']);
                    if (empty($bb_data) || (isset($final_results[$doc_res['id']]) && isset($final_results[$doc_res['id']]['rated_objects'][$doc_res['bb_id']]))) {
                        continue;
                    }

                    $rated_object_data = array(
                        'bb_id' => $doc_res['bb_id'],
                        'object' => '',
                        'object_name' => '',
                        'address' => array(
                            'city' => '',
                            'quarter' => '',
                            'address' => '',
                            'street_num' => ''
                        ),
                        'address_line' => '',
                        'land_area' => '',
                        'build_area' => '',
                        'real_area' => '',
                        'price_per_sq_m' => '',
                        'degree_compl' => ''
                    );

                    foreach ($bb_data as $key_var => $var_value) {
                        if (in_array($key_var, $object_type_var_names) && !empty($var_value)) {
                            $rated_object_data['object'] = $var_value;
                        } elseif (in_array($key_var, $city_var_names)) {
                            $rated_object_data['address']['city'] = $var_value;
                        } elseif (in_array($key_var, $quarter_var_names)) {
                            $rated_object_data['address']['quarter'] = $var_value;
                        } elseif (in_array($key_var, $address_var_names)) {
                            $rated_object_data['address']['address'] = $var_value;
                        } elseif (in_array($key_var, $str_num_var_names)) {
                            $rated_object_data['address']['street_num'] = $var_value;
                        } elseif (in_array($key_var, $land_area_var_names)) {
                            $rated_object_data['land_area'] = $var_value;
                        } elseif (in_array($key_var, $build_area_var_names)) {
                            $rated_object_data['build_area'] = $var_value;
                        } elseif (in_array($key_var, $real_build_area_var_names)) {
                            $rated_object_data['real_area'] = $var_value;
                        } elseif (in_array($key_var, $price_per_sq_m_var_names)) {
                            $rated_object_data['price_per_sq_m'] = $var_value;
                        } elseif (in_array($key_var, $degree_completion_var_names)) {
                            $rated_object_data['degree_compl'] = $var_value;
                        }
                    }

                    // check filters
                    if (empty($rated_object_data['object'])) {
                        continue;
                    }

                    $nomenclature_ids[] = $rated_object_data['object'];
                    $nomenclature_ids[] = $rated_object_data['address']['city'];
                    $nomenclature_ids[] = $rated_object_data['address']['quarter'];
                    $nomenclature_ids[] = $rated_object_data['address']['address'];

                    if (!isset($final_results[$doc_res['id']])) {
                        $final_results[$doc_res['id']] = [];
                    }
                    $final_results[$doc_res['id']]['rated_objects'][$doc_res['bb_id']] = $rated_object_data;
                    $final_results[$doc_res['id']]['rowspan'] = count($final_results[$doc_res['id']]['rated_objects']);
                }

                $nom_data = array();
                // get the data for nomenclatures
                if (!empty($nomenclature_ids)) {
                    // get the data for all the needed nomenclatures
                    $nom_query = 'SELECT n.id as idx, ni18n.name' . "\n" .
                        'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                        '  ON (n.id=ni18n.parent_id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                        'WHERE n.id IN ("' . implode('","', $nomenclature_ids) . '")';
                    $nom_data = $registry['db']->getAssoc($nom_query);
                }

                // find if there are correcting documents
                $correcting_documents_relations = array();
                $sql_correct = 'SELECT dr.link_to as report, rd.id as correct' . "\n" .
                    'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS rd' . "\n" .
                    ' ON (dr.parent_id=rd.id AND rd.active=1 AND rd.deleted_by=0 AND rd.type="' . DOCUMENT_CORRECT . '")' . "\n" .
                    'WHERE dr.parent_model_name="Document" AND dr.link_to_model_name="Document" AND dr.origin="transformed" AND dr.link_to IN ("' . implode('","', array_keys($final_results)) . '") AND rd.id IS NOT NULL' . "\n" .
                    'ORDER BY rd.id ASC';
                $correct_list = $registry['db']->GetAll($sql_correct);
                foreach ($correct_list as $correct_rel) {
                    $correcting_documents_relations[$correct_rel['report']] = $correct_rel['correct'];
                }
                $correct_list = array();

                if (!empty($correcting_documents_relations)) {
                    //sql to take the ids of the needed additional vars
                    $sql_for_total_correct = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.name="' . RATE_OBJECTS_TOTAL . '" AND fm.model_type="' . DOCUMENT_CORRECT . '"';
                    $correct_total_var_id = $registry['db']->GetOne($sql_for_total_correct);

                    $sql_correct_list = 'SELECT d.id, d_cstm_total.value as value' . "\n" .
                        'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_total' . "\n" .
                        '  ON (d_cstm_total.model_id=d.id AND d_cstm_total.var_id="' . $correct_total_var_id . '")' . "\n" .
                        'WHERE d.id IN ("' . implode('","', $correcting_documents_relations) . '")' . "\n";
                    $correct_list = $registry['db']->GetAssoc($sql_correct_list);
                }

                // search authorization letters
                $authorization_letters = array();
                if (!empty($final_results)) {
                    $sql_authrization_letters_sql = 'SELECT dr.parent_id as report, ald.id as authorization_letter_id, ald.custom_num as authorization_letter_num' . "\n" .
                        'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS ald' . "\n" .
                        ' ON (dr.link_to=ald.id AND ald.active=1 AND ald.deleted_by=0 AND ald.type="' . DOCUMENT_AUTHORIZATION_LETTER . '")' . "\n" .
                        'WHERE dr.parent_model_name="Document" AND dr.link_to_model_name="Document" AND dr.origin="transformed" AND dr.parent_id IN ("' . implode('","', array_keys($final_results)) . '") AND ald.id IS NOT NULL' . "\n" .
                        'ORDER BY ald.id ASC';
                    $authorization_letters = $registry['db']->GetAssoc($sql_authrization_letters_sql);
                }

                // complete the object and address data
                foreach ($final_results as $fr_key => $fin_res) {
                    foreach ($fin_res['rated_objects'] as $bb_id => $obj_data) {
                        if (!empty($obj_data['object']) && array_key_exists($obj_data['object'], $nom_data)) {
                            $final_results[$fr_key]['rated_objects'][$bb_id]['object_name'] = $nom_data[$obj_data['object']];
                        }
                        if (!empty($obj_data['address']['city']) && array_key_exists($obj_data['address']['city'], $nom_data)) {
                            $final_results[$fr_key]['rated_objects'][$bb_id]['address']['city'] = $nom_data[$obj_data['address']['city']];
                        }
                        if (!empty($obj_data['address']['quarter']) && array_key_exists($obj_data['address']['quarter'], $nom_data)) {
                            $final_results[$fr_key]['rated_objects'][$bb_id]['address']['quarter'] = $nom_data[$obj_data['address']['quarter']];
                        }
                        if (!empty($obj_data['address']['address']) && array_key_exists($obj_data['address']['address'], $nom_data)) {
                            $final_results[$fr_key]['rated_objects'][$bb_id]['address']['address'] = $nom_data[$obj_data['address']['address']];
                        }
                        // Convert from bgn to eur
                        $final_results[$fr_key]['rated_objects'][$bb_id]['price_per_sq_m'] = (float) $obj_data['price_per_sq_m'] * (float) $bgn_eur_conversion_rate;

                        $address_line_elements = $final_results[$fr_key]['rated_objects'][$bb_id]['address'];
                        if (!empty($address_line_elements['street_num'])) {
                            $address_line_elements['address'] .= (!empty($address_line_elements['address']) ? ' ' : '') . $address_line_elements['street_num'];
                            unset($address_line_elements['street_num']);
                        }
                        $address_line_elements = array_filter($address_line_elements);
                        $final_results[$fr_key]['rated_objects'][$bb_id]['address_line'] = implode(', ', $address_line_elements);
                        unset($final_results[$fr_key]['rated_objects'][$bb_id]['address']);
                    }

                    if (array_key_exists($fr_key, $correcting_documents_relations)) {
                        $correct_total = $correct_list[$correcting_documents_relations[$fr_key]];
                        $final_results[$fr_key]['rate_total'] = $correct_total;
                        $final_results[$fr_key]['vat'] = (((float) $correct_total * 20) / 100);
                        $final_results[$fr_key]['rate_total_with_vat'] = (((float) $correct_total * 20) / 100) + $correct_total;
                    }

                    if (array_key_exists($fr_key, $authorization_letters)) {
                        $final_results[$fr_key]['authorization_letter_id'] = $authorization_letters[$fr_key]['authorization_letter_id'];
                        $final_results[$fr_key]['authorization_letter_num'] = $authorization_letters[$fr_key]['authorization_letter_num'];
                    }
                    $final_results[$fr_key]['rated_objects'] = array_values($final_results[$fr_key]['rated_objects']);
                }

                // calculate totals
                foreach ($final_results as $fin_res) {
                    $totals['rate_total'] += round($fin_res['rate_total'], 2);
                    $totals['vat'] += round($fin_res['vat'], 2);
                    $totals['rate_total_with_vat'] += round($fin_res['rate_total_with_vat'], 2);
                }
            }
        } else {
            $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_fields'));
        }
        $final_results['additional_options']['totals'] = $totals;

        // pagination
        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }

}

?>
