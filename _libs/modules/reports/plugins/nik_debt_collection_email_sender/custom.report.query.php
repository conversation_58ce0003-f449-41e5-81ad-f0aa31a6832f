<?php
    class Nik_Debt_Collection_Email_Sender extends Reports {

        /**
         * @var integer Maximal number of sent emails per customer to display
         */
        const MAX_NUM_EMAILS = 3;

        public static function buildQuery(&$registry, $filters = array()) {

            // set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                // default model language is the interface language
                $model_lang = $registry['lang'];
            }
            $db = &$registry['db'];

            // false when method is called for display in interface,
            // true when method is called for document creation
            $background_mode = $registry['request']->get('background_mode');

            // prepare the array for the final results
            $final_results = array();

            /**
             * @var bool $use_tags Flag whether special behaviour per tag should be applied
             */
            $use_tags = isset($filters['use_tags']) ? $filters['use_tags'] : defined('USE_TAGS') && USE_TAGS;
            if ($use_tags && !isset($filters['tag'])) {
                $filters['tag'] = 0;
            }

            $additional_where = array();
            if (empty($filters['types'])) {
                if (defined('FIN_DOCUMENTS_TYPES') && FIN_DOCUMENTS_TYPES) {
                    $filters['types'] = preg_split('#\s*,\s*#', FIN_DOCUMENTS_TYPES);
                } else {
                    $filters['types'] = $db->GetCol(
                        'SELECT id FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                        'WHERE active = 1 AND deleted_by = 0 AND id NOT IN (' . implode(', ', array(PH_FINANCE_TYPE_CORRECT_REASON, PH_FINANCE_TYPE_INTEREST_ACCOUNT)) . ')'
                    );
                }
                if (empty($filters['types'])) {
                    $filters['types'] = array('0');
                }
            }
            $additional_where[] = 'fir.type IN (\'' . implode('\', \'', $filters['types']) . '\')';
            // get type of created documents according to selected own company
            $email_document_type = '';
            if (isset($filters['company'])) {
                $additional_where[] = 'fir.company = \'' . $filters['company'] . '\'';
                if (defined('DOCUMENT_TYPE' . $filters['company'])) {
                    $email_document_type = constant('DOCUMENT_TYPE' . $filters['company']);
                }
            }
            if ($background_mode) {
                // in background mode filter customers according to selected recipients
                if (!empty($filters['recipients']) && is_array($filters['recipients'])) {
                    $additional_where[] = 'fir.customer IN (\'' . implode('\', \'', array_keys($filters['recipients'])) . '\')';
                    // expand selected recipients into an array
                    foreach ($filters['recipients'] as &$recipients) {
                        $recipients = explode(',', $recipients);
                    }
                    unset($recipients);
                } else {
                    $additional_where[] = '0';
                    $filters['recipients'] = array();
                }
            } else {
                // in display mode filter by customer
                if (!empty($filters['customer'])) {
                    $additional_where[] = 'fir.customer = \'' . $filters['customer'] . '\'';
                }
                if (isset($filters['recipients'])) {
                    unset($filters['recipients']);
                }
            }
            if (!empty($filters['due'])) {
                if ($filters['due'] == 'upcoming') {
                    $additional_where[] = 'fir.date_of_payment BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ' .
                        (defined('UPCOMING_NUM_DAYS') && UPCOMING_NUM_DAYS ? UPCOMING_NUM_DAYS : DEFAULT_UPCOMING_NUM_DAYS) . ' DAY)';
                } elseif (preg_match('#^overdue_(\d+)(_(\d+))?$#', $filters['due'], $matches)) {
                    if (isset($matches[3])) {
                        $additional_where[] = 'fir.date_of_payment BETWEEN DATE_SUB(CURDATE(), INTERVAL ' .
                            $matches[3] . ' DAY) AND DATE_SUB(CURDATE(), INTERVAL ' . $matches[1] . ' DAY)';
                    } else {
                        $additional_where[] = 'fir.date_of_payment <= DATE_SUB(CURDATE(), INTERVAL ' . $matches[1] . ' DAY)';
                    }
                }
            }
            if (!empty($filters['office'])) {
                $office_ids = $filters['office'];
                if (is_array($filters['office'])) {
                    $office_ids = implode(', ', $filters['office']);
                }
                $additional_where[] = 'fir.office IN (' . $office_ids . ')';
            }

            $ignore_tags = defined('IGNORE_TAGS') && IGNORE_TAGS ? implode(',', array_filter(array_map('intval', preg_split('#\s*,\s*#', IGNORE_TAGS)))) : '';
            if ($ignore_tags) {
                $additional_where[] = 'tm_reason.tag_id IS NULL';
            }

            $prec = $registry['config']->getSectionParams('precision');

            $query = 'SELECT fir.id AS idx, ' . "\n" .
                     'fir.id, fir.num, fir.customer, fir.type, fir.issue_date, fir.date_of_payment, fir.office,' . "\n" .
                     'ROUND(fir.total_with_vat, ' . $prec['gt2_total_with_vat'] . ') AS total_with_vat, fir.currency,' . "\n" .
                     'fir.payment_status, firis.invoice_status, SUM(IFNULL(fb.paid_amount, 0)) AS paid_amount,' . "\n" .
                     'IF(fir.date_of_payment < CURDATE(), DATEDIFF(CURDATE(), fir.date_of_payment), \'\') AS days_overdue,' . "\n" .
                     'c.is_company, IF (c.is_company = 0 AND c.active = 1 AND c.deleted_by = 0, c.email, \'\') AS recipients,' . "\n" .
                     ($use_tags ?
                         'c.assigned, CONCAT(ui18n.firstname, \' \', ui18n.lastname) AS assigned_name,' . "\n" .
                         'cc.value as assigned_soft, CONCAT(ui18n2.firstname, \' \', ui18n2.lastname) AS assigned_soft_name,' . "\n" :
                     '') .
                     ($background_mode ? 'IFNULL(cb.id, 0) AS branch, ' : '') .
                     'TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname)) AS customer_name' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     '  ON fir.customer = c.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     '  ON c.id = ci18n.parent_id AND ci18n.lang = \'' . $model_lang . '\'' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_INVOICE_STATUS . ' AS firis' . "\n" .
                     '  ON fir.id = firis.parent_id' . "\n" .
                     // join to paid_to side as it will get data for most records,
                     // distributed amount to credit notes will be taken per record, if necessary
                     'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                     '  ON fb.paid_to = fir.id AND fb.paid_to_model_name = \'Finance_Incomes_Reason\'' . "\n" .
                     ($background_mode ?
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS cb' . "\n" .
                     '  ON c.id = cb.parent_customer AND cb.subtype = \'branch\' AND cb.is_main = 1' :
                     '') . "\n" .
                     ($use_tags ?
                     'JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                     '  ON tm.model_id = c.id AND tm.model = \'Customer\' AND tm.tag_id =\'' . $filters['tag'] . '\'' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                     '  ON c.assigned = ui18n.parent_id AND ui18n.lang = \'' . $model_lang . '\'' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     '  ON fm.name="assigned_soft" AND fm.model="Customer"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS cc' . "\n" .
                     '  ON fm.id=cc.var_id AND cc.model_id=c.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                     '  ON cc.value = ui18n2.parent_id AND ui18n2.lang = \'' . $model_lang . '\'' . "\n" :
                     '') .
                     ($ignore_tags ?
                     'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm_reason' . "\n" .
                     '  ON tm_reason.model_id = fir.id AND tm_reason.model = \'Finance_Incomes_Reason\' AND tm_reason.tag_id IN (' . $ignore_tags . ')' . "\n" :
                     '') .
                     'WHERE fir.active = 1 AND fir.annulled_by = 0 AND fir.status = \'finished\'' . "\n" .
                     '  AND fir.payment_status IN (\'unpaid\', \'partial\') AND firis.invoice_status != \'invoiced\'' . "\n" .
                     ($additional_where ? '  AND ' . implode("\n" . '  AND ', $additional_where) : '') . "\n" .
                     'GROUP BY fir.id' . "\n" .
                     'ORDER BY customer_name ASC, fir.customer ASC, fir.date_of_payment ASC, fir.id ASC';
            $results_per_page = defined('RESULTS_PER_PAGE') ? RESULTS_PER_PAGE : 0;
            if ($registry['action'] != 'export' && $results_per_page && !$background_mode) {
                $allRecords = $db->GetAssoc($query);
                $total = count($allRecords);

                $records_per_page = (!empty($filters['display']) ? $filters['display'] : $results_per_page);
                $page = (!empty($filters['page']) ? $filters['page'] : 1);

                // prepare the pagination
                $query .= ' LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $records = $db->GetAssoc($query);
            if (!isset($total)) {
                $total = count($records);
            }

            // convert due amount into specified currency
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = !empty($filters['currency']) ? $filters['currency'] : Finance_Currencies::getMain($registry);
            $conversion_rates = array();

            // get remaining amounts and unset unnecessary models
            foreach ($records as $id => &$record) {
                // process reasons and proformas
                if ($record['type'] > PH_FINANCE_TYPE_MAX || $record['type'] == PH_FINANCE_TYPE_PRO_INVOICE) {
                    // get reason/proforma model
                    $reason = new Finance_Incomes_Reason($registry,
                        array(
                            'id' => $id,
                            'type' => $record['type'],
                            'type_name' => ' ',
                            'active' => 1,
                            'status' => 'finished',
                            'payment_status' => $record['payment_status'],
                            'paid_amount' => $record['paid_amount'],
                            'total_with_vat' => $record['total_with_vat']
                        )
                    );
                    if ($record['type'] == PH_FINANCE_TYPE_PRO_INVOICE) {
                        // get parent reason model of proforma
                        $reason->getRelatives(array('get_parent_reasons' => array(
                            'fir.active = 1',
                            'fir.annulled_by = 0',
                            'fir.status = \'finished\''
                        )));
                        $reason = $reason->get('parent_reasons') ?: array();
                        if ($reason) {
                            $reason = reset($reason);
                            // reason is paid and proforma is left loose, do not include it
                            if (!in_array($reason->get('payment_status'), array('unpaid', 'partial'))) {
                                unset($records[$id]);
                                continue;
                            }
                        }
                        $record['total_remaining_amount'] = bcsub($record['total_with_vat'], $record['paid_amount'], 2);
                    }

                    // there is no reason when proforma is from contract or is a free advance proforma
                    if ($reason) {
                        // max amount that can be paid directly to reason
                        $reason_remaining_amount = $reason->getRemainingAmount();

                        // get proformas of reason
                        $reason->getRelatives(array('get_pro_invoices' => array(
                            'fir.active = 1',
                            'fir.annulled_by = 0',
                            'fir.status = \'finished\'',
                            'fir.payment_status IN (\'unpaid\', \'partial\')'
                        )));
                        $proformas = $reason->get('pro_invoices') ?: array();
                        foreach ($proformas as $proforma) {
                            if ($proforma->get('payment_status') == 'unpaid') {
                                $proforma->set('paid_amount', 0, true);
                            } else {
                                $proforma->getPaidAmount();
                            }
                            $proforma_remaining_amount = bcsub($proforma->get('total_with_vat'), $proforma->get('paid_amount'), 2);

                            if (bccomp($reason_remaining_amount, $proforma_remaining_amount, 2) > -1) {
                                $reason_remaining_amount = bcsub($reason_remaining_amount, $proforma_remaining_amount, 2);
                            } elseif (isset($records[$proforma->get('id')])) {
                                // unset proforma from results
                                unset($records[$proforma->get('id')]);
                            }
                        }
                        unset($reason);
                        // check whether to display reason in results and what remaining amount to display
                        if ($record['type'] > PH_FINANCE_TYPE_MAX) {
                            if (bccomp($reason_remaining_amount, 0, 2) <= 0) {
                                unset($records[$id]);
                                continue;
                            } else {
                                $record['total_remaining_amount'] = $reason_remaining_amount;
                            }
                        }
                    }
                } else {
                    // get paid amount to credit note
                    if ($record['payment_status'] != 'unpaid' && $record['type'] == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                        $record['paid_amount'] = -1 * $db->GetOne(
                            'SELECT SUM(IFNULL(fb.paid_amount, 0)) FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                            'WHERE fb.parent_id = ' . $id . ' AND fb.parent_model_name = \'Finance_Incomes_Reason\''
                        );
                    }
                    $record['total_remaining_amount'] = bcsub($record['total_with_vat'], $record['paid_amount'], 2);
                }

                if ($record['currency'] != $currency) {
                    if (!isset($conversion_rates[$record['currency']])) {
                        $conversion_rates[$record['currency']] = Finance_Currencies::getRate($registry, $record['currency'], $currency);
                    }
                    $record['total_remaining_amount'] = bcmul($record['total_remaining_amount'], $conversion_rates[$record['currency']], 2);
                }

                // do not display records with remaining amount 0 (for example after currency conversion)
                if (bccomp($record['total_remaining_amount'], 0, 2) == 0) {
                    unset($records[$id]);
                }
            }
            unset($record);

            // get unique customer ids - key is first id with that customer, value is customer id
            $cust_keys = array();

            if ($records) {
                $prev_rowspan = $prev_cust = 0;
                $prev_k = -1;
                foreach ($records as $id => &$record) {
                    if ($prev_cust != $record['customer']) {
                        if ($prev_k > -1) {
                            $records[$prev_k]['rowspan'] = $prev_rowspan;
                            $prev_rowspan = 1;
                            $cust_keys[$prev_k] = $prev_cust;
                        } else {
                            $prev_rowspan++;
                        }
                        $prev_k = $id;
                        $prev_cust = $record['customer'];
                    } else {
                        $prev_rowspan++;
                    }
                }
                $records[$prev_k]['rowspan'] = $prev_rowspan;
                $cust_keys[$prev_k] = $prev_cust;

                if (!$background_mode) {
                    if (!empty($filters['session_param'])) {
                        // get latest files per model
                        $query = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                                 '  WHERE f.model = \'Finance_Incomes_Reason\' AND f.origin = \'generated\' AND f.deleted_by = 0' . "\n" .
                                 '    AND f.model_id IN (' . implode(', ', array_keys($records)) . ')' . "\n" .
                                 '  GROUP BY f.model_id';
                        $files = $db->GetCol($query);
                        if ($files) {
                            $query = 'SELECT model_id, id AS file_id, filename, path' . "\n" .
                                     'FROM ' . DB_TABLE_FILES . "\n" .
                                     'WHERE id IN (' . implode(',', $files) . ')';
                            $files = $db->GetAssoc($query);
                            foreach ($files as $id => $data) {
                                if (isset($records[$id])) {
                                    $records[$id] = $records[$id] + $data;
                                }
                            }
                        }
                    }

                    // get last created documents per customer
                    if ($email_document_type) {
                        $delimiter = '^^^';
                        $query = 'SELECT d.customer, GROUP_CONCAT(CONCAT(d.id, \'|\', DATE_FORMAT(IF(d.date, d.date, d.added), \'%d.%m.%Y\'), \' \', IFNULL(esb.subject, \'\')) ORDER BY d.id DESC, esb.id ASC SEPARATOR \'' . $delimiter . '\')' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_EMAILS_SENTBOX . ' AS esb' . "\n" .
                                 '  ON esb.model = \'Document\' AND esb.model_id = d.id' . "\n" .
                                 'WHERE d.type = \'' . $email_document_type . '\'' . "\n" .
                                 '  AND d.customer IN (' . implode(', ', $cust_keys) . ')' . "\n" .
                                 'GROUP BY d.customer';
                        $cust_data = $db->GetAssoc($query);
                        foreach ($cust_data as $cust_id => $data) {
                            $cust_key = array_search($cust_id, $cust_keys);
                            if ($cust_key && isset($records[$cust_key])) {
                                $records[$cust_key]['last_emails'] = array();
                                $data = explode($delimiter, $data);
                                foreach ($data as $d) {
                                    $d = explode('|', $d, 2);
                                    if (isset($records[$cust_key]['last_emails'][$d[0]]) || count($d) < 2) {
                                        continue;
                                    }
                                    $records[$cust_key]['last_emails'][$d[0]] = $d[1];
                                    if (count($records[$cust_key]['last_emails']) >= self::MAX_NUM_EMAILS) {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                // prepare recipients
                if ($background_mode || !empty($filters['session_param'])) {
                    foreach ($cust_keys as $cust_key => $cust_id) {
                        if (!$records[$cust_key]['is_company']) {
                            $recipients = $records[$cust_key]['recipients'];
                            $records[$cust_key]['recipients'] = array();
                            if ($recipients) {
                                $recipients = explode("\n", $recipients);
                                $recipients = explode('|', $recipients[0]);
                                if (empty($filters['recipients']) ||
                                isset($filters['recipients'][$cust_id]) && is_array($filters['recipients'][$cust_id]) && in_array($cust_id, $filters['recipients'][$cust_id])) {
                                    $records[$cust_key]['recipients'][$cust_id] =
                                        array(
                                            'email' => $recipients[0],
                                            'name' => $records[$cust_key]['customer_name']
                                        );
                                }
                            }
                        } else {
                            // get financial contacts
                            $records[$cust_key]['recipients'] = array();
                            $cust = new Customer($registry,
                                array('id' => $records[$cust_key]['customer'], 'is_company' => 1, 'financial_persons' => 1));
                            $contacts = $cust->getAllEmails(true);
                            foreach ($contacts as $contact_name => $data) {
                                if ($data) {
                                    foreach ($data as $option) {
                                        if (preg_match('#\(contact_(\d+)\)$#', $option['option_value'], $matches) &&
                                        (empty($filters['recipients']) ||
                                        isset($filters['recipients'][$cust_id]) && is_array($filters['recipients'][$cust_id]) && in_array($matches[1], $filters['recipients'][$cust_id]))) {
                                            if (array_key_exists($matches[1], $records[$cust_key]['recipients'])) {
                                                continue;
                                            }
                                            $records[$cust_key]['recipients'][$matches[1]] =
                                                array(
                                                    'email' => $option['label'],
                                                    'name' => $contact_name
                                                );
                                        }
                                    }
                                }
                            }
                            unset($cust);
                        }
                    }
                }
            }

            if (!$background_mode && !empty($filters['session_param'])) {
                /*
                // update selected items in session
                $selected_items = $registry['session']->get($filters['session_param'], 'selected_items');
                if (is_array($selected_items) && isset($selected_items['ids'])) {
                    if (!empty($selected_items['ids'])) {
                        $selected_items['ids'] = array_intersect($selected_items['ids'], $cust_keys);
                    }
                    if (!empty($selected_items['ignore_ids'])) {
                        $selected_items['ignore_ids'] = array_intersect($selected_items['ignore_ids'], $cust_keys);
                    }
                    $registry['session']->set($filters['session_param'], $selected_items, 'selected_items', true);
                }
                */

                // prepare options for email and print templates
                if ($records && !empty($email_document_type)) {
                    $records['additional_options']['patterns'] = Dropdown::getCustomDropdown(array(
                        $registry,
                        'table' => 'DB_TABLE_PATTERNS',
                        'table_i18n' => 'DB_TABLE_PATTERNS_I18N',
                        'where' => 't.active = 1 AND t.model = \'' . CREATE_MODEL . '\' AND t.model_type = \'' . $email_document_type . '\' AND t.list = 0',
                        'order_by' => 't.position ASC',
                        'value_prefix' => 'pattern_'
                    ));
                    $email_id = 0;
                    if ($use_tags) {
                        if (!empty($filters['tag']) && !empty($filters['due']) && preg_match('#^overdue_(\d+)(_\d+)?$#', $filters['due'], $due_matches)) {
                            $email_id = "TAG{$filters['tag']}_DAYS{$due_matches[1]}_EMAIL";
                            $email_id = defined($email_id) ? intval(constant($email_id)) : 0;
                        }
                        if (!$email_id) {
                            $registry['messages']->setWarning($registry['translater']->translate('error_reports_missing_patterns'));
                        }
                    }
                    $records['additional_options']['email_templates'] =
                        !$use_tags || $email_id ?
                        Dropdown::getCustomDropdown(array(
                            $registry,
                            'table' => 'DB_TABLE_EMAILS',
                            'table_i18n' => 'DB_TABLE_EMAILS_I18N',
                            'where' => 't.active = 1 AND t.model = \'' . CREATE_MODEL . '\' AND t.model_type = \'' . $email_document_type . '\' AND t.name = \'\'' .
                                ($email_id ? ' AND t.id = ' . $email_id : ''),
                            'label' => 'subject'
                        )) :
                        array();
                    $records['additional_options']['max_recipients'] = defined('MAX_RECIPIENTS') ? MAX_RECIPIENTS : 0;
                } else {
                    if (empty($email_document_type)) {
                        $registry['messages']->setWarning($registry['translater']->translate('error_reports_missing_patterns'));
                    }
                }
            }

            $records['additional_options']['paginate'] = $results_per_page;

            $final_results = $records;

            if (!empty($filters['paginate'])) {
                $results = array($final_results, $total);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Set selected recipients into session filters of report so that they
         * are passed into query string in background mode
         *
         * @param object $registry - the main registry
         * @param array $selected - selected ids from session
         */
        public static function setTemporarySelection(&$registry, $selected) {
            $session_param = 'reports_' . $registry['report_type']['name'] . '_report';
            $session_filters = $registry['session'][$session_param];
            $session_filters['recipients'] = array();

            //get selected recipients not from the request but from the session
            $selected_recipients = $registry['session']->get(
                'reports_' . $registry['report_type']['name'] . '_report_recipients',
                'selected_items'
            );
            $recipient_parent_customers = array();
            if (!empty($selected_recipients) && !empty($selected_recipients['ids'])) {
                $query = 'SELECT c1.id, IF(c3.id IS NOT NULL, c3.id, IF(c2.id IS NOT NULL, c2.id, c1.id)) AS parent_customer' . "\n" .
                    'FROM ' . DB_TABLE_CUSTOMERS . ' c1' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' c2' . "\n" .
                    '  ON c2.id=c1.parent_customer' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' c3' . "\n" .
                    '  ON c3.id=c2.parent_customer' . "\n" .
                    'WHERE c1.id IN (' . implode(', ', $selected_recipients['ids']) . ')';
                $recipient_parent_customers = $registry['db']->GetAssoc($query);
            }

            $grouped_recipients = array();
            foreach($recipient_parent_customers as $recipient => $customer) {
                $grouped_recipients[$customer][] = $recipient;
            }

            foreach($grouped_recipients as $customer => $cust_recipients) {
                $session_filters['recipients'][$customer] = implode(',', $cust_recipients);
            }
            $registry['session']->set($session_param, $session_filters, '', true);

            //clear the selection of selected items
            $registry['session']->remove('reports_' . $registry['report_type']['name'] . '_report', 'selected_items');
            $registry['session']->remove('reports_' . $registry['report_type']['name'] . '_report_recipients', 'selected_items');
        }

        /**
         * Do nothing - temporary table is not used
         *
         * @param object $registry - the main registry
         */
        public static function dropTemporaryTable(&$registry) {
            return;
        }

        /**
         * Report results for selected recipients prepared in a way suitable
         * for document creation and sending
         *
         * @param object $registry - the main registry
         * @param array $filters - report filters
         * @return mixed[][]|NULL[][]|number[][][][]|mixed[][][][]|number[]|mixed[]
         */
        public static function getCustomData(&$registry, $filters = array()) {
            // filters are missing - something must be wrong
            if (!$filters) {
                return array();
            }

            $results = array();
            foreach (self::buildQuery($registry, $filters) as $id => $record) {
                if ($id == 'additional_options') {
                    continue;
                }
                if (!isset($results[$record['customer']])) {
                    $results[$record['customer']] = array(
                        'customer'      => $record['customer'],
                        'customer_name' => $record['customer_name'],
                        'branch'        => $record['branch'],
                        'date'          => date_create()->format('Y-m-d'),
                        'employee'      => $registry['currentUser']->get('employee'),
                        'gt2'           => array(
                            'values'        => array(),
                            'plain_values'  => array(
                                'currency'          => $filters['currency'],
                                'total_vat_rate'    => 0
                            )
                        ),
                        'recipients'    => array(),
                        'assigned'      => isset($record['assigned']) ? $record['assigned'] : null,
                    );
                }
                // recipients are set only in the first row for customer
                if (isset($record['rowspan']) && !empty($record['recipients']) && is_array($record['recipients'])) {
                    $results[$record['customer']]['recipients'] = $record['recipients'];
                }
                // prepare GT2 values data
                $idx = -1 * (count($results[$record['customer']]['gt2']['values']) + 1);
                $results[$record['customer']]['gt2']['values'][$idx] = array(
                    'article_id'        => $record['id'],
                    'article_name'      => $record['num'],
                    'quantity'          => 1,
                    'price'             => $record['total_remaining_amount'],
                    'article_height'    => $record['total_with_vat'],
                    'article_barcode'   => $record['currency'],
                    'free_field1'       => $record['issue_date'],
                    'free_field3'       => $record['date_of_payment'],
                    'free_field4'       => $record['days_overdue'],
                    'free_field5'       => $record['type'],
                );
            }

            return $results;
        }
    }

?>
