<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <strong>{#reports_object#}</strong>: {$reports_results.main_object}
    <table border="1">
      <caption>{$reports_results.caption}</caption>
        {foreach from=$reports_results.data key=day_number item=day name=days}
          {if $smarty.foreach.days.first}
            <tr>
            {assign var='empty_tds' value=$day.week_day-1}
            {if $empty_tds gt 0}
              {'<td></td>'|str_repeat:$empty_tds}
            {/if}
          {elseif $day.week_day eq 1 }
            <tr>
          {/if}
            <td style="vertical-align: middle; text-align: left; min-width:140px;">
              <div style="text-align: center;"><span><strong>{$day.day_name}</strong></span></div>
              <div style="text-align: center;"><span><strong>{$day.date}</strong></span></div>
              {foreach from=$day.subobjects item=flat_properties} 

                {assign var='flat_text' value='Празен'}

                {if is_array($flat_properties.clients) && $flat_properties.clients|@count gt 0 }
                  {foreach from=$flat_properties.clients item=client}
                    {assign var='flat_text' value=$client.name|cat:', '|cat:$client.from|cat:" - "|cat:$client.to }
                  {/foreach}
                {/if}  
                <div><span style="color:{$flat_properties.color}">{$flat_properties.name} </span></div>
              {/foreach}
            </td>
          {if $empty_tds eq 6}
            </tr>
          {/if}
        {/foreach}
    </table>
  </body>
</html>