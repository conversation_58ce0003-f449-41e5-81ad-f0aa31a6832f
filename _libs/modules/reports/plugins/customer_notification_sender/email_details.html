<h3>{#reports_send_notification_results#|escape}</h3>
<table border="1" cellspacing="0" cellpadding="5">
  <tr>
    <td>{help label_content=#report#}</td>
    <td><a href="{$base_url}d={$smarty.request.filters.d}" target="_blank">{$reports_results.report_name|escape}</a></td>
  </tr>
  <tr>
    <td>{help label_content=#reports_notification_type#}</td>
    <td>{capture assign='notification_type_label'}reports_notification_type_{$reports_results.notification_type}{/capture}{$smarty.config.$notification_type_label|default:'-'|escape}</td>
  </tr>
  {if !empty($reports_results.sender_data)}
  <tr>
    <td>{help label_content=#reports_sender_data#}</td>
    <td>{$reports_results.sender_data|escape}</td>
  </tr>
  {/if}
  {if !empty($reports_results.test_mode)}
  <tr>
    <td>{help label_content=#reports_test_mode#}</td>
    <td>{#yes#|escape}</td>
  </tr>
  {/if}
  {if !empty($reports_results.test_recipient)}
  <tr>
    <td>{help label_content=#reports_test_recipient#}</td>
    <td>{#yes#|escape}</td>
  </tr>
  {/if}
  <tr>
    <td>{help label_content=#reports_template#}</td>
    <td>{$reports_results.subject|default:'-'}</td>
  </tr>
  <tr>
    <td>{help label_content=#reports_total#}</td>
    <td>{$reports_results.total|default:0}</td>
  </tr>
  <tr>
    <td>{help label_content=#reports_sent#}</td>
    <td>{math equation='a-b' a=$reports_results.total|default:0 b=$reports_results.erred|default:0}</td>
  </tr>
  <tr>
    <td>{help label_content=#reports_erred#}</td>
    <td>{$reports_results.erred|default:0}</td>
  </tr>
  {if !empty($reports_results.additional_info)}
  <tr>
    <td colspan="2">
      {help label_content=#reports_additional_info#}
    </td>
  </tr>
  <tr>
    <td colspan="2">
      <ul>
        {foreach from=$reports_results.additional_info item='info'}
          <li>{$info}</li>
        {/foreach}
      </ul>
    </td>
  </tr>
  {/if}
</table>