<?php
    class Custom_Report_Filters extends Report_Filters {
        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('DOCUMENT_TYPE_ID', 29);
            define('DEPARTMENT_ID', 20);
            define('DEMO_NUM', 'demo_num');
            define('ACCOUNT_DATE', 'account_date');
            define('MODULE_NAME', 'account_date__modul');
            define('DESCRIPTION', 'account_date__desc');
            define('ACT_NAME', 'account_date__act');
            define('ADDED_BY', 'account_date__add');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'type'              => 'autocompleter',
                'label'             => $this->i18n('reports_customer'),
                'help'              => $this->i18n('reports_customer'),
                'value'             => ''
            );
            $filters['customers'] = $filter;

            //DEFINE MODULE FILTER
            $query = 'SELECT fo.option_value, fo.label FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" . 
                     '  INNER JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo ON (fo.parent_name=fm.name AND lang="' . $registry['lang'] . '")' . "\n" .
                     'WHERE fm.model="Document" AND fm.model_type="' . DOCUMENT_TYPE_ID . '" AND fm.name="' . MODULE_NAME . '"';
            $records_module = $registry['db']->GetAll($query);

            $options_module = array();
            foreach ($records_module as $record) {
                    $options_module[] = array(
                                        'label'         => $record['label'],
                                        'option_value'  => $record['option_value']
                    );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'module',
                'name'      => 'module',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_module'),
                'help'      => $this->i18n('reports_module'),
                'options'   => $options_module,
            );
            $filters['module'] = $filter;

            //DEFINE ACTION FILTER
            $query = 'SELECT fo.option_value, fo.label FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" . 
                     '  INNER JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo ON (fo.parent_name=fm.name AND lang="' . $registry['lang'] . '")' . "\n" .
                     'WHERE fm.model="Document" AND fm.model_type="' . DOCUMENT_TYPE_ID . '" AND fm.name="' . ACT_NAME . '"';
            $records_action = $registry['db']->GetAll($query);

            $options_action = array();
            foreach ($records_action as $record) {
                    $options_action[] = array(
                                        'label'         => $record['label'],
                                        'option_value'  => $record['option_value']
                    );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'action',
                'name'      => 'action',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_action'),
                'help'      => $this->i18n('reports_action'),
                'options'   => $options_action,
            );
            $filters['action'] = $filter;

            //DEFINE ADDED_BY FILTER
            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
            $filters_departments = array('model_lang' => $registry['lang'],
                                      'sanitize' => true,
                                      'where' => array(
                                                    'd.active = "1"',
                                                    'd.id = "' . DEPARTMENT_ID . '"'
                                                 )
                                     );
            $users = Departments::getUsers($registry, $filters_departments);

            //prepare users
            $options_users = array();

            foreach($users as $user) {
                $options_users[] = array(
                    'label' => $user['firstname'] . ' ' . $user['lastname'],
                    'option_value' => $user['id']
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'added_by',
                'name' => 'added_by',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_added_by'),
                'help' => $this->i18n('reports_added_by'),
                'options' => $options_users,
            );
            $filters['added_by'] = $filter;

            //DEFINE WORD SERACH FILTER
            $filter = array (
                'custom_id' => 'key_words',
                'name' => 'key_words',
                'type' => 'text',
                'label' => $this->i18n('reports_key_words'),
                'help' => $this->i18n('reports_key_words')
            );
            $filters['key_words'] = $filter;

            return $filters;
        }
    }
?>