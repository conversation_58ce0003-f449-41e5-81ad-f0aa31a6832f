<table border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
  {if ! $reports_additional_options.dont_show_main_report}
    <tr>
      <td>
        <a name="main_report" />
        <h1 style="color: #000000; margin-bottom: 0px;">{#reports_main_report#}</h1>
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{#reports_report_for_employees#}</a><br />{/if}
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a><br />{/if}
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a><br />{/if}
        <br />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="70%">
          <tr>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_full_num#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_type#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_project#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_employee#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_date_begin#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_date_end#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
{*
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_late#|escape}</div></td>
*}
            <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_status#|escape}</div></td>
          </tr>
          {counter start=$pagination.start name='item_counter' print=false}
          {foreach from=$reports_results item=result name=results}
            <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}{if !$result.include} row_orange{/if}">
              <td class="t_border hright" nowrap="nowrap" width="25">
                {counter name='item_counter' print=true}
              </td>
              <td class="t_border">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$result.record_type}s&amp;{$result.record_type}s=view&amp;view={$result.id}">{$result.full_num|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" nowrap="nowrap">
                {capture assign='record_type'}reports_{$result.record_type}{/capture}
                {$smarty.config.$record_type}, {$result.type_name|default:"&nbsp;"}
              </td>
              <td class="t_border" nowrap="nowrap">
                {$result.customer_name|default:"&nbsp;"}
              </td>
              <td class="t_border" nowrap="nowrap">
                {$result.project_name|default:"&nbsp;"}
              </td>
              <td class="t_border" nowrap="nowrap">
                {$result.made_by_name|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.start_period|date_format:#date_mid#|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.end_period|date_format:#date_mid#|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right">
                {$result.formated_duration|default:"&nbsp;"}
              </td>
{*
              <td class="t_border" align="right">
                {if $result.late <= 0}&nbsp;{else}{$result.formated_late}{/if}
              </td>
*}
              <td align="right" nowrap="nowrap">
                {capture assign='status_icon'}{$theme->imagesUrl}{$result.record_type}s_{$result.status}.png{/capture}
                {if $result.substatus}{$result.substatus}&nbsp;{/if}<img src="{$status_icon}" width="16" height="16" border="0" alt="" title="" />
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="11">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="11"></td>
          </tr>
        </table>
      </td>
    </tr>
  {elseif isset($reports_additional_options.employees)}
    <tr>
      <td>
        <a name="report_for_employees" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a><br />{/if}
        <h1 style="color: #000000; margin-bottom: 0px;">{#reports_report_for_employees#}</h1>
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a><br />{/if}
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a><br />{/if}
        <br />
      </td>
    </tr>
    <tr>
      <td>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
          <tr>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_employee_name#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_projects#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_documents#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_tasks#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
{*
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_lates#|escape}</div></td>
            <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_late#|escape}</div></td>
*}
          </tr>
          {foreach from=$reports_additional_options.employees item=result name=results_employee}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border">
                {$result.employee_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.projects|default:"0"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.documents|default:"0"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.tasks|default:"0"}
              </td>
              <td class="t_border" nowrap="nowrap" align="right">
                {$result.formated_duration|default:"0:00"}
              </td>
{*
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.lates|default:"0"}
              </td>
              <td align="right" nowrap="nowrap">
                {if $result.late <= 0}&nbsp;{else}{$result.formated_late}{/if}
              </td>
*}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="7">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="7"></td>
          </tr>
        </table>
      </td>
    </tr>
  {elseif isset($reports_additional_options.projects)}
    <tr>
      <td>
        <a name="report_for_projects" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a><br />{/if}
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{#reports_report_for_employees#}</a><br />{/if}
        <h1 style="color: #000000; margin-bottom: 0px;">{#reports_report_for_projects#}</h1>
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a><br />{/if}
        <br />
      </td>
    </tr>
    <tr>
      <td>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
          <tr>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_project#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_employee_name#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_documents#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_tasks#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
{*
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_lates#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_late#|escape}</div></td>
*}
            <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
          </tr>
          {foreach from=$reports_additional_options.projects item=result name=results_projects}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" nowrap="nowrap">
                {$result.project_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {foreach from=$result.employees_names item=employee name=emps}
                  {$employee|escape|default:"&nbsp;"}
                  {if !$smarty.foreach.emps.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" align="right">
                {foreach from=$result.documents item=doc name=docs}
                  {$doc|escape|default:"0"}{if !$smarty.foreach.docs.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" align="right">
                {foreach from=$result.tasks item=task name=tas}
                  {$task|escape|default:"0"}{if !$smarty.foreach.tas.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" align="right">
                {foreach from=$result.formated_duration item=duration name=dur}
                  {$duration|escape|default:"0:00"}{if !$smarty.foreach.dur.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
{*
              <td class="t_border" align="right">
                {foreach from=$result.lates item=late name=lat}
                  {$late|escape|default:"0"}{if !$smarty.foreach.lat.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td class="t_border" align="right">
                {foreach from=$result.formated_late item=format_late name=flate}
                  {$format_late|escape|default:"0:00"}{if !$smarty.foreach.flate.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
*}
              <td align="right" nowrap="nowrap">
                {$result.formated_total_duration|default:"0:00"}
              </td>
              
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="8">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="8"></td>
          </tr>
        </table>
      </td>
    </tr>
  {elseif isset($reports_additional_options.customers)}
    <tr>
      <td>
        <a name="report_for_customers" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a><br />{/if}
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{#reports_report_for_employees#}</a><br />{/if}
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a><br />{/if}
        <h1 style="color: #000000; margin-bottom: 0px;">{#reports_report_for_customers#}</h1>
        <br />
      </td>
    </tr>
    <tr>
      <td style="vertical-align: top;">
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
          <tr>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer_name#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_projects#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_documents#|escape}</div></td>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_tasks#|escape}</div></td>
            <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
          </tr>
          {foreach from=$reports_additional_options.customers item=result name=results_customers}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border">
                {$result.customer_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.projects|default:"0"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.documents|default:"0"}
              </td>
              <td class="t_border" align="right" nowrap="nowrap">
                {$result.tasks|default:"0"}
              </td>
              <td nowrap="nowrap" align="right">
                {$result.formated_duration|default:"0:00"}
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="5">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="5"></td>
          </tr>
        </table>
      </td>
    </tr>
  {elseif $reports_additional_options.charts}
    <tr>
      <td>
        <table border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
          {foreach from=$reports_additional_options.charts item='single_chart'}
            <tr>
              <td>
                {include file="chart.html" chart=$single_chart is_dashlet=1}
              </td>
            </tr>
          {/foreach}
        </table>
      </td>
    </tr>
  {/if}
</table>