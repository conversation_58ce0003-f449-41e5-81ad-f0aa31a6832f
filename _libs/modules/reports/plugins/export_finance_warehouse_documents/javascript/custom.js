/**
 * Function to load the statuses of selected document type
 */
function changeDocumentStatuses(element) {
    // prepare ajax options
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function (t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            eval('var doc_types = ' + t.responseText + ';');

            var document_type = $('document_type');
            document_type.options.length = 0;

            if (doc_types.length) {
                removeClass(document_type, 'missing_records');
                document_type.options[0] = new Option('[' + i18n['labels']['all'].toLowerCase() + ']', '', false, true);
                addClass(document_type.options[0], 'undefined');

                for (var j = 0; j < doc_types.length; j++) {
                    document_type.options[j+1] = new Option(doc_types[j]['label'], doc_types[j]['option_value'], false, false);
                }
                toggleUndefined(document_type);
            } else {
                document_type.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(document_type, 'missing_records');
            }

            Effect.Fade('loading');
        },
        on404: function (t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function (t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_change_document_list&report_type=' + $('report_type').value + '&document_type=' + element.value;
    new Ajax.Request(url, opt);
}
