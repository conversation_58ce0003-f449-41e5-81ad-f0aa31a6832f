<?php
    Class Eset_Cash_Box_Availability_Plovdiv Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('DOCUMENT_TYPE_INCOME_ID', 3);
            define('DOCUMENT_TYPE_EXPENSE_ID', 4);
            define('CASH_DESK', 'cash_desk');
            define('PLOVDIV', 'cash_desk_pl');
            define('PAYING_CURRENCY', 'price_currency');
            define('PRICE', 'price_digit');
            define('PRICE_EXPENCES', 'price_digits');

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $current_date = date(PH_LOGGER_DATE_FORMAT);
            $incomings = array();
            $expences = array();

            // incomings
            $sql_for_add_vars_1 = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_INCOME_ID . ' AND (fm.name="' . CASH_DESK . '" OR fm.name="' . PAYING_CURRENCY . '" OR fm.name="' . PRICE . '") ORDER BY fm.position';
            $var_ids_1 = $registry['db']->GetAll($sql_for_add_vars_1);

            $in_cash_desk_id = '';
            $in_paying_currency_id = '';
            $in_price_id = '';

            foreach ($var_ids_1 as $vars) {
                if ($vars['name'] == CASH_DESK) {
                    $in_cash_desk_id = $vars['id'];
                } else if ($vars['name'] == PAYING_CURRENCY) {
                    $in_paying_currency_id = $vars['id'];
                } else if ($vars['name'] == PRICE) {
                    $in_price_id = $vars['id'];
                }
            }

            $sql_1['select']  =   'SELECT d.id AS document_id, d.full_num AS full_num, dt.direction AS direction, ' . "\n" . 
                                '  d.active AS active, ci18n.name AS customer, d.type, dti18n.name AS type_name, ' . "\n" . 
                                '  d.added AS added, ' . "\n" . 
                                '  d_cstm_cash_desk.value AS cash_desk, ' . "\n" . 
                                '  d_cstm_price.value AS price, ' . "\n" . 
                                '  d_cstm_paying_currency.value AS paying_currency ' . "\n";

            //from clause
            $sql_1['from'] =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                            '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                            '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_cash_desk' . "\n" .
                            '  ON (d.id=d_cstm_cash_desk.model_id AND d_cstm_cash_desk.var_id="' . $in_cash_desk_id . '")' . "\n" . 
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_price ' . "\n" .
                            '  ON (d.id=d_cstm_price.model_id AND d_cstm_price.var_id="' . $in_price_id . '")' . "\n" . 
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                            '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $in_paying_currency_id . '")' . "\n";

            // construct where
            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.type="' . DOCUMENT_TYPE_INCOME_ID . '"';
            $where[] = 'd.active!=0';
            $where[] = 'd.added <= "' . $current_date . '"' .  "\n";
            $where[] = 'd_cstm_cash_desk.value="' . PLOVDIV . '"';

            $sql_1['where'] = 'WHERE ' . implode(' AND ', $where);

            //search basic details with current lang parameters
            $query = implode("\n", $sql_1);
            $records = $registry['db']->GetAll($query);

            $incomings = $records;

            // expenses
            $sql_for_add_vars_2 = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_EXPENSE_ID . ' AND (fm.name="' . CASH_DESK . '" OR fm.name="' . PAYING_CURRENCY . '" OR fm.name="' . PRICE_EXPENCES . '") ORDER BY fm.position';
            $var_ids_2 = $registry['db']->GetAll($sql_for_add_vars_2);

            $out_cash_desk_id = '';
            $out_paying_currency_id = '';
            $out_price_id = '';

            foreach ($var_ids_2 as $vars) {
                if ($vars['name'] == CASH_DESK) {
                    $out_cash_desk_id = $vars['id'];
                } else if ($vars['name'] == PRICE_EXPENCES) {
                    $out_price_id = $vars['id'];
                } else if ($vars['name'] == PAYING_CURRENCY) {
                    $out_paying_currency_id = $vars['id'];
                }
            }

            $sql_2['select']  =   'SELECT d.id AS document_id, d.full_num AS full_num, dt.direction AS direction, ' . "\n" . 
                                '  d.active AS active, ci18n.name AS customer, d.type, dti18n.name AS type_name, ' . "\n" . 
                                '  d.added AS added, ' . "\n" . 
                                '  d_cstm_cash_desk.value AS cash_desk, ' . "\n" . 
                                '  d_cstm_price.value AS price, ' . "\n" . 
                                '  d_cstm_paying_currency.value AS paying_currency ' . "\n";

            //from clause
            $sql_2['from'] =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                            '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                            '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_cash_desk' . "\n" .
                            '  ON (d.id=d_cstm_cash_desk.model_id AND d_cstm_cash_desk.var_id="' . $out_cash_desk_id . '")' . "\n" . 
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_price ' . "\n" .
                            '  ON (d.id=d_cstm_price.model_id AND d_cstm_price.var_id="' . $out_price_id . '")' . "\n" . 
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                            '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $out_paying_currency_id . '")' . "\n";

            // construct where
            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.type="' . DOCUMENT_TYPE_EXPENSE_ID . '"';
            $where[] = 'd.active!=0';
            $where[] = 'd.added <= "' . $current_date . '"' .  "\n";
            $where[] = 'd_cstm_cash_desk.value="' . PLOVDIV . '"';

            $sql_2['where'] = 'WHERE ' . implode(' AND ', $where);

            //search basic details with current lang parameters
            $query = implode("\n", $sql_2);
            $records = $registry['db']->GetAll($query);
            $expences = $records;

            $total_BGN = 0;
            $total_USD = 0;
            $total_EUR = 0;

            if (! empty($incomings)) {
                foreach ($incomings as $income) {
                    if (!empty($income['paying_currency']) && !empty($income['price'])) {
                        $var_currency = 'total_' . $income['paying_currency'];
                        $$var_currency += $income['price'];
                    }
                }
            }

            if (! empty($expences)) {
                foreach ($expences as $expence) {
                    if (!empty($expence['paying_currency']) && !empty($expence['price'])) {
                        $var_currency = 'total_' . $expence['paying_currency'];
                        $$var_currency = $$var_currency - $expence['price'];
                    }
                }
            }

            $records_all['additional_options']['total_BGN'] = $total_BGN;
            $records_all['additional_options']['total_USD'] = $total_USD;
            $records_all['additional_options']['total_EUR'] = $total_EUR;

            if (!empty($filters['paginate'])) {
                $results = array($records_all, 0);
            } else {
                //no pagination required return only the models
                $results = $records_all;
            }

            return $results;
        }
    }
?>