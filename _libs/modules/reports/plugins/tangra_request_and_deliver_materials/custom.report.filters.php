<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('DOCUMENT_REQUEST_DELIVERY_INNER_MARKET', 5);
            define('DOCUMENT_REQUEST_DELIVERY_INNER_MARKET_DIRECTION', 3);
            define('DOCUMENT_REQUEST_DELIVERY_IMPORT', 8);
            define('DOCUMENT_REQUEST_DELIVERY_IMPORT_DIRECTION', 3);

            define('INNER_MARKET_DIRECTION', 'ord_napravlenie');
            define('INNER_MARKET_REQUESTED_BY', 'ord_sastavil');
            define('INNER_MARKET_EXTECTED_DELIVERY_DATE', 'ord_data_dost');
            define('INNER_MARKET_DELIVERY_DATE', 'orm_poluch_otd');
            define('INNER_MARKET_PRODUCT_NAME', 'ord_name');
            define('INNER_MARKET_ACCEPTANCE_DATE', 'ors_poluch_otd');
            define('INNER_MARKET_CUSTOMER_ID', 'customers_id');
            define('INNER_MARKET_NOTES', 'ors_notes');
            define('INNER_MARKET_NUM_RECEIVE_DOC', 'orm_doc_otd');
            define('INNER_MARKET_NOTES_2', 'orm_notes');

            define('IMPORT_DIRECTION', 'zaqvn_napravlenie');
            define('IMPORT_REQUESTED_BY', 'zaqvn_sastavil');
            define('IMPORT_EXTECTED_DELIVERY_DATE', 'zaqvn_data_dost');
            define('IMPORT_DELIVERY_DATE', 'orm_poluch_otd');
            define('IMPORT_PRODUCT_NAME', 'zaqvn_name');
            define('IMPORT_ACCEPTANCE_DATE', 'ors_poluch_otd');
            define('IMPORT_CUSTOMER_NAME', 'customers_id');
            define('IMPORT_NOTES', 'ors_notes');
            define('IMPORT_NUM_RECEIVE_DOC', 'orm_doc_otd');
            define('IMPORT_NOTES_2', 'orm_notes');


            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customer',
                'name'              => 'customer',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customer'),
                'help'              => $this->i18n('reports_customer'),
                'value'             => ''
            );
            $filters['customer'] = $filter;

            //DEFINE DELIVERER FILTER
            $filter = array (
                'custom_id'         => 'deliverer',
                'name'              => 'deliverer',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_deliverer'),
                'help'              => $this->i18n('reports_deliverer'),
                'value'             => ''
            );
            $filters['deliverer'] = $filter;

            //DEFINE DOCUMENT NUM
            $filter = array (
                'custom_id' => 'document_num',
                'name' => 'document_num',
                'type' => 'text',
                'label' => $this->i18n('reports_document_num'),
                'help' => $this->i18n('reports_document_num_help')
            );
            $filters['document_num'] = $filter;

            //DEFINE DOCUMENT NUM
            $filter = array (
                'custom_id' => 'document_num_receive',
                'name' => 'document_num_receive',
                'type' => 'text',
                'label' => $this->i18n('reports_document_num_receive'),
                'help' => $this->i18n('reports_document_num_receive_help')
            );
            $filters['document_num_receive'] = $filter;

            //DEFINE DIRECTIONS FILTER
            $query = 'SELECT fo.option_value as option_value, fo.label as label' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo' . "\n" .
                     'WHERE fo.parent_name="' . INNER_MARKET_DIRECTION . '" AND fo.lang="' . $registry['lang'] . '"' . "\n";
            $directions = $registry['db']->GetAll($query);
            $options_directions = array();

            foreach($directions as $direction) {
                $options_directions[] = array(
                    'label'         => $direction['label'],
                    'option_value'  => $direction['option_value']
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'direction',
                'name'      => 'direction',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_direction'),
                'help'      => $this->i18n('reports_direction_help'),
                'options'   => $options_directions,
            );
            $filters['direction'] = $filter;

            //DEFINE EMPLOYEES FILTER
            $query = 'SELECT c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label ' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                     'WHERE type="' . PH_CUSTOMER_EMPLOYEE . '" AND c.deleted_by=0 AND c.code!=1 AND c.active=1' . "\n" .
                     'ORDER BY ci18n.name, ci18n.lastname';

            $employees = $registry['db']->GetAll($query);
            $options_employees = array();

            foreach($employees as $employee) {
                $options_employees[] = array(
                    'label'         => $employee['label'],
                    'option_value'  => $employee['option_value']
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee_help'),
                'options'   => $options_employees,
            );
            $filters['employee'] = $filter;

            //DEFINE EXPECTED DELIVERY DATE FILTER FROM
            $filter = array (
                'custom_id' => 'expected_from_date',
                'name' => 'expected_from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_expected_from_date'),
                'help' => $this->i18n('reports_expected_from_date')
            );
            $filters['expected_from_date'] = $filter;

            //DEFINE EXPECTED DELIVERY DATE FILTER TO
            $filter = array (
                'custom_id' => 'expected_to_date',
                'name' => 'expected_to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_expected_to_date'),
                'help' => $this->i18n('reports_expected_to_date')
            );
            $filters['expected_to_date'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'delivery_date_from',
                'name' => 'delivery_date_from',
                'type' => 'date',
                'label' => $this->i18n('reports_delivery_date_from'),
                'help' => $this->i18n('reports_delivery_date_from')
            );
            $filters['delivery_date_from'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'delivery_date_to',
                'name' => 'delivery_date_to',
                'type' => 'date',
                'label' => $this->i18n('reports_delivery_date_to'),
                'help' => $this->i18n('reports_delivery_date_to')
            );
            $filters['delivery_date_to'] = $filter;

            //DEFINE SHOW ARCHIVES FILTER
            $filter = array (
                'custom_id' => 'show_archives',
                'name'      => 'show_archives',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_show_archive'),
                'help'      => $this->i18n('reports_show_archive'),
                'options'   => array(
                                   array(
                                       'label'         => '',
                                       'option_value'  => '1'
                                   )
                               )
            );
            $filters['show_archives'] = $filter;

            return $filters;
        }
    }
?>