var reports_address_expenses_and_incomes_for_period_interval = '';
document.observe('dom:loaded', function () {
    /**
     * Export button
     */
    $('export_button').observe('click', function () {
        /**
         * Validate
         */
        if (!$('period_from').value || !$('period_to').value) {
            alert('Моля, попълнете задължителните филтри!');
            return false;
        }

        /**
         * Export
         */
        Cookie.set('report_address_expenses_and_incomes_for_period_custom_export', 1, 1);

        Effect.Center('loading');
        Effect.Appear('loading');

        reports_address_expenses_and_incomes_for_period_interval = window.setInterval(
            function () {
                if (!Cookie.get('report_address_expenses_and_incomes_for_period_custom_export')) {
                    Effect.Fade('loading');
                    clearInterval(reports_address_expenses_and_incomes_for_period_interval);
                }
            },
            1000
        );

        window.location = env.base_url + '?' + Form.serialize($('reports_generated'));
    });

    /**
     * Hide the "Range" filter when for "Export" filter is selected "all"
     */
    $$('[name="kind"]:checked').each(function (element) {
        if (element.value == 'all') {
            addClass($('range_from').closest('tr'), 'hidden');
        }
    });

    /**
     * Show/Hide the "Range" filter
     */
    $$('[name="kind"]').invoke('observe', 'change', function () {
        if (this.value == 'all') {
            addClass($('range_from').closest('tr'), 'hidden');
        } else {
            removeClass($('range_from').closest('tr'), 'hidden');
        }
    });

    /**
     * Make the "Range" filter to allow only numbers
     */
    $$('[name="range_from"], [name="range_to"]').invoke('observe', 'keypress', function (event) {
        return changeKey(this, event, insertOnlyDigits);
    });
});
