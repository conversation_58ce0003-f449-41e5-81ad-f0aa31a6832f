<?php

class Custom_Report_Filters extends Report_Filters {
    /**
     * Defining filters for the certain type report
     */
    public function defineFilters(&$registry) {
        $filters = array();

        $report_name = '';
        if ($this->reportName) {
            $report_name = $this->reportName;
        } else {
            $report_name = $registry['report_type']['name'];
        }

        // FROM 'Period' Filter
        $filters['period_from'] = array(
            'label'             => $this->i18n('reports_payment_period'),
            'help'              => $this->i18n('reports_payment_period'),
            'first_filter_label'=> $this->i18n('from'),
            'name'              => 'period_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'period_to',
            'required'          => true
        );
        $filters['period_to'] = array(
            'name' => 'period_to',
            'type' => 'date',
        );

        //DEFINE 'Customer' Filter
        $filters['customer'] = array(
            'custom_id' => 'customer',
            'name' => 'customer',
            'type' => 'custom_filter',
            'actual_type' => 'autocompleter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'autocomplete_buttons' => 'clear',
            'width' => '222',
            'autocomplete' => array(
                'type' => 'customers',
                'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                'suggestions'  => '<name> <lastname>',
                'fill_options' => array(
                    '$customer               => <id>',
                    '$customer_autocomplete  => <name> <lastname>',
                    '$customer_oldvalue      => <name> <lastname>',
                ),
                'clear' => 1,
                'buttons_hide' => 'search'
            ),
            'label' => $this->i18n('reports_filter_customer'),
            'help' => $this->i18n('reports_filter_customer_help')
        );

        return $filters;
    }

    public function processDependentFilters($filters) {
        $unset_filters = array();

        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter'])) {
                if (isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }

}

?>