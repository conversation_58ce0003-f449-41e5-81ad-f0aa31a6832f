<script type="text/javascript" src="{$reports_additional_options.scripts_url}?{$system_options.build}"></script>

{capture assign='precision'}%.{$reports_additional_options.quantity_precision}f{/capture}
<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 10px;">
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;" nowrap="nowrap">
      {#num#|escape}
    </td>
    <td class="t_border" style="vertical-align: middle;"><div style="width: 440px;">{#reports_nomenclature#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="width: 50px;">{#reports_nomenclature_measure#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="width: 65px;">{#reports_nomenclature_batch#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="width: 65px;">{#reports_nomenclature_serial#|escape}</div></td>
    <td style="vertical-align: middle;" class="t_border"><div style="width: 80px;">{#reports_reserved_availability#|escape}</div></td>
    <td style="vertical-align: middle;" class="t_border"><div style="width: 80px;">{#reports_availability#|escape}</div></td>
    <td style="vertical-align: middle;" class="t_border"><div style="width: 80px;">{#reports_handovered_quantity#|escape}</div></td>
    <td style="vertical-align: middle;"><div style="width: 80px;">{#reports_price#|escape}</div></td>
  </tr>
  {counter start=0 name='item_counter' print=false}
  {foreach from=$reports_results key=k name=list_res item=result}
    {capture assign='row_class_article'}{cycle name='cycle_articles' values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
    {foreach from=$result.batches name=btch key=batch_id item=batch}
      <tr class="{$row_class_article}">
        {if $smarty.foreach.btch.first}
          <td class="t_border hright" nowrap="nowrap" rowspan="{$result.rowspan}" style="vertical-align: middle;">
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border" rowspan="{$result.rowspan}" style="vertical-align: middle;">
            {$result.name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border" rowspan="{$result.rowspan}" align="center" style="vertical-align: middle;">
            {$result.article_measure|escape|default:"&nbsp;"}
            <input type="hidden" name="measure[{$result.id}]" id="measure_{$result.id}" value="{$result.measure}" />
          </td>
        {/if}
        <td class="t_border" align="center" style="vertical-align: middle;">
          {$batch.batch_code|escape|default:"&nbsp;"}
        </td>
        <td class="t_border" align="center" style="vertical-align: middle;">
          {$batch.serial|escape|default:"&nbsp;"}
        </td>
        <td class="hright t_border" align="center" style="vertical-align: middle;">
          {if $batch.quantity_reserved}
            {$batch.quantity_reserved|escape|string_format:"$precision"|default:"-"}
          {else}
            -
          {/if}
        </td>
        <td class="t_border hright" style="vertical-align: middle; background-color: #FDEF8F!important;">
          {if $batch.quantity_total}
            {$batch.quantity_total|escape|string_format:"$precision"|default:"-"}
          {else}
            -
          {/if}
          <input type="hidden" name="available_quantity[{$result.id}|{$batch_id}]" id="available_quantity_{$result.id}|{$batch_id}" value="{$batch.quantity_total}" />
          <input type="hidden" name="custom_num[{$result.id}|{$batch_id}]" id="custom_num[{$result.id}|{$batch_id}]" value="{$batch.custom_num}" />
          <input type="hidden" name="serial_num[{$result.id}|{$batch_id}]" id="serial_num[{$result.id}|{$batch_id}]" value="{$batch.serial}" />
          <input type="hidden" name="expire_date[{$result.id}|{$batch_id}]" id="expire_date[{$result.id}|{$batch_id}]" value="{$batch.expire}" />
          <input type="hidden" name="delivery_price[{$result.id}|{$batch_id}]" id="delivery_price[{$result.id}|{$batch_id}]" value="{$batch.delivery_price}" />
          <input type="hidden" name="delivery_price_currency[{$result.id}|{$batch_id}]" id="delivery_price_currency[{$result.id}|{$batch_id}]" value="{$batch.delivery_price_currency}" />
        </td>

        <td class="t_border hright" style="vertical-align: middle;">
            <input id="handover_quantity_{$result.id}|{$batch_id}"
                   name="handover_quantity[{$result.id}|{$batch_id}]"
                   class="txtbox" type="text" autocomplete="off" onfocus="highlight(this);"
                   onblur="unhighlight(this); processCompletedQuantity(this, {$reports_additional_options.quantity_precision})"
                   onkeyup="checkAvailableQuantities(this, {$reports_additional_options.quantity_precision});"
                   onkeypress="return changeKey(this, event, insertOnlyFloats);"
                   style="width: 80px; text-align: right;"
                   oncontextmenu="return false;"
                   ondrop="return false;" />
        </td>
        <td class="hright" style="vertical-align: middle;">
            <input id="quantity_price_{$result.id}|{$batch_id}"
                   name="quantity_price[{$result.id}|{$batch_id}]"
                   class="txtbox" type="text" autocomplete="off" onfocus="highlight(this);"
                   onblur="unhighlight(this); processCompletedQuantity(this, '')"
                   onkeypress="return changeKey(this, event, insertOnlyFloats);"
                   style="width: 80px; text-align: right;"
                   oncontextmenu="return false;"
                   ondrop="return false;" />
        </td>
      </tr>
    {/foreach}
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="9">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="9"></td>
  </tr>
</table>

<table border="0">
  <tr>
    <td>
      <label for="new_document_branch" style="color: #666666; font-size: 11px;">{#reports_branch#}: {#required#}</label>
    </td>
    <td>
      {include file=input_autocompleter.html
        name='new_document_branch'
        autocomplete=$reports_additional_options.branches_autocompleter
        autocomplete_type='projects'
        autocomplete_var_type='basic'
        autocomplete_buttons='clear'
        width=244
        standalone=true
        required=1
        label=#reports_branch#
        help=#reports_branch#
      }
    </td>
    <td>
      <label for="new_document_branch" style="margin-left: 20px; color: #666666; font-size: 11px;">{#reports_period_date#}: {#required#}</label>
    </td>
    <td>
      {include file=input_date.html
        name='new_document_date'
        width=80
        standalone=true
        required=1
        label=#reports_period_date#
        help=#reports_period_date#
      }
    </td>
    <td>
      <button type="submit" name="add_united_invoice" class="button" onclick="issueDocuments(this); return false;" style="margin-left: 20px;">{#reports_create_finance_documents#|escape}</button>
    </td>
  </tr>
</table>