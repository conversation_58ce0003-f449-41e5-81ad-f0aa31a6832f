<?php
    Class Credilink_History_Of_Credit_Changes Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            // Prepare array for the results
            $results = array(
                'change_types' => array()
            );

            // Check required filters
            if (empty($filters['period_from']) || empty($filters['period_to'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));
            } else {
                // Get the lang
                $lang = $registry['lang'];

                // Get the database object
                $db = $registry['db'];

                // Process filter change_type
                if (empty($filters['change_type'])) {
                    $filters['change_type'] = array(1, 2, 3, 4, 5);
                }

                /**
                 * Get fields
                 */
                // Get documents Contracts fields
                $query = "
                    SELECT name, id
                      FROM " . DB_TABLE_FIELDS_META . " AS fm
                      WHERE model = 'Document'
                        AND model_type = 6
                        AND name IN ('type_repayment', 'repayment_day', 'contract_rate', 'contract_warranty', 'date_time_extensions',
                                     'amount_extend_int', 'amount_extend_fee', 'maturity_extended_install', 'date_court_completion', 'grant_credit_currency')";
                $fields_contract = $db->GetAssoc($query);

                // Get projects Court proceedings fields
                $query = "
                    SELECT name, id
                      FROM " . DB_TABLE_FIELDS_META . " AS fm
                      WHERE model = 'Project'
                        AND model_type = 1
                        AND name IN ('contract_num_id', 'date_proceedings', 'contract_num', 'contract_date', 'personal_num')";
                $fields_cp = $db->GetAssoc($query);

                /**
                 * Get data
                 */
                // Change type: 1
                if (in_array(1, $filters['change_type'])) {
                    $query = "
                        SELECT d.id                                 AS id,
                            di.name                                 AS num,
                            d.date                                  AS date,
                            dc5.value                               AS currency,
                            c.id                                    AS customer_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                            IF(c.is_company, c.eik, c.ucn)          AS customer_ucn,
                            DATE(dc2.value)                         AS change_date,
                            SUM(
                              IF(g.price > 0,
                                g.quantity,
                                0
                              )
                            ) - dc3.value                           AS rate_change,
                            SUM(
                              IF(g.price > 0,
                                g.article_trademark,
                                0
                              )
                            ) - dc4.value                           AS guarantor_fee
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                            ON (!d.deleted
                              AND d.active
                              AND d.type = 6
                              AND d.substatus = 20" .
                              (!empty($filters['client']) ? "
                              AND d.customer = '{$filters['client']}'" : '') . "
                              AND di.parent_id = d.id
                              AND di.lang = '{$lang}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                            ON (dc1.model_id = d.id
                              AND dc1.var_id = {$fields_contract['type_repayment']}
                              AND dc1.num = 1
                              AND dc1.lang = ''
                              AND dc1.value = '1')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                            ON (dc2.model_id = d.id
                              AND dc2.var_id = {$fields_contract['repayment_day']}
                              AND dc2.num = 1
                              AND dc2.lang = ''
                              AND DATE(dc2.value) IS NOT NULL
                              AND DATE(dc2.value) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}')
                          JOIN " . DB_TABLE_CUSTOMERS . " AS c
                            ON (c.id = d.customer)
                          JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = c.id
                              AND ci.lang = '{$lang}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc3
                            ON (dc3.model_id = d.id
                              AND dc3.var_id = {$fields_contract['contract_rate']}
                              AND dc3.num = 1
                              AND dc3.lang = '')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc4
                            ON (dc4.model_id = d.id
                              AND dc4.var_id = {$fields_contract['contract_warranty']}
                              AND dc4.num = 1
                              AND dc4.lang = '')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc5
                            ON (dc5.model_id = d.id
                              AND dc5.var_id = {$fields_contract['grant_credit_currency']}
                              AND dc5.num = 1
                              AND dc5.lang = '')
                          JOIN " . DB_TABLE_GT2_DETAILS . " AS g
                            ON (g.model = 'Document'
                              AND g.model_id = d.id)
                          GROUP BY d.id";
                    $c1 = $db->GetAll($query);
                }

                // Change type: 2
                if (in_array(2, $filters['change_type'])) {
                    $query = "
                        SELECT d.id                                 AS id,
                            di.name                                 AS num,
                            d.date                                  AS date,
                            dc5.value                               AS currency,
                            c.id                                    AS customer_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                            IF(c.is_company, c.eik, c.ucn)          AS customer_ucn,
                            DATE(dc2.value)                         AS change_date
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                            ON (!d.deleted
                              AND d.active
                              AND d.type = 6
                              AND d.substatus = 20" .
                              (!empty($filters['client']) ? "
                              AND d.customer = '{$filters['client']}'" : '') . "
                              AND di.parent_id = d.id
                              AND di.lang = '{$lang}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                            ON (dc1.model_id = d.id
                              AND dc1.var_id = {$fields_contract['type_repayment']}
                              AND dc1.num = 1
                              AND dc1.lang = ''
                              AND dc1.value = '2')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                            ON (dc2.model_id = d.id
                              AND dc2.var_id = {$fields_contract['repayment_day']}
                              AND dc2.num = 1
                              AND dc2.lang = ''
                              AND DATE(dc2.value) IS NOT NULL
                              AND DATE(dc2.value) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc5
                            ON (dc5.model_id = d.id
                              AND dc5.var_id = {$fields_contract['grant_credit_currency']}
                              AND dc5.num = 1
                              AND dc5.lang = '')
                          JOIN " . DB_TABLE_CUSTOMERS . " AS c
                            ON (c.id = d.customer)
                          JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = c.id
                              AND ci.lang = '{$lang}')";
                    $c2 = $db->GetAll($query);
                }


                // Get documents Contracts last bank payments
                if (!empty($c1) || !empty($c2)) {
                    if (empty($c1)) {
                        $c1 = array();
                    }
                    if (empty($c2)) {
                        $c2 = array();
                    }
                    $repaid_contracts_ids = array_merge(array_column($c1, 'id'), array_column($c2, 'id'));
                    $query = "
                        SELECT d.id AS document_id,
                            /* TODO: Optimize this! */
                            SUBSTRING_INDEX(GROUP_CONCAT(fp.amount ORDER BY fp.id DESC), ',', 1) AS amount
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_FINANCE_REASONS_RELATIVES . " AS frr
                            ON (d.id IN (" . implode(', ', $repaid_contracts_ids) . ")
                              AND frr.link_to_model_name = 'Document'
                              AND frr.link_to = d.id
                              AND frr.parent_model_name = 'Finance_Incomes_Reason')
                          JOIN " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                            ON (fir.id = frr.parent_id
                              AND !fir.annulled
                              AND fir.active
                              AND fir.type = 104
                              AND fir.status = 'finished')
                          JOIN " . DB_TABLE_FINANCE_BALANCE . " AS fb
                            ON (fb.paid_to_model_name = 'Finance_Incomes_Reason'
                              AND fb.paid_to = fir.id
                              AND fb.parent_model_name = 'Finance_Payment')
                          JOIN " . DB_TABLE_FINANCE_PAYMENTS . " AS fp
                            ON (fp.id = fb.parent_id
                              AND fp.type = 'BP')
                          GROUP BY d.id";
                    $repaid_contracts_last_payments = $db->GetAssoc($query);

                    // Process data for changes of type 1
                    foreach ($c1 as $c) {
                        if (!isset($results['change_types'][1]['contracts'])) {
                            $results['change_types'][1]['contracts'] = array();
                        }
                        if (!isset($results['change_types'][1]['contracts'][$c['id']])) {
                            $results['change_types'][1]['contracts'][$c['id']] = array(
                                'num'           => $c['num'],
                                'date'          => $c['date'],
                                'currency'      => $c['currency'],
                                'customer_id'   => $c['customer_id'],
                                'customer_name' => $c['customer_name'],
                                'customer_ucn'  => $c['customer_ucn'],
                                'changes'       => array()
                            );
                        }
                        $results['change_types'][1]['contracts'][$c['id']]['changes'][] = array(
                            'date'          => $c['change_date'],
                            'rate_change'   => $c['rate_change'],
                            'guarantor_fee' => $c['guarantor_fee'],
                            'last_payment'  => isset($repaid_contracts_last_payments[$c['id']]) ? $repaid_contracts_last_payments[$c['id']] : 0
                        );
                    }

                    // Process data for changes of type 2
                    foreach ($c2 as $c) {
                        if (!isset($results['change_types'][2]['contracts'])) {
                            $results['change_types'][2]['contracts'] = array();
                        }
                        if (!isset($results['change_types'][2]['contracts'][$c['id']])) {
                            $results['change_types'][2]['contracts'][$c['id']] = array(
                                'num'           => $c['num'],
                                'date'          => $c['date'],
                                'currency'      => $c['currency'],
                                'customer_id'   => $c['customer_id'],
                                'customer_name' => $c['customer_name'],
                                'customer_ucn'  => $c['customer_ucn'],
                                'changes'       => array()
                            );
                        }
                        $results['change_types'][2]['contracts'][$c['id']]['changes'][] = array(
                            'date'         => $c['change_date'],
                            'last_payment' => isset($repaid_contracts_last_payments[$c['id']]) ? $repaid_contracts_last_payments[$c['id']] : 0
                        );
                    }
                }

                // Change type: 3
                if (in_array(3, $filters['change_type'])) {
                    $query = "
                        SELECT d.id                                 AS id,
                            di.name                                 AS num,
                            d.date                                  AS date,
                            dc5.value                               AS currency,
                            c.id                                    AS customer_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                            IF(c.is_company, c.eik, c.ucn)          AS customer_ucn,
                            DATE(d2.deadline)                       AS change_date,
                            d2.id                                   AS annex_id,
                            d2.full_num                             AS annex_num
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                            ON (!d.deleted
                              AND d.active
                              AND d.type = 6
                              AND d.substatus NOT IN (" . CONTRACTS_SUBSTATUS_NOT_IN . ")" .
                        (!empty($filters['client']) ? "
                              AND d.customer = '{$filters['client']}'" : '') . "
                              AND di.parent_id = d.id
                              AND di.lang = '{$lang}')
                          JOIN " . DB_TABLE_CUSTOMERS . " AS c
                            ON (c.id = d.customer)
                          JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = c.id
                              AND ci.lang = '{$lang}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc5
                            ON (dc5.model_id = d.id
                              AND dc5.var_id = {$fields_contract['grant_credit_currency']}
                              AND dc5.num = 1
                              AND dc5.lang = '')
                          INNER JOIN " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr                              
                            ON (dr.parent_model_name='Document' AND dr.link_to_model_name='Document' AND dr.link_to=d.id AND dr.origin='transformed')
                          INNER JOIN " . DB_TABLE_DOCUMENTS . " AS d2
                            ON (d2.id=dr.parent_id AND d2.type=21 AND d2.active=1 AND d2.deleted_by=0 AND d2.status='closed' AND DATE(d2.deadline) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}')";
                    $c3 = $db->GetAll($query);

                    // Process data for changes of type 3
                    foreach ($c3 as $c) {
                        if (!isset($results['change_types'][3]['contracts'])) {
                            $results['change_types'][3]['contracts'] = array();
                        }
                        if (!isset($results['change_types'][3]['contracts'][$c['id']])) {
                            $results['change_types'][3]['contracts'][$c['id']] = array(
                                'num'           => $c['num'],
                                'date'          => $c['date'],
                                'currency'      => $c['currency'],
                                'customer_id'   => $c['customer_id'],
                                'customer_name' => $c['customer_name'],
                                'customer_ucn'  => $c['customer_ucn'],
                                'changes'       => array()
                            );
                        }
                        $results['change_types'][3]['contracts'][$c['id']]['changes'][] = array(
                            'id'   => $c['annex_id'],
                            'num'  => $c['annex_num'],
                            'date' => $c['change_date']
                        );
                    }
                }

                // Change type: 4
                // Да не е анулиран или измама.
                // Нарочно не се гледа да е в статус 23 "Преминал към съдебно дело",
                // зщото може да е минал през този статус и вече да е в друг, но
                // важното е историческото действие, а не текущото му състояние.
                // Т.е. важното е, че някога е минал през съдебно дело.
                if (in_array(4, $filters['change_type'])) {
                    $query = "
                        SELECT d.id                                 AS id,
                            pc2.value                               AS num,
                            pc3.value                               AS date,
                            dc5.value                               AS currency,
                            ci.parent_id                            AS customer_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                            pc4.value                               AS customer_ucn,
                            DATE(pc5.value)                         AS change_date,
                            p.id                                    AS cp_id
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc5
                            ON (dc5.model_id = d.id
                              AND dc5.var_id = {$fields_contract['grant_credit_currency']}
                              AND dc5.num = 1
                              AND dc5.lang = '')
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc1
                            ON (!d.deleted
                              AND d.active
                              AND d.type = 6
                              AND d.substatus NOT IN (" . CONTRACTS_SUBSTATUS_NOT_IN . ")" .
                              (!empty($filters['client']) ? "
                              AND d.customer = '{$filters['client']}'" : '') . "
                              AND pc1.var_id = {$fields_cp['contract_num_id']}
                              AND pc1.lang = ''
                              AND pc1.num = 1
                              AND pc1.value = d.id)
                          JOIN " . DB_TABLE_PROJECTS . " AS p
                            ON (p.id = pc1.model_id
                              AND !p.deleted
                              AND p.active
                              AND p.type = 1
                              AND p.status IN ('planning', 'finished')" .
                              (!empty($filters['client']) ? "
                              AND p.customer = '{$filters['client']}'" : '') . ")
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc5
                            ON (pc5.model_id = p.id
                              AND pc5.var_id = {$fields_cp['date_proceedings']}
                              AND pc5.num = 1
                              AND pc5.lang = ''
                              AND DATE(pc5.value) IS NOT NULL
                              AND DATE(pc5.value) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}')
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc2
                            ON (pc2.model_id = p.id
                              AND pc2.var_id = {$fields_cp['contract_num']}
                              AND pc2.num = 1
                              AND pc2.lang = '')
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc3
                            ON (pc3.model_id = p.id
                              AND pc3.var_id = {$fields_cp['contract_date']}
                              AND pc3.num = 1
                              AND pc3.lang = '')
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc4
                            ON (pc4.model_id = p.id
                              AND pc4.var_id = {$fields_cp['personal_num']}
                              AND pc4.num = 1
                              AND pc4.lang = '')
                          JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = p.customer
                              AND ci.lang = '{$lang}')";
                    $c4 = $db->GetAll($query);

                    // Process data for changes of type 4
                    foreach ($c4 as $c) {
                        if (!isset($results['change_types'][4]['contracts'])) {
                            $results['change_types'][4]['contracts'] = array();
                        }
                        if (!isset($results['change_types'][4]['contracts'][$c['id']])) {
                            $results['change_types'][4]['contracts'][$c['id']] = array(
                                'num'           => $c['num'],
                                'date'          => $c['date'],
                                'currency'      => $c['currency'],
                                'customer_id'   => $c['customer_id'],
                                'customer_name' => $c['customer_name'],
                                'customer_ucn'  => $c['customer_ucn'],
                                'changes'       => array()
                            );
                        }
                        $results['change_types'][4]['contracts'][$c['id']]['changes'][] = array(
                            'date'  => $c['change_date'],
                            'cp_id' => $c['cp_id']
                        );
                    }
                }

                // Change type: 5
                if (in_array(5, $filters['change_type'])) {
                    $query = "
                        SELECT d.id                                 AS id,
                            di.name                                 AS num,
                            d.date                                  AS date,
                            dc5.value                               AS currency,
                            c.id                                    AS customer_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                            IF(c.is_company, c.eik, c.ucn)          AS customer_ucn,
                            DATE(dc1.value)                         AS change_date,
                            p.id                                    AS cp_id
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                            ON (!d.deleted
                              AND d.active
                              AND d.type = 6
                              AND d.substatus = 24" .
                              (!empty($filters['client']) ? "
                              AND d.customer = '{$filters['client']}'" : '') . "
                              AND di.parent_id = d.id
                              AND di.lang = '{$lang}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                            ON (dc1.model_id = d.id
                              AND dc1.var_id = {$fields_contract['date_court_completion']}
                              AND dc1.num = 1
                              AND dc1.lang = ''
                              AND DATE(dc1.value) IS NOT NULL
                              AND DATE(dc1.value) BETWEEN '{$filters['period_from']}' AND '{$filters['period_to']}')
                          JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc5
                            ON (dc5.model_id = d.id
                              AND dc5.var_id = {$fields_contract['grant_credit_currency']}
                              AND dc5.num = 1
                              AND dc5.lang = '')
                          JOIN " . DB_TABLE_CUSTOMERS . " AS c
                            ON (c.id = d.customer)
                          JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = c.id
                              AND ci.lang = '{$lang}')
                          JOIN " . DB_TABLE_PROJECTS_CSTM . " AS pc1
                            ON (pc1.var_id = {$fields_cp['contract_num_id']}
                              AND pc1.lang = ''
                              AND pc1.num = 1
                              AND pc1.value = d.id)
                          JOIN " . DB_TABLE_PROJECTS . " AS p
                            ON (p.id = pc1.model_id
                              AND !p.deleted
                              AND p.active
                              AND p.type = 1
                              AND p.status IN ('planning', 'finished')" .
                              (!empty($filters['client']) ? "
                              AND p.customer = '{$filters['client']}'" : '') . ")";
                    $c5 = $db->GetAll($query);

                    // Process data for changes of type 5
                    foreach ($c5 as $c) {
                        if (!isset($results['change_types'][5]['contracts'])) {
                            $results['change_types'][5]['contracts'] = array();
                        }
                        if (!isset($results['change_types'][5]['contracts'][$c['id']])) {
                            $results['change_types'][5]['contracts'][$c['id']] = array(
                                'num'           => $c['num'],
                                'date'          => $c['date'],
                                'currency'      => $c['currency'],
                                'customer_id'   => $c['customer_id'],
                                'customer_name' => $c['customer_name'],
                                'customer_ucn'  => $c['customer_ucn'],
                                'changes'       => array()
                            );
                        }
                        $results['change_types'][5]['contracts'][$c['id']]['changes'][] = array(
                            'date'  => $c['change_date'],
                            'cp_id' => $c['cp_id']
                        );
                    }
                }
            }

            // Hide the export button if there are no results to export
            if (empty($results['change_types'])) {
                $payments['additional_options']['dont_show_export_button'] = true;
            }

            if (!empty($filters['paginate'])) {
                $results = array($results, 0);
            }

            return $results;
        }
    }
?>
