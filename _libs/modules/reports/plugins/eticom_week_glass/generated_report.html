<input type="hidden" name="save_url" value="1" />
<input type="hidden" name="skip_session_ids" value="1" />
<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_production_date#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_contract_number#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_address#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_total_area#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_first_glass#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_second_glass#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_breadth_glass#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_others#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}">
        <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});"
                   type="checkbox"
                   name='items[]'
                   value="{$result.document_id}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($result.document_id, $selected_items.ids) ||
                       (@$selected_items.select_all eq 1 && @!in_array($result.document_id, $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
            </td>
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.document_id}">{$result.production_date|date_format:#date_short#|default:"&nbsp;"}</a>
        </td>
        <td class="t_border">
          {$result.contract_number|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.customer|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.address|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.total_area|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.first_glass|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.second_glass|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.breadth_glass|default:"&nbsp;"}
        </td>
        <td>
          {$result.other|escape|default:"&nbsp;"}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="11">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="11"></td>
    </tr>
  </table>
</td>
</tr>
    <tr>
      <td class="pagemenu" bgcolor="#FFFFFF" colspan="8">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
        }
      </td>
    </tr>
</table>