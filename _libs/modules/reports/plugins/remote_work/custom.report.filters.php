<?php
    class Custom_Report_Filters extends Report_Filters {
        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from',
                'name'              => 'from',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'additional_filter' => 'to',
                'first_filter_label'=> $this->i18n('reports_from'),
                'label'             => $this->i18n('reports_period'),
                'help'              => $this->i18n('reports_period')
            );
            $filters['from'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to',
                'name'            => 'to',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to'),
                'help'            => $this->i18n('reports_to')
            );
            $filters['to'] = $filter;

            // DEFINE CUSTOMERS AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'custom_filter',
                'actual_type'       => 'autocompleter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'custom_buttons'    => 'search',
                'width'             => 222,
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'autocomplete'      => array('search'       => array('<name>'),
                                             'sort'         => array('<name>'),
                                             'type'         => 'customers',
                                             'clear'        => 1,
                                             'suggestions'  => '[<code>] <name> <lastname>',
                                             'buttons_hide' => 'search',
                                             'id_var'       => 'customers',
                                             'fill_options' => array('$customers => <id>',
                                                                     '$customers_autocomplete => [<code>] <name> <lastname>',
                                                               ),
                                             'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['customers'] = $filter;

            // DEFINE CLIENT GROUP FILTER
            if (NOMENCLATURE_CLIENTS_GROUPS) {
                $filter = array (
                    'custom_id'       => 'client_group',
                    'name'            => 'client_group',
                    'type'            => 'autocompleter',
                    'width'           => 222,
                    'label'           => $this->i18n('reports_client_group'),
                    'help'            => $this->i18n('reports_client_group'),
                    'autocomplete'    => array (
                        'search'       => array('<name>'),
                        'sort'         => array('<name>'),
                        'type'         => 'nomenclatures',
                        'clear'        => 1,
                        'suggestions'  => '<name>',
                        'buttons_hide' => 'search',
                        'id_var'       => 'client_group',
                        'fill_options' => array('$client_group => <id>',
                            '$client_group_autocomplete => <name>'),
                        'filters'      => array(
                            '<type>' => (string)NOMENCLATURE_CLIENTS_GROUPS
                        ),
                        'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                    )
                );
                $filters['client_group'] = $filter;
            }

            // CUSTOMERS TAGS
            $tags_list = Dropdown::getTags(array($registry, 'model' => 'Customers', 'active' => 1));
            $tags_options = array();
            foreach ($tags_list as $tg) {
                if (!is_array($tg)) {
                    continue;
                }
                $tags_options = array_merge($tags_options, $tg);
            }
            usort($tags_options, function ($a, $b) { return $a['label'] > $b['label'] ? 1 : -1; });

            $filter = array (
                'custom_id' => 'customer_tag',
                'name'      => 'customer_tag',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_customer_tag'),
                'help'      => $this->i18n('reports_customer_tag'),
                'options'   => $tags_options
            );
            $filters['customer_tag'] = $filter;

            // DEFINE BRANCH FILTER
            if (NOMENCLATURE_BRANCH) {
                $filter = array (
                    'custom_id'       => 'nom_branch',
                    'name'            => 'nom_branch',
                    'type'            => 'autocompleter',
                    'width'           => 222,
                    'label'           => $this->i18n('reports_nom_branch'),
                    'help'            => $this->i18n('reports_nom_branch'),
                    'autocomplete'    => array (
                        'search'       => array('<name>'),
                        'sort'         => array('<name>'),
                        'type'         => 'nomenclatures',
                        'clear'        => 1,
                        'suggestions'  => '<name>',
                        'buttons_hide' => 'search',
                        'id_var'       => 'nom_branch',
                        'fill_options' => array('$nom_branch => <id>',
                                                '$nom_branch_autocomplete => <name>'),
                        'filters'      => array(
                            '<type>' => (string)NOMENCLATURE_BRANCH
                        ),
                        'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                    )
                );
                $filters['nom_branch'] = $filter;
            }

            // DEFINE DEPARTMENTS AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'user_department',
                'name'              => 'user_department',
                'type'              => 'autocompleter',
                'width'             => 222,
                'hidden'            => false,
                'label'             => $this->i18n('reports_user_department'),
                'help'              => $this->i18n('reports_user_department'),
                'autocomplete'      => array(
                    'type'             => 'autocompleters',
                    'search'           => array('<name>'),
                    'url'              => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                    'plugin_search'    => 'searchDepartmentsAndUsers',
                    'suggestions'      => '<name>',
                    'fill_options'     => array(
                        '$user_department => <id>',
                        '$user_department_autocomplete => <name>',
                        '$user_department_oldvalue => <name>',
                        '$user_ids => <user_ids>'
                    ),
                    'plugin_params' => array(
                        'hide_portal_users' => '1'
                    ),
                    'buttons_hide'     => 'search',
                    'execute_after'    => 'loadSelectedUserDepartments'
                ),
                'value'                 => ''
            );
            $filters['user_department'] = $filter;

            //DEFINE EMPLOYEES FILTER
            // prepare all the users with positions like the ones in the positions filter
            $available_positions = array();

            $users_query = 'SELECT u.id as option_value, CONCAT(ui18n.firstname, \' \', ui18n.lastname) as label, u.active, "1" as display' . "\n" .
                           'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                           ' ON (u.id=ui18n.parent_id AND ui18n.lang="' . $registry['lang'] . '")' . "\n" .
                           'WHERE u.id > 0 AND u.hidden = 0 AND u.is_portal=0' . "\n" .
                           'ORDER BY u.active DESC, CONCAT(ui18n.firstname, \' \', ui18n.lastname) ASC' . "\n";
            $options_employees = $registry['db']->GetAll($users_query);

            $filter = array (
                'custom_id'         => 'employees',
                'name'              => 'employees',
                'type'              => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/employees_filter.html',
                'label'             => $this->i18n('reports_employees'),
                'help'              => $this->i18n('reports_employees'),
                'options'           => $options_employees
            );
            $filters['employees'] = $filter;

            // DEFINE IT PROJECT AUTOCOMPLETER
            $filter = array (
                'custom_id'       => 'it_project',
                'name'            => 'it_project',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_it_project'),
                'help'            => $this->i18n('reports_it_project'),
                'autocomplete'    => array (
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'projects',
                    'clear'        => 1,
                    'suggestions'  => '<name>',
                    'id_var'       => 'it_project',
                    'fill_options' => array('$it_project => <id>',
                                            '$it_project_autocomplete => <name>'),
                    'filters'      => array(
                        '<type>' => (string)PROJECT_TYPE_IT_PROJECT
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'projects', 'projects', 'ajax_select')
                )
            );
            $filters['it_project'] = $filter;

            //DEFINE SKIP PROJECT WORK FILTER
            $filter = array (
                'custom_id'         => 'exclude_project_work',
                'name'              => 'exclude_project_work',
                'type'              => 'checkbox_group',
                'label'             => $this->i18n('reports_exclude_project_work'),
                'help'              => $this->i18n('reports_exclude_project_work'),
                'options'           => array(
                    array(
                        'option_value'  => 1,
                        'label'         => ''
                    )
                )
            );
            $filters['exclude_project_work'] = $filter;

            if (!empty(TRANSPORT_TIME_ACTIVITY)) {
                //DEFINE INCLUDE TRANSPORT TIME FILTER
                $filter = array (
                    'custom_id'         => 'include_transport_time',
                    'name'              => 'include_transport_time',
                    'type'              => 'checkbox_group',
                    'label'             => $this->i18n('reports_include_transport_time'),
                    'help'              => $this->i18n('reports_include_transport_time'),
                    'options'           => array(
                        array(
                            'option_value'  => 1,
                            'label'         => ''
                        )
                    )
                );
                $filters['include_transport_time'] = $filter;
            }

            //DEFINE TIMESHEETED TIME FILTER
            $options_reported_time = array(
                array(
                    'option_value'  => 'working_time',
                    'label'         => $this->i18n('reports_show_reported_working_time'),
                ),
                array(
                    'option_value'  => 'overtime',
                    'label'         => $this->i18n('reports_show_reported_overtime'),
                ),
            );

            $filter = array (
                'custom_id'         => 'reported_time',
                'name'              => 'reported_time',
                'type'              => 'checkbox_group',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/employees_filter.html',
                'label'             => $this->i18n('reports_reported_time'),
                'help'              => $this->i18n('reports_reported_time'),
                'options'           => $options_reported_time
            );
            $filters['reported_time'] = $filter;

            // DEFINE TYPE WORK FILTER
            $fast_reports_types = array_filter(preg_split('/\s*,\s*/', TASK_TYPE_FAST_REPORT_ID));
            $sql = 'SELECT `parent_id` as option_value, `name` as label FROM ' . DB_TABLE_TASKS_TYPES_I18N . ' WHERE `parent_id` IN ("' . implode('","', $fast_reports_types) . '") AND `lang`="' . $registry['lang'] . '"';
            $fast_reports_options = $registry['db']->GetAll($sql);

            $options_time_work = array();
            $ticket_types = array_filter(preg_split('/\s*,\s*/', DOCUMENT_TYPE_TICKET_ID));
            $available_document_types = array(DOCUMENT_TYPE_REMOTE_WORK_ID, DOCUMENT_TYPE_VISIT_ID, DOCUMENT_TYPE_MEINTANANCE_PROTOCOL_ID);

            // clear the options which are not completed
            $available_document_types = array_filter(array_merge($available_document_types, $ticket_types));

            if (!empty($available_document_types)) {
                $sql = 'SELECT `parent_id` as option_value, `name` as label FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE `parent_id` IN ("' . implode('","', $available_document_types) . '") AND `lang`="' . $registry['lang'] . '"';
                $options_time_work = $registry['db']->GetAll($sql);
            }
            $options_time_work = array_merge($options_time_work, $fast_reports_options);

            $filter = array (
                'custom_id'         => 'time_work',
                'name'              => 'time_work',
                'type'              => 'checkbox_group',
                'first_option_label'=> $this->i18n('all'),
                'label'             => $this->i18n('reports_time_work'),
                'help'              => $this->i18n('reports_time_work'),
                'options'           => $options_time_work
            );
            $filters['time_work'] = $filter;

            // DEFINE DEPARTMENTS
            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
            $departments_tree = Departments::getTreeDescendants($registry, array('where' => array('d1.id = ' . SUBDEPARTMENTS_TREE_ROOTS)));

            $departments_options = array();
            foreach ($departments_tree as $dep_leave) {
                if ($dep_leave->get('id') == SUBDEPARTMENTS_TREE_ROOTS) {
                    continue;
                }
                $departments_options[] = array(
                    'option_value' => $dep_leave->get('id'),
                    'label'        => sprintf('%s %s', str_repeat('--', ($dep_leave->get('level')-1)), $dep_leave->get('name'))
                );
            }

            $filter = array (
                'custom_id'         => 'department',
                'name'              => 'department',
                'type'              => 'checkbox_group',
                'label'             => $this->i18n('reports_department'),
                'help'              => $this->i18n('reports_department'),
                'options'           => $departments_options
            );
            $filters['department'] = $filter;


            $filter = array (
                'name'          => 'show_table_main',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_main_table'),
                'help'          => $this->i18n('reports_main_table'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_table_main'] = $filter;

            $filter = array (
                'name'          => 'show_table_customer',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_customer_table'),
                'help'          => $this->i18n('reports_customer_table'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_table_customer'] = $filter;

            $filter = array (
                'name'          => 'show_graph_by_company',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_show_graph_by_company'),
                'help'          => $this->i18n('reports_show_graph_by_company'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_graph_by_company'] = $filter;

            $filter = array (
                'name'          => 'show_graph_by_employee',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_show_graph_by_employee'),
                'help'          => $this->i18n('reports_show_graph_by_employee'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_graph_by_employee'] = $filter;

            $filter = array (
                'name'          => 'show_graph_working_time_by_type',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_show_working_time_by_type'),
                'help'          => $this->i18n('reports_show_working_time_by_type'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_graph_working_time_by_type'] = $filter;

            $filter = array (
                'name'          => 'show_graph_worked_time',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_show_graph_worked_time'),
                'help'          => $this->i18n('reports_show_graph_worked_time'),
                'option_value'  => '1',
                'setting'       => true,
                'value'         => '1'
            );
            $filters['show_graph_worked_time'] = $filter;

            return $filters;
        }


        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            if (empty($filters['time_work']['value'])) {
                $filters['time_work']['value'] = array_column($filters['time_work']['options'], 'option_value');
            }

            if (!empty($filters['customer_tag']['value'])) {
                $tags_selected = $filters['customer_tag']['value'];
                usort($filters['customer_tag']['options'], function ($a, $b) use ($tags_selected) {
                    if (in_array($a['option_value'], $tags_selected) == in_array($b['option_value'], $tags_selected)) {
                        return ($a['label'] > $b['label'] ? 1 : -1);
                    } else {
                        return (in_array($a['option_value'], $tags_selected) ? -1 : 1);
                    }
                });
            }

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }

    }
?>
