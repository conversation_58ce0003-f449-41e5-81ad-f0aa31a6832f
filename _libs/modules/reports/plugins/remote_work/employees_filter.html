<tr>
  <td class="labelbox">
    <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help label_content=$filter_settings.label text_content=$filter_settings.help}</label>
  </td>
  <td>&nbsp;</td>
  <td nowrap="nowrap">
    <div class="m_header_menu">
      <div style="float: right;padding-bottom: 4px;" id="tab_toggler_{$filter_settings.custom_id}">
        <span onclick="toggleCheckboxes(this, '{$filter_settings.name}', true, 'tab_container_{$filter_settings.custom_id}')" class="pointer">{#check_all#|escape}</span> |
        <span onclick="toggleCheckboxes(this, '{$filter_settings.name}', false, 'tab_container_{$filter_settings.custom_id}')" class="pointer">{#check_none#|escape}</span>
      </div>
    </div>
    <div class="m_header_m_menu scroll_box_container">
      <div class="scroll_box" id="tab_container_{$filter_settings.custom_id}" style="height: 90px; box-sizing: border-box;">
        {foreach from=$filter_settings.options item='option'}
          <div id="{$filter_settings.custom_id}_option_{$option.option_value|escape}" style="display:{if $option.display}block{else}none{/if};" class="tab_container_{$filter_settings.custom_id}_options">
            <input type="checkbox" name="{$filter_settings.name}[]" id="{$filter_settings.custom_id|default:$filter_settings.name}_{$option.option_value|escape}" value="{$option.option_value|escape}" title="{$option.label|strip_tags:false|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if is_array($filter_settings.value) && in_array($option.option_value, $filter_settings.value) && $option.display} checked="checked"{/if}{if !$option.display} disabled="disabled"{/if} />
            <label for="{$filter_settings.custom_id|default:$filter_settings.name}_{$option.option_value|escape}"{if !$option.active} class="inactive_option"{/if}>
              {if !$option.active}*{/if}{$option.label|escape|default:'&nbsp;'}
            </label>
          </div>
        {/foreach}
      </div>
    </div>
  </td>
</tr>
