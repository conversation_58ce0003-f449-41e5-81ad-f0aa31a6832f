<table border="0" cellpadding="0" cellspacing="10" width="90%">
  {if !empty($reports_results)}
    <tr>
      <td>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
          <tr>
            <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer_trademark#|escape}</div></td>
            {foreach from=$reports_additional_options.months key=month_key name=mi item=month_info}
              <td class="t_caption{if !$smarty.foreach.mi.last} t_border{/if}" nowrap="nowrap"><div class="t_caption_title">{$month_info|escape}</div></td>
            {/foreach}
          </tr>
          {foreach from=$reports_results key=cus_tm_key name=ci item=customers_info}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" nowrap="nowrap" style="vertical-align: middle;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customers_info.customer}">{$customers_info.customer_name|escape}</a>{if $customers_info.trademark} / <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$customers_info.trademark}">{$customers_info.trademark_name|escape}</a>{/if}
              </td>
              {foreach from=$customers_info.months key=mon_key name=md item=month_days}
                <td{if !$smarty.foreach.md.last} class="t_border"{/if} nowrap="nowrap" style="vertical-align: top;">
                  {foreach from=$month_days item=days_info name=di key=day_key}
                    {foreach from=$days_info item=day_info name=dsi}
                      {capture assign='const_type_change'}{$day_info.type_change|mb_upper}_LABEL_{$lang|mb_upper}{/capture}
                      {capture assign='history_event'}reports_history_event_{$day_info.type_change}{/capture}
                      {capture assign='changing_document_lang_var'}reports_{$day_info.changing_document_type}{/capture}
                      <strong>{$day_key|date_format:#date_short#|escape|default:"&nbsp;"}</strong> -
                      {if $smarty.const.SHOW_ARTICLE_NAME && $day_info.article_name}
                        {$day_info.article_name|escape}
                      {elseif $smarty.const.$const_type_change}
                        {$smarty.const.$const_type_change|escape|default:"&nbsp;"}
                      {else}
                        {$smarty.config.$history_event|escape|default:"&nbsp;"}
                      {/if}
                      {if ($day_info.type_change eq 'rent' || $day_info.type_change eq 'service_tax')}
                        {$day_info.current_area|default:"&nbsp;"} 
                        [{if $day_info.previous_single_price || $day_info.previous_subtotal}{if $day_info.previous_single_price && $day_info.previous_subtotal}{$day_info.previous_single_price} / {$day_info.previous_subtotal}{elseif ($day_info.previous_single_price && !$day_info.previous_subtotal)}{$day_info.previous_single_price}{elseif (!$day_info.previous_single_price && $day_info.previous_subtotal)}{$day_info.previous_subtotal}{/if} {$day_info.currency|escape}{else}<i>{#reports_no_value#|escape}</i>{/if} -> {if $day_info.current_single_price || $day_info.current_subtotal}{if $day_info.current_single_price && $day_info.current_subtotal}{$day_info.current_single_price} / {$day_info.current_subtotal}{elseif ($day_info.current_single_price && !$day_info.current_subtotal)}{$day_info.current_single_price}{elseif (!$day_info.current_single_price && $day_info.current_subtotal)}{$day_info.current_subtotal}{/if} {$day_info.currency|escape}{else}<i>{#reports_no_value#|escape}</i>{/if}]
                      {elseif ($day_info.type_change eq 'change_period')}
                        [{$day_info.old_validity|date_format:#date_short#|escape|default:"&nbsp;"} -> {$day_info.new_validity|date_format:#date_short#|escape|default:"&nbsp;"}]
                      {elseif ($day_info.type_change eq 'payment_period')}
                        {capture assign='payment_period_description'}reports_payment_period_{$day_info.is_changed}{/capture}
                        {if $day_info.is_changed eq 'service_tax_step' || $day_info.is_changed eq 'rent_step'}
                          [{$smarty.config.$payment_period_description|escape|default:"&nbsp;"}]
                        {else}
                          {$smarty.config.$payment_period_description|escape|default:"&nbsp;"} {$day_info.current_area|default:"&nbsp;"} [{if $day_info.previous_period_type && $day_info.previous_period_count}{$day_info.previous_period_count} {$day_info.previous_period_type}{else}<i>{#reports_no_value#|escape}</i>{/if} -> {if $day_info.current_period_type && $day_info.current_period_count}{$day_info.current_period_count} {$day_info.current_period_type}{else}<i>{#reports_no_value#|escape}</i>{/if}]
                        {/if}
                      {/if}
                      - <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$day_info.changing_document_id}">{$smarty.config.$changing_document_lang_var|escape} {$day_info.changing_document_num|escape} / {$day_info.changing_document_date|date_format:#date_short#|escape|default:"&nbsp;"}</a>
                      {if !$smarty.foreach.dsi.last}<br />{/if}
                    {/foreach}
                    {if !$smarty.foreach.di.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
              {/foreach}
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="10"></td>
          </tr>
        </table>
      </td>
    </tr>
  {else}
    <h1><font color="#FF0000"><strong>{#reports_no_cnahges#|escape}</strong></font>
  {/if}
</table>
