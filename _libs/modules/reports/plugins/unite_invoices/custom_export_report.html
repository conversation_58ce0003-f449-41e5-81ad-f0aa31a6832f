<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td><strong>{#reports_th_customer#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_order_num#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_date#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_nomenclature#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_quantity#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_measure#|escape|default:"&nbsp;"}</strong></td>
        <td><strong>{#reports_th_total#|escape|default:"&nbsp;"}</strong></td>
      </tr>
      {foreach from=$reports_results key='model_id' item='result'}
        {foreach from=$result.rows key='gt2_row_id' item='gt2_row' name='gt2_rows'}
          <tr>
            {if $smarty.foreach.gt2_rows.first}
              <td style="vertical-align:middle; mso-number-format:'\@';" rowspan="{$result.rowspan}">
                {$result.customer_name|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align:middle; mso-number-format:'\@';" rowspan="{$result.rowspan}">
                {$result.full_num|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align:middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$result.rowspan}">
                {$result.date|date_format:#date_short#|escape}
              </td>
            {/if}
            <td style="mso-number-format:'\@';">
              {$gt2_row.article_name|escape|default:"&nbsp;"}
            </td>
            <td align="right" style="mso-number-format:'0\.00';">
              {$gt2_row.quantity|string_format:"%.2f"|escape|default:"0.00"}
            </td>
            <td style="mso-number-format:'\@';">
              {$gt2_row.measure|escape|default:"&nbsp;"}
            </td>
            {if $smarty.foreach.gt2_rows.first}
              <td style="vertical-align:middle; text-align: right; mso-number-format:'0\.00';" rowspan="{$result.rowspan}">
                {$result.total|string_format:"%.2f"|escape|default:"0.00"}
              </td>
            {/if}
          </tr>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="7">
            <span style="color: red;">{#no_items_found#|escape}</span>
          </td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
