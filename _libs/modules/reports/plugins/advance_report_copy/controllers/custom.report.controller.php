<?php


class Custom_Report_Controller extends Reports_Controller {
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'copy_report':
                $this->_copyReport();
                break;
            default:
                parent::execute();
        }
    }

    public function _copyReport() {
        /** @var Registry $registry */
        $registry = $this->registry;

        $reportTypeName = $this->getReportType()['name'];
        $lang = $registry['lang'];
        $this->registry['translater']->loadFile(PH_MODULES_DIR . "reports/plugins/{$reportTypeName}/i18n/{$lang}/reports.ini");

        require_once __DIR__."/../models/ReportCopyProcess.php";
        require_once __DIR__."/../models/ReportCopyModel.php";

        /** @var Request $request */
        $request = $this->registry['request'];

        $validationErrors = false;
        $sourceReportId = $request->getGet('source_report');
        if (empty($sourceReportId)) {
            $validationErrors = true;
            $registry['messages']->setError($this->i18n('reports_error_source_report_missing'), '', -1);
        }
        $targetReportId = $request->getGet('target_report');
        if (empty($targetReportId)) {
            $validationErrors = true;
            $registry['messages']->setError($this->i18n('reports_error_target_report_missing'), '', -1);
        }
        $copyImages = (bool) $request->getGet('copy_images', false);

        if ($validationErrors) {
            $this->end();
            return;
        }

        try {
            $getOldVars = $registry->get('get_old_vars');
            $registry->set('get_old_vars', true, true);

            $copyProcess = ReportCopyProcess::createFromReportIds($registry, $sourceReportId, $targetReportId);
            $copyProcess->doCopy($copyImages, false);

            $registry['messages']->setMessage($this->i18n('reports_success_copy'), '', -1);
        } catch(\Exception $e) {
            $registry['messages']->setError($this->i18n('reports_error_copy_failed'), '', -1);
        } finally {
            $registry->set('get_old_vars', $getOldVars, true);
            $this->end();
        }
    }

    private function end()
    {
        if (Auth::$is_rest) {
            $messages = $this->registry['messages']->getAll();
            if (!empty($messages['errors'])) {
                http_response_code(400);
            }
            echo json_encode($messages);
        } else {
            $this->registry['messages']->insertInSession($this->registry);
            $reportTypeName = $this->getReportType()['name'];
            $this->redirect($this->module, '', ['report_type' => $reportTypeName]);
        }
    }
}
