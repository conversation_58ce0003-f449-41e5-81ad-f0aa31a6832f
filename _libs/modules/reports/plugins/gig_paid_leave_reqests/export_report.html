<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if $reports_results}

      {assign var='table_colspan' value=10}
      {if is_array($reports_additional_options.other_years_unused_paid_days) && $reports_additional_options.other_years_unused_paid_days|@count}
        {assign var='colspan' value=$reports_additional_options.other_years_unused_paid_days|@count}
        {assign var='rowspan' value=2}
        {math equation="x + y" x=$table_colspan y=$colspan assign=table_colspan}
        {assign var=show_additional_title_column value=1}
      {else}
        {assign var='colspan' value=1}
        {assign var='rowspan' value=3}
        {math equation="x + 1" x=$table_colspan assign=table_colspan}
        {assign var=show_additional_title_column value=0}
      {/if}
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <th rowspan="3" style="vertical-align: middle; text-align: left;">{#reports_employee_name#|escape}</th>
          <th colspan="4" style="vertical-align: middle;">{#reports_available_days_off#|escape} {$reports_additional_options.current_year|escape} {#year_short#}</th>
          <th rowspan="{$rowspan}" colspan="{$colspan}" style="vertical-align: middle;">{#reports_available_days_off_from_previous_years#|escape} {#reports_available_days_off_from_previous_years_at_the_moment#|escape}</th>
          <th colspan="4" style="vertical-align: middle;">{#reports_for_the_current_year#} {$reports_additional_options.current_year|escape} {#year_short#}</th>
          <th rowspan="3" style="vertical-align: middle;">{#reports_days_off_left#|escape}</th>
        </tr>
        <tr>
          <th style="vertical-align: middle;" rowspan="2">{#reports_from_other_years#|escape}</th>
          <th style="vertical-align: middle;" colspan="2">{#from#|escape} {$reports_additional_options.current_year|escape} {#year_short#}</th>
          <th style="vertical-align: middle;" rowspan="2">{#total#}</th>
          <th style="vertical-align: middle;" colspan="2">{#reports_from_other_years#|escape}</th>
          <th style="vertical-align: middle;" colspan="2">{#from#|escape} {$reports_additional_options.current_year|escape} {#year_short#}</th>
        </tr>
        <tr class="reports_title_row hcenter">
          <th style="vertical-align: middle;">{#reports_available_days_off_current_year#}</th>
          <th style="vertical-align: middle;">{#reports_available_days_off_current_year_special#}</th>
          {foreach from=$reports_additional_options.other_years_unused_paid_days item=year_free_days}
            <th style="vertical-align: middle;">{$year_free_days} {#year_short#}</th>
          {/foreach}
          <th style="vertical-align: middle;">{#reports_used_days_off_from_other_years#}</th>
          <th style="vertical-align: middle;">{#reports_asked_days_off_from_other_years#}</th>
          <th style="vertical-align: middle;">{#reports_used_days_off#}</th>
          <th style="vertical-align: middle;">{#reports_asked_days_off#}</th>
        </tr>
        {foreach from=$reports_results item=customer}
          <tr>
            <td nowrap="nowrap" style="vertical-align: middle;">
              {$customer.name|escape}
            </td>
            <td>
              {$customer.free_days_from_other_years|escape|default:"0"}
            </td>
            <td>
              {$customer.days_for_the_current_year|escape|default:"0"}
            </td>
            <td>
              {$customer.special_days_for_the_current_year|escape|default:"0"}
            </td>
            <td>
              {$customer.free_days_for_the_year|escape|default:"0"}
            </td>
            {foreach from=$reports_additional_options.other_years_unused_paid_days item=year_free_days}
              <td>
                {$customer.free_days_from_other_years_info[$year_free_days]|escape|default:"0"}
              </td>
            {foreachelse}
              <td align="right">
                -
              </td>
            {/foreach}
            <td>
              {$customer.free_days_paid_used_from_other_years|escape|default:"0"}
            </td>
            <td>
              {$customer.free_days_paid_asked_from_other_years|escape|default:"0"}
            </td>
            <td>
              {$customer.free_days_paid_used|escape|default:"0"}
            </td>
            <td>
              {$customer.free_days_paid_asked|escape|default:"0"}
            </td>
            <td>
              {$customer.free_days_left|escape|default:"0"}
            </td>
          </tr>
        {/foreach}
      </table>
    {else}
      <h1 style="color: red;">{#reports_no_results#|escape}</h1>
    {/if}
  </body>
</html>