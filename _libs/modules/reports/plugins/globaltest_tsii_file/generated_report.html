<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 1200px;">
        <tr class="reports_title_row">
          <td colspan="3" style="text-align: center; vertical-align: middle;" class="t_border">{#reports_tsii_data#}</td>
          <td colspan="3" style="text-align: center; vertical-align: middle;" class="t_border">{#reports_tsii_measure_range#}</td>
          <td colspan="3" style="text-align: center; vertical-align: middle;" class="t_border">{#reports_tsii_producer#}</td>
          <td colspan="3" style="text-align: center; vertical-align: middle;">{#reports_tsii_parameters#}</td>
        </tr>
        <tr class="t_even1">
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr class="t_even1">
          <td width="135"><div style="width: 135px;">{#reports_code#}:</div></td>
          <td style="background-color: #F8F8F8;" width="180"><div style="width: 180px;">{$reports_results.tsii.code|escape|default:"&nbsp;"}</div></td>
          <td class="t_v_border t_border" width="1"><div style="width: 1px;">&nbsp;</div></td>
          <td width="70"><div style="width: 70px;">{#reports_tsii_tech_range#}:</div></td>
          <td style="background-color: #F8F8F8;" width="177"><div style="width: 177px;">{$reports_results.tsii.tech_range|escape|default:"&nbsp;"}</div></td>
          <td class="t_v_border t_border" width="1"><div style="width: 1px;">&nbsp;</div></td>
          <td width="105"><div style="width: 105px;">{#reports_tsii_tech_year#}:</div></td>
          <td style="background-color: #F8F8F8;" width="150"><div style="width: 150px;">{$reports_results.tsii.tech_year|escape|default:"&nbsp;"}</div></td>
          <td class="t_v_border t_border" width="1"><div style="width: 1px;">&nbsp;</div></td>
          <td width="140"><div style="width: 140px;">{#reports_tsii_tech_parameters#}:</div></td>
          <td style="background-color: #F8F8F8;" width="150"><div style="width: 150px;">{$reports_results.tsii.tech_parameters|escape|default:"&nbsp;"}</div></td>
          <td class="t_v_border" width="1"><div style="width: 1px;">&nbsp;</div></td>
        </tr>
        <tr class="t_even1">
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr class="t_even1">
          <td>{#reports_tsii_name#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.name|escape|default:"&nbsp;"}{if $reports_results.tsii.tech_kind}, {$reports_results.tsii.tech_kind|escape|default:"&nbsp;"}{/if}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_tech_model#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_model|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_tech_producer#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_producer|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_tech_interval#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_interval|escape|default:"&nbsp;"}</td>
          <td class="t_v_border">&nbsp;</td>
        </tr>
        <tr class="t_even1">
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr class="t_even1">
          <td>{#reports_tech_serial_num#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_serial_num|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_tech_date_add#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_date_add|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_next_date#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.next_date|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tsii_tech_borders#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_borders|escape|default:"&nbsp;"}</td>
          <td class="t_v_border">&nbsp;</td>
        </tr>
        <tr class="t_even1">
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr class="t_even1">
          <td>{#reports_tech_ident_num#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_ident_num|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td>{#reports_tech_place#}:</td>
          <td style="background-color: #F8F8F8;">{$reports_results.tsii.tech_place|escape|default:"&nbsp;"}</td>
          <td class="t_v_border t_border">&nbsp;</td>
          <td colspan="3" class="t_v_border t_border">&nbsp;</td>
          <td colspan="3" class="t_v_border">&nbsp;</td>
        </tr>
        <tr class="t_even1">
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border t_border"></td>
          <td colspan="3" class="t_v_border"></td>
        </tr>
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="77" ><div style="width:  77px;">{#reports_cert_date#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="82" ><div style="width:  82px;">{#reports_cert_custom_num#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="146"><div style="width: 146px;">{#reports_cert_action#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="259"><div style="width: 259px;">{#reports_cert_result#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="267"><div style="width: 267px;">{#reports_customer#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="146"><div style="width: 146px;">{#reports_cert_file#}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="145"><div style="width: 145px;">{#reports_cert_tech_interval#}</div></td>
        </tr>
        {foreach from=$reports_results.certificates item=certificate name=foreach_certificates}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border" style="text-align: center; max-width: 77px;">{$certificate.date|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: center; max-width: 82px;">{$certificate.custom_num|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 146px;">{$certificate.pk_action|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 259px;">{$certificate.pk_rezultat|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 267px;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$certificate.customer_id}" target="_blank">{$certificate.customer_name|escape|default:"&nbsp;"}</a></td>
            <td class="t_v_border t_border" style="text-align: center; max-width: 146px;">{if $certificate.pk_file_exists}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$certificate.pk_file_model}&amp;{$certificate.pk_file_model}=getfile&amp;getfile={$certificate.pk_file_model_id}&amp;file={$certificate.pk_file_id}" target="_blank">{/if}{if $certificate.pk_file_filename && $certificate.pk_file_extension}<img border="0" width="14" height="14" src="{$theme->imagesUrl}{$certificate.pk_file_extension}.png" alt="{$certificate.pk_file_filename}" title="{$certificate.pk_file_filename}" class="{if $certificate.pk_file_exists}pointer{else}dimmed{/if}" />{else}{$certificate.pk_file_filename|escape|default:"&nbsp;"}{/if}{if $certificate.pk_file_exists}</a>{/if}</td>
            <td class="t_v_border" style="text-align: center; max-width: 145px;">{$certificate.tech_interval|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="6">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="146"><div style="width: 146px;">{#reports_protocol_date#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="440"><div style="width: 440px;">{#reports_protocol_repair_description#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="267"><div style="width: 267px;">{#reports_customer#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="146"><div style="width: 146px;">{#reports_protocol_repair_rezultat#}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="145"><div style="width: 145px;">{#reports_protocol_file#}</div></td>
        </tr>
        {foreach from=$reports_results.protocols item=protocol name=foreach_protocols}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border" style="text-align: center; max-width: 146px;">{$protocol.date|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 440px; word-wrap: break-word;">{$protocol.repair_description|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 267px;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$protocol.customer_id}" target="_blank">{$protocol.customer_name|escape|default:"&nbsp;"}</a></td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 146px;">{$protocol.repair_rezultat|escape|default:"&nbsp;"}</td>
            <td class="t_v_border" style="text-align: center; max-width: 145px;">{if $protocol.repair_file_exists}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$protocol.repair_file_model}&amp;{$protocol.repair_file_model}=getfile&amp;getfile={$protocol.repair_file_model_id}&amp;file={$protocol.repair_file_id}" target="_blank">{/if}{if $protocol.repair_file_filename && $protocol.repair_file_extension}<img border="0" width="14" height="14" src="{$theme->imagesUrl}{$protocol.repair_file_extension}.png" alt="{$protocol.repair_file_filename}" title="{$protocol.repair_file_filename}" class="{if $protocol.repair_file_exists}pointer{else}dimmed{/if}" />{else}{$protocol.repair_file_filename|escape|default:"&nbsp;"}{/if}{if $protocol.repair_file_exists}</a>{/if}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="5">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="73"><div style="width: 73px;">{#reports_check_protocol_date#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="62"><div style="width: 62px;">{#reports_check_protocol_full_num#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="170"><div style="width: 170px;">{#reports_check_protocol_action#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="259"><div style="width: 259px;">{#reports_check_protocol_result#}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="424"><div style="width: 424px;">{#reports_customer#}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="145"><div style="width: 145px;">{#reports_check_protocol_file#}</div></td>
        </tr>
        {foreach from=$reports_results.check_protocols item=check_protocol name=foreach_check_protocols}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border" style="text-align: center; max-width: 73px;">{$check_protocol.date|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: center; max-width: 62px;">{$check_protocol.full_num|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 170px;">{$check_protocol.action|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 259px;">{$check_protocol.rezultat|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 424px;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$check_protocol.customer_id}" target="_blank">{$check_protocol.customer_name|escape|default:"&nbsp;"}</a></td>
            <td class="t_v_border" style="text-align: center; max-width: 145px;">{if $check_protocol.file_exists}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$check_protocol.file_model}&amp;{$check_protocol.file_model}=getfile&amp;getfile={$check_protocol.file_model_id}&amp;file={$check_protocol.file_id}" target="_blank">{/if}{if $check_protocol.file_filename && $check_protocol.file_extension}<img border="0" width="14" height="14" src="{$theme->imagesUrl}{$check_protocol.file_extension}.png" alt="{$check_protocol.file_filename}" title="{$check_protocol.file_filename}" class="{if $check_protocol.file_exists}pointer{else}dimmed{/if}" />{else}{$check_protocol.file_filename|escape|default:"&nbsp;"}{/if}{if $check_protocol.file_exists}</a>{/if}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="6">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
