<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
      <style type="text/css">
          br { mso-data-placement: same-cell; }
      </style>
    {/literal}
  </head>
  <body>
    {if is_array($reports_results) && $reports_results|@count > 0}
      <table border="1">
        <tr>
          <td style="vertical-align: middle; text-align: center; width: 300px;"><strong>{#reports_client#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 150px;"><strong>{#reports_contract#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 150px;"><strong>{#reports_filter_subcontractor#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_owed_sum#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_month_tax#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_late_periods#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_total_uniken#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_subcontractor_percent#|escape}</strong></td>
          <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_subcontractor_owed_sum#|escape}</strong></td>
        </tr>
        {foreach from=$reports_results item=customer name=cstm}
          {foreach from=$customer.contracts item=contract name=con}
            <tr>
              {if $smarty.foreach.con.first}
                <td style="mso-number-format:\@; vertical-align: middle;" rowspan="{$customer.rowspan}">
                  {$customer.name|escape|default:"&nbsp;"}
                </td>
              {/if}
              <td style="mso-number-format:\@">
                {$contract.full_num|escape|default:"&nbsp;"}
              </td>
              <td style="mso-number-format:\@">
                {foreach from=$contract.subcontractors item=subcontractor name=sub_con}
                  {$subcontractor|escape}{if !$smarty.foreach.sub_con.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              <td style="mso-number-format:'0\.00';">
                {$contract.owed_sum|escape|default:"&nbsp;"|string_format:"%.2f"}
              </td>
              <td style="mso-number-format:'0\.00';">
                {if $contract.calculate_month_delay}
                  {$contract.monthly_tax|escape|default:"&nbsp;"|string_format:"%.2f"}
                {else}
                  -
                {/if}
              </td>
              <td style="mso-number-format:'0\.0';">
                {if $contract.calculate_month_delay}
                  {$contract.period_delay|escape|default:"&nbsp;"|string_format:"%.1f"}
                {else}
                  -
                {/if}
              </td>
              <td style="mso-number-format:'0\.00';">
                {$contract.paid_sum|escape|default:"&nbsp;"|string_format:"%.2f"}
              </td>
              <td style="mso-number-format:'0\.00';">
                {$contract.percentage|escape|default:"&nbsp;"|string_format:"%.2f"}
              </td>
              <td style="mso-number-format:'0\.00';">
                {$contract.owed_sum_by_contractor|escape|default:"&nbsp;"|string_format:"%.2f"}
              </td>
            </tr>
          {/foreach}
        {/foreach}
        <tr>
          <td style="background-color: #98bcff; text-align: right;" colspan="3">
            <strong>{#reports_total#|escape}</strong>
          </td>
          <td style="background-color: #98bcff; mso-number-format:'0\.00';">
            <strong>{$reports_additional_options.total_owed|escape|default:"0.00"|string_format:"%.2f"}</strong>
          </td>
          <td style="background-color: #98bcff; text-align: right;" colspan="2">
            <strong>{#reports_total#|escape}</strong>
          </td>
          <td style="background-color: #98bcff; mso-number-format:'0\.00';">
            <strong>{$reports_additional_options.total_paid|escape|default:"0.00"|string_format:"%.2f"}</strong>
          </td>
          <td style="background-color: #98bcff; text-align: right;">
            <strong>{#reports_total#|escape}</strong>
          </td>
          <td style="background-color: #98bcff; mso-number-format:'0\.00';">
            <strong>{$reports_additional_options.total_owed_to_contractor|escape|default:"0.00"|string_format:"%.2f"}</strong>
          </td>
        </tr>
      </table>
    {else}
      <h1 style="padding: 5px; color: red;">{#error_reports_no_results_to_show#|escape}</h1>
    {/if}
  </body>
</html>
