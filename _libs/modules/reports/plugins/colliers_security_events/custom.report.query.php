<?php
    class Colliers_Security_Events extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('DOCUMENT_DAILY_SECURITY_REPORTS_ID', 52);
            define('SECURITY_DATE_REPORT', 'date_report');
            define('SECURITY_ZONE', 'security_zone');
            define('SECURITY_EVENTS', 'security_events');
            define('SECURITY_DESCRIPTION', 'security_description');
            define('SECURITY_COMMENTS', 'security_comments');

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $records = array();
            $additional_records = array();

            if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
                $add_vars = array(SECURITY_DATE_REPORT, SECURITY_ZONE, SECURITY_EVENTS, SECURITY_DESCRIPTION, SECURITY_COMMENTS);
                //get additional variables from `_fields_meta`
                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                                    '  WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_DAILY_SECURITY_REPORTS_ID . ' AND fm.name IN ("' . implode('","', $add_vars) . '")';
                $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                $security_date_report_id = '';
                $security_zone_id = '';
                $security_events_id = '';
                $security_description_id = '';
                $security_comments_id = '';

                //assign the ids to vars
                foreach ($var_ids as $vars) {
                    if ($vars['name'] == SECURITY_DATE_REPORT) {
                        $security_date_report_id = $vars['id'];
                    } else if ($vars['name'] == SECURITY_ZONE) {
                        $security_zone_id = $vars['id'];
                    } else if ($vars['name'] == SECURITY_EVENTS) {
                        $security_events_id = $vars['id'];
                    } else if ($vars['name'] == SECURITY_DESCRIPTION) {
                        $security_description_id = $vars['id'];
                    } else if ($vars['name'] == SECURITY_COMMENTS) {
                        $security_comments_id = $vars['id'];
                    }
                }

                $sql = array();
                $sql['select'] = 'SELECT d.id AS id, d.full_num AS full_num, ' . "\n" .
                                 '  DATE_FORMAT(d_cstm_date_report.value, "%Y-%m-%d") as date_report, ' . "\n" .
                                 '  d_cstm_security_zone.value as security_zone, nomi18n_zone.name as zone_name, ' . "\n" .
                                 '  d_cstm_security_event.value as security_event, nomi18n_event.name as security_event_name, ' . "\n" .
                                 '  d_cstm_security_description.value as description, d_cstm_security_comment.value as comment' . "\n";

                //from clause
                $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_report' . "\n" .
                                 '  ON (d.id=d_cstm_date_report.model_id AND d_cstm_date_report.var_id="' . $security_date_report_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_security_zone' . "\n" .
                                 '  ON (d.id=d_cstm_security_zone.model_id AND d_cstm_security_zone.var_id="' . $security_zone_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as nomi18n_zone' . "\n" .
                                 '  ON (nomi18n_zone.parent_id=d_cstm_security_zone.value AND nomi18n_zone.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_security_event' . "\n" .
                                 '  ON (d.id=d_cstm_security_event.model_id AND d_cstm_security_event.var_id="' . $security_events_id . '" AND d_cstm_security_event.num=d_cstm_security_zone.num)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as nomi18n_event' . "\n" .
                                 '  ON (nomi18n_event.parent_id=d_cstm_security_event.value AND nomi18n_event.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_security_description' . "\n" .
                                 '  ON (d.id=d_cstm_security_description.model_id AND d_cstm_security_description.var_id="' . $security_description_id . '" AND d_cstm_security_description.num=d_cstm_security_zone.num)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_security_comment' . "\n" .
                                 '  ON (d.id=d_cstm_security_comment.model_id AND d_cstm_security_comment.var_id="' . $security_comments_id . '" AND d_cstm_security_comment.num=d_cstm_security_zone.num)' . "\n";

                // construct where
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_DAILY_SECURITY_REPORTS_ID . '"';
                $where[] = 'd.active!=0';
                $where[] = 'd.status!="opened"';
                $where[] = 'DATE_FORMAT(d_cstm_date_report.value, "%Y-%m-%d") >= "' . $filters['date_from'] . '"';
                $where[] = 'DATE_FORMAT(d_cstm_date_report.value, "%Y-%m-%d") <= "' . $filters['date_to'] . '"';

                $sql['where'] = 'WHERE ' . implode(' AND ', $where);

                $sql['order'] = 'ORDER BY d_cstm_date_report.value ASC';

                $query_docs = implode("\n", $sql);
                $records = $registry['db']->GetAll($query_docs);

                foreach ($records as $rec) {
                    if (!isset($additional_records[$rec['security_event']])) {
                        $additional_records[$rec['security_event']] = array(
                            'name'  => $rec['security_event_name'],
                            'count' => 0
                        );
                    }
                    $additional_records[$rec['security_event']]['count']++;
                }

                usort($additional_records, array('self', 'sortingResultsByFrequency'));
                $additional_records = array_slice($additional_records, 0, 10);
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }
            $records['additional_options'] = $additional_records;

            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                $results = $records;
            }

            return $results;
        }

        public static function sortingResultsByFrequency($a, $b) {
            return ($a['count'] < $b['count']) ? 1 : -1;
        }
    }
?>
