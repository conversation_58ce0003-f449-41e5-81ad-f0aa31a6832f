<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('DOCUMENT_TYPE_EXPENSE_ID', 8);
            define('GALIA', 4);
            define('MILENA', 3);
            define('BOBBY', 2);
            define('PEPPI', 333);
            define('HRISTOMIRA', 332);
            define('GALIA_OPTION', 'slgalia');
            define('MILENA_OPTION', 'slmilena');
            define('BOBBY_OPTION', 'slbobi');
            define('PEPPI_OPTION', 'slpepi');
            define('HRISTOMIRA_OPTION', 'slhrisi');
            define('TYPE_EXPENSE', 'type_expense');
            define('TYPE_PAYING', 'type_paying');
            define('PAYING_CURRENCY', 'paying_currency');
            define('PAYING_VALUE', 'paying_value');
            define('TAKEN_MONEY', 'take_money');

            // $filters - array containing description of all filters
            $filters = array();

            $employees_ids = array(GALIA, MILENA, BOBBY, PEPPI, HRISTOMIRA);

            //DEFINE CASH BOX
            //get employees' options
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_employees = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort'          => array('ci18n.name', 'ci18n.lastname'),
                                       'where'         => array('c.type = ' . PH_CUSTOMER_EMPLOYEE,
                                                                'c.active = 1',
                                                                'c.id IN (' . implode(', ', $employees_ids) . ')'));
            $employees = Customers::search($registry, $filters_employees);

            $options_cash_box = array();

            foreach ($employees as $employee) {
                if ($employee->get('id') == GALIA) {
                    $prefix = GALIA_OPTION;
                } else if ($employee->get('id') == MILENA) {
                    $prefix = MILENA_OPTION;
                } else if ($employee->get('id') == BOBBY) {
                    $prefix = BOBBY_OPTION;
                } else if ($employee->get('id') == PEPPI) {
                    $prefix = PEPPI_OPTION;
                } else if ($employee->get('id') == HRISTOMIRA) {
                    $prefix = HRISTOMIRA_OPTION;
                }
                $option_value = $prefix . '_' . $employee->get('id');

                $options_cash_box[] = array(
                    'label'         => $employee->get('name') . ' ' . $employee->get('lastname'),
                    'option_value'  => $option_value
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'cash_box',
                'name'      => 'cash_box',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_cash_box'),
                'help'      => $this->i18n('reports_cash_box_help'),
                'options'   => $options_cash_box,
            );
            $filters['cash_box'] = $filter;

            return $filters;
        }
    }
?>