<?php

Class Unite_In_Expense_Invoice Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        $final_results = array();

        if (!defined('RELATION_GT2_FIELD') || !constant('RELATION_GT2_FIELD')) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_settings_not_completed'));
        } else {
            if (!empty($filters['customer']) && !empty($filters['currency'])) {
                $expenses_reasons_types_included = array_filter(preg_split('/\s*\,\s*/', EXPENSES_REASONS_TYPES_INCLUDED));

                $sql = array();
                $sql['select'] = 'SELECT fir.id as idx, fir.id, fir.num, fdti18n.name as type_name, fir.customer, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer_name, ' . "\n" .
                                 '  fir.total, fir.total_with_vat, fir.company, fci18n.name as company_name, fir.issue_date, fir.date_of_payment, CAST(fir.company AS UNSIGNED) as unite_key' . "\n";

                //from clause
                $sql['from']  =  'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fir' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                                 '  ON (fdti18n.parent_id=fir.type AND fdti18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (ci18n.parent_id=fir.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                                 '  ON (fci18n.parent_id=fir.company AND fci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                 '  ON (frr.link_to=fir.id AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.parent_model_name="Finance_Expenses_Reason")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fir1' . "\n" .
                                 '  ON (fir1.id=frr.parent_id AND fir1.annulled_by=0 AND fir1.active=1 AND fir1.type IN ("' . implode('","', array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE)) . '"))' . "\n";

                $where = array();
                $where[] = 'fir.active=1';
                $where[] = 'fir.annulled_by=0';
                $where[] = 'fir.status="finished"';
                $where[] = 'fir.payment_status="unpaid"';
                $where[] = 'fir.customer="' . $filters['customer'] . '"';
                $where[] = 'fir.currency="' . $filters['currency'] . '"';
                if (!empty($expenses_reasons_types_included)) {
                    $where[] = 'fir.type IN ("' . implode('","', $expenses_reasons_types_included) . '")';
                } else {
                    $where[] = 'fir.type > 100';
                }
                $where[] = 'fir1.id IS NULL';
                if ($filters['company']) {
                    $where[] = 'fir.company="' . $filters['company'] . '"';
                }
                if (!empty($filters['document_type'])) {
                    $where[] = 'fir.type="' . $filters['document_type'] . '"';
                }
                if (!empty($filters['period_from'])) {
                    $where[] = 'fir.issue_date>="' . $filters['period_from'] . '"';
                }
                if (!empty($filters['period_to'])) {
                    $where[] = 'fir.issue_date<="' . $filters['period_to'] . '"';
                }
                $sql['where'] = 'WHERE ' . implode(' AND ', $where) . "\n";
                $sql['group'] = 'GROUP BY fir.id';

                // Build the query for the short results
                $query = implode("\n", $sql);

                $final_results = $registry['db']->GetAssoc($query);

            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_fields'));
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}

?>
