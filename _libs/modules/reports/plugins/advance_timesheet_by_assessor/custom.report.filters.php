<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE PERIOD TYPE FILTER
            $type_period_search_options = array(
                array(
                    'label'         => '[' . $this->i18n('please_select') . ']',
                    'option_value'  => '',
                    'onclick'       => "switchVisibleRows('month,year,from_date,to_date', '')",
                    'class_name'    => 'undefined'
                ),
                array(
                    'label'         => $this->i18n('type_period_search_by_dates'),
                    'option_value'  => 'by_dates',
                    'onclick'       => "switchVisibleRows('month,year','from_date,to_date')"
                ),
                array(
                    'label'         => $this->i18n('type_period_search_by_fixed_period'),
                    'option_value'  => 'by_fixed_period',
                    'onclick'       => "switchVisibleRows('from_date,to_date','month,year')"
                )
            );

            $filter = array (
                'custom_id'          => 'type_period_search',
                'name'               => 'type_period_search',
                'type'               => 'custom_filter',
                'custom_template'    => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/type_period_filter.html',
                'options'            => $type_period_search_options,
                'skip_please_select' => true,
                'label'              => $this->i18n('reports_type_period_search'),
                'help'               => $this->i18n('reports_type_period_search')
            );
            $filters['type_period_search'] = $filter;

            //DEFINE DATE FROM FILTER
            // get tags names and ids
            $month_options = array();
            $year_options = array();

            $available_month_tags = explode(',', MONTH_TAGS);
            $available_year_tags = explode(',', YEAR_TAGS);
            $available_tags = array_merge($available_month_tags, $available_year_tags);

            $sql_tags = 'SELECT t.id, ti18n.name FROM ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                '  ON (t.id=ti18n.parent_id AND ti18n.lang="' . $registry['lang'] . '")' . "\n" .
                'WHERE t.id IN (' . implode(',', $available_tags) . ') AND t.model="documents"' . "\n" .
                'ORDER BY t.place ASC';
            $tags_info = $registry['db']->GetAll($sql_tags);

            foreach ($tags_info as $tag_inf) {
                if (in_array($tag_inf['id'], $available_month_tags)) {
                    $month_options[] = array(
                        'label'        => $tag_inf['name'],
                        'option_value' => $tag_inf['id']
                    );
                } else {
                    $year_options[] = array(
                        'label'        => $tag_inf['name'],
                        'option_value' => $tag_inf['id']
                    );
                }
            }

            $filter = array (
                'custom_id'         => 'month',
                'name'              => 'month',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/month_year_filter.html',
                'type'              => 'custom_filter',
                'required'          => 0,
                'options'           => $month_options,
                'width'             => 96,
                'additional_filter' => 'year',
                'hidden'            => true,
                'first_filter_label'=> $this->i18n('reports_month'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['month'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'year',
                'name'            => 'year',
                'type'            => 'dropdown',
                'options'         => $year_options,
                'width'           => 96,
                'hidden'            => true,
                'label'           => $this->i18n('reports_year'),
                'help'            => $this->i18n('reports_year')
            );
            $filters['year'] = $filter;

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'required'          => 0,
                'width'             => 65,
                'additional_filter' => 'to_date',
                'hidden'            => true,
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'hidden'          => true,
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE EMPLOYEE AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'employee',
                'name'              => 'employee',
                'actual_type'       => 'autocompleter',
                'type'              => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'width'             => 222,
                'label'             => $this->i18n('reports_employee'),
                'help'              => $this->i18n('reports_employee'),
                'autocomplete'      => array('search'       => array('<name>'),
                                             'sort'         => array('<name>'),
                                             'type'         => 'customers',
                                             'clear'        => 1,
                                             'suggestions'  => '[<code>] <name> <lastname>',
                                             'buttons_hide' => 'search',
                                             'id_var'       => 'employee',
                                             'fill_options' => array('$employee => <id>',
                                                                     '$employee_autocomplete => [<code>] <name> <lastname>',
                                                               ),
                                              'filters' => array(
                                                  '<type>'                     => strval(PH_CUSTOMER_EMPLOYEE),
                                                  '<a__' . EMPLOYEE_TYPE . '>' => '!=' . strval(EMPLOYEE_TYPE_OTHER),
                                                  '<active>'                   => strval('IN(0, 1)')
                                              ),
                                             'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['employee'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            if ($filters['type_period_search']['value'] == 'by_dates') {
                $filters['from_date']['hidden'] = false;
                $filters['to_date']['hidden'] = false;
            } elseif ($filters['type_period_search']['value'] == 'by_fixed_period') {
                $filters['year']['hidden'] = false;
                $filters['month']['hidden'] = false;
            }

            // check certain filters and return empty value if they are hidden
            $filters_to_check = array('year', 'month', 'from_date', 'to_date');
            foreach ($filters_to_check as $flt_chk) {
                if (isset($filters[$flt_chk]) && !empty($filters[$flt_chk]['hidden'])) {
                    $filters[$flt_chk]['value'] = '';
                }
            }

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            // check if the filter for employee has to be readonly
            $users_change_assessor = preg_split('/\s*\,\s*/', USERS_TO_CHANGE_ASSESSOR);
            foreach ($users_change_assessor as $usr_key => $uca) {
                if (!trim($uca)) {
                    unset($users_change_assessor[$usr_key]);
                }
            }

            if (!in_array($registry['currentUser']->get('id'), $users_change_assessor)) {
                // make the field readonly
                $filters['employee']['readonly'] = true;
                $filters['employee']['width'] = 200;

                // assign the employee of the current user to the filter
                $filters['employee']['value'] = '';
                $filters['employee']['value_autocomplete'] = '';

                // get employee data
                if ($registry['currentUser']->get('employee')) {
                    // sql to take the user data
                    $sql_customer = 'SELECT c.id, c.code, CONCAT(ci18n.name, \' \', ci18n.lastname) as name' . "\n" .
                                    'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                    '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                    'WHERE c.id="' . $registry['currentUser']->get('employee') . '"' . "\n";
                    $employee_data = $registry['db']->GetRow($sql_customer);

                    if (!empty($employee_data)) {
                        $filters['employee']['value'] = array(0 => $employee_data['id']);
                        $filters['employee']['value_autocomplete'] = array(0 => sprintf('[%s] %s', $employee_data['code'], $employee_data['name']));
                    }
                }
            }

            return $filters;
        }
    }
?>