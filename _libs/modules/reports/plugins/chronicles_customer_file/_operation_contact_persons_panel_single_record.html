{if $operation_contact_person.valid}
  <table border="0" id="operation_contact_person_panel_{$operation_contact_person.id}" cellpadding="3" cellspacing="2" width="100%" style="margin-top: 5px;">
    <tr>
      <td style="text-align: center; cursor: pointer; width: 14px;"><img src="{$theme->imagesUrl}edit.png" width="14" height="14" border="0" alt="{#edit#|escape}" title="{#edit#|escape}" onclick="activateCorrectionLightbox('operation_contact_person_panel', '{$operation_contact_person.lang}', '{$operation_contact_person.id}', 'edit');" /></td>
      <td class="cell_active_records{if $operation_contact_person.approaching_validity_end} validity_approach_mark{/if}" style="width: 299px;">
        <strong>{$operation_contact_person.name|escape|default:"&nbsp;"}</strong>
      </td>
      <td class="cell_active_records" style="width: 225px; vertical-align: middle;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/phone.png" width="14" height="14" border="0" alt="{#reports_phone#|escape}" title="{#reports_phone#|escape}" /></div> {$operation_contact_person.phone|escape|default:"&nbsp;"}</td>
      <td class="cell_active_records" style="width: 225px; vertical-align: middle;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/email.png" width="14" height="14" border="0" alt="{#reports_email#|escape}" title="{#reports_email#|escape}" /></div> {$operation_contact_person.email|escape|default:"&nbsp;"}</td>
      <td class="cell_active_records" style="width: 225px; padding: 0; vertical-align: top;" rowspan="2">
        <div class="lang_container">
          <div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/comment.png" width="16" height="16" border="0" alt="{#comment#|escape}" title="{#comment#|escape}" /></div> {$operation_contact_person.comment|escape|nl2br|default:"&nbsp;"}
          <div class="lang_box_active" id="lang_box_op_cp{$operation_contact_person.id}">
            {foreach from=$available_installation_langs item=curr_lang}
              {capture assign="lang_i18n_var"}lang_{$curr_lang}{/capture}
              <img src="{$theme->imagesUrl}flags/{$curr_lang}.png" id="operation_contact_person_panel_{$operation_contact_person.id}_{$curr_lang}" alt="{$smarty.config.$lang_i18n_var|escape}" title="{$smarty.config.$lang_i18n_var|escape}" border="0"{if $operation_contact_person.lang ne $curr_lang} style="cursor: pointer;" class="dimmed" onclick="checkTranslation('operation_contact_person_panel', '{$curr_lang}', '{$operation_contact_person.id}', true);"{else} class="selected"{/if} />
            {/foreach}
          </div>
        </div>
      </td>
    </tr>
    <tr>
      <td class="cell_active_records" style="width: 321px;" colspan="2">{$operation_contact_person.position|escape|default:"&nbsp;"}</td>
      <td class="cell_active_records" style="width: 225px; vertical-align: middle;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/gsm.png" width="14" height="14" border="0" alt="{#reports_gsm#|escape}" title="{#reports_gsm#|escape}" /></div> {$operation_contact_person.mobile|escape|default:"&nbsp;"}</td>
      <td class="cell_active_records" style="width: 225px; vertical-align: middle;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/birthday.png" width="14" height="14" border="0" alt="{#reports_birthday#|escape}" title="{#reports_birthday#|escape}" /></div> {$operation_contact_person.bithday|date_format:#date_short#|escape|default:"&nbsp;"}</td>
    </tr>
  </table>
{else}
  <table border="0" id="operation_contact_person_panel_history_{$operation_contact_person.id}" cellpadding="3" cellspacing="0" width="100%" style="margin-top: 5px;">
    <tr>
      <td class="cell_history_records" style="text-align: center; cursor: pointer; width: 65px; vertical-align: middle; color: #990000; text-align: center;" rowspan="2">
        <strong>{$operation_contact_person.validity_start|date_format:#date_short#|escape|default:"&nbsp;"}<br />
        {$operation_contact_person.validity_end|date_format:#date_short#|escape|default:"&nbsp;"}</strong>
      </td>
      <td class="cell_history_records" style="width: 248px;">
        <strong>{$operation_contact_person.name|escape|default:"&nbsp;"}</strong>
      </td>
      <td class="cell_history_records" style="width: 225px;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/phone.png" width="14" height="14" border="0" alt="{#reports_phone#|escape}" title="{#reports_phone#|escape}" /></div> {$operation_contact_person.phone|escape|default:"&nbsp;"}</td>
      <td class="cell_history_records" style="width: 225px;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/email.png" width="14" height="14" border="0" alt="{#reports_email#|escape}" title="{#reports_email#|escape}" /></div> {$operation_contact_person.email|escape|default:"&nbsp;"}</td>
      <td class="cell_history_records" style="width: 225px; padding:0;" rowspan="2">
        <div class="lang_container">
          <div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/comment.png" width="16" height="16" border="0" alt="{#comment#|escape}" title="{#comment#|escape}" /></div> {$operation_contact_person.comment|escape|nl2br|default:"&nbsp;"}
          <div class="lang_box_history" id="lang_box_op_cp{$operation_contact_person.id}">
            {foreach from=$available_installation_langs item=curr_lang}
              {capture assign="lang_i18n_var"}lang_{$curr_lang}{/capture}
              <img src="{$theme->imagesUrl}flags/{$curr_lang}.png" id="operation_contact_person_panel_{$operation_contact_person.id}_{$curr_lang}" alt="{$smarty.config.$lang_i18n_var|escape}" title="{$smarty.config.$lang_i18n_var|escape}" border="0"{if $operation_contact_person.lang ne $curr_lang} style="cursor: pointer;" class="dimmed" onclick="checkTranslation('operation_contact_person_panel', '{$curr_lang}', '{$operation_contact_person.id}', false);"{else} class="selected"{/if} />
            {/foreach}
          </div>
        </div>
      </td>
    </tr>
    <tr>
      <td style="background-color: #FFFFFF; border: 1px #E4E4E4 solid; width: 248px;">{$operation_contact_person.position|escape|default:"&nbsp;"}</td>
      <td style="background-color: #FFFFFF; width: 225px; border: 1px #E4E4E4 solid;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/gsm.png" width="14" height="14" border="0" alt="{#reports_gsm#|escape}" title="{#reports_gsm#|escape}" /></div> {$operation_contact_person.mobile|escape|default:"&nbsp;"}</td>
      <td style="background-color: #FFFFFF; width: 225px; border: 1px #E4E4E4 solid;"><div class="chronicles_icon"><img src="{$templatesUrlPlugin}{$report_type}/icons/birthday.png" width="14" height="14" border="0" alt="{#reports_birthday#|escape}" title="{#reports_birthday#|escape}" /></div> {$operation_contact_person.bithday|date_format:#date_short#|escape|default:"&nbsp;"}</td>
    </tr>
  </table>
{/if}
