<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border"><div style="width: 110px;">{#reports_nom_code#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border"><div style="width: 410px;">{#reports_testing_method#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border"><div style="width: 110px;">{#reports_next_date#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border"><div style="width: 110px;">{#reports_calibrating_period#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;"><div style="width: 110px;">{#reports_add_document#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=res key=res_key}
          {cycle values='t_odd,t_even' assign='current_row_class'}
          {capture assign='current_row_class'}{if $res.color}row_{$res.color}{else}{$current_row_class}{/if}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_border" width="114">{$res.code|escape|default:"&nbsp;"}</td>
            <td class="t_border" width="414"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$res.id}" target="_blank">{$res.name|escape|default:"&nbsp;"}</a></td>
            <td class="t_border" width="114">{$res.next_date|date_format:#date_short#|default:"&nbsp;"}</td>
            <td class="t_border" width="114">{$res.calibratin_interval_label|escape|default:"&nbsp;"}</td>
            <td class="hcenter" width="114"><img src="{$theme->imagesUrl}small/verification_protocol.png" border="0" alt="{#reports_add_verification_protocol#|escape}" title="{#reports_add_verification_protocol#|escape}" style="cursor: pointer;" onclick="addGlobaltestProtocol('{$res.id}', '{$smarty.const.VERIFYING_PROTOCOL_ID}');" /> <img src="{$theme->imagesUrl}small/validation_protocol.png" border="0" alt="{#reports_add_validation_protocol#|escape}" title="{#reports_add_validation_protocol#|escape}" style="cursor: pointer;"{if $res.existing_validation_protocol} class="dimmed" onclick="alert('{#reports_validation_protocol_already_added#|escape}');"{else} onclick="addGlobaltestProtocol('{$res.id}', '{$smarty.const.VALIDATING_PROTOCOL_ID}');"{/if} /></td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="5">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
