<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {

            $product_types = (defined('NOMENCLATURE_TYPE_PRODUCTS') ? NOMENCLATURE_TYPE_PRODUCTS : '');
            $material_types = (defined('NOMENCLATURE_TYPE_MATERIALS') ? NOMENCLATURE_TYPE_MATERIALS : '');

            // If required settings are not specified
            if (empty($product_types) || empty($material_types)) {
                // Raise an error
                $registry['messages']->setError($registry['translater']->translate('error_report_not_set'));
                // Hide the generate button
                $registry->set('hide_generate_button', true, true);
            }

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE ARTICLE FILTER
            $filter = array (
                'custom_id'             => 'article',
                'name'                  => 'article',
                'type'                  => 'custom_filter',
                'custom_template'       => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/custom_filters.html',
                'additional_filter'     => 'number',
                'width'                 => 422,
                'label'                 => $this->i18n('reports_article'),
                'help'                  => $this->i18n('reports_article'),
                'autocomplete'      => array(
                    'type'          => 'nomenclatures',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'clear'         => 1,
                    'buttons_hide'  => 'search',
                    'suggestions'   => '[<code>] <name>',
                    'fill_options'  => array(
                        '$article_autocomplete => [<code>] <name>',
                        '$article_oldvalue => [<code>] <name>',
                        '$article => <id>'
                    ),
                    'filters' => array(
                        '<subtype>' => 'commodity',
                        '<type>'    => NOMENCLATURE_TYPE_PRODUCTS
                    )
                )
            );
            // if filtering by categories is specified
            if (defined('NOMENCLATURE_CATEGORIES') && NOMENCLATURE_CATEGORIES) {
                $filter['autocomplete']['filters']['<category>'] = NOMENCLATURE_CATEGORIES;
            }
            $filters['article'] = $filter;

            // DEFINE NUMBER ADDITIONAL FILTER
            $filter = array(
                'custom_id'             => 'number',
                'name'                  => 'number',
                'type'                  => 'text',
                'width'                 => 80,
                'label'                 => $this->i18n('reports_number'),
                'help'                  => $this->i18n('reports_number'),
                'restrict'              => 'insertOnlyDigits',
                'custom_class'          => 'hright'
            );

            $filters['number'] = $filter;

            $filter = array (
                'custom_id' => 'include_production_requests',
                'name'      => 'include_production_requests',
                'type'      => 'checkbox_group',
                'options'   => array(array('option_value' => 1, 'label' => ' ')),
                'label'     => $this->i18n('reports_include_production_requests'),
                'help'      => $this->i18n('reports_include_production_requests')
            );

            $filters['include_production_requests'] = $filter;

            return $filters;
        }

        /**
         * Process some filters that depend on the request or on each other
         *
         * @param array $filters - the report filters
         * @return array - report filters after processing
         */
        function processDependentFilters(&$filters) {
            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }
            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }

?>
