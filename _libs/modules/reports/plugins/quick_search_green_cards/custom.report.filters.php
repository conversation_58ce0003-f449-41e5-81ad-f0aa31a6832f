<?php
    class Custom_Report_Filters extends Report_Filters {
        private static $registry;

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();
            self::$registry = $registry;

            // DEFINE DATE FILTER
            $filters['period_from'] = array(
                'name'              => 'period_from',
                'type'              => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'first_filter_label'=> $this->i18n('from'),
                'label'             => $this->i18n('reports_filter_from'),
                'additional_filter' => 'period_to',
                'width'             => '65'
            );
            $filters['period_to'] = array(
                'name'     => 'period_to',
                'label'    => $this->i18n('to'),
                'type'     => 'date',
                'width'    => '64',
                'required' => true
            );

            // DEFINE TYPE FILTER
            $included_documents = array_filter(preg_split('/\s*,\s*/', INCLUDED_DOCUMENTS));
            $types_options = array();
            if (!empty($included_documents)) {
                $sql = 'SELECT `parent_id` as option_value, `name` as label FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE `parent_id` IN (' . implode(',', $included_documents) . ') AND `lang`="' . $registry['lang'] . '"' . "\n";
                $types_options = $registry['db']->GetAll($sql);
            }

            $filter = array (
                'custom_id'       => 'doc_type',
                'name'            => 'doc_type',
                'type'            => 'dropdown',
                'onchange'        => "changeDocumentStatuses(this)",
                'first_option_label' => $this->i18n('all'),
                'options'         => $types_options,
                'label'           => $this->i18n('reports_type'),
                'help'            => $this->i18n('reports_type')
            );
            $filters['doc_type'] = $filter;

            // DEFINE STATUS FILTER
            $filter = array (
                'custom_id'       => 'status',
                'name'            => 'status',
                'type'            => 'dropdown',
                'options'         => array(),
                'label'           => $this->i18n('reports_status'),
                'help'            => $this->i18n('reports_status')
            );
            $filters['status'] = $filter;

            // DEFINE CLIENT FILTER
            $filter = array (
                'custom_id'         => 'client',
                'name'              => 'client',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_client'),
                'help'              => $this->i18n('reports_client'),
            );
            $filters['client'] = $filter;

            // DEFINE INCOME NUM FILTER
            $filter = array (
                'custom_id' => 'income_num',
                'name'  => 'income_num',
                'type'  => 'text',
                'label' => $this->i18n('reports_income_num'),
                'help'  => $this->i18n('reports_income_num')
            );
            $filters['income_num'] = $filter;

            // DEFINE REF NUM OUR FILTER
            $filter = array (
                'custom_id' => 'ref_num_our',
                'name'  => 'ref_num_our',
                'type'  => 'text',
                'label' => $this->i18n('reports_ref_num_our'),
                'help'  => $this->i18n('reports_ref_num_our')
            );
            $filters['ref_num_our'] = $filter;

            // DEFINE REF NUM SORT FILTER
            $filter = array (
                'custom_id' => 'ref_num_sort',
                'name'  => 'ref_num_sort',
                'type'  => 'text',
                'label' => $this->i18n('reports_ref_num_sort'),
                'help'  => $this->i18n('reports_ref_num_sort')
            );
            $filters['ref_num_sort'] = $filter;

            // DEFINE REF NUM COMM FILTER
            $filter = array (
                'custom_id' => 'ref_num_comm',
                'name'  => 'ref_num_comm',
                'type'  => 'text',
                'label' => $this->i18n('reports_ref_num_comm'),
                'help'  => $this->i18n('reports_ref_num_comm')
            );
            $filters['ref_num_comm'] = $filter;

            // DEFINE CONTACT FILTER
            $filters['contact'] = array(
                'custom_id' => 'contact',
                'name' => 'contact',
                'type' => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width' => '222',
                'autocomplete' => array(
                    'type' => 'customers',
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions' => '[<code>] <name> <lastname>',
                    'fill_options' => array(
                        '$contact               => <id>',
                        '$contact_autocomplete  => [<code>] <name> <lastname>',
                        '$contact_oldvalue      => [<code>] <name> <lastname>',
                    ),
                    'filters' => array(
                        '<type>' => strval(CUSTOMERS_CAUSED_DAMAGE_TYPES)
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_contact'),
                'help'  => $this->i18n('reports_contact')
            );

            // DEFINE TYPE DAMAGE FILTER
            $filter = array (
                'custom_id'       => 'type_damage',
                'name'            => 'type_damage',
                'type'            => 'checkbox_group',
                'options'         => $registry['db']->GetAll('SELECT `option_value`, `label` FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE `parent_name`="' . DOCUMENT_VAR_TYPE_DAMAGE . '" AND `lang`="' . $registry['lang'] . '" ORDER BY `position` ASC'),
                'label'           => $this->i18n('reports_type_damage'),
                'help'            => $this->i18n('reports_type_damage')
            );
            $filters['type_damage'] = $filter;

            // DEFINE PTP LOCATION FILTER
            $filters['ptp_location'] = array(
                'custom_id' => 'ptp_location',
                'name' => 'ptp_location',
                'type' => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width' => '222',
                'autocomplete' => array(
                    'type' => 'nomenclatures',
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions' => '[<code>] <name>',
                    'fill_options' => array(
                        '$ptp_location               => <id>',
                        '$ptp_location_autocomplete  => [<code>] <name>',
                        '$ptp_location_oldvalue      => [<code>] <name>',
                    ),
                    'filters' => array(
                        '<type>' => strval(NOMENCLATURE_PTP_LOCATION)
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_ptp_location'),
                'help'  => $this->i18n('reports_ptp_location')
            );

            // DEFINE DOCUMENT EVENT FILTER
            $filter = array (
                'custom_id'       => 'document_event',
                'name'            => 'document_event',
                'type'            => 'dropdown',
                'options'         => $registry['db']->GetAll('SELECT `option_value`, `label` FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE `parent_name`="' . DOCUMENT_VAR_DAMAGE . '" AND `lang`="' . $registry['lang'] . '" ORDER BY `position` ASC'),
                'label'           => $this->i18n('reports_document_event'),
                'help'            => $this->i18n('reports_document_event')
            );
            $filters['document_event'] = $filter;

            //DEFINE POLICY NUM FILTER
            $filter = array (
                'custom_id' => 'policy_num',
                'name'  => 'policy_num',
                'type'  => 'text',
                'label' => $this->i18n('reports_policy_num'),
                'help'  => $this->i18n('reports_policy_num')
            );
            $filters['policy_num'] = $filter;

            //DEFINE OWNER FILTER
            $filter = array (
                'custom_id' => 'owner',
                'name'  => 'owner',
                'type'  => 'text',
                'label' => $this->i18n('reports_owner'),
                'help'  => $this->i18n('reports_owner')
            );
            $filters['owner'] = $filter;

            //DEFINE REG NUM FILTER
            $filter = array (
                'custom_id' => 'reg_num',
                'name'  => 'reg_num',
                'type'  => 'text',
                'label' => $this->i18n('reports_reg_num'),
                'help'  => $this->i18n('reports_reg_num')
            );
            $filters['reg_num'] = $filter;

            //DEFINE CAR FILTER
            $filter = array (
                'custom_id' => 'car',
                'name'  => 'car',
                'type'  => 'text',
                'label' => $this->i18n('reports_car'),
                'help'  => $this->i18n('reports_car')
            );
            $filters['car'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            $filters['status']['options'] = self::prepareTypeStatuses(self::$registry, $filters['doc_type']['value']);

            return $filters;
        }

        function prepareTypeStatuses($registry, $type_id) {
            if (!$type_id) {
                $options = array(
                    array(
                        'label'        => $this->i18n('documents_status_opened'),
                        'option_value' => 'opened'
                    ),
                    array(
                        'label'        => $this->i18n('documents_status_locked'),
                        'option_value' => 'locked'
                    ),
                    array(
                        'label'        => $this->i18n('documents_status_closed'),
                        'option_value' => 'closed'
                    )
                );
            } else {
                $main_statuses = array('opened', 'locked', 'closed');
                $sql = 'SELECT `name` as label, `id` as option_value, `status` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE `active`=1 AND `deleted_by`=0 AND `doc_type`=' . $type_id . ' AND `lang`="' . $registry['lang'] . '" ORDER BY `status`, `sequence`';
                $available_substatuses = $registry['db']->GetAll($sql);

                foreach ($main_statuses as $ms) {
                    $options[] = array(
                        'label'        => $this->i18n('documents_status_' . $ms),
                        'option_value' => $ms
                    );
                    foreach ($available_substatuses as $avb_st) {
                        if ($avb_st['status'] == $ms) {
                            $options[] = array(
                                'label'        => '- ' . $avb_st['label'],
                                'option_value' => $ms . '_' . $avb_st['option_value']
                            );
                        }
                    }
                }
            }

            return $options;
        }
    }
?>