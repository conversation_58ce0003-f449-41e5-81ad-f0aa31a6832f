<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
    <table border="1">
      <tr>
        <td><strong>{#reports_customer#|escape}</strong></td>
        <td><strong>{#reports_manager#|escape}</strong></td>
        <td><strong>{#reports_code#|escape}</strong></td>
        <td><strong>{#reports_status#|escape}</strong></td>
        <td><strong>{#reports_description#|escape}</strong></td>
        <td><strong>{#reports_my_client_is#|escape}</strong></td>
        <td><strong>{#reports_type_case#|escape}</strong></td>
        <td><strong>{#reports_case_num#|escape}</strong></td>
        <td><strong>{#reports_instance#|escape}</strong></td>
        <td><strong>{#reports_court#|escape}</strong></td>
        <td><strong>{#reports_first_side#|escape}</strong></td>
        <td><strong>{#reports_second_side#|escape}</strong></td>
        <td><strong>{#reports_third_side#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item=result name=results}
        <tr class="{cycle values='t_odd,t_even'}">
          <td>
            {$result.customer_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.manager_name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format: \@;">
            {$result.code|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.stage_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.description|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.my_client_is_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.type_case_name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format: \@;">
            {$result.case_num|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.instance_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.court_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.first_side|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.second_side|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.third_side|escape|default:"&nbsp;"}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td colspan="13"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>