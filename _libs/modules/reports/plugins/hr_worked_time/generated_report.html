{literal}
  <style type="text/css">
    .worked_time tr td:nth-last-child(-n+3) {
      text-align: right;
    }
    .worked_time  {
      background-color: white;
      margin-bottom: 10px;
    }
    .worked_time .gain {
      color: #008000;
    }
    .worked_time .lose {
      color: #ff0000;
    }
  </style>
{/literal}
{if not $reports_additional_options.isError}
<h1>{#worked_time_heading#}</h1>
<table style="width:500px;border-collapse: collapse;" cellpadding="5" class="t_table t_list worked_time">
  <tr class="reports_title_row hcenter">
    <th class="t_border" style="width:25px;"></th>
    <th class="t_border" style="width:75px;">{#worked_time_date#}</th>
    <th class="t_border">{#reports_employee#}</th>
    <th class="t_border" style="width:60px;">{#wored_time_scheduled_time#}</th>
    <th class="t_border" style="width:60px;">{#worked_time_worked_time#}</th>
    <th class="t_border" style="width:60px;">{#worked_time_difference#}</th>
  </tr>
{foreach from=$reports_results item=employees key=day name=schedule}
  {capture assign='empCount'}{if is_array($employees)}{$employees|@count}{else}0{/if}{/capture}
  {foreach from=$employees item=employee name=employ}
  {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
  <tr>
    {if $smarty.foreach.employ.first}
    <td class="vmiddle t_border t_v_border" style="text-align: center;" rowspan="{$empCount}">{$smarty.foreach.schedule.iteration}</td>
    <td class="vmiddle t_border t_v_border" style="text-align: center;" rowspan="{$empCount}">{$day|date_format:#date_short#}</td>
    {/if}
    <td class="{$current_row_class} t_border vmiddle">{$employee.name|escape}</td>
    <td class="{$current_row_class} t_border vmiddle">{$employee.schedule_time|string_format:"%.2f"}</td>
    <td class="{$current_row_class} t_border vmiddle">{$employee.worked_time|string_format:"%.2f"}</td>
    <td class="{$current_row_class} t_border vmiddle {if $employee.time_difference > 0}gain{elseif $employee.time_difference < 0}lose{/if}">{$employee.time_difference|string_format:"%.2f"}</td>
  </tr>
  {/foreach}
  {foreachelse}
  <tr>
    <td class="error" colspan="8"><strong>{#worked_time_no_results#|escape}</strong></td>
  </tr>
{/foreach}
  <tr class="row_blue">
    <td colspan="3"  style="text-align: right;"><strong>{#worked_time_total_time#}</strong></td>
    <td><strong>{$reports_additional_options.totals.schedule_time|string_format:"%.2f"}</strong></td>
    <td><strong>{$reports_additional_options.totals.worked_time|string_format:"%.2f"}</strong></td>
    <td class="{if $reports_additional_options.totals.time_difference > 0}gain{elseif $reports_additional_options.totals.time_difference < 0}lose{/if}"><strong>{$reports_additional_options.totals.time_difference|string_format:"%.2f"}</strong></td>
  </tr>
  <tr><td class="t_footer" colspan="6"></td></tr>
</table>
{/if}