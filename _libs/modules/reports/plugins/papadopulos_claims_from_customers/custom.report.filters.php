<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $options_companies = array();

            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters_companies = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('fc.position', 'fci18n.name'),
                                       'where'      => array('fc.deleted=0',
                                                             'fc.active=1')
            );

            $companies = Finance_Companies::search($registry, $filters_companies);
            foreach($companies as $company) {
                $options_companies[$company->get('id')] = array(
                    'label'         => $company->get('name'),
                    'option_value'  => $company->get('id'));
            }

            $filter = array (
                'custom_id'             => 'company',
                'name'                  => 'company',
                'type'                  => 'dropdown',
                'required'              => 1,
                'label'                 => $this->i18n('reports_company'),
                'help'                  => $this->i18n('reports_company'),
                'options'               => $options_companies,
                'value'                 => ''
            );
            $filters['company'] = $filter;

            // DEFINE CUSTOMERS AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'custom_filter',
                'actual_type'       => 'autocompleter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'custom_buttons'    => 'search',
                'width'             => 222,
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'autocomplete'      => array('search' => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'customers',
                    'fill_options' => array('$customers => <id>',
                                            '$customers_autocomplete => <name> <lastname>',
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['customers'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE REASON NUM FILTER
            $filter = array (
                'custom_id' => 'reason_num',
                'name'      => 'reason_num',
                'type'      => 'text',
                'label'     => $this->i18n('reports_reason_num'),
                'help'      => $this->i18n('reports_reason_num')
            );
            $filters['reason_num'] = $filter;

            //DEFINE INVOICE NUM FILTER
            $filter = array (
                'custom_id' => 'invoice_num',
                'name'      => 'invoice_num',
                'type'      => 'text',
                'label'     => $this->i18n('reports_invoice_num'),
                'help'      => $this->i18n('reports_invoice_num')
            );
            $filters['invoice_num'] = $filter;

            //DEFINE TAKE MONEY FILTER
            $options_take_money = array(
                array ( 'label' => $this->i18n('reports_obligations_paid'),
                        'option_value' => 'paid'
                       ),
                array ( 'label' => $this->i18n('reports_obligations_partial'),
                        'option_value' => 'partial'
                       ),
                array ( 'label' => $this->i18n('reports_obligations_unpaid'),
                        'option_value' => 'unpaid'
                       )
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'take_money',
                'name'      => 'take_money',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_obligations'),
                'help'      => $this->i18n('reports_obligations'),
                'options'   => $options_take_money,
            );
            $filters['take_money'] = $filter;

            //DEFINE FALLING DATE FILTER
            $filter = array (
                'custom_id' => 'falling_date',
                'name'      => 'falling_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_falling_date'),
                'help'      => $this->i18n('reports_falling_date')
            );
            $filters['falling_date'] = $filter;

            return $filters;
        }
    }
?>