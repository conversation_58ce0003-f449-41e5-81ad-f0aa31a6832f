<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'            => 'customer',
                'name'                 => 'customer',
                'type'                 => 'autocompleter',
                'autocomplete_type'    => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'                => $this->i18n('reports_customer_name'),
                'help'                 => $this->i18n('reports_customer_name'),
                'execute_after'        => 'clearDependentProjectFields'
            );
            $filters['customer'] = $filter;

            //DEFINE PROJECTS' FILTER
            $filter = array (
                'custom_id'            => 'project',
                'name'                 => 'project',
                'type'                 => 'autocompleter',
                'required'             => 1,
                'autocomplete_type'    => 'projects',
                'autocomplete_buttons' => 'clear',
                'filters'              => array(
                    '<customer>' => '$customer',
                    '<type>'     => PROJECT_TYPE_CASE
                ),
                'label'                => $this->i18n('reports_project_name'),
                'help'                 => $this->i18n('reports_project_name')
            );
            $filters['project'] = $filter;

            return $filters;
        }
    }
?>