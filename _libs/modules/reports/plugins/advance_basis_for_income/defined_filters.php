<?php

return [
    // DEFINE DATE FROM FILTER
    'from_date' => [
        'custom_id' => 'from_date',
        'name' => 'from_date',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
        'type' => 'custom_filter',
        'required' => 1,
        'width' => 65,
        'additional_filter' => 'to_date',
        'first_filter_label' => $this->i18n('filter_from_date'),
        'label' => $this->i18n('filter_from_date'),
        'help' => $this->i18n('filter_from_date'),
    ],
    // DEFINE DATE TO FILTER
    'to_date' => [
        'custom_id' => 'to_date',
        'name' => 'to_date',
        'type' => 'date',
        'width' => 65,
        'label' => $this->i18n('to'),
        'help' => $this->i18n('filter_to_date'),
    ],
    // DEFINE BANK AUTOCOMPLETER
    'bank' => [
        'custom_id' => 'bank',
        'name' => 'bank',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$bank_autocomplete  => <name> <lastname>',
                '$bank_oldvalue      => <name> <lastname>',
                '$bank               => <id>',
            ],
            'filters' => [
                '<type>' => strval(BANK_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_bank_name'),
        'help' => $this->i18n('filter_bank_name'),
    ],
    // DEFINE MINIMUM SUM FROM FILTER
    'min_sum' => [
        'custom_id' => 'min_sum',
        'name' => 'min_sum',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_from_to.html',
        'type' => 'custom_filter',
        'required' => 0,
        'width' => 65,
        'restrict'  => 'insertOnlyFloats',
        'additional_filter' => 'max_sum',
        'first_filter_label' => $this->i18n('filter_min_sum'),
        'label' => $this->i18n('filter_min_sum'),
        'help' => $this->i18n('filter_min_sum'),
    ],
    // DEFINE MAXIMUM SUM TO FILTER
    'max_sum' => [
        'custom_id' => 'max_sum',
        'name' => 'max_sum',
        'type' => 'custom_filter',
        'width' => 65,
        'restrict'  => 'insertOnlyFloats',
        'label' => $this->i18n('filter_max_sum'),
        'help' => $this->i18n('filter_max_sum'),
    ],
    // DEFINE CUSTOMER TYPE
    'payment_type' => [
        'custom_id' => 'payment_type',
        'name' => 'payment_type',
        'type' => 'dropdown',
        'required' => 0,
        'label' => $this->i18n('filter_payment_type'),
        'help' => $this->i18n('filter_payment_type'),
        'options' => [],
    ],
    // DEFINE ASSIGNOR AUTOCOMPLETER
    'assignor' => [
        'custom_id' => 'assignor',
        'name' => 'assignor',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$assignor_autocomplete  => <name> <lastname>',
                '$assignor_oldvalue      => <name> <lastname>',
                '$assignor               => <id>',
            ],
            'filters' => [
                '<type>' => strval(ASSIGNOR_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_assignor_name'),
        'help' => $this->i18n('filter_assignor_name'),
    ],
    // DEFINE CUSTOMER TYPE
    'customer_type' => [
        'custom_id' => 'customer_type',
        'name' => 'customer_type',
        'type' => 'dropdown',
        'required' => 0,
        'label' => $this->i18n('filter_customer_type'),
        'help' => $this->i18n('filter_customer_type'),
        'skip_please_select' => true,
        'options' => [
            [
                'label' => $this->i18n('filter_customer_all'),
                'option_value' => '0,1'
            ],
            [
                'label' => $this->i18n('filter_customer_legal'),
                'option_value' => '1'
            ],
            [
                'label' => $this->i18n('filter_customer_individual'),
                'option_value' => '0'
            ],
        ],
    ],
    // DEFINE CUSTOMER AUTOCOMPLETER
    'customer' => [
        'custom_id' => 'customer',
        'name' => 'customer',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$customer_autocomplete  => <name> <lastname>',
                '$customer_oldvalue      => <name> <lastname>',
                '$customer               => <id>',
            ],
            'filters' => [
                '<type>' => strval(CUSTOMER_TYPES),
                '<is_company>' => '$customer_type'
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_customer_name'),
        'help' => $this->i18n('filter_customer_name'),
    ],
    // DEFINE REPORT NUMBER FIELD
    'full_num' => [
        'custom_id' => 'full_num',
        'name' => 'full_num',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'documents',
        'autocomplete' => [
            'type' => 'documents',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'documents',
                'documents'
            ),
            'suggestions' => '<full_num>',
            'fill_options' => [
                '$full_num_autocomplete  => <full_num>',
                '$full_num_oldvalue      => <full_num>',
                '$full_num               => <id>',
            ],
            'filters' => [
                '<type>' => strval(REPORT_DOCUMENTS_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_full_num'),
        'help' => $this->i18n('filter_full_num'),
    ],
    // DEFINE REPORT CUSTOM NUMBER FIELD
    'custom_num' => [
        'custom_id' => 'custom_num',
        'name' => 'custom_num',
        'type' => 'custom_filter',
        'actual_type' => 'text',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'label' => $this->i18n('filter_custom_num'),
        'help' => $this->i18n('filter_custom_num'),
    ],
    // DEFINE REPORTS WITHOUT INCOME
    'reports_without_income' => [
        'custom_id' => 'reports_without_income',
        'name' => 'reports_without_income',
        'type' => 'checkbox_group',
        'label' => $this->i18n('filter_reports_without_income'),
        'help' => $this->i18n('filter_reports_without_income'),
        'options' => [
            [
                'option_value' => '1',
                'label' => '',
            ],
        ],
    ],
    // DEFINE SCRIPTS
    'custom_scripts' => [
        'custom_id' => 'custom_scripts',
        'name' => 'custom_scripts',
        'custom_template' => PH_MODULES_DIR . 'reports/plugins/'
            . $registry['report_type']['name'] . '/custom_scripts.html',
        'type' => 'custom_filter',
    ],
];
