<?php
    class Custom_Report_Filters extends Report_Filters {
        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('DOCUMENT_LOAN_CONTRACT', '6');
            define('CONTRACT_INSTALLMENT_NUM', 'column_connection');
            define('CONTRACT_CURRENCY', 'grant_credit_currency');
            define('CONTRACT_PAYMENT_ID', 'payment_document_id');
            define('CONTRACT_PAYMENT_DATE', 'true_payment_date');
            define('CONTRACT_PAYMENT_PRINCIPAL', 'principal');
            define('CONTRACT_PAYMENT_WARRANTY', 'amount_warranty');
            define('CONTRACT_PAYMENT_INTEREST', 'contractual_interest');
            define('CONTRACT_PAYMENT_PENALTY', 'penalty_interest');
            define('CONTRACT_PAYMENT_FEE_MANG', 'fee_management');
            define('CONTRACT_PAYMENT_FEE_COMMIT', 'fee_commitment');
            define('CONTRACT_PAYMENT_LPG', 'overdue_principle_interest');
            define('CONTRACT_PAYMENT_CURRENCY', 'currency_rate');
            define('CONTRACT_INSTALLMENT_GT2_ROW', 'column_connection_id');

            $filters = array();

            $report_name = '';
            if ($this->reportName) {
                $report_name = $this->reportName;
            } else {
                $report_name = $registry['report_type']['name'];
            }

            //DEFINE THE DATES FILTER
            $filters['date_from'] = array(
                'name'               => 'date_from',
                'type'               => 'custom_filter',
                'custom_template'    => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'first_filter_label' => $this->i18n('from'),
                'additional_filter'  => 'date_to',
                'width'              => '64',
                'required'           => 1,
                'label'              => $this->i18n('reports_payment_date'),
                'help'               => $this->i18n('reports_payment_date')
            );
            $filters['date_to'] = array(
                'name'               => 'date_to',
                'type'               => 'date',
                'width'              => '64',
                'label'              => $this->i18n('to')
            );

            //DEFINE CLIENT FILTER
            $filter = array (
                'custom_id'       => 'client',
                'name'            => 'client',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_client'),
                'help'            => $this->i18n('reports_client'),
                'autocomplete'    => array(
                    'filters'       => array(),
                    'clear'         => 1,
                    'type'          => 'autocompleters',
                    'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                    'plugin_search' => 'customQuery',
                    'plugin_params' => array(
                        'sql'    => "SELECT DISTINCT(c.id) as id, IF(c.is_company, c.eik, c.ucn) as eik_ucn, TRIM(CONCAT(ci.name, ' ', ci.lastname)) as name FROM " . DB_TABLE_DOCUMENTS . " AS d INNER JOIN " . DB_TABLE_CUSTOMERS . " AS c ON (c.id=d.customer AND c.type='" . CUSTOMER_TYPE_CLIENT . "' AND c.subtype='normal') LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " ci ON (ci.parent_id = c.id AND ci.lang = 'bg') WHERE d.type='" . DOCUMENT_TYPE_ID . "' AND d.active=1 AND d.deleted_by=0 AND d.substatus!='" . DOCUMENT_SUBSTATUS_ANNULED . "' AND (<search_string_parts>)",
                        'search' => 'c.eik, ci.name, ci.lastname, c.ucn'
                            ),
                            'suggestions'   => '<name> (<eik_ucn>)',
                            'fill_options'  => array(
                            '$client => <id>',
                            '$client_autocomplete => <name> (<eik_ucn>)',
                            '$client_oldvalue => <name> (<eik_ucn>)',
                            ),
                            'id_var'        => 'client',
                            'buttons_hide'  => 'search report add'
                                )
            );
            $filters['client'] = $filter;

            //DEFINE CONFIRM STATUS ONLY
            $filters['confirm_status'] = array (
                'custom_id' => 'confirm_status',
                'name'      => 'confirm_status',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_confirm_status'),
                'help'      => $this->i18n('reports_confirm_status'),
                'options'   => array(
                    array(
                        'label'        => '',
                        'option_value' => '1',
                    )
                )
            );

            // Filter: Cash/Bank account
            $finance_cashboxes = $registry['currentUser']->get('finance_cashboxes');
            $cashboxes_ids = empty($finance_cashboxes['PKO']['view']) ? array() : $finance_cashboxes['PKO']['view'];
            $finance_bank_accounts = $registry['currentUser']->get('finance_bank_accounts');
            $bank_accounts_ids = empty($finance_bank_accounts['BP']['view']) ? array() : $finance_bank_accounts['BP']['view'];
            $optgroups = array();
            $query = "
                SELECT fci.name AS label,
                    CONCAT('cash_', fc.id) AS option_value
                  FROM " . DB_TABLE_FINANCE_CASHBOXES . " AS fc
                  LEFT JOIN " . DB_TABLE_FINANCE_CASHBOXES_I18N . " AS fci
                    ON (fci.parent_id = fc.id
                      AND fci.lang = '{$registry['lang']}')
                  WHERE fc.active = 1
                    AND fc.deleted_by = 0
                    AND fc.id IN ('" . implode("', '", $cashboxes_ids) . "')
                  ORDER BY fci.name";
            $options_cashboxes = $registry['db']->GetAll($query);
            if (!empty($options_cashboxes)) {
                $optgroups[$this->i18n('reports_filter_cash_bank_cashboxes')] = $options_cashboxes;
            }
            $query = "
                SELECT fbai.name AS label,
                    CONCAT('bank_', fba.id) AS option_value
                  FROM " . DB_TABLE_FINANCE_BANK_ACCOUNTS . " AS fba
                  LEFT JOIN " . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . " AS fbai
                    ON (fbai.parent_id = fba.id
                      AND fbai.lang = '{$registry['lang']}')
                  WHERE fba.active = 1
                    AND fba.deleted_by = 0
                    AND fba.id IN ('" . implode("', '", $bank_accounts_ids) . "')
                  ORDER BY fbai.name";
            $options_bank_accounts = $registry['db']->GetAll($query);
            if (!empty($options_bank_accounts)) {
                $optgroups[$this->i18n('reports_filter_cash_bank_bank_accounts')] = $options_bank_accounts;
            }
            $filters['cash_bank'] = array(
                'name' => 'cash_bank',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_filter_cash_bank'),
                'optgroups' => $optgroups
            );

            return $filters;
        }

        /**
         * Process some filters that depends on the request
         *
         * @param array $filters - the report filters
         */
        function processDependentFilters(&$filters) {
            $unset_filters = array();

            foreach ($filters as $name => $filter) {
                // Process the filter from/to date
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }
?>