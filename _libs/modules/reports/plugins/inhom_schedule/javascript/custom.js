var reportInhomSchedule = {
    field_projects: 'projects',
    field_orders: 'orders',
    field_requests: 'request_index',
    field_ppps: 'ppp_index',

    colors: {
        red: '#FF7878',
        green: '#A0FA6B',
        yellow: '#F3F781'
    },

    msg: {
        no_changes: 'No changes for saving!',
        cant_export_changes: 'To export the changes you need to save them first!',
        validate_real_supply_date: 'The supply date of PPP is required!'
    },

    _haveChanges: function () {
        return ($$('input[id^="' + this.field_projects + '_"]:not([disabled])').length
                || $$('input[id^="' + this.field_orders + '_"]:not([disabled])').length
                || $$('input[id^="' + this.field_requests + '_"]:not([disabled])').length
                || $$('input[id^="' + this.field_ppps + '_"]:not([disabled])').length);
    },

    _validate: function () {
        if ($$('input[name^="real_supply_date["][value=""]').length) {
            alert(this.msg.validate_real_supply_date);
            return false;
        }

        return true;
    },

    save: function (e) {
        if (this._validate()) {
            if (!e.disabled && this._haveChanges()) {
                e.disabled = true;
                e.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save&report_type=' + $('report_type').value;
                e.form.submit();
            } else {
                alert(this.msg.no_changes);
            }
        }
    },

    generate: function (sort, order) {
        $('sort').value = sort;
        $('order').value = order;
        $('sort').form.submit();
    },

    exportReport: function (e) {
        if (this._haveChanges()) {
            alert(this.msg.cant_export_changes);
        } else if (!e.disabled) {
            e.disabled = true;
            url = env.base_host + env.base_url + '?'
                + env.module_param + '=reports&reports=export&report_type=' + $('report_type').value + '&pattern=';
            redirect(url);
        }
    },

    change: function (field_name, index) {
        var changed_field = '';
        if (field_name == 'w_priority'
             || field_name == 'w_production_start_date'
             || field_name == 'w_notes'
             || field_name == 'w_status_ready'
             || field_name == 'w_advance_date'
             || field_name == 'w_advance_value'
             || field_name == 'w_advance_date_final'
             || field_name == 'w_advance_value_final') {
            changed_field = this.field_projects;
        } else if (field_name == 'make_date_num') {
            changed_field = this.field_orders;
        } else if (field_name == 'supplier_id'
            || field_name == 'supplier_name'
                || field_name == 'rqa_deadline'
                    || field_name == 'supply_date') {
            changed_field = this.field_requests;
        } else if (field_name == 'real_supply_date') {
            changed_field = this.field_ppps;
        }
        if (changed_field) {
            $(changed_field + '_' + index).disabled = false;
        }
    },

    changeDate: function (element, record, id) {
        var field_name = element.id.replace(/_\d+$/, '');
        var field_index = element.id.replace(/^.+_(\d+)$/, '$1');
        var date = element.value;
        var date_formatted = $(field_name + '_formatted_' + field_index).value;
        var now = new Date();
        now = now.format('Y-m-d');
        var color = '';
        if (field_name == 'supply_date') {
            if (date == '') {
                color = '';
            } else if (date < now) {
                color = this.colors.red;
            } else if (date == now) {
                color = this.colors.green;
            } else if (date > now) {
                color = this.colors.yellow;
            }
        }
        $$('.' + record + '_' + id).each(function (e) {
            var index = e.id.replace(/^.+_(\d+)$/, '$1');
            $(field_name + '_' + index).value = date;
            $(field_name + '_formatted_' + index).value = date_formatted;
            if (field_name == 'supply_date') {
                var td = $(field_name + '_' + index).parentNode;
                td.style.backgroundColor = color;
            }
            reportInhomSchedule.change(field_name, index);
        });
    },

    changeAutocompleter: function (autocomplete, data) {
        if (typeof data['$supplier_id'] != 'undefined') {
            $$('.' + autocomplete.custom_param_record + '_' + autocomplete.custom_param_id).each(function (e) {
                var index = e.id.replace(/^.+_(\d+)$/, '$1');
                $('supplier_id_' + index).value = data['$supplier_id'];
                $('supplier_name_' + index).value = data['$supplier_name'];
                reportInhomSchedule.change('supplier_id', index);
            });
        }
    },

    changeText: function (element, record, id) {
        var field_name = element.id.replace(/_\d+$/, '');
        var field_index = element.id.replace(/^.+_(\d+)$/, '$1');
        var field_value = element.value;
        $$('.' + record + '_' + id).each(function (e) {
            var index = e.id.replace(/^.+_(\d+)$/, '$1');
            $(field_name + '_' + index).value = field_value;
            reportInhomSchedule.change(field_name, index);
        });
    }
}