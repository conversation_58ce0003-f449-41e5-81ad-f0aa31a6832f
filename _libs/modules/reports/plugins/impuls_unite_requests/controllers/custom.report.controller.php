<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_check_deliverers':
                $this->_checkDeliverers();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /*
     * AJAX function to check if all the deliverers are the same
     */
    public function _checkDeliverers() {
        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];
        Reports::getReportSettings($this->registry, $report->get('type'));

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->registry['translater']->loadFile($i18n_file);

        $result = array(
            'continue'  => false,
            'message'   => '',
            'template'  => array()
        );

        $request = $this->registry['request'];
        $session_param = $request->get('table_session_param');

        $selected_items = $this->registry['session']->get($session_param, 'selected_items');
        if (!empty($selected_items) && !empty($selected_items['ids'])) {
            $model_ids = array();
            $gt2_rows_ids = array();

            foreach ($selected_items['ids'] as $selected_indexes) {
                @list($gt2_row, $model_id) = explode('_', $selected_indexes);
                $gt2_rows_ids[] = $gt2_row;
                $model_ids[] = $model_id;
            }

            // find the deliverer additional var
            $available_documents = array();
            if (defined('GENERAL_REPORT_TYPES')) {
                $available_documents = array_filter(preg_split('#\s*,\s*#', constant('GENERAL_REPORT_TYPES')));
            }

            $sql_for_add_vars = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type IN ("' . implode('","', $available_documents) . '") AND fm.name IN ("' . DELIVERER . '") ORDER BY fm.position';
            $deliverer_vars_ids = $this->registry['db']->getCol($sql_for_add_vars);

            // get deliverers for the required models
            $query = sprintf('SELECT `value` FROM %s WHERE `var_id` IN (%s) AND `model_id` IN (%s)', DB_TABLE_DOCUMENTS_CSTM, implode(',', $deliverer_vars_ids), implode(',', $model_ids));
            $deliverers_ids = $this->registry['db']->getCol($query);

            $deliverers_ids = array_unique($deliverers_ids);
            if (count($deliverers_ids)) {
                if (count($deliverers_ids)>1) {
                    $result['message'] = $this->i18n('message_reports_select_only_one_deliverer');
                } else {
                    $result['continue'] = true;
                }
            } else {
                $result['message'] = $this->i18n('message_reports_no_deliverer_data');
            }
        } else {
            $result['message'] = $this->i18n('message_reports_no_rows_to_issue_expense_reason');
        }

        if ($result['continue']) {
            $sql_for_add_vars = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type IN (' . GENERAL_REPORT_TYPES . ') AND fm.name="' . DOCUMENT_REQUEST_PAYMENT_TYPE . '" ORDER BY fm.position';
            $payment_types = $this->registry['db']->getCol($sql_for_add_vars);

            // get the rows to calculate the quantities to be entered in the new document
            $query = 'SELECT d.type as doc_type, d_cstm_payment.value as payment_type ' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                     '  ON (d.id=gt2_det.model_id)' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment' . "\n" .
                     '  ON (d.id=d_cstm_payment.model_id AND d_cstm_payment.var_id IN ("' . implode('","', $payment_types) . '"))' . "\n" .
                     'WHERE gt2_det.id  IN (' . implode(',', $gt2_rows_ids) . ') AND gt2_det.model="Document"';
            $reason_gt2_rows = $this->registry['db']->getAll($query);

            $available_companies = array();
            $included_companies = array_filter(preg_split('#\s*,\s*#', AVAILABLE_COMPANIES));
            foreach ($included_companies as $inc_comp) {
                $available_companies[$inc_comp] = array(
                    'documents'    => array(),
                    'cashbox'      => '',
                    'bank_account' => ''
                );
                if (defined('COMPANY_' . $inc_comp . '_DOCUMENT_TYPES')) {
                    $available_companies[$inc_comp]['documents'] = array_filter(preg_split('#\s*,\s*#', constant('COMPANY_' . $inc_comp . '_DOCUMENT_TYPES')));
                }
                if (defined('COMPANY_' . $inc_comp . '_BANK_ACCOUNT')) {
                    $available_companies[$inc_comp]['bank_account'] = constant('COMPANY_' . $inc_comp . '_BANK_ACCOUNT');
                }
                if (defined('COMPANY_' . $inc_comp . '_CASHBOX')) {
                    $available_companies[$inc_comp]['cashbox'] = constant('COMPANY_' . $inc_comp . '_CASHBOX');
                }
            }

            $payment_type_options = array();
            $company_data_params = array(
                $this->registry,
                'lang'              => $this->registry['lang'],
                //hardcode the office
                'office_id '        => array(1),
                'company_id'        => array(),
                'payment_direction' => 'expenses',
                'active'            => 1
            );
            $cashboxes = array();
            $cashboxes = array();
            foreach ($reason_gt2_rows as $key_row => $reason_row) {
                // define company data
                $current_company = 0;
                $current_container_id = 0;
                $current_container_type = '';
                if ($reason_row['payment_type'] == PAYMENT_TYPE_CASE) {
                    $current_container_type = 'cash';
                } elseif ($reason_row['payment_type'] == PAYMENT_TYPE_BANK) {
                    $current_container_type = 'bank';
                }

                foreach ($available_companies as $comp_id => $avb_comp) {
                    if (in_array($reason_row['doc_type'], $avb_comp['documents'])) {
                        $current_company = $comp_id;
                        if (!in_array($current_company, $company_data_params['company_id'])) {
                            $company_data_params['company_id'][] = $current_company;
                        }
                        if ($current_container_type == 'cash') {
                            $current_container_id = $avb_comp['cashbox'];
                        } elseif ($current_container_type == 'bank') {
                            $current_container_id = $avb_comp['bank_account'];
                        }
                    }
                }
                $payment_type_options[] = sprintf('%d_1_%s_%d', $current_company, $current_container_type, $current_container_id);
            }
            $payment_type_options = array_unique($payment_type_options);

            $expenses_types_to_add = array();
            if (defined('EXPENSES_REASONS_TO_CREATE') && EXPENSES_REASONS_TO_CREATE) {
                $expenses_types = preg_split('#\s*,\s*#', EXPENSES_REASONS_TO_CREATE);
                $expenses_types = array_filter($expenses_types);

                if (!empty($expenses_types)) {
                    // get available types to add
                    $sql_expense_types = 'SELECT `parent_id` as option_value, `name` as label' . "\n" .
                                         'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                                         'WHERE parent_id IN (' . implode(",", $expenses_types) . ') AND lang="' . $this->registry['lang'] . '" ORDER BY FIELD(parent_id, "' . implode('","', $expenses_types) . '")';
                    $expenses_types_to_add = $this->registry['db']->getAll($sql_expense_types);
                }
            }

            $viewer = new Viewer($this->registry);
            $viewer->data['expenses_types_to_add'] = $expenses_types_to_add;
            $viewer->data['special_session_param'] = $session_param;
            $viewer->data['help_message'] = $this->i18n('reports_add_expense_reason_instructions');
            $viewer->data['current_report_type'] = $report->get('type');

            if (count($payment_type_options) == 1) {
                $viewer->data['companies_data'] = reset($payment_type_options);
            } else {
                //this is very unlikely to happen, but the user should choose from dropdown
                require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
                $companies_data = Finance_Dropdown::getCompaniesData($company_data_params);
                $viewer->data['companies_data_label'] = $this->i18n('reports_chose_container');
                $viewer->data['companies_data'] = $companies_data;
            }
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/';
            $viewer->template = '_add_expense_reason.html';
            $result['template']['content'] = $viewer->fetch();
            $result['template']['title'] = $this->i18n('reports_add_expense_reason');
        }

        print json_encode($result);
        exit;
    }

    /*
     * Custom function to create Finance Expenses Reason and redirect to adding the model
     */
    public function customCreateModel($settings) {
        $request = $this->registry['request'];
        $session = $this->registry['session'];

        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];
        Reports::getReportSettings($this->registry, $report->get('type'));

        // get session ids
        $table_session_param = $request->get('table_session_param');
        $session_indexes = $session->get($table_session_param, 'selected_items');

        if ($table_session_param && !empty($session_indexes) && !empty($session_indexes['ids'])) {
            // get default currency of selected company (or main currency of installation by default)
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $main_currency = Finance_Currencies::getMain($this->registry);
            $currency_multipliers = array();
            $currency_multipliers[$main_currency . '->' . $main_currency] = 1;
            $prec = $this->registry['config']->getSectionParams('precision');

            $gt2_rows_ids = array();

            foreach ($session_indexes['ids'] as $selected_index) {
                @list($gt2_row, $model_id) = explode('_', $selected_index);
                $gt2_rows_ids[$gt2_row] = array(
                    'row_id'       => $gt2_row,
                    'model_id'     => $model_id,
                    'source_model' => 'Document'
                );
            }

            // get all the needed additional vars ids
            $available_documents = array();
            if (defined('GENERAL_REPORT_TYPES')) {
                $available_documents = array_filter(preg_split('#\s*,\s*#', constant('GENERAL_REPORT_TYPES')));
            }
            $vars_names = array(DOCUMENT_REQUEST_PROJECT, DELIVERER, DOCUMENT_REQUEST_PAYMENT_DATE, CURRENCY);

            $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type IN ("' . implode('","', $available_documents) . '") AND fm.name IN ("' . implode('","', $vars_names) . '") ORDER BY fm.position';
            $add_vars_ids = $this->registry['db']->getAll($sql_for_add_vars);

            $project_ids = array();
            $payment_types = array();
            $deliverer_ids = array();
            $payment_dates_ids = array();
            $currency_ids = array();

            foreach ($add_vars_ids as $vars) {
                if ($vars['name'] == DOCUMENT_REQUEST_PROJECT) {
                    $project_ids[] = $vars['id'];
                } else if ($vars['name'] == DELIVERER) {
                    $deliverer_ids[] = $vars['id'];
                } else if ($vars['name'] == CURRENCY) {
                    $currency_ids[] = $vars['id'];
                } else if ($vars['name'] == DOCUMENT_REQUEST_PAYMENT_DATE) {
                    $payment_dates_ids[] = $vars['id'];
                }
            }

            // get the rows to calculate the quantities to be entered in the new document
            $query = 'SELECT gt2_det.id as idx, gt2_det.*, gt2_det_i18n.*, d.full_num, d.type as doc_type, d.project, pi18n.name as project_name, ' . "\n" .
                     '  d_cstm_payment_date.value as payment_date, d_cstm_alternative_project.value as alternative_project,  d_cstm_alternative_project_name.name as alternative_project_name, ' . "\n" .
                     '  d_cstm_deliverer.value as deliverer, CONCAT(ci18n.name, " ", ci18n.lastname) as deliverer_name, c.admit_VAT_credit, d_cstm_currency.value as currency' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2_det' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_det_i18n' . "\n" .
                     ' ON (gt2_det.id=gt2_det_i18n.parent_id AND gt2_det_i18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                     ' ON (d.id=gt2_det.model_id)' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                     ' ON (d.project=pi18n.parent_id AND pi18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_deliverer' . "\n" .
                     '  ON (d.id=d_cstm_deliverer.model_id AND d_cstm_deliverer.var_id IN (' . implode(',', $deliverer_ids) . '))' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_currency' . "\n" .
                     '  ON (d.id=d_cstm_currency.model_id AND d_cstm_currency.var_id IN (' . implode(',', $currency_ids) . '))' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     ' ON (d_cstm_deliverer.value=c.id)' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     ' ON (d_cstm_deliverer.value=ci18n.parent_id AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment' . "\n" .
                     '  ON (d.id=d_cstm_payment.model_id AND d_cstm_payment.var_id IN ("' . implode('","', $payment_types) . '"))' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment_date' . "\n" .
                     '  ON (d.id=d_cstm_payment_date.model_id AND d_cstm_payment_date.var_id IN ("' . implode('","', $payment_dates_ids) . '"))' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_alternative_project' . "\n" .
                     '  ON (d.id=d_cstm_alternative_project.model_id AND d_cstm_alternative_project.var_id IN ("' . implode('","', $project_ids) . '"))' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS d_cstm_alternative_project_name' . "\n" .
                     '  ON (d_cstm_alternative_project_name.parent_id=d_cstm_alternative_project.value AND d_cstm_alternative_project_name.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'WHERE gt2_det.id  IN (' . implode(',', array_keys($gt2_rows_ids)) . ') AND gt2_det.model="Document"';
            $reason_gt2_rows = $this->registry['db']->getAssoc($query);

            // get the customer for the row
            $first_gt2_row = reset($reason_gt2_rows);
            $customer = $first_gt2_row['deliverer'];
            $customer_name = $first_gt2_row['deliverer_name'];
            $admit_vat_credit_options = array();
            $payment_dates_options = array();
            $requests_list = array();

            foreach ($reason_gt2_rows as $key_row => $reason_row) {
                $requests_list[] = $reason_row['full_num'];
                $admit_vat_credit_options[] = intval($reason_row['admit_VAT_credit']);
                $payment_dates_options[] = $reason_row['payment_date'];

                $reason_gt2_rows[$key_row]['article_alternative_deliverer'] = (!empty($reason_row['project']) ? $reason_row['project'] : $reason_row['alternative_project']);
                $reason_gt2_rows[$key_row]['article_alternative_deliverer_name'] = (!empty($reason_row['project']) ? $reason_row['project_name'] : $reason_row['alternative_project_name']);

                $requests_list[] = $reason_row['full_num'];

                $currency_key = $reason_row['currency'] . '->' . $main_currency;
                if (!isset($currency_multipliers[$currency_key])) {
                    $currency_multipliers[$currency_key] = Finance_Currencies::getRate($this->registry, $reason_row['currency'], $main_currency);
                }
                $current_multiplier  = $currency_multipliers[$currency_key];

                // convert the currencies
                $reason_gt2_rows[$key_row]['last_delivery_price'] = round(($reason_row['last_delivery_price']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['price'] = round(($reason_row['price']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['discount_value'] = round(($reason_row['discount_value']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['discount_value'] = round(($reason_row['discount_value']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['surplus_value'] = round(($reason_row['surplus_value']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['price_with_discount'] = round(($reason_row['price_with_discount']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['vat_value'] = round(($reason_row['vat_value']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['profit_no_final_discount'] = round(($reason_row['profit_no_final_discount']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal_profit_no_final_discount'] = round(($reason_row['subtotal_profit_no_final_discount']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal'] = round(($reason_row['subtotal']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal_with_discount'] = round(($reason_row['subtotal_with_discount']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal_discount_value'] = round(($reason_row['subtotal_discount_value']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal_with_vat'] = round(($reason_row['subtotal_with_vat']*$current_multiplier), $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['subtotal_with_vat_with_discount'] = round(($reason_row['subtotal_with_vat_with_discount']*$current_multiplier), $prec['gt2_rows']);

                // correct the precision
                $reason_gt2_rows[$key_row]['quantity'] = round($reason_row['quantity'], $prec['gt2_quantity']);
                $reason_gt2_rows[$key_row]['discount_percentage'] = round($reason_row['discount_percentage'], $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['surplus_percentage'] = round($reason_row['surplus_percentage'], $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['article_height'] = round($reason_row['article_height'], $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['article_width'] = round($reason_row['article_width'], $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['article_weight'] = round($reason_row['article_weight'], $prec['gt2_rows']);
                $reason_gt2_rows[$key_row]['article_volume'] = round($reason_row['article_volume'], $prec['gt2_rows']);

                // unset the vars that are not needed any longer
                unset($reason_gt2_rows[$key_row]['deliverer']);
                unset($reason_gt2_rows[$key_row]['deliverer_name']);
                unset($reason_gt2_rows[$key_row]['full_num']);
                unset($reason_gt2_rows[$key_row]['doc_type']);
                unset($reason_gt2_rows[$key_row]['project']);
                unset($reason_gt2_rows[$key_row]['project_name']);
                unset($reason_gt2_rows[$key_row]['alternative_project']);
                unset($reason_gt2_rows[$key_row]['alternative_project_name']);
                unset($reason_gt2_rows[$key_row]['payment_type']);
                unset($reason_gt2_rows[$key_row]['payment_date']);
                unset($reason_gt2_rows[$key_row]['currency']);
            }

            $requests_list = array_unique($requests_list);
            $admit_vat_credit_options = array_unique($admit_vat_credit_options);
            $payment_dates_options = array_unique($payment_dates_options);
            $reason = implode(',', $requests_list);

            // prepare the model
            require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.model.php';

            $transform_model = Finance_Expenses_Reasons::buildModel($this->registry);
            $transform_model->set('type', $request->get('expense_type'), true);

            // get default total VAT rate for type (or for installation by default)
            $query = 'SELECT default_vat' . "\n" .
                     'FROM ' . DB_TABLE_TYPES_VAT_OPTIONS . "\n" .
                     'WHERE model="Finance_Expenses_Reason" AND model_type="' . $request->get('expense_type') . '"' . "\n" .
                     '  AND company="0"';
            $total_vat_rate = $this->registry['db']->GetOne($query);
            if ($total_vat_rate === '') {
                $query = 'SELECT value FROM ' . DB_TABLE_FINANCE_VAT_RATES . ' WHERE parent_id=0 AND is_default=1';
                $total_vat_rate = $this->registry['db']->GetOne($query);
            }
            $transform_model->set('total_vat_rate', $total_vat_rate, true);
            $transform_model->set('currency', $main_currency, true);

            $table = $transform_model->getGT2Vars();
            $table['values'] = $reason_gt2_rows;
            $table['rows'] = array_keys($reason_gt2_rows);

            $transform_model->set('grouping_table_2', $table, true);
            $transform_model->calculateGT2();
            $transform_model->set('customer', $customer, true);
            $transform_model->set('customer_name', $customer_name, true);
            $transform_model->set('description', $reason, true);

            $transform_model->set('payment_type', 'bank', true);
            if (count($admit_vat_credit_options) == 1) {
                $transform_model->set('admit_VAT_credit', reset($admit_vat_credit_options), true);
            }
            if (count($payment_dates_options) == 1) {
                $transform_model->set('date_of_payment', reset($payment_dates_options), true);
            }

            // prepare transform params
            $transform_params = array(
                'origin_method' => 'Report',
                'origin_method_id' => $report->get('type'),
                'origin_model'          => array(),
                'origin_id'             => array(),
                'origin_gt2_relations'  => array(),
                'transform_from_report' => true,
            );
            foreach ($gt2_rows_ids as $gt2_inf) {
                $transform_params['origin_model'][] = $gt2_inf['source_model'];
                $transform_params['origin_id'][] = $gt2_inf['model_id'];
                $transform_params['origin_gt2_relations'][$gt2_inf['row_id']] = array(
                    'model'    => $gt2_inf['source_model'],
                    'model_id' => $gt2_inf['model_id']
                );
            }

            $transform_model->set('transform_params', serialize($transform_params), true);
            $transform_model->sanitize();

            $this->registry['session']->set('report_custom_model', serialize($transform_model), $table_session_param, true);

            $this->redirect('finance', 'add', array('type' => $request->get('expense_type'), 'report_session_param' => $table_session_param), 'expenses_reasons');
            exit;
        }
    }

    /**
     * Generating of a report after submit
     */
    protected function _export() {
        // check for pattern set in the request
        $export_pattern = $this->registry['request']->get('pattern');
        $this->registry->set('export_pattern', $export_pattern, true);

        require_once $this->viewersDir . 'reports.export.viewer.php';
        $this->viewer = new Reports_Export_Viewer($this->registry);
        $report = $this->registry->get('report_type');
        $this->viewer->export_template_path = PH_MODULES_DIR . 'reports/plugins/' . $report['name'] . '/custom_export_report.html';
    }
}

?>
