<div class="m_header_m_menu scroll_box_container" style="width: {if $width}{$width}px{else}100%{/if};">
  <div class="scroll_box" id="tab_container_{$custom_id}" style="height: 90px; overflow-x: hidden;">
    {foreach from=$options item='option_f'}
      <div id="{$custom_id}_option_{$option_f.option_value|escape}" class="tab_container_{$custom_id}_options" style="white-space: nowrap;{if $width} width: {$width}px!important;{/if}">
        <input type="checkbox" name="{$name}[]" id="{$custom_id|default:$name}_{$option_f.option_value|escape}" value="{$option_f.option_value|escape}" title="{$option_f.label|strip_tags:false|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if is_array($value) && in_array($option_f.option_value, $value)} checked="checked"{/if} /> <label for="{$custom_id|default:$name}_{$option_f.option_value|escape}">{$option_f.label|escape|default:'&nbsp;'}</label>
      </div>
    {/foreach}
  </div>
</div>