<?php
    Class Fo_Orders_History Extends Reports {
        // One working day in seconds
        public static $working_day_duration = 0;

        public static function buildQuery(&$registry, $filters = array()) {
            // Prepare an array for the final results
            $final_results = array();

            // Require filter "Period"
            if (empty($filters['period_from']) && empty($filters['period_to'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_required_filter_period'));
            } else {
                // Get model lang
                if (empty($filters['model_lang'])) {
                    // Default model language is the interface language
                    $model_lang = $registry['lang'];
                } else {
                    $model_lang = $filters['model_lang'];
                }

                // Prepare the database object
                $db = $registry['db'];

                // Prepare the used fields
                $fields = [
                    FIELD_DATE_REQUEST,
                    FIELD_APPROVAL_DATE,
                    FIELD_OBJECT_ID,
                    FIELD_SERVICE_ID,
                    FIELD_DESCRIPTION_SERVICE
                ];
                $field_region_name = defined('FIELD_REGION_NAME') ? constant('FIELD_REGION_NAME') : '';
                if ($field_region_name) {
                    $fields[] = $field_region_name;
                }
                $query = 'SELECT `name`, `id`' . "\n" .
                         '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                         '  WHERE `model`      = \'Document\'' . "\n" .
                         '    AND `model_type` = \'' . DOCUMENT_TYPE_REQUEST . '\'' . "\n" .
                         '    AND `name`       IN (\'' . implode('\', \'', $fields) . '\')';
                $fields = $db->GetAssoc($query);

                // SQL query for the results
                $query = 'SELECT `d`.`id`     AS `idx`,' . "\n" .
                         '    `d`.`id`        AS `document_id`,' . "\n" .
                         '    `d`.`full_num`  AS `document_num`,' . "\n" .
                         '    `dc2`.`value`   AS `date_request`,' . "\n" .
                         '    `dc4`.`value`   AS `approval_date`,' . "\n" .
                         '    `d`.`customer`  AS `customer_id`,' . "\n" .
                         '    TRIM(CONCAT(`ci18n`.`name`, \' \', `ci18n`.`lastname`)) AS `customer_name`,' . "\n" .
                         '    `dc3`.`value`   AS `object_id`,' . "\n" .
                         '    `ni18n3`.`name` AS `object_name`,' . "\n" .
                         '    `n`.`code`      AS `object_code`,' . "\n" .
                         ($field_region_name ? (
                         '    `dc6`.`value`   AS `region_name`,' . "\n") : '') .
                         '    `dc1`.`value`   AS `service_id`,' . "\n" .
                         '    `ni18n1`.`name` AS `service_name`,' . "\n" .
                         '    `dc5`.`value`   AS `description_service`,' . "\n" .
                         '    `d`.`status`    AS `document_status`,' . "\n" .
                         '    `d`.`substatus` AS `document_substatus`,' . "\n" .
                         '    `ds`.`name`     AS `document_substatus_name`' . "\n" .
                         '  FROM `' . DB_TABLE_DOCUMENTS . '` AS `d`' . "\n" .
                         '  JOIN `' . DB_TABLE_CUSTOMERS_I18N . '` AS `ci18n`' . "\n" .
                         '    ON (`ci18n`.`parent_id` = `d`.`customer`' . "\n" .
                         '      AND `ci18n`.`lang`    = \'' . $model_lang . '\')' . "\n" .
                         '  LEFT JOIN `documents_statuses` AS `ds`' . "\n" .
                         '    ON (`ds`.`id`     = `d`.`substatus`' . "\n" .
                         '      AND `ds`.`lang` = \'' . $model_lang . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc1`' . "\n" .
                         '    ON (`dc1`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc1`.`var_id` = \'' . $fields[FIELD_SERVICE_ID] . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_NOMENCLATURES_I18N . '` AS `ni18n1`' . "\n" .
                         '    ON (`ni18n1`.`parent_id` = `dc1`.`value`' . "\n" .
                         '      AND `ni18n1`.`lang`    = \'' . $model_lang . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc2`' . "\n" .
                         '    ON (`dc2`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc2`.`var_id` = \'' . $fields[FIELD_DATE_REQUEST] . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc3`' . "\n" .
                         '    ON (`dc3`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc3`.`var_id` = \'' . $fields[FIELD_OBJECT_ID] . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_NOMENCLATURES . '` AS `n`' . "\n" .
                         '    ON (`n`.`id` = `dc3`.`value`)' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_NOMENCLATURES_I18N . '` AS `ni18n3`' . "\n" .
                         '    ON (`ni18n3`.`parent_id` = `dc3`.`value`' . "\n" .
                         '      AND `ni18n3`.`lang`    = \'' . $model_lang . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc4`' . "\n" .
                         '    ON (`dc4`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc4`.`var_id` = \'' . $fields[FIELD_APPROVAL_DATE] . '\')' . "\n" .
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc5`' . "\n" .
                         '    ON (`dc5`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc5`.`var_id` = \'' . $fields[FIELD_DESCRIPTION_SERVICE] . '\')' . "\n" .
                         ($field_region_name ? (
                         '  LEFT JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc6`' . "\n" .
                         '    ON (`dc6`.`model_id` = `d`.`id`' . "\n" .
                         '      AND `dc6`.`var_id` = \'' . $fields[$field_region_name] . '\'' . "\n" .
                         '      AND `dc6`.`num` = \'1\'' . "\n" .
                         '      AND `dc6`.`lang` = \'' . $model_lang . '\')' . "\n") : '') .
                         '  WHERE `d`.`type`       = \'' . DOCUMENT_TYPE_REQUEST . '\'' . "\n" .
                         '    AND `d`.`deleted_by` = \'0\'' . "\n" .
                         '    AND `d`.`active`     = \'1\'' . "\n" .
                         '    AND `dc4`.`value`    != \'\'' . "\n" .
                         '    AND `dc4`.`value`    IS NOT NULL';

                // Apply filters
                $sql_where = array();
                if ($filters['period_from'] != '') {
                    $sql_where[] = 'DATE(`dc2`.`value`) >= \'' . $filters['period_from'] . '\'';
                }
                if ($filters['period_to'] != '') {
                    $sql_where[] = 'DATE(`dc2`.`value`) <= \'' . $filters['period_to'] . '\'';
                }
                if ($filters['client'] != '') {
                    $sql_where[] = '`d`.`customer` = \'' . $filters['client'] . '\'';
                }
                if ($filters['object'] != '') {
                    $sql_where[] = '`dc3`.`value` = \'' . $filters['object'] . '\'';
                }
                if ($filters['status'] != '') {
                    if (in_array($filters['status'], array('opened', 'locked', 'closed'))) {
                        $sql_where[] = '`d`.`status` = \'' . $filters['status'] . '\'';
                    } else {
                        $sql_where[] = '`d`.`substatus` = \'' . $filters['status'] . '\'';
                    }
                }
                if (count($sql_where) > 0) {
                    $query .= "\n" . '    AND ' . implode("\n" . '    AND ', $sql_where);
                }

                // Set the order
                $query .= "\n" . '  ORDER BY `d`.`id` ASC';

                // Get results
                $records = $db->GetAssoc($query);

                // If there are any results
                if (count($records) > 0) {
                    // Get the ids of the documents
                    $documents_ids_str = "'" . implode("', '", array_keys($records)) . "'";

                    // Prepare an array for the history of each document from the results
                    $documents_history = array();

                    // Get the history data directly from the history table
                    $query = 'SELECT `dh`.`model_id` AS `document_id`,' . "\n" .
                             '    `dh`.`h_id`        AS `history_id`,' . "\n" .
                             '    `dh`.`h_date`      AS `history_date`,' . "\n" .
                             '    `da`.`field_name`  AS `field_name`,' . "\n" .
                             '    `da`.`field_value` AS `field_value`,' . "\n" .
                             '    `da`.`label`       AS `field_label`' . "\n" .
                             '  FROM `' . DB_TABLE_DOCUMENTS_HISTORY . '` AS `dh`' . "\n" .
                             '  JOIN `' . DB_TABLE_DOCUMENTS_AUDIT . '` AS `da`' . "\n" .
                             '    ON (`da`.`parent_id`    = `dh`.`h_id`' . "\n" .
                             '      AND `da`.`field_name` IN (\'status\', \'substatus\'))' . "\n" .
                             '  WHERE `dh`.`model`    = \'Document\'' . "\n" .
                             '    AND `dh`.`model_id` IN (' . $documents_ids_str . ')' . "\n" .
                             '    AND `dh`.`lang`     = \'' . $model_lang . '\'' . "\n" .
                             '  ORDER BY `dh`.`h_id` ASC';
                    $history_rows = $db->GetAll($query);

                    // Build a multidimensional array
                    // where the first level keys are the documents ids
                    // and the second level keys are the history events dates
                    foreach ($history_rows as $history_row) {
                        // Prepare some variables (i.e. simplify the code)
                        $document_id  = $history_row['document_id'];
                        $history_date = $history_row['history_date'];

                        // If the current history row is the first for his document
                        if (!isset($documents_history[$document_id])) {
                            // Add a custom history row as first
                            $documents_history[$document_id][$records[$document_id]['date_request']] = array('status' => array(
                                'value' => '',
                                'label' => $registry['translater']->translate('reports_history_date_request')));
                        }

                        // Show the current history row only if the current history date is greater than or equal to the approval date
                        if ($history_date >= $records[$document_id]['approval_date']) {
                            // Prepare the array for the new history row
                            if (!isset($documents_history[$document_id][$history_date])) {
                                $documents_history[$document_id][$history_date] = array();
                            }

                            // Set the status or the substatus data for the history row
                            $documents_history[$document_id][$history_date][$history_row['field_name']] = array(
                                'value' => $history_row['field_value'],
                                'label' => $history_row['field_label']);
                        }
                    }

                    // Get the comments
                    $show_nonportal_comments_to_portal_users = (defined('SHOW_NONPORTAL_COMMENTS_TO_PORTAL_USERS') && SHOW_NONPORTAL_COMMENTS_TO_PORTAL_USERS == 1 ? true : false);
                    $query = "
                        SELECT c.model_id                                AS document_id,
                            c.id                                         AS id,
                            c.added                                      AS added,
                            TRIM(CONCAT(ui.firstname, ' ', ui.lastname)) AS user_name,
                            c.subject                                    AS subject,
                            c.content                                    AS content
                          FROM " . DB_TABLE_COMMENTS . " AS c
                          JOIN " . DB_TABLE_USERS_I18N . " AS ui
                            ON (ui.parent_id = c.added_by
                              AND ui.lang = '{$model_lang}')
                          WHERE c.deleted_by = 0
                            AND c.model = 'Document'
                            AND c.model_id IN ({$documents_ids_str})" .
                            ($registry['currentUser']->get('is_portal') && !$show_nonportal_comments_to_portal_users ? "
                            AND c.is_portal = 1" : '') . "
                          ORDER BY c.id DESC";
                    $comments = $db->GetAll($query);
                    $documents_comments = array();
                    foreach ($comments AS $c) {
                        $documents_comments[$c['document_id']][$c['id']] = $c;
                    }

                    // Get the substatuses for status "closed"
                    $report_filters = $registry->get('report_filters');
                    $status_options = $report_filters['status']['options'];
                    $is_closed_substatus = false;
                    $closed_substatuses = array();
                    foreach ($status_options as $status_option) {
                        if ($is_closed_substatus) {
                            $closed_substatuses[] = $status_option['option_value'];
                        }
                        if ($status_option['option_value'] == 'closed') {
                            $is_closed_substatus = true;
                        }
                    }

                    // Calculate the working time using the current date
                    if (empty(self::$working_day_duration)) {
                        $working_time_start         = strtotime(sprintf('%s %s:00', date('Y-m-d'), WORKING_TIME_STARTS));
                        $working_time_end           = strtotime(sprintf('%s %s:00', date('Y-m-d'), WORKING_TIME_ENDS));
                        self::$working_day_duration = $working_time_end - $working_time_start;
                    }

                    // Prepare the current date time
                    $current_date_time = General::strftime('%Y-%m-%d %H:%M:%S', time());

                    // Get the duration format
                    $duration_format = $registry['translater']->translate('reports_duration_format');

                    // Load the Calendar class
                    require_once(PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php');

                    // Go through each result (i.e. each document)
                    foreach ($records as $record_key => $record) {
                        // Format the date
                        $records[$record_key]['date_request'] = General::strftime('%d.%m.%Y', $record['date_request']);

                        // Set the status name
                        $records[$record_key]['document_status_name'] = sprintf($registry['translater']->translate('documents_status_' . $record['document_status']));

                        // Prepare for history
                        $records[$record_key]['history'] = array();

                        // Calculate the total history rows count
                        $history_rows_count = count($documents_history[$record['document_id']]);

                        // Prepare a variable, used to check which is the current history row number
                        $current_history_row = 0;

                        // Prepare a history rows counter
                        $history_rows_counter = 0;

                        // Prepare a flag to check if a given row is the last for the current document history
                        $is_last_history_row = false;

                        // Prepare a variable, which will be used to remember the previous history row date
                        $prev_document_history_date = '';

                        // Prepare variable for calculating the total duration for the current document
                        $document_history_total_duration = 0;

                        // Go through each history row of the current document
                        foreach ($documents_history[$record['document_id']] as $document_history_date => $document_history) {
                            // Check if this is the last history row
                            $history_rows_counter++;
                            if ($history_rows_counter == $history_rows_count) {
                                $is_last_history_row = true;
                            }

                            // Check if this is a "closed" status or substatus
                            $is_closed = false;
                            if (isset($document_history['status']['value']) && $document_history['status']['value'] == 'closed'
                                    || isset($document_history['substatus']['value']) && in_array($document_history['substatus']['value'], $closed_substatuses)) {
                                $is_closed = true;
                            }

                            // If the status or the substatus is NOT "closed" or this is the last history row
                            if (!$is_closed || $is_last_history_row) {
                                // Calculate the current history row number
                                $current_history_row++;

                                // Prepare the status for the current history row
                                if ($is_closed) {
                                    $status = $registry['translater']->translate('reports_history_status_closed');
                                } elseif (!empty($document_history['substatus']['label']) && $document_history['substatus']['label'] != '-') {
                                    $status = $document_history['substatus']['label'];
                                } elseif (!empty($document_history['status']['label'])) {
                                    $status = $document_history['status']['label'];
                                } else {
                                    $status = '';
                                }

                                // The default value for time of the current history row is empty string ''
                                $time = '';

                                // If this is not the first history row
                                if ($current_history_row > 1) {
                                    // Calculate the time for the previous history row
                                    $current_history_row_duration = self::calcDuration($registry, $prev_document_history_date, $document_history_date);
                                    $records[$record_key]['history'][$current_history_row-2]['time'] = sprintf('%s (%s)',
                                        General::strftime('%d.%m.%Y, %H:%M', $prev_document_history_date),
                                        self::formatDuration($duration_format, $current_history_row_duration));

                                    // Add this duration to the total duration of the current document
                                    $document_history_total_duration += $current_history_row_duration;
                                }

                                // If this is the last history row
                                if ($is_last_history_row) {
                                    // If the status is "closed"
                                    if ($is_closed) {
                                        // Do not calculate the duration
                                        $time = General::strftime('%d.%m.%Y, %H:%M', $document_history_date);
                                    } else {
                                        // Calculate the time for the current history row
                                        $current_history_row_duration = self::calcDuration($registry, $document_history_date, $current_date_time);
                                        $time = sprintf('%s (%s)',
                                            General::strftime('%d.%m.%Y, %H:%M', $document_history_date),
                                            self::formatDuration($duration_format, $current_history_row_duration));
                                    }

                                    // Add this duration to the total duration of the current document
                                    $document_history_total_duration += $current_history_row_duration;
                                }

                                // Set the history row for the current document
                                $records[$record_key]['history'][] = array(
                                    'status' => $status,
                                    'time'   => $time);

                                // Set the current history row date as previous one
                                $prev_document_history_date = $document_history_date;
                            }
                        }

                        // Set the total duration for the current document
                        $records[$record_key]['history_total_duration'] = self::formatDuration($duration_format, $document_history_total_duration);

                        // Set the comments for this document
                        $records[$record_key]['comments'] = (isset($documents_comments[$record['document_id']]) ? $documents_comments[$record['document_id']] : array());
                    }

                    // Set the records as final results
                    $final_results = $records;
                }
            }

            $final_results['additional_options']['has_field_region_name'] = !empty($field_region_name);

            // Prepare the results
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Calculate the working time (in seconds) between two dates
         *
         * @param mixed $registry         - the main system registry
         * @param string $start_date_time - start date (format: Y-m-d H:i:s)
         * @param string $end_date_time   - end date   (format: Y-m-d H:i:s)
         * @return number                 - the working time (in seconds) between the start date and the end date
         */
        public static function calcDuration(&$registry, $start_date_time, $end_date_time) {
            // Default duration is zero
            $duration = 0;

            // Convert the start and the end date time into Unix timestamp (i.e. into seconds)
            $start_date_time = strtotime($start_date_time);
            $end_date_time   = strtotime($end_date_time);

            // Get the start and the end dates
            $start_date = General::strftime('%Y-%m-%d', $start_date_time);
            $end_date   = General::strftime('%Y-%m-%d', $end_date_time);

            // If the time of the start date is before the first posible working time
            // (for example, if the time of the start date is 07:00, and the first posible working time is 09:00)
            $start_date_first_working_time = strtotime(sprintf('%s %s:00', $start_date, WORKING_TIME_STARTS));
            if ($start_date_time < $start_date_first_working_time) {
                // Set the time of the start date to be the first posible working time
                $start_date_time = $start_date_first_working_time;
            }

            // If the time of the end date is after the last posible working time
            // (for example, if the time of the end date is 20:00, and the last posible working time is 18:00)
            $end_date_first_working_time = strtotime(sprintf('%s %s:00', $end_date, WORKING_TIME_ENDS));
            if ($end_date_time > $end_date_first_working_time) {
                // Set the time of the end date to be the last posible working time
                $end_date_time = $end_date_first_working_time;
            }

            // Basic check: the start date should be before the end date
            if ($start_date_time < $end_date_time) {
                // Get the count of the working days into the current period
                $working_days_count = Calendars_Calendar::getWorkingDays($registry, $start_date, $end_date);

                // If there are any working days into the period
                if ($working_days_count > 0) {
                    $country_code = Calendars_Calendar::getCountryCode($registry);
                    // Get the nonworking dates for the current period
                    $query = 'SELECT `date`' . "\n" .
                             '  FROM `' . DB_TABLE_COUNTRY_NONWORKDAYS . '`' . "\n" .
                             '  WHERE `date`    >= \'' . $start_date . '\'' . "\n" .
                             '    AND `date`    <= \'' . $end_date . '\' ' . "\n" .
                             '    AND `country` = \'' . $country_code . '\'';
                    $nonworking_days = $registry['db']->GetCol($query);

                    // If the start date is equal to the end date
                    if ($start_date == $end_date) {
                        // The duration can be directly calculated for this date
                        $duration = $end_date_time - $start_date_time;
                    } else {
                        // For now, each day is worked all day
                        $full_days_working_count = $working_days_count;

                        // Start date duration (this will have value only if the start date is a working day)
                        $start_date_duration = 0;
                        // If the start date is a working day
                        if (!in_array($start_date, $nonworking_days)) {
                            // Separate the start date from the full day worked days
                            $full_days_working_count--;

                            // Calculate the duration for the start day
                            $start_date_duration = strtotime(sprintf('%s %s:00', $start_date, WORKING_TIME_ENDS)) - $start_date_time;

                            // If the time of the start date is after the time when the working day ends
                            if ($start_date_duration < 0) {
                                // The duration for the start date is zero
                                $start_date_duration = 0;
                            }
                        }

                        // End date duration (this will have value only if the end date is a working day)
                        $end_date_duration = 0;
                        if (!in_array($end_date, $nonworking_days)) {
                            // Separate the start date from the full day worked days
                            $full_days_working_count--;

                            // Calculate the duration for the end day
                            $end_date_duration   = $end_date_time - strtotime(sprintf('%s %s:00', $end_date, WORKING_TIME_STARTS));

                            // If the time of the end date is before the time when the working day starts
                            if ($end_date_duration < 0) {
                                // The duration for the end date is zero
                                $end_date_duration = 0;
                            }
                        }

                        // Get the seconds for one working day
                        $working_day_duration = self::$working_day_duration;

                        // Calculate the duration for the full day working days
                        $full_days_working_duration = $full_days_working_count * $working_day_duration;

                        // Calculate the total duration for the period
                        $duration = $full_days_working_duration + $start_date_duration + $end_date_duration;
                    }
                }
            }

            return $duration;
        }

        /**
         * Format a duration
         *
         * @param string $format   - string format in which the duration should be formated
         * @param number $duration - the duration (in seconds)
         * @return string          - the formated duration
         */
        public static function formatDuration($format, $duration) {
            // Get the working day in seconds
            $working_day_duration = self::$working_day_duration;

            // Format the duration
            $duration_formated = sprintf($format,
                // Calculate: days
                floor($duration / $working_day_duration),
                // Calculate: hours
                floor(($duration % $working_day_duration) / 3600),
                // Calculate: minutes
                floor((($duration % $working_day_duration) % 3600) / 60));

            return $duration_formated;
        }
    }
?>
