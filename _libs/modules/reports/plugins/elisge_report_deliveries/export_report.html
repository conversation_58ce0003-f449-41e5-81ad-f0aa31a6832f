<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td nowrap="nowrap" width="25">{#num#|escape}</td>
          <td nowrap="nowrap">{#reports_customer#|escape}</td>
          <td nowrap="nowrap">{#reports_article_name#|escape}</td>
          <td nowrap="nowrap">{#reports_article_measure#|escape}</td>
          <td nowrap="nowrap">{#reports_contract_quantity#|escape}</td>
          <td nowrap="nowrap">{#reports_delivered_quantity#|escape}</td>
          <td nowrap="nowrap">{#reports_left_quantity#|escape}</td>
          <td nowrap="nowrap">{#reports_protocols#|escape}</td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td valign="top">
              {counter name='item_counter' print=true}
            </td>
            <td nowrap="nowrap">
              {$result.customer|escape|default:"&nbsp;"}
            </td>
            <td nowrap="nowrap" width="500" valign="top">
              {$result.article_name|escape|default:"&nbsp;"}
            </td>
            <td valign="top">
              {$result.measure|escape|default:"&nbsp;"}
            </td>
            <td valign="top">
              {$result.amount_contract|default:"0"}
            </td>
            <td valign="top">
              {$result.amount_delivered|default:"0"}
            </td>
            <td valign="top">
              {$result.amount_left|default:"0"}
            </td>
            <td style="mso-number-format:\@">
              {assign var='documents' value=$result.documents}
              {foreach from=$documents item='document'}
                {$document.full_num|numerate:$document.direction|default:"&nbsp;"}<br />
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="8">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
      </table>
  </body>
</html>