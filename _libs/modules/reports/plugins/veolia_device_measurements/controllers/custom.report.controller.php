<?php

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Setttings of the report
     */
    public $report_class = '';
    public $filters = array();
    public $reportSettings = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'custom_export':
                $this->_customExport();
                break;
            default:
                parent::execute();
        }
    }

    /**
     * Saves activities schedule document
     */
    private function _customExport() {

        /** @var Registry $registry */
        $registry = &$this->registry;

        // get current report
        $report = $this->getReportModel();
        $this->reportSettings = Reports::getReportSettings($registry);

        $i18n_files = array();
        // load plugin i18n files
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->registry['translater']->loadFile($i18n_files);

        /** @var Activities_Schedule $report_class */
        $this->report_class = Reports::getPluginFactory($report->get('type'));
        $this->filters = $this->registry['session']->get('reports_' . $report->get('type') . '_report');

        // prepare report settings
        Reports::getReportSettings($registry, $report->get('type'));

        if ($registry['request']->get('export_type') == 'main_devices') {
            $data = $this->prepareMainDevicesExport();
            $doc_name = $this->i18n('reports_export_main_devices');
            $file_name = 'main_devices.xlsx';
            $spreadsheet_title = 'Main devices';
        } elseif ($registry['request']->get('export_type') == 'individual_devices') {
            $data = $this->prepareIndividualDevicesExport();
            $doc_name = $this->i18n('reports_export_individual_devices');
            $file_name = 'individual_devices.xlsx';
            $spreadsheet_title = 'Individual devices';
        }

        $spreadsheet = new Spreadsheet();

        $spreadsheet->getProperties()
            ->setCreator('BG Service')
            ->setLastModifiedBy('BG Service')
            ->setTitle($doc_name)
            ->setSubject($doc_name);
        $spreadsheet->setActiveSheetIndex(0)->setTitle($spreadsheet_title);

        $row = 1;
        foreach ($data as $data_row) {
            $column = 0;
            foreach ($data_row as $row_dat) {
                $xls_column = $this->excelNumbertoColumn($column);
                $spreadsheet->getActiveSheet()
                            ->getCell($xls_column . $row)
                            ->setValueExplicit(
                                strval($row_dat),
                                \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING2
                            );

/*
                // PASS NUMBERS AS REAL NUMBERS BUT LEFT ALIGNED
                $spreadsheet->setActiveSheetIndex(0)
                    ->getStyle($xls_column . $row)
                    ->getNumberFormat()
                    ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);
                $spreadsheet->setActiveSheetIndex(0)
                    ->setCellValue($xls_column . $row, strval($row_dat));*/
                $column++;
            }
            $row++;
        }

        // Redirect output to a client’s web browser (Xlsx)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $file_name . '"');
        header('Cache-Control: max-age=0');

        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0

        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * Function that prepares the export for main devices
     */
    public function prepareMainDevicesExport() : array
    {
        $data = array();

        $results = $this->report_class::buildQuery($this->registry, $this->filters);
        $temp_data = $results['results'];

        // get the stations data
        $stations_data = $this->getStationsMainDeviceAdditionalData(array_column($temp_data, 'toStation_id'), true);
        $devices_data = $this->getMainDevicesAdditionalData(array_keys($temp_data));

        $index = 0;
        foreach ($temp_data as $tmp_id => $tmp) {
            $service_type = '';
            $device_type = '';
            if ($tmp['type'] == DEVICE_TYPE_MAIN_HEATMETER) {
                $service_type = $this->i18n('report_service_type_heatmeter');
                $device_type = $this->i18n('report_device_type_heatmeter');
            } elseif ($tmp['type'] == DEVICE_TYPE_MAIN_WATERMETER_BGV) {
                $service_type = $this->i18n('report_service_type_watermeter_bgv');
                $device_type = $this->i18n('report_device_type_watermeter');
            } elseif ($tmp['type'] == DEVICE_TYPE_MAIN_WATERMETER_ADDITIONAL) {
                $service_type = $this->i18n('report_service_type_watermeter_additional');
                $device_type = $this->i18n('report_device_type_watermeter');
            }

            $new_indication_date = $tmp['indicationDate'] ? new DateTime($tmp['indicationDate']) : '';
            $old_indication_date = isset($devices_data[$tmp_id]) && !empty($devices_data[$tmp_id]['indication_old']) ? new DateTime($devices_data[$tmp_id]['indication_old']) : '';

            $masterDevice = '';
            if (isset($devices_data[$tmp_id])) {
                if (!empty($this->reportSettings['useNzoomIdForMainExport'])) {
                    $masterDevice = $tmp_id;
                } elseif (isset($devices_data[$tmp_id])) {
                    $masterDevice = $devices_data[$tmp_id]['psiro_code'];
                }
            }

            $data[$tmp_id] = array(
                'index'                       => ++$index,
                'station_name'                => $tmp['toStation'],
                'station_code'                => isset($stations_data[$tmp['toStation_id']]) ? $stations_data[$tmp['toStation_id']]['psiro_code'] : '',
                'entrance'                    => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['entrance'] : '',
                'code'                        => $tmp['name'],
                'type_name'                   => $device_type,
                'units'                       => $tmp['type'] == DEVICE_TYPE_MAIN_HEATMETER ? $this->i18n('report_measurement_unit_heatmeter') : $this->i18n('report_measurement_unit_watermeter'),
                'service_type'                => $service_type,
                'master_device'               => $masterDevice,
                'indication_old_date'         => $old_indication_date ? $old_indication_date->format('d/m/Y') . ' 17:00' : '',
                'indication_date'             => $new_indication_date ? $new_indication_date->format('d/m/Y') . ' 17:00' : '',
                'indication_old'              => strval($tmp['indicationOld']),
                'indication_new'              => strval($tmp['indicationNew']),
                'working_hours_st'            => isset($stations_data[$tmp['toStation_id']]) ? $stations_data[$tmp['toStation_id']]['working_hours_st'] : '',
                'working_hours_bgv'           => isset($stations_data[$tmp['toStation_id']]) ? $stations_data[$tmp['toStation_id']]['working_hours_bgv'] : '',
                'working_hours_wtr'           => isset($stations_data[$tmp['toStation_id']]) ? $stations_data[$tmp['toStation_id']]['working_hours_wtr'] : '',
                'working_hours_tec'           => isset($stations_data[$tmp['toStation_id']]) ? $stations_data[$tmp['toStation_id']]['working_hours_tec'] : '',
                'working_hours_wo_device'     => $devices_data[$tmp_id]['non_working_hours'],
                'water_temp'                  => $tmp['type'] == DEVICE_TYPE_MAIN_WATERMETER_BGV ? 55 : '',
                'notes'                       => '',
                'measure_type'                => '',
                'specific_expense_curr_month' => round(floatval($stations_data[$tmp['toStation_id']]['expense_current_month']), 2),
                'specific_expense_prev_month' => $stations_data[$tmp['toStation_id']]['expense_previous_month']
            );
        }

        // prepare the labels
        $first_row = reset($data);
        $first_row = array_keys($first_row);
        $labels = array();
        for ($i=0; $i<count($first_row); $i++) {
            $labels[$first_row[$i]] = $this->i18n('reports_main_devices_export_col_' . ($i+1));
        }
        $data = array($labels) + $data;

        return $data;
    }

    /**
     * Function that will get the needed data for stations
     */
    public function getStationsMainDeviceAdditionalData($stations_list, $get_expenses = false) : array
    {
        $stations_vars = array('psiro_code', 'fdr_id', 'fts_responsible_id');
        $tech_expenses_vars = array(
            'to_station_id', 'working_hours', 'working_hours_heat',
            'working_hours_water', 'tech_costs_value', 'period_month',
            'period_year'
        );

        $sql = "SELECT `name`, `id`, `multilang` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Nomenclature' AND `model_type` = " . NOM_TYPE_STATION .
               " AND `name` IN ('" . implode("','", $stations_vars) . "')" . "\n";
        $stations_vars = $this->registry['db']->GetAssoc($sql);

        $sql = "SELECT `name`, `id`, `multilang` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `model_type` = " . DOC_TYPE_EXPENSE .
               " AND `name` IN ('" . implode("','", $tech_expenses_vars) . "')" . "\n";
        $tech_expenses_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, n_cstm_ps.value as psiro_code, CONCAT(ci_fdr.name, " ", ci_fdr.lastname) as fdr, CONCAT(ci_fts.name, " ", ci_fts.lastname) as fts' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_ps' . "\n" .
               ' ON (n_cstm_ps.model_id=n.id AND n_cstm_ps.var_id="' . $stations_vars['psiro_code']['id'] . '" AND n_cstm_ps.num=1 AND n_cstm_ps.lang="' . ($stations_vars['psiro_code']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_fdr' . "\n" .
               ' ON (n_cstm_fdr.model_id=n.id AND n_cstm_fdr.var_id="' . $stations_vars['fdr_id']['id'] . '" AND n_cstm_fdr.num=1 AND n_cstm_fdr.lang="' . ($stations_vars['fdr_id']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci_fdr' . "\n" .
               ' ON (ci_fdr.parent_id=n_cstm_fdr.value AND ci_fdr.lang="' . $this->registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_fts' . "\n" .
               ' ON (n_cstm_fts.model_id=n.id AND n_cstm_fts.var_id="' . $stations_vars['fts_responsible_id']['id'] . '" AND n_cstm_fts.num=1 AND n_cstm_fts.lang="' . ($stations_vars['fts_responsible_id']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci_fts' . "\n" .
               ' ON (ci_fts.parent_id=n_cstm_fts.value AND ci_fts.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE n.id IN ("' . implode('","', $stations_list) . '")';
        $stations_info = $this->registry['db']->GetAssoc($sql);

        $station_data = array();
        foreach ($stations_info as $si_id => $si) {
            $station_data[$si_id] = array(
                'id'                => $si_id,
                'psiro_code'        => $si['psiro_code'],
                'fdr'               => $si['fdr'],
                'fts'               => $si['fts'],
                'working_hours_st'  => 0,
                'working_hours_bgv' => 0,
                'working_hours_wtr' => 0,
                'working_hours_tec' => 0,
                'expense_current_month' => 0,
                'expense_previous_month' => 0,
            );
        }

        if (!$get_expenses) {
            return $station_data;
        }

        // process filters
        $end_date = new DateTime($this->filters['measure_date_to']);

        $sql = 'SELECT d_cstm_st.value as station, d_cstm_wh.value as working_hours, d_cstm_whh.value as working_hours_heat, ' . "\n" .
               '       d_cstm_whw.value as working_hours_water, d_cstm_tc.value as tech_costs' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pm' . "\n" .
               ' ON (d_cstm_pm.model_id=d.id AND d_cstm_pm.var_id="' . $tech_expenses_vars['period_month']['id'] . '" AND d_cstm_pm.num=1 AND d_cstm_pm.lang="' . ($tech_expenses_vars['period_month']['multilang'] ? $this->registry['lang'] : '') . '"
                     AND d_cstm_pm.value="' . $end_date->format('n') . '"
                     AND d.type="' . DOC_TYPE_EXPENSE . '" AND d.active=1 AND d.deleted_by=0 AND d.status="closed" AND d.substatus="0")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_py' . "\n" .
               ' ON (d_cstm_py.model_id=d.id AND d_cstm_py.var_id="' . $tech_expenses_vars['period_year']['id'] . '" AND d_cstm_py.num=1 AND d_cstm_py.lang="' . ($tech_expenses_vars['period_year']['multilang'] ? $this->registry['lang'] : '') . '"
                     AND d_cstm_py.value="' . $end_date->format('Y') . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_st' . "\n" .
               ' ON (d_cstm_st.model_id=d.id AND d_cstm_st.var_id="' . $tech_expenses_vars['to_station_id']['id'] . '" AND d_cstm_st.lang="' . ($tech_expenses_vars['to_station_id']['multilang'] ? $this->registry['lang'] : '') . '"
                     AND d_cstm_st.value IN ("' . implode('","', array_keys($station_data)) . '"))' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_wh' . "\n" .
               ' ON (d_cstm_wh.model_id=d.id AND d_cstm_wh.var_id="' . $tech_expenses_vars['working_hours']['id'] . '" AND d_cstm_wh.num=d_cstm_st.num AND d_cstm_wh.lang="' . ($tech_expenses_vars['working_hours']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_whh' . "\n" .
               ' ON (d_cstm_whh.model_id=d.id AND d_cstm_whh.var_id="' . $tech_expenses_vars['working_hours_heat']['id'] . '" AND d_cstm_whh.num=d_cstm_st.num AND d_cstm_whh.lang="' . ($tech_expenses_vars['working_hours_heat']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_whw' . "\n" .
               ' ON (d_cstm_whw.model_id=d.id AND d_cstm_whw.var_id="' . $tech_expenses_vars['working_hours_water']['id'] . '" AND d_cstm_whw.num=d_cstm_st.num AND d_cstm_whw.lang="' . ($tech_expenses_vars['working_hours_water']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tc' . "\n" .
               ' ON (d_cstm_tc.model_id=d.id AND d_cstm_tc.var_id="' . $tech_expenses_vars['tech_costs_value']['id'] . '" AND d_cstm_tc.num=d_cstm_st.num AND d_cstm_tc.lang="' . ($tech_expenses_vars['tech_costs_value']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n";
        $working_hours_per_station = $this->registry['db']->GetAll($sql);

        foreach ($working_hours_per_station as $whs) {
            if (!isset($station_data[$whs['station']])) {
                continue;
            }
            $station_data[$whs['station']]['working_hours_st'] = $whs['working_hours'];
            $station_data[$whs['station']]['working_hours_bgv'] = $whs['working_hours_heat'];
            $station_data[$whs['station']]['working_hours_wtr'] = $whs['working_hours_water'];
            $station_data[$whs['station']]['working_hours_tec'] = $whs['tech_costs'];
        }
        $station_data = $this->processExpenses(clone $end_date, $station_data, 'expense_current_month');
        $prev_month_date = clone $end_date;
        $prev_month_date->modify('last day of previous month');
        $station_data = $this->processExpenses(clone $prev_month_date, $station_data, 'expense_previous_month');

        return $station_data;
    }

    /**
     * Function that will get the needed data for devices
     */
    public function getMainDevicesAdditionalData($devices_ids) : array
    {
        $devices_vars = array('psiro_code', 'entrance_id', 'indication_old_date', 'to_object_id', 'nom_status');

        $sql = "SELECT `name`, GROUP_CONCAT(`id`) FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Nomenclature' AND `name` IN ('" . implode("','", $devices_vars) . "')" . "\n" .
               "GROUP BY `name`";
        $devices_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, n_cstm_ps.value as psiro_code, n_cstm_indc.value as indication_old, n_cstm_entr_nm.name as entrance, ' . "\n" .
               '       0 as non_working_hours, n_cstm_status.value as status' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_ps' . "\n" .
               ' ON (n_cstm_ps.model_id=n.id AND n_cstm_ps.var_id IN (' . $devices_vars['psiro_code'] . ') AND n_cstm_ps.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_entr' . "\n" .
               ' ON (n_cstm_entr.model_id=n.id AND n_cstm_entr.var_id IN (' . $devices_vars['entrance_id'] . ') AND n_cstm_entr.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS n_cstm_entr_nm' . "\n" .
               ' ON (n_cstm_entr_nm.parent_id=n_cstm_entr.value AND n_cstm_entr_nm.lang="' . $this->registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_indc' . "\n" .
               ' ON (n_cstm_indc.model_id=n.id AND n_cstm_indc.var_id IN (' . $devices_vars['indication_old_date'] . ') AND n_cstm_indc.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_status' . "\n" .
               ' ON (n_cstm_status.model_id=n.id AND n_cstm_status.var_id IN (' . $devices_vars['nom_status'] . ') AND n_cstm_status.num=1)' . "\n" .
               'WHERE n.id IN ("' . implode('","', $devices_ids) . '")';
        $devices_info = $this->registry['db']->GetAssoc($sql);

        /*
         * CALCULATE NON WORKING HOURS
         */
        $protocol_vars = array('protocolaction_type', 'device_id');

        $sql = "SELECT `name`, GROUP_CONCAT(`id`) FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `name` IN ('" . implode("','", $protocol_vars) . "') AND `model_type` IN (" . DOC_TYPE_PROTOCOLS_EXPLOITATION . ")" . "\n" .
               "GROUP BY `name`";
        $protocol_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT d.id, d.date, d_dev.value as device, d_action.value as action' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_dev' . "\n" .
               ' ON (d_dev.model_id=d.id AND d_dev.var_id IN (' . $protocol_vars['device_id'] . ') AND d_dev.num=1 AND d_dev.value IN ("' . implode('","', $devices_ids) . '"))' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_action' . "\n" .
               ' ON (d_action.model_id=d.id AND d_action.var_id IN (' . $protocol_vars['protocolaction_type'] . ') AND d_action.num=1)' . "\n" .
               'WHERE d.type IN (' . DOC_TYPE_PROTOCOLS_EXPLOITATION . ') AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $this->filters['measure_date_from'] . '" AND d.date<="' . $this->filters['measure_date_to'] . '"' . "\n" .
               'ORDER BY d.date ASC';
        $protocols_info = $this->registry['db']->GetAll($sql);

        $protocols_data = array();
        foreach ($protocols_info as $prot) {
            if (!isset($protocols_data[$prot['device']])) {
                $protocols_data[$prot['device']][$this->filters['measure_date_from']] = !($prot['action'] != '1');
                $end_date = new DateTime($this->filters['measure_date_to']);
                $end_date->add(new DateInterval('P1D'));
                $protocols_data[$prot['device']][$end_date->format('Y-m-d')] = ($devices_info[$prot['device']]['status'] == '1');
            }
            $protocols_data[$prot['device']][$prot['date']] = ($prot['action'] != '1');
            ksort($protocols_data[$prot['device']]);
        }

        // calculate the non working hours
        $start_date = new DateTime($this->filters['measure_date_from']);
        $end_date = new DateTime($this->filters['measure_date_to']);
        $end_date->add(new DateInterval('P1D'));
        $interval = $start_date->diff($end_date)->days;

        foreach ($devices_info as $k => $dev_inf) {
            if (isset($protocols_data[$k])) {
                // process the exploitation protocols
                $prev_state = array();
                foreach ($protocols_data[$k] as $prot_date => $prot_state) {
                    if (empty($prev_state)) {
                        $prev_state = array(
                            'date'  => $prot_date,
                            'state' => $prot_state
                        );
                        continue;
                    }
                    if (!$prev_state['state']) {
                        $period_start = new DateTime($prev_state['date']);
                        $period_end = new DateTime($prot_date);
                        $period_interval = $period_start->diff($period_end)->days;
                        $devices_info[$k]['non_working_hours'] += $period_interval*24;
                    }
                    $prev_state = array(
                        'date'  => $prot_date,
                        'state' => $prot_state
                    );
                }
            } elseif ($dev_inf['status'] != '1')  {
                $devices_info[$k]['non_working_hours'] = $interval * 24;
            }
        }

        return $devices_info;
    }

    /**
     * Function that will get the needed data for devices
     */
    public function getIndividualDevicesAdditionalData($devices_ids) : array
    {
        $devices_vars = array('psiro_code', 'prescriptions_issued_id', 'to_object_id', 'radiator_id',
                              'metrological_suitability_date', 'hollander_number', 'power_heating_capacity',
                              'appliances_report_type', 'indication_doc_id', 'heating_element_description');

        $sql = "SELECT `name`, GROUP_CONCAT(`id`) FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Nomenclature' AND `name` IN ('" . implode("','", $devices_vars) . "')" . "\n" .
               "GROUP BY `name`";
        $devices_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, n.type, n_cstm_ps.value as psiro_code, n_cstm_app_rep.value as appliances_report_type,' . "\n" .
               '       ni18n.name as device_name, n_cstm_ind_doc.value as indication_doc, "" as indication_source,' . "\n" .
               '       n_cstm_rad.value as radiator, n_cstm_holl.value as hollander_number, n_cstm_pwr.value as power,' . "\n" .
               '       DATE_FORMAT(n_cstm_metr.value, "%Y") as metrology_date, n_cstm_desc.value as description,' . "\n" .
               '       d_perscr.date as perscription_date' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_ps' . "\n" .
               ' ON (n_cstm_ps.model_id=n.id AND n_cstm_ps.var_id IN (' . $devices_vars['psiro_code'] . ') AND n_cstm_ps.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_app_rep' . "\n" .
               ' ON (n_cstm_app_rep.model_id=n.id AND n_cstm_app_rep.var_id IN (' . $devices_vars['appliances_report_type'] . ') AND n_cstm_app_rep.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_metr' . "\n" .
               ' ON (n_cstm_metr.model_id=n.id AND n_cstm_metr.var_id IN (' . $devices_vars['metrological_suitability_date'] . ') AND n_cstm_metr.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_ind_doc' . "\n" .
               ' ON (n_cstm_ind_doc.model_id=n.id AND n_cstm_ind_doc.var_id IN (' . $devices_vars['indication_doc_id'] . ') AND n_cstm_ind_doc.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_rad' . "\n" .
               ' ON (n_cstm_rad.model_id=n.id AND n_cstm_rad.var_id IN (' . $devices_vars['radiator_id'] . ') AND n_cstm_rad.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_holl' . "\n" .
               ' ON (n_cstm_holl.model_id=n.id AND n_cstm_holl.var_id IN (' . $devices_vars['hollander_number'] . ') AND n_cstm_holl.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_pwr' . "\n" .
               ' ON (n_cstm_pwr.model_id=n.id AND n_cstm_pwr.var_id IN (' . $devices_vars['power_heating_capacity'] . ') AND n_cstm_pwr.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_desc' . "\n" .
               ' ON (n_cstm_desc.model_id=n.id AND n_cstm_desc.var_id IN (' . $devices_vars['heating_element_description'] . ') AND n_cstm_desc.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_perscr' . "\n" .
               ' ON (n_cstm_perscr.model_id=n.id AND n_cstm_perscr.var_id IN (' . $devices_vars['prescriptions_issued_id'] . ') AND n_cstm_perscr.num=1)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d_perscr' . "\n" .
               ' ON (d_perscr.id=n_cstm_perscr.value)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE n.id IN ("' . implode('","', $devices_ids) . '")';
        $devices_info = $this->registry['db']->GetAssoc($sql);

        // get heatspreaders
        $individual_heaters = array(DEVICE_TYPE_HEATSPREADER, DEVICE_TYPE_RADIATOR);
        $heatspreaders = array_combine(array_keys($devices_info), array_column($devices_info, 'type'));
        $heatspreaders = array_keys($heatspreaders, DEVICE_TYPE_HEATSPREADER);
        $readiators_extra_info = array();
        foreach ($heatspreaders as $heat_k) {
            $readiators_extra_info[] = $devices_info[$heat_k]['radiator'];
        }
        $readiators_extra_info = array_filter($readiators_extra_info);
        if (!empty($readiators_extra_info)) {
            $sql = 'SELECT n.id, n_cstm_desc.value as description' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_desc' . "\n" .
                   ' ON (n_cstm_desc.model_id=n.id AND n_cstm_desc.var_id IN (' . $devices_vars['heating_element_description'] . ') AND n_cstm_desc.num=1)' . "\n" .
                   'WHERE n.id IN ("' . implode('","', $readiators_extra_info) . '")';
            $readiators_extra_info = $this->registry['db']->GetAssoc($sql);
        }

        // get indication docs
        $indication_docs = $this->getIndicationDocsInfo(array_column($devices_info, 'indication_doc'));
        $device_protocols = $this->getDeviceProtocols(array_keys($devices_info));

        $radiators_data = array();
        $radiators_list = array();
        $radiators_tmp = array_combine(array_keys($devices_info), array_column($devices_info, 'type'));
        foreach ($individual_heaters as $heat_tp) {
            $radiators_list = array_merge($radiators_list, array_keys($radiators_tmp, $heat_tp));
        }

        foreach ($radiators_list as $dev_id) {
            $radiators_data[$dev_id] = $devices_info[$dev_id]['device_name'];
        }
        $radiators_states = $this->getRadiatorStates($radiators_data);

        foreach ($devices_info as $dev_id => $dev_inf) {
            $power = '';
            $condition = '';
            if (in_array($dev_inf['type'], $individual_heaters)) {
                $power = (!empty($dev_inf['power']) ? $dev_inf['power'] : 0);
            }
            $devices_info[$dev_id]['power'] = $power;
            if ($dev_inf['type'] == DEVICE_TYPE_HEATSPREADER) {
                $devices_info[$dev_id]['description'] = (!empty($readiators_extra_info[$dev_inf['radiator']]) ? $readiators_extra_info[$dev_inf['radiator']] : '');
            }

            if (in_array($dev_inf['type'], $individual_heaters)) {
                $condition = $this->i18n('reports_device_type_heater');
            }
            $devices_info[$dev_id]['condition'] = $condition;

            //define indication
            $indication_type = '';
            if (isset($indication_docs[$dev_inf['indication_doc']])) {
                if ($indication_docs[$dev_inf['indication_doc']]['type'] == '7') {
                    $indication_type = $this->i18n('reports_measurement_type_selfcheck');
                } else if ($dev_inf['appliances_report_type'] == '1' &&
                           isset($indication_docs[$dev_inf['indication_doc']]['devices'][$dev_id]) &&
                           $indication_docs[$dev_inf['indication_doc']]['devices'][$dev_id] == '2164') {
                    $indication_type = $this->i18n('reports_measurement_type_no_access');
                } else if ($dev_inf['appliances_report_type'] == '1' &&
                           !empty($indication_docs[$dev_inf['indication_doc']]['date']) &&
                           $indication_docs[$dev_inf['indication_doc']]['date']>=$this->filters['measure_date_from'] &&
                           $indication_docs[$dev_inf['indication_doc']]['date']>=$this->filters['measure_date_to']) {
                    $indication_type = $this->i18n('reports_measurement_type_visual');
                } else if ($dev_inf['appliances_report_type'] == '2' &&
                           !empty($indication_docs[$dev_inf['indication_doc']]['date']) &&
                           $indication_docs[$dev_inf['indication_doc']]['date']>=$this->filters['measure_date_from'] &&
                           $indication_docs[$dev_inf['indication_doc']]['date']>=$this->filters['measure_date_to']) {
                    $indication_type = $this->i18n('reports_measurement_type_distant_measurement');
                } else if ($dev_inf['appliances_report_type'] == '1' &&
                           !empty($indication_docs[$dev_inf['indication_doc']]['date']) &&
                           $indication_docs[$dev_inf['indication_doc']]['date']<=$this->filters['measure_date_from']) {
                    $indication_type = $this->i18n('reports_measurement_type_no_visual');
                } else if ($dev_inf['appliances_report_type'] == '2' &&
                           !empty($indication_docs[$dev_inf['indication_doc']]['date']) &&
                           $indication_docs[$dev_inf['indication_doc']]['date']<=$this->filters['measure_date_from']) {
                    $indication_type = $this->i18n('reports_measurement_type_not_measured');
                }
            }
            $devices_info[$dev_id]['indication_source'] = $indication_type;
            $devices_info[$dev_id]['protocol_date'] = '';
            if (!empty($device_protocols[$dev_id])) {
                $device_protocol_date = new DateTime($device_protocols[$dev_id]);
                $devices_info[$dev_id]['protocol_date'] = $device_protocol_date->format('d/m/Y');
            }
            $devices_info[$dev_id]['radiator_state'] = $radiators_states[$dev_id] ?? '';
        }

        return $devices_info;
    }

    /**
     * @param array $station_data
     * @param array $periods_list
     * @return array
     */
    private function getStationsCoefficients(array $stations_ids, array $periods_list): array
    {
        $measurements_vars = array(
            'to_station_id',
            'device_id',
            'indication_old',
            'indication_new',
            'indication_date'
        );
        $sql = "SELECT `name`, `id`, `multilang` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `model_type` = " . DOC_TYPE_MEASUREMENTS .
               " AND `name` IN ('" . implode("','", $measurements_vars) . "')" . "\n";
        $measurements_vars = $this->registry['db']->GetAssoc($sql);

        $nom_types_list = array(DEVICE_TYPE_MAIN_HEATMETER, DEVICE_TYPE_MAIN_WATERMETER_BGV);
        $sql = 'SELECT d.id as doc_id, d_cstm_st.value as station, d_cstm_dev.value as device, nom.type as device_type, DATE_FORMAT(d_cstm_dat.value, "%Y-%m") as month_period, ' . "\n" .
               '       d_cstm_old.value as old_indication, d_cstm_new.value as new_indication ' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_st' . "\n" .
               ' ON (d_cstm_st.model_id=d.id AND d_cstm_st.var_id="' . $measurements_vars['to_station_id']['id'] . '" AND d_cstm_st.lang="' . ($measurements_vars['to_station_id']['multilang'] ? $this->registry['lang'] : '') . '"
                           AND d_cstm_st.value IN ("' . implode('","', $stations_ids) . '")
                           AND d.type="' . DOC_TYPE_MEASUREMENTS . '" AND d.active=1 AND d.deleted_by=0)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dat' . "\n" .
               '  ON (d_cstm_dat.model_id=d.id AND d_cstm_dat.var_id="' . $measurements_vars['indication_date']['id'] . '" AND d_cstm_dat.num=d_cstm_st.num AND d_cstm_dat.lang="' . ($measurements_vars['indication_date']['multilang'] ? $this->registry['lang'] : '') . '"
                           AND DATE_FORMAT(d_cstm_dat.value, "%Y-%m") IN ("' . implode('","',
                   $periods_list) . '"))' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dev' . "\n" .
               '  ON (d_cstm_dev.model_id=d.id AND d_cstm_dev.var_id="' . $measurements_vars['device_id']['id'] . '" AND d_cstm_dev.num=d_cstm_st.num AND d_cstm_dev.lang="' . ($measurements_vars['device_id']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
               '  ON (nom.id=d_cstm_dev.value AND nom.type IN (' . implode(',', $nom_types_list) . '))' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_old' . "\n" .
               '  ON (d_cstm_old.model_id=d.id AND d_cstm_old.var_id="' . $measurements_vars['indication_old']['id'] . '" AND d_cstm_old.num=d_cstm_st.num AND d_cstm_old.lang="' . ($measurements_vars['indication_old']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_new' . "\n" .
               '  ON (d_cstm_new.model_id=d.id AND d_cstm_new.var_id="' . $measurements_vars['indication_new']['id'] . '" AND d_cstm_new.num=d_cstm_st.num AND d_cstm_new.lang="' . ($measurements_vars['indication_new']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n";
        $measurements = $this->registry['db']->GetAll($sql);

        $periods = array();
        // define the relation between period, stations and needed documents
        foreach ($measurements as $meas) {
            if (!isset($periods[$meas['month_period']])) {
                $periods[$meas['month_period']] = array();
            }
            if (!isset($periods[$meas['month_period']][$meas['station']])) {
                $periods[$meas['month_period']][$meas['station']] = $meas['doc_id'];
            }
            if ($periods[$meas['month_period']][$meas['station']] > $meas['doc_id']) {
                $periods[$meas['month_period']][$meas['station']] = $meas['doc_id'];
            }
        }
        ksort($periods);

        $temp_stations = array();
        foreach ($measurements as $meas) {
            if ($periods[$meas['month_period']][$meas['station']] != $meas['doc_id']) {
                continue;
            }
            if (!isset($temp_stations[$meas['station']])) {
                $temp_stations[$meas['station']] = array(
                    'total_heatmeter' => 0,
                    'total_watermeter' => 0,
                    'total_avg_coefficient' => 0,
                    'periods' => array()
                );
                foreach ($periods_list as $per) {
                    $temp_stations[$meas['station']]['periods'][$per] = array(
                        'heatmeter' => 0,
                        'watermeter' => 0,
                        'coefficient' => 0,
                    );
                }
            }

            $current_key = 'watermeter';
            $current_total_key = 'total_watermeter';
            if ($meas['device_type'] == DEVICE_TYPE_MAIN_HEATMETER) {
                $current_key = 'heatmeter';
                $current_total_key = 'total_heatmeter';
            }
            $temp_stations[$meas['station']][$current_total_key] += (floatval($meas['new_indication']) - floatval($meas['old_indication']));
            $temp_stations[$meas['station']]['periods'][$meas['month_period']][$current_key] += (floatval($meas['new_indication']) - floatval($meas['old_indication']));
            $temp_stations[$meas['station']]['periods'][$meas['month_period']]['coefficient'] =
                !empty($temp_stations[$meas['station']]['periods'][$meas['month_period']]['watermeter']) ? floatval($temp_stations[$meas['station']]['periods'][$meas['month_period']]['heatmeter']) / floatval($temp_stations[$meas['station']]['periods'][$meas['month_period']]['watermeter']) : '';
        }

        // calculate the coefficient
        foreach ($temp_stations as $k_st => $st_id) {
            if (!$st_id['total_watermeter']) {
                $temp_stations[$k_st]['total_avg_coefficient'] = '';
                continue;
            }
            $temp_stations[$k_st]['total_avg_coefficient'] = ($st_id['total_heatmeter'] / count($st_id['periods'])) / ($st_id['total_watermeter'] / count($st_id['periods']));
        }
        return $temp_stations;
    }

    /**
     * @param DateTime $end_date
     * @param array $station_data
     * @param string $complete_element
     * @return array
     */
    private function processExpenses(DateTime $end_date, array $station_data, string $complete_element): array
    {
        // get expenses
        $summer_months = array_filter(preg_split('#\s*,\s*#', SUMMER_MONTHS));
        $winter_months = array_filter(preg_split('#\s*,\s*#', WINTER_MONTHS));

        // get current month coefficient
        $periods_list = array($end_date->format('Y-m'));
        if (in_array($end_date->format('n'), $summer_months)) {
            $stations_coefficient = $this->getStationsCoefficients(array_keys($station_data), $periods_list);
            foreach ($stations_coefficient as $station_id => $station_co) {
                $station_data[$station_id][$complete_element] = $station_co['periods'][$end_date->format('Y-m')]['coefficient'];
            }
        } elseif (in_array($end_date->format('n'), $winter_months)) {
            $tmp_date = clone $end_date;
            $tmp_date->modify('last day of previous month');
            while (in_array($tmp_date->format('n'), $winter_months)) {
                $periods_list[] = $tmp_date->format('Y-m');
                $tmp_date->modify('last day of previous month');
            }
            $stations_coefficient = $this->getStationsCoefficients(array_keys($station_data), $periods_list);
            foreach ($stations_coefficient as $station_id => $station_co) {
                $station_data[$station_id][$complete_element] = $station_co['total_avg_coefficient'];
            }
        }

        return $station_data;
    }

    /**
     * Function that prepares the export for individual devices
     */
    public function prepareIndividualDevicesExport() : array
    {
        $data = array();

        $results = $this->report_class::buildQuery($this->registry, $this->filters);
        $temp_data = $results['results'];
        $devices_data = $this->getIndividualDevicesAdditionalData(array_keys($temp_data));
        $stations_data = $this->getStationsMainDeviceAdditionalData(array_column($temp_data, 'toStation_id'));
        $objects_data = $this->getObjectsAdditionalData(array_column($temp_data, 'toObject_id'));

        foreach ($temp_data as $tmp_id => $tmp) {
            $indication_date = new DateTime($tmp['indicationDate']);
            $deviations = '';
            if (!empty($this->reportSettings['useNzoomIdForIndExport'])) {
               $deviations = $tmp_id;
            } elseif (isset($devices_data[$tmp_id])) {
                $deviations = $devices_data[$tmp_id]['psiro_code'];
            }
            $data[$tmp_id] = array(
                'diviations'                  => $deviations,
                'diviations_state'            => '',
                'code'                        => $tmp['name'],
                'indication_old'              => $tmp['indicationOld'],
                'indication_new'              => $tmp['indicationNew'],
                'measurement_type'            => $tmp['reportMethod'],
                'indication_date'             => $indication_date->format('Y-m-d 17:00:00'),
                'device_note'                 => '',
                'prescriptions'               => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['perscription_date'] : '',
                'registered'                  => '',
                'people_living'               => isset($objects_data[$tmp['toObject_id']]['people_living']) ? $objects_data[$tmp['toObject_id']]['people_living'] : '',
                'metrology_check'             => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['metrology_date'] : '',
                'holender'                    => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['hollander_number'] : '',
                'date_record_protocol'        => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['protocol_date'] : '',
                'fdr'                         => isset($stations_data[$tmp['toStation_id']]['fdr']) ? $stations_data[$tmp['toStation_id']]['fdr'] : '',
                'room'                        => $tmp['room'],
                'device_condition'            => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['condition'] : '',
                'device_power'                => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['power'] : '',
                'radiator_state'              => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['radiator_state'] : '',
                'heater_description'          => isset($devices_data[$tmp_id]) ? $devices_data[$tmp_id]['description'] : '',
                'batch_num'                   => isset($objects_data[$tmp['toObject_id']]['lot']) ? $objects_data[$tmp['toObject_id']]['lot'] : '',
                'brand'                       => $tmp['manufacturer_name'],
                'responsible_ftc'             => isset($stations_data[$tmp['toStation_id']]['fts']) ? $stations_data[$tmp['toStation_id']]['fts'] : '',
            );
        }

        // prepare the labels
        $first_row = reset($data);
        $first_row = array_keys($first_row);
        $labels = array();
        for ($i=0; $i<count($first_row); $i++) {
            $labels[$first_row[$i]] = $this->i18n('reports_individual_devices_export_col_' . ($i+1));
        }
        $data = array($labels) + $data;

        return $data;
    }

    /**
     * Function that will get the needed data for objects
     */
    public function getObjectsAdditionalData($objects_list) : array
    {
        $object_vars = ['people_living_count', 'lot_id'];

        $sql = "SELECT `name`, `id`, `multilang` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Nomenclature' AND `model_type` = " . NOM_OBJECT_TYPE .
               " AND `name` IN ('" . implode("','", $object_vars) . "')" . "\n";
        $object_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, n_cstm_pl.value as people_living, lot_nm.name as lot ' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_pl' . "\n" .
               ' ON (n_cstm_pl.model_id=n.id AND n_cstm_pl.var_id="' . $object_vars['people_living_count']['id'] . '" AND n_cstm_pl.num=1 AND n_cstm_pl.lang="' . ($object_vars['people_living_count']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_lot' . "\n" .
               ' ON (n_cstm_lot.model_id=n.id AND n_cstm_lot.var_id="' . $object_vars['lot_id']['id'] . '" AND n_cstm_lot.num=1 AND n_cstm_lot.lang="' . ($object_vars['lot_id']['multilang'] ? $this->registry['lang'] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS lot_nm' . "\n" .
               ' ON (lot_nm.parent_id=n_cstm_lot.value AND lot_nm.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE n.id IN ("' . implode('","', $objects_list) . '")';
        return $this->registry['db']->GetAssoc($sql);
    }

    /**
     * Function that will get the needed data for indication documents
     */
    public function getDeviceProtocols($devices) : array
    {
        if (empty($devices)) {
            return array();
        }

        $protocol_vars = ['device_id'];

        $sql = "SELECT `name`, GROUP_CONCAT(`id`) FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `model_type` IN (11,13,14)" .
               " AND `name` IN ('" . implode("','", $protocol_vars) . "')" . "\n" .
               "GROUP BY `name`" . "\n";
        $protocol_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT d_cstm_dev.value as device_id, MAX(d.id) as protocol_id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dev' . "\n" .
               ' ON (d_cstm_dev.model_id=d.id AND d_cstm_dev.var_id IN (' . $protocol_vars['device_id'] . ') AND d_cstm_dev.value IN ("' . implode('","', $devices) . '") AND d.type IN (11,13,14) AND d.active=1 AND d.deleted_by=0)' . "\n" .
               'GROUP BY d_cstm_dev.value';
        $protocols_data = $this->registry['db']->GetAssoc($sql);

        if (!empty($protocols_data)) {
            $sql = 'SELECT d.id, d.date' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'WHERE d.id IN ("' . implode('","', $protocols_data) . '")';
            $protocols_dates = $this->registry['db']->GetAssoc($sql);
        }

        foreach ($protocols_data as $dev_id => $prot_id) {
            $protocols_data[$dev_id] = $protocols_dates[$prot_id] ?? '';
        }

        return $protocols_data;
    }

    /**
     * Function that will get the needed data for indication documents
     */
    public function getIndicationDocsInfo($indication_docs) : array
    {
        $indication_docs = array_filter($indication_docs);
        if (empty($indication_docs)) {
            return $indication_docs;
        }

        $included_model_types = [5,6,7];
        $indication_vars = ['device_id', 'indication_source', 'indication_date'];

        $sql = "SELECT `model_type`, `name`, `id` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `model_type` IN (" . implode(',', $included_model_types) . ")" .
               " AND `name` IN ('" . implode("','", $indication_vars) . "')" . "\n" .
               "ORDER BY FIELD(`name`,'" . implode("','", $indication_vars) . "')";
        $indication_vars_tmp = $this->registry['db']->GetAll($sql);

        $indication_vars_per_type = [];
        foreach ($indication_vars_tmp as $ind_var) {
            if (!isset($indication_vars_per_type[$ind_var['model_type']])) {
                $indication_vars_per_type[$ind_var['model_type']] = array_fill_keys($indication_vars, '');
            }
            $indication_vars_per_type[$ind_var['model_type']][$ind_var['name']] = $ind_var['id'];
        }

        $union_queries = [];
        foreach ($included_model_types as $inc_model_type) {
            $sql = [
                'select' => 'SELECT d.id, d.type',
                'from'   => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                'where'  => 'WHERE d.id IN (' . implode(',', array_unique($indication_docs)) . ') AND d.type=' . $inc_model_type . ' AND d.active=1 AND d.deleted_by=0'
            ];

            $included_row_source = false;
            foreach ($indication_vars_per_type[$inc_model_type] as $var_name => $var_id) {
                switch ($var_name) {
                    case 'device_id':
                        if ($var_id) {
                            $sql['select'] .= ', d_cstm_dev.value as device';
                            $sql['from'] .= "\n" . 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dev' . "\n" .
                                                   ' ON (d_cstm_dev.model_id=d.id AND d_cstm_dev.var_id=' . $var_id . ')';
                            $included_row_source = true;
                        } else {
                            $sql['select'] .= ', "" as device';
                        }
                        break;
                    case 'indication_date':
                        if ($var_id) {
                            $sql['select'] .= ', d_cstm_dat.value as indication_date';
                            $sql['from'] .= "\n" . 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dat' . "\n" .
                                                   ' ON (d_cstm_dat.model_id=d.id AND d_cstm_dat.var_id=' . $var_id . ' AND d_cstm_dat.num=1)';
                        } else {
                            $sql['select'] .= ', "" as indication_date';
                        }
                        break;
                    case 'indication_source':
                        if ($var_id && $included_row_source) {
                            $sql['select'] .= ', d_cstm_src.value as source';
                            $sql['from'] .= "\n" . 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_src' . "\n" .
                                                   ' ON (d_cstm_src.model_id=d.id AND d_cstm_src.var_id=' . $var_id . ' AND d_cstm_src.num=d_cstm_dev.num)';
                        } else {
                            $sql['select'] .= ', "" as source';
                        }
                        break;
                }
            }
            $union_queries[] = '(' . implode("\n", $sql) . ')';
        }

        $indication_data = $this->registry['db']->GetAll(implode(' UNION ALL ', $union_queries));
        $indications = array();
        foreach ($indication_data as $ind_dat) {
            if (!isset($indications[$ind_dat['id']])) {
                $indications[$ind_dat['id']] = array(
                    'type'    => $ind_dat['type'],
                    'date'    => $ind_dat['indication_date'],
                    'devices' => array()
                );
            }
            $indications[$ind_dat['id']]['devices'][$ind_dat['device']] = $ind_dat['source'];
        }

        return $indications;
    }

    /**
     * Function that will take the radiator states
     */
    public function getRadiatorStates($radiators_list) : array
    {
        if (empty($radiators_list)) {
            return $radiators_list;
        }

        $protocol_vars = ['device_id', 'room_condition'];

        $sql = "SELECT `name`, `id` FROM " . DB_TABLE_FIELDS_META . "\n" .
               "WHERE  `model` = 'Document' AND `model_type` IN (15)" .
               " AND `name` IN ('" . implode("','", $protocol_vars) . "')" . "\n";
        $protocol_vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT d.id, d.date, d_cstm_dev.value as device_code, d_cstm_cond.value rad_condition' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dev' . "\n" .
               ' ON (d.type=15 AND d.active=1 AND d.deleted_by=0 AND d_cstm_dev.model_id=d.id AND
                     d_cstm_dev.var_id IN (' . $protocol_vars['device_id'] . ') AND d_cstm_dev.value IN ("' . implode('","', $radiators_list) . '") AND
                     d.date<="' . $this->filters['measure_date_to'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_cond' . "\n" .
               ' ON (d_cstm_cond.model_id=d.id AND d_cstm_cond.var_id IN (' . $protocol_vars['room_condition'] . ') AND d_cstm_cond.num=d_cstm_dev.num)' . "\n" .
               'ORDER BY d.date ASC';
        $protocol_data = $this->registry['db']->GetAll($sql);

        $radiators_states = array();
        foreach ($protocol_data as $prot) {
            $rad_id = array_search($prot['device_code'], $radiators_list);
            if ($rad_id === false) {
                continue;
            }
            $radiators_states[$rad_id] = $this->i18n('reports_radiator_state_' . $prot['rad_condition']);
        }

        return $radiators_states;
    }

    function excelNumbertoColumn($num) {
        $numeric = $num % 26;
        $letter = chr(65 + $numeric);
        $num2 = intval($num / 26);
        if ($num2 > 0) {
            return $this->excelNumbertoColumn($num2 - 1) . $letter;
        } else {
            return $letter;
        }
    }
}
