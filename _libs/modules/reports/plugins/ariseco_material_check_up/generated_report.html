{assign var='settings' value=$reports_additional_options.settings}

{if !$reports_additional_options.allow_process_orders_current_user}
  <style type="text/css">
    table.freeze_table_headers th.freeze_column.order_date,
    table.freeze_table_headers td.freeze_column.order_date {ldelim}
        left: 112px!important;
    {rdelim}
  </style>
{/if}

<table class="reports_table results{if $settings.arise_freeze_table_headers} freeze_table_headers{/if}">
  <thead>
    <tr class="reports_title_row">
      <th colspan="1">&nbsp;</th>
      <th colspan="{if $reports_additional_options.allow_process_orders_current_user}8{else}7{/if}">{#th_production_order_information#}</th>
      <th colspan="4">{#th_materials_cut_sku_child_sku#}</th>
      <th colspan="5">{#th_availability_other_warehouses#}</th>
    </tr>
    <tr class="reports_title_row">
      <th class="atelier_producer freeze_column">{#th_atelier_producer#}</th>
      {if $reports_additional_options.allow_process_orders_current_user}
        <th class="select_orders freeze_column">
          <label for="select_orders">{#th_select_orders#}</label><br />
          <input type="checkbox" id="select_orders" />
        </th>
      {/if}
      <th class="order_date freeze_column">{#th_order_date#}</th>
      <th class="order_deadline">{#th_order_deadline#}</th>
      <th class="sku_code">{#th_sku_code#}</th>
      <th class="sku_name">{#th_sku_name#}</th>
      <th class="production_deadline">{#th_production_deadline#}</th>
      <th class="production_quantity">{#th_production_quantity#}</th>
      <th class="production_notes">{#th_production_notes#}</th>
      <th class="material_code">{#th_material_code#}</th>
      <th class="material_name">{#th_material_name#}</th>
      <th class="material_type">{#th_material_type#}</th>
      <th class="needed_quantity">{#th_needed_quantity#}</th>
      <th class="warehouse_name">{#th_warehouse_name#}</th>
      <th class="batch_code">{#th_batch_code#}</th>
      <th class="available_quantity">{#th_available_quantity#}</th>
      <th class="reserved_quantity">{#th_reserved_quantity#}</th>
      <th class="reservation_notes">{#th_reservation_notes#}</th>
    </tr>
  </thead>
  <tbody>
    {array_push array='empty_batch' key=0 new_item=''}
    {array_push array='empty_atelier_warehouse_batches' key=0 new_item=$empty_batch}
    {foreach from=$reports_results item='atelier' key='atelier_id' name='ateliers'}
      {foreach from=$atelier.orders item='order' key='order_id' name='orders'}
        {foreach from=$order.skus item='sku' key='sku_id' name='skus'}
          {foreach from=$sku.productions item='production' name='productions'}
            <tr class="order_{$order_id}">
              {if $smarty.foreach.orders.first && $smarty.foreach.skus.first && $smarty.foreach.productions.first}
                <td rowspan="{$atelier.rowspan}" class="atelier_producer freeze_column">
                  <div class="sticky_content">
                    {$atelier.name|escape}
                  </div>
                </td>
              {/if}
              {if $smarty.foreach.skus.first && $smarty.foreach.productions.first}
                {if $reports_additional_options.allow_process_orders_current_user}
                  <td rowspan="{$order.rowspan}" class="select_orders freeze_column">
                    <div class="sticky_content">
                      {include file='input_checkbox.html'
                        name='select_orders'
                        eq_indexes=true
                        index=$order_id
                        standalone=true
                        option_value=$order_id
                        value=''
                      }
                    </div>
                  </td>
                {/if}
                <td rowspan="{$order.rowspan}" class="order_date freeze_column">
                  <div class="sticky_content">
                    {$order.type_name|escape}<br />
                    ({$order.name|escape})<br />
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order_id}" target="_blank">
                      {$order.full_num|escape} / {$order.date|date_format:#date_short#}
                    </a>
                  </div>
                </td>
                <td rowspan="{$order.rowspan}" class="order_deadline">
                  <div class="sticky_content">
                    {$order.deadline|date_format:#date_short#}
                  </div>
                </td>
              {/if}
              {if $smarty.foreach.productions.first}
                {cycle assign='sku_odd_even' values='t_odd1 t_odd2,t_even1 t_even2'}
                <td rowspan="{$sku.rowspan}" class="sku_code {$sku_odd_even}">
                  <div class="sticky_content">
                    {$sku.code|escape}
                  </div>
                </td>
                <td rowspan="{$sku.rowspan}" class="sku_name {$sku_odd_even}">
                  <div class="sticky_content">
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$sku_id}" target="_blank">
                      {$sku.name|escape}
                    </a>
                  </div>
                </td>
              {/if}
              <td class="production_deadline {$sku_odd_even}">
                <div class="sticky_content">
                  {$production.deadline|date_format:#date_short#}
                </div>
              </td>
              <td class="production_quantity {$sku_odd_even}">
                <div class="sticky_content">
                  {$production.quantity|number_format_depending_type:2:".":" "}
                  {$sku.measure_name|escape}
                </div>
              </td>
              <td class="production_notes {$sku_odd_even}">
                <div class="sticky_content">
                  {$production.notes|escape|nl2br}
                </div>
              </td>
              {if $smarty.foreach.productions.first}
                {capture assign='index_class'}table_container_{counter name='row_table_container'}{/capture}
                <td colspan="9" rowspan="{$sku.rowspan}" class="{$sku_odd_even} table_container {$index_class}" data-index-class="{$index_class}">
                  {if !empty($reports_additional_options.skus_materials[$sku_id])}
                    <table>
                      {foreach from=$reports_additional_options.skus_materials[$sku_id] item='material' key='material_id' name='skus_materials'}
                        <tr{if $smarty.foreach.skus_materials.last} class="last"{/if}>
                          <td class="material_code">
                            <div class="sticky_content">
                              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;reports=generate_report&amp;report_type=ariseco_inventory_management&amp;material[0]={$material_id}&amp;material_autocomplete[0]=%5B{$material.code|escape:'url'}%5D%20{$material.name|escape:'url'}" target="_blank">
                                {$material.code|escape}
                              </a>
                            </div>
                          </td>
                          <td class="material_name">
                            <div class="sticky_content">
                              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$material_id}" target="_blank">
                                {$material.name|escape}
                              </a>
                            </div>
                          </td>
                          <td class="material_type">
                            <div class="sticky_content">
                              {$material.type_name|escape}
                            </div>
                          </td>
                          <td class="needed_quantity">
                            {math assign='needed_quantity' equation="x*y" x=$sku.total_production_quantity y=$material.quantity|default:0}
                            <div class="sticky_content{if $needed_quantity eq 0} zero_needed_quantity{/if}"{if $needed_quantity eq 0} data-order-id="{$order_id}"{/if}>
                              {if $material.type eq $settings.nom_type_cut_sku || $material.type eq $settings.nom_type_semi_product}
                                {capture assign='needed_quantity_key'}{$order_id}_{$material_id}_{counter name='needed_quantities'}{/capture}
                                {include file='input_text.html'
                                  name='needed_quantity'
                                  eq_indexes=true
                                  index=$needed_quantity_key
                                  standalone=true
                                  value=$needed_quantity
                                  restrict='insertOnlyFloats'
                                }
                              {else}
                                {$needed_quantity|number_format_depending_type:2:".":" "}
                              {/if}
                              {$material.measure_name|escape}
                            </div>
                          </td>
                          {capture assign='index_class'}table_container_{counter name='row_table_container'}{/capture}
                          <td colspan="5" class="table_container {$index_class} last" data-index-class="{$index_class}">
                            {assign var='atelier_material_warehouses' value=''}
                            {assign var='material_warehouses' value=''}
                            {if !empty($reports_additional_options.materials_warehouses[$material_id])}
                              {assign var='material_warehouses' value=$reports_additional_options.materials_warehouses[$material_id]}
                            {/if}
                            {if !empty($atelier.warehouse_id) && array_key_exists($atelier.warehouse_id, $reports_additional_options.warehouses_names)}
                              {if !empty($material_warehouses) && array_key_exists($atelier.warehouse_id, $material_warehouses)}
                                {assign var='atelier_warehouse_batches' value=$material_warehouses[$atelier.warehouse_id]}
                              {else}
                                {assign var='atelier_warehouse_batches' value=$empty_atelier_warehouse_batches}
                              {/if}
                              {array_push array='atelier_material_warehouses' key=$atelier.warehouse_id new_item=$atelier_warehouse_batches}
                            {/if}
                            {if !empty($material_warehouses)}
                              {foreach from=$material_warehouses item='material_warehouse_batches' key='material_warehouse_id'}
                                {array_push array='atelier_material_warehouses' key=$material_warehouse_id new_item=$material_warehouse_batches}
                              {/foreach}
                            {/if}

                            {if !empty($atelier_material_warehouses)}
                              <table>
                                {foreach from=$atelier_material_warehouses item='material_warehouse_batches' key='material_warehouse_id' name='atelier_material_warehouses'}
                                  {foreach from=$material_warehouse_batches item='material_warehouse_batch' name='material_warehouse_batches'}
                                    <tr class="{if $atelier.warehouse_id eq $material_warehouse_id}atelier_warehouse{/if}{if $smarty.foreach.atelier_material_warehouses.last && $smarty.foreach.material_warehouse_batches.last} last{/if}">
                                      {if $smarty.foreach.material_warehouse_batches.first}
                                        <td class="warehouse_name" rowspan="{$material_warehouse_batches|@count}">
                                          <div class="sticky_content">
                                            {$reports_additional_options.warehouses_names[$material_warehouse_id]|escape}
                                          </div>
                                        </td>
                                      {/if}
                                      <td class="batch_code">
                                        <div class="sticky_content">
                                          {$material_warehouse_batch.code|escape}
                                        </div>
                                      </td>
                                      {assign var='available_quantity' value=$material_warehouse_batch.without_reserved|default:0}
                                      <td class="available_quantity{if $atelier.warehouse_id eq $material_warehouse_id && $available_quantity lt $needed_quantity} available_lt_needed_quantity{/if}">
                                        <div class="sticky_content">
                                          {$available_quantity|number_format_depending_type:2:".":" "}
                                          {$material.measure_name|escape}
                                        </div>
                                      </td>
                                      <td class="reserved_quantity">
                                        <div class="sticky_content">
                                          {if $material_warehouse_batch.reserved}
                                            {$material_warehouse_batch.reserved|number_format_depending_type:2:".":" "}
                                            {$material.measure_name|escape}
                                          {/if}
                                        </div>
                                      </td>
                                      {if $smarty.foreach.material_warehouse_batches.first}
                                        {capture assign='index_class'}table_container_{counter name='row_table_container'}{/capture}
                                        <td rowspan="{$material_warehouse_batches|@count}" class="table_container {$index_class} last" data-index-class="{$index_class}">
                                          {if !empty($reports_additional_options.reservations_notes[$material_id][$material_warehouse_id])}
                                            <table>
                                              {foreach from=$reports_additional_options.reservations_notes[$material_id][$material_warehouse_id] item='reservation_notes' name='reservations_notes'}
                                                <tr{if $smarty.foreach.reservations_notes.last} class="last"{/if}>
                                                  <td class="reservation_notes last">
                                                    {$reservation_notes|escape|nl2br}
                                                  </td>
                                                </tr>
                                              {/foreach}
                                            </table>
                                          {else}
                                            {* Just keep the columns *}
                                            <table class="keep_the_columns">
                                              <tbody>
                                                <tr class="last">
                                                  <td class="reservation_notes last">&nbsp;</td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          {/if}
                                        </td>
                                      {/if}
                                    </tr>
                                  {/foreach}
                                {/foreach}
                              </table>
                            {else}
                              {* Just keep the columns *}
                              <table class="keep_the_columns">
                                <tbody>
                                  <tr class="last">
                                    <td class="warehouse_name">&nbsp;</td>
                                    <td class="batch_code">&nbsp;</td>
                                    <td class="available_quantity">&nbsp;</td>
                                    <td class="reserved_quantity">&nbsp;</td>
                                    <td class="reservation_notes last">&nbsp;</td>
                                  </tr>
                                </tbody>
                              </table>
                            {/if}
                          </td>
                        </tr>
                      {/foreach}
                    </table>
                  {else}
                    {* Just keep the columns *}
                    <table class="keep_the_columns">
                      <tbody>
                        <tr class="last">
                          <td class="material_code">&nbsp;</td>
                          <td class="material_name">&nbsp;</td>
                          <td class="material_type">&nbsp;</td>
                          <td class="needed_quantity">&nbsp;</td>
                          {capture assign='index_class'}table_container_{counter name='row_table_container'}{/capture}
                          <td colspan="5" class="table_container {$index_class} last" data-index-class="{$index_class}">
                            {* Just keep the columns *}
                            <table class="keep_the_columns">
                              <tbody>
                                <tr class="last">
                                  <td class="warehouse_name">&nbsp;</td>
                                  <td class="batch_code">&nbsp;</td>
                                  <td class="available_quantity">&nbsp;</td>
                                  <td class="reserved_quantity">&nbsp;</td>
                                  <td class="reservation_notes last">&nbsp;</td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  {/if}
                </td>
              {/if}
            </tr>
          {/foreach}
        {/foreach}
      {/foreach}
    {foreachelse}
      <tr>
        <td colspan="18" class="error">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
  </tbody>
</table>

{if $reports_additional_options.allow_process_orders_current_user}
  <button type="button" class="button" id="button_process_orders">{#button_process_orders#}</button>
{/if}
