<?php

Class Garitagepark_Hierarchy Extends Reports {
    private static $free_nom_types_of_interest = array(
        NOM_TYPE_PARKING_PLACE,
        NOM_TYPE_WAREHOUSE,
        NOM_TYPE_COMMERCIAL_AREA,
        NOM_TYPE_OFFICE
    );

    public static function buildQuery(&$registry, $filters = array()) {
        $plain_noms_list = self::prepareNomTree($registry, $filters);

        if (!empty($filters['paginate'])) {
            $results = array($plain_noms_list, 0);
        } else {
            $results = $plain_noms_list;
        }

        return $results;
    }

    public static function createPlainTree(&$tree, &$new_tree) {
        foreach ($tree as $tr) {
            if (!empty($tr['id'])) {
                $new_tree[] = array(
                    'id' => $tr['id'],
                    'name' => $tr['name'],
                    'level' => $tr['level'],
                    'type' => $tr['type'],
                    'files' => $tr['files'],
                    'tag' => $tr['tag'],
                    'tree_path' => $tr['tree_path'],
                    'equipment' => 0,
                    'filtered' => false
                );
                if (!empty($tr['included'])) {
                    self::createPlainTree($tr['included'], $new_tree);
                }
            }
        }

        return true;
    }

    public static function clearTreeTakenNoms($tree) {
        foreach ($tree as $key => $tr) {
            if (!empty($tr['included'])) {
                $tree[$key]['included'] = self::clearTreeTakenNoms($tr['included']);
            }

            // check if nom is free
            if (in_array($tree[$key]['type'], self::$free_nom_types_of_interest)) {
                if ($tree[$key]['tag'] || $tree[$key]['contract_taken']) {
                    unset($tree[$key]);
                }
            } else {
                if (empty($tree[$key]['included'])) {
                    unset($tree[$key]);
                }
            }
        }
        return $tree;
    }

    public static function prepareNomTree(&$registry, $filters = array()) {
        $plain_noms_list = array();
        $collapse_elements = array();
        $selected_element = '';
        $elements_tree_index_relations = array();
        $message_searched_data_not_found = '';

        $object_types = array(
            NOM_TYPE_APARTMENT,
            NOM_TYPE_BUILDING,
            NOM_TYPE_COMPLEX,
            NOM_TYPE_MEASURING_DEVICE,
            NOM_TYPE_HOUSE,
            NOM_TYPE_OFFICE,
            NOM_TYPE_PARKING_PLACE,
            NOM_TYPE_COMMERCIAL_AREA,
            NOM_TYPE_WAREHOUSE,
            NOM_TYPE_SUBCOMPLEX,
            NOM_TYPE_EQUIPMENT
        );

        $third_level_types = array(
            NOM_TYPE_BUILDING,
            NOM_TYPE_HOUSE,
            NOM_TYPE_PARKING_PLACE,
            NOM_TYPE_WAREHOUSE
        );
        $fourth_level_types = array(
            NOM_TYPE_HOUSE,
            NOM_TYPE_APARTMENT,
            NOM_TYPE_WAREHOUSE,
            NOM_TYPE_PARKING_PLACE,
            NOM_TYPE_OFFICE,
            NOM_TYPE_COMMERCIAL_AREA
        );

        $free_filter_nom_types = array(
            NOM_TYPE_BUILDING,
            NOM_TYPE_PARKING_PLACE,
            NOM_TYPE_WAREHOUSE,
            NOM_TYPE_COMMERCIAL_AREA,
            NOM_TYPE_OFFICE
        );
        $contract_types_to_check = preg_split('/\s*,\s*/', CONTRACT_TYPES_TO_CHECK);

        // get all the nomenclatures from second level
        // get the complex vars
        $complex_house_var_ids = array(NOM_VAR_COMPLEX, NOM_VAR_BUILDING, NOM_VAR_FILE, NOM_VAR_FILE_CATEGORY, NOM_VAR_FILE_SUBCATEGORY, NOM_VAR_EQUIPMENT, NOM_VAR_SUBCOMPLEX);
        $sql = 'SELECT `name`, GROUP_CONCAT(`id` SEPARATOR "|") FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type` IN ("' . implode('","', $object_types) . '") AND `name` IN ("' . implode('","', $complex_house_var_ids) . '") GROUP BY `name`';
        $complex_house_var_ids = $registry['db']->GetAssoc($sql);
        foreach ($complex_house_var_ids as $var_name => $var_ids) {
            $complex_house_var_ids[$var_name] = explode('|', $var_ids);
        }

        // BUILD THE TREE
        // get all the complexes
        $sql = 'SELECT n.id as idx, n.id, CONCAT(nti18n.name, " ", ni18n.name) as name, n.type as type, 1 as level, COUNT(n_cstm_file.value) as files, t.tag_id as tag, 0 as contract_taken, \'\' as tree_path' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
               '  ON (nti18n.parent_id=n.type AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
               '  ON (n_cstm_file.model_id=n.id AND n_cstm_file.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_FILE]) . '") AND n_cstm_file.value IS NOT NULL AND n_cstm_file.value!="")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t' . "\n" .
               '  ON (t.model=\'Nomenclature\' AND t.model_id=n.id AND t.tag_id="' . TAG_TAKEN_ID . '")' . "\n" .
               'WHERE n.active=1 AND n.deleted_by=0 AND n.type="' . NOM_TYPE_COMPLEX . '"' . "\n" .
               'GROUP BY n.id' . "\n" .
               'ORDER BY CONCAT(nti18n.name, " ", ni18n.name) ASC' . "\n";
        $included_nomenclatures = $registry['db']->GetAssoc($sql);

        if (!empty($included_nomenclatures)) {
            $nomenclatures_relations = array_fill_keys(array_keys($included_nomenclatures), '');

            $sql = 'SELECT n.id as idx, n.id, CONCAT(nti18n.name, " ", ni18n.name) as name, n.type, n.code, n_cstm.value as complex_id, "" as subcomplex_id, COUNT(n_cstm_file.value) as files, t.tag_id as tag, 0 as contract_taken, \'\' as tree_path' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                   '  ON (n_cstm.model_id=n.id AND n_cstm.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_COMPLEX]) . '") AND n_cstm.value IN ("' . implode('","', array_keys($included_nomenclatures)) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                   '  ON (nti18n.parent_id=n.type AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
                   '  ON (n_cstm_file.model_id=n.id AND n_cstm_file.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_FILE]) . '") AND n_cstm_file.value IS NOT NULL AND n_cstm_file.value!="")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t' . "\n" .
                   '  ON (t.model=\'Nomenclature\' AND t.model_id=n.id AND t.tag_id="' . TAG_TAKEN_ID . '")' . "\n" .
                   'WHERE n.active=1 AND n.deleted_by=0 AND n.type="' . NOM_TYPE_SUBCOMPLEX . '"' . "\n" .
                   'GROUP BY n.id' . "\n" .
                   'ORDER BY CONCAT(nti18n.name, " ", ni18n.name) ASC' . "\n";
            $subcomplexes_noms = $registry['db']->GetAssoc($sql);

            foreach ($subcomplexes_noms as $cp) {
                if (!isset($included_nomenclatures[$cp['complex_id']]['included'][$cp['id']])) {
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['id']] = $cp;
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['id']]['tree_path'] = implode('_', array($cp['complex_id']));
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['id']]['level'] = 2;
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['id']]['included'] = array();
                }
                $nomenclatures_relations[$cp['id']] = $cp['complex_id'];
            }

            $buildings_included = array();

            // get all the nomenclatures for the selected complex
            if (!empty($filters['free_tag'])) {
                $noms_lvl3 = array_intersect($third_level_types, $free_filter_nom_types);
            } else {
                $noms_lvl3 = $third_level_types;
            }

            $sql = 'SELECT n.id as idx, n.id, CONCAT(nti18n.name, " ", ni18n.name) as name, n.type, n.code, n_cstm.value as complex_id, n_cstm_subcomplex.value as subcomplex_id, n_cstm_building.value as building_id, COUNT(n_cstm_file.value) as files, t.tag_id as tag, 0 as contract_taken, \'\' as tree_path' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                   '  ON (n_cstm.model_id=n.id AND n_cstm.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_COMPLEX]) . '") AND n_cstm.value IN ("' . implode('","', array_keys($nomenclatures_relations)) . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_subcomplex' . "\n" .
                   '  ON (n_cstm_subcomplex.model_id=n.id AND n_cstm_subcomplex.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_SUBCOMPLEX]) . '") AND n_cstm_subcomplex.value IN ("' . implode('","', array_keys($nomenclatures_relations)) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_building' . "\n" .
                   '  ON (n_cstm_building.model_id=n.id AND n_cstm_building.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_BUILDING]) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                   '  ON (nti18n.parent_id=n.type AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
                   '  ON (n_cstm_file.model_id=n.id AND n_cstm_file.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_FILE_CATEGORY]) . '") AND n_cstm_file.value IS NOT NULL AND n_cstm_file.value!="")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t' . "\n" .
                   '  ON (t.model=\'Nomenclature\' AND t.model_id=n.id AND t.tag_id="' . TAG_TAKEN_ID . '")' . "\n" .
                   'WHERE n.type IN ("' . implode('","', $noms_lvl3) . '") AND n.active=1 AND n.deleted_by=0' . "\n" .
                   'GROUP BY n.id' . "\n" .
                   'ORDER BY IF(n.type=' . NOM_TYPE_BUILDING . ', 1, 0) DESC, CONCAT(nti18n.name, " ", ni18n.name) ASC';
            $complexes_noms = $registry['db']->GetAssoc($sql);

            $complexes_contract_noms = array();
            if (!empty($filters['free_tag'])) {
                $contracts_inactive = preg_split('/\s*,\s*/', CONTRACTS_SUBSTATUSES_INACTIVE);
                $sql = 'SELECT gt2.free_field1, GROUP_CONCAT(c.id)' . "\n" .
                       'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       ' ON (gt2.model_id=c.id AND gt2.model="Contract" AND gt2.free_field1 IN ("' . implode('","', array_keys($complexes_noms)) . '"))' . "\n" .
                       'WHERE c.type IN ("' . implode('","', $contract_types_to_check) . '") AND c.subtype="contract" AND c.date_start<="' . date('Y-m-d') . '" AND c.date_validity>="' . date('Y-m-d') . '" AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND c.substatus NOT IN ("' . implode('","', $contracts_inactive) . '")' . "\n" .
                       'GROUP BY gt2.free_field1' . "\n";
                $complexes_contract_noms = $registry['db']->GetAssoc($sql);
            }

            foreach ($complexes_noms as $cp) {
                if ($cp['type'] == NOM_TYPE_BUILDING) {
                    $buildings_included[] = $cp['id'];
                }
                $key = $cp['complex_id'] . '_' . $cp['subcomplex_id'];
                if ($cp['building_id']) {
                    $key .= '_' . $cp['building_id'];
                }
                $nomenclatures_relations[$cp['id']] = $key;

                if (in_array($cp['type'], $free_filter_nom_types) && isset($complexes_contract_noms[$cp['id']])) {
                    $cp['contract_taken'] = 1;
                }

                if (empty($cp['building_id'])) {
                    if (!isset($included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['id']])) {
                        $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['id']] = $cp;
                        $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['id']]['tree_path'] = implode('_', array($cp['complex_id'], $cp['subcomplex_id']));
                        $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['id']]['level'] = 3;
                        $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['id']]['included'] = array();
                    }
                } else {
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['building_id']]['included'][$cp['id']] = $cp;
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['building_id']]['included'][$cp['id']]['tree_path'] = implode('_', array($cp['complex_id'], $cp['subcomplex_id'], $cp['building_id']));
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['building_id']]['included'][$cp['id']]['level'] = 4;
                    $included_nomenclatures[$cp['complex_id']]['included'][$cp['subcomplex_id']]['included'][$cp['building_id']]['included'][$cp['id']]['included'] = array();
                }
            }

            if (!empty($buildings_included)) {
                // get all the nomenclatures for the selected complex
                if (!empty($filters['free_tag'])) {
                    $noms_lvl4 = array_intersect($fourth_level_types, $free_filter_nom_types);
                } else {
                    $noms_lvl4 = $fourth_level_types;
                }

                // get all the nomenclatures related to buildings
                $sql = 'SELECT n.id as idx, n.id, n_cstm_building.value as parent_id, CONCAT(nti18n.name, " ", ni18n.name) as name, n.type, COUNT(n_cstm_file.value) as files, t.tag_id as tag, 0 as contract_taken, \'\' as tree_path' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_building' . "\n" .
                       '  ON (n_cstm_building.model_id=n.id AND n_cstm_building.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_BUILDING]) . '") AND n_cstm_building.value IN ("' . implode('","', $buildings_included) . '"))' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                       '  ON (nti18n.parent_id=n.type AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_file' . "\n" .
                       '  ON (n_cstm_file.model_id=ni18n.parent_id AND n_cstm_file.var_id IN ("' . implode('","', $complex_house_var_ids[NOM_VAR_FILE_CATEGORY]) . '") AND n_cstm_file.value IS NOT NULL AND n_cstm_file.value!="")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t' . "\n" .
                       '  ON (t.model=\'Nomenclature\' AND t.model_id=n.type AND t.tag_id="' . TAG_TAKEN_ID . '")' . "\n" .
                       'WHERE n.type IN ("' . implode('","', $noms_lvl4) . '") AND n.id NOT IN ("' . implode('","', array_keys($nomenclatures_relations)) . '") AND n.active=1 AND n.deleted_by=0' . "\n" .
                       'GROUP BY n.id' . "\n" .
                       'ORDER BY CONCAT(nti18n.name, " ", ni18n.name) ASC' . "\n";
                $buildings_related_noms = $registry['db']->GetAssoc($sql);

                $buildings_contract_noms = array();
                if (!empty($filters['free_tag'])) {
                    $sql = 'SELECT gt2.free_field1, GROUP_CONCAT(c.id)' . "\n" .
                        'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                        'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                        ' ON (gt2.model_id=c.id AND gt2.model="Contract" AND gt2.free_field1 IN ("' . implode('","', array_keys($buildings_related_noms)) . '"))' . "\n" .
                        'WHERE c.type IN ("' . implode('","', $contract_types_to_check) . '") AND c.subtype="contract" AND c.date_start<="' . date('Y-m-d') . '" AND c.date_validity>="' . date('Y-m-d') . '" AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND c.substatus NOT IN ("' . implode('","', $contracts_inactive) . '")' . "\n" .
                        'GROUP BY gt2.free_field1' . "\n";
                    $buildings_contract_noms = $registry['db']->GetAssoc($sql);
                }

                foreach ($buildings_related_noms as $brn) {
                    if (isset($nomenclatures_relations[$brn['parent_id']])) {
                        $parent_ids = explode('_', $nomenclatures_relations[$brn['parent_id']]);
                        $parent_ids = array_filter($parent_ids);
                        $count_levels = count($parent_ids);
                        if ($count_levels == 2) {
                            if (in_array($brn['type'], $free_filter_nom_types) && isset($buildings_contract_noms[$brn['id']])) {
                                $brn['contract_taken'] = 1;
                            }
                            $nomenclatures_relations[$brn['id']] = $nomenclatures_relations[$brn['parent_id']] . '_' . $brn['parent_id'];
                            $included_nomenclatures[$parent_ids[0]]['included'][$parent_ids[1]]['included'][$brn['parent_id']]['included'][$brn['id']] = $brn;
                            uasort($included_nomenclatures[$parent_ids[0]]['included'][$parent_ids[1]]['included'][$brn['parent_id']]['included'], function ($a, $b) { return $a['name'] > $b['name'] ? 1 : -1; } );
                            $included_nomenclatures[$parent_ids[0]]['included'][$parent_ids[1]]['included'][$brn['parent_id']]['included'][$brn['id']]['level'] = 4;
                            $included_nomenclatures[$parent_ids[0]]['included'][$parent_ids[1]]['included'][$brn['parent_id']]['included'][$brn['id']]['tree_path'] = implode('_', array($parent_ids[0], $parent_ids[1], $brn['parent_id']));
                        }
                    }
                }
            }

            if (!empty($filters['free_tag'])) {
                $included_nomenclatures = self::clearTreeTakenNoms($included_nomenclatures);
            }
            self::createPlainTree($included_nomenclatures, $plain_noms_list);

            if (!empty($filters['object'])) {
                $flag_object_found = false;
                $sql = 'SELECT `parent_id` as id, `name` as name FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE `parent_id`="' . $filters['object'] . '"';
                $eq_object = $registry['db']->GetRow($sql);
            }
            foreach ($plain_noms_list as $key => $inc_nom) {
                $elements_tree_index_relations[$inc_nom['id']] = $key + 2;
                if (!empty($filters['object']) && $filters['object'] == $inc_nom['id']) {
                    $flag_object_found = true;
                    $plain_noms_list[$key]['filtered'] = true;
                    $collapse_elements[] = $inc_nom['tree_path'];
                    $selected_element = $inc_nom['id'] . '_' . $inc_nom['type'];
                } elseif (!empty($filters['free_tag']) && !$inc_nom['tag']) {
                    $collapse_elements[] = $inc_nom['tree_path'];
                }
            }

            if (isset($flag_object_found) && !$flag_object_found) {
                $link = sprintf(
                    '%s://%s%sindex.php?%s=nomenclatures&nomenclatures=view&view=%d',
                    (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                    $_SERVER["HTTP_HOST"], PH_BASE_URL, $registry['module_param'], $eq_object['id']
                );
                $message_searched_data_not_found = sprintf($registry['translater']->translate('error_searched_object_not_found'), $link, $eq_object['name'], (!empty($filters['free_tag']) ? ' ' . $registry['translater']->translate('error_possible_not_free_nom') : ''));
            }

            if ($registry['currentUser']->checkRights('nomenclatures' . NOM_TYPE_MEASURING_DEVICE, 'list') && $registry['currentUser']->checkRights('nomenclatures' . NOM_TYPE_EQUIPMENT, 'list') && !empty($elements_tree_index_relations)) {
                // get the vars for nomenclature
                $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type` IN ("' . implode('","', array(NOM_TYPE_EQUIPMENT, NOM_TYPE_MEASURING_DEVICE)) . '") AND `name`="' . EQUIPMENT_OBJECT .  '"';
                $eq_object = $registry['db']->GetCol($sql);

                // check for equipment
                $sql = 'SELECT n_cstm.value as id, COUNT(n.id) as equipment' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                       '  ON (n_cstm.model_id=n.id AND n_cstm.var_id IN ("' . implode('","', $eq_object) . '") AND n_cstm.value IN ("' . implode('","', array_keys($elements_tree_index_relations)) . '") AND n_cstm.lang="")' . "\n" .
                       'WHERE n.type IN ("' . implode('","', array(NOM_TYPE_EQUIPMENT, NOM_TYPE_MEASURING_DEVICE)) . '") AND n.active=1 AND n.deleted_by=0' . "\n" .
                       'GROUP BY n_cstm.value' . "\n";
                $equipment = $registry['db']->GetAssoc($sql);
                foreach ($plain_noms_list as $key => $inc_nom) {
                    if (isset($equipment[$inc_nom['id']])) {
                        $plain_noms_list[$key]['equipment'] = $equipment[$inc_nom['id']];
                    }
                }
            }
        }

        $restricted_files_users = array();
        if (defined(strtoupper('RESTRICTED_FILES_USERS')) && constant('RESTRICTED_FILES_USERS')) {
            $restricted_files_users = preg_split('/\s*,\s*/', RESTRICTED_FILES_USERS);
            $restricted_files_users = array_filter($restricted_files_users);
        }

        $plain_noms_list['additional_options'] = array(
            'file_view_permissions' => !in_array($registry['currentUser']->get('id'), $restricted_files_users),
            'message_not_found'     => $message_searched_data_not_found,
            'tree_colpase'          => TREE_COLPASED,
            'collapse_elements'     => json_encode($collapse_elements),
            'selected_element'      => $selected_element,
            'elements_tree_index_relations' => json_encode($elements_tree_index_relations)
        );

        return $plain_noms_list;
    }
}

?>
