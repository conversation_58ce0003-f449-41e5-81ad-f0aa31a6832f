<tr>
  <td class="labelbox">
    <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help label_content=$filter_settings.label text_content=$filter_settings.help}</label>
  </td>
  <td>&nbsp;</td>
  <td>
    {foreach from=$filter_settings.options item=option name=opt key=key_opt}
      <input type="radio" name="{$filter_settings.name}" id="{$filter_settings.custom_id|default:$filter_settings.name}_{$key_opt}" value="{$option.option_value|escape}" title="{$option.label|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="{$option.onchange}"{if $filter_settings.value eq $option.option_value} checked="checked"{/if} /> <label for="{$filter_settings.custom_id|default:$filter_settings.name}_{$key_opt}"{if !$smarty.foreach.opt.last} style="padding-right: 20px;"{/if}>{$option.label|escape}</label>
    {/foreach}
  </td>
</tr>