<script type="text/javascript" src="{$reports_additional_options.scripts_url}?{$system_options.build}"></script>
{literal}
  <style>
      tr.t_odd.main_row > td  {
        white-space: nowrap;
        background-color: #EDEDED;
        border-top-color: #EDEDED;
        border-bottom-color: #EDEDED;
      }
      tr.t_even.main_row > td  {
        white-space: nowrap;
        background-color: #DFDFDF;
        border-top-color: #DFDFDF;
        border-bottom-color: #DFDFDF;
      }
      td.quantity_reserved {
          background-color: #FFD000A0!important;
      }
      td.quantity_data {
          background-color: #FDEF8FA0!important;
      }
      td.warehouse_quantities {
          width: 40px;
      }
      td.warehouse_quantities_moving {
          background-color: #98bcff!important;
      }
      img.row_expander {
          float: left;
          margin-right: 3px;
          width: 12px;
          height: 12px;
          border: none;
      }
      div.row_expander_container {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
      }
      div.row_expander_num {
          flex: 1;
      }
      tr.t_odd.batch_row > td  {
          background-color: #F8F8F8;
          border-top-color: #F8F8F8;
          border-bottom-color: #F8F8F8;
      }
      tr.t_even.batch_row > td  {
          background-color: #FFFFFF;
          border-top-color: #FFFFFF;
          border-bottom-color: #FFFFFF;
      }
      td.quantity_reserved_batch {
          background-color: #FFD000A0!important;
      }
      td.quantity_data_batch {
          background-color: #FDEF8FA0!important;
      }
  </style>
{/literal}


{assign var='hard_coded_columns' value='6'}
{if $reports_additional_options.warehouses && is_array($reports_additional_options.warehouses)}
  {math equation="x + (y*2)" x=$hard_coded_columns y=$reports_additional_options.warehouses|@count assign='total_columns'}
{else}
  {assign var=total_columns value=$hard_coded_columns}
{/if}
{if $reports_additional_options.show_code_separate_column}
  {math equation="x + 1" x=$total_columns assign='total_columns'}
{/if}
{if $reports_additional_options.show_prices_columns}
  {math equation="x + y" x=$total_columns y=$reports_additional_options.show_prices_columns|@count assign='total_columns'}
{/if}
{capture assign='precision'}%.{$reports_additional_options.quantity_precision}f{/capture}
{capture assign='precision_value'}%.{$reports_additional_options.value_precision}f{/capture}
<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border vmiddle" nowrap="nowrap">
            {if $reports_additional_options.have_batches}
              <img id="show_batches_all" src="{$theme->imagesUrl}small/expand.png"   alt="{#reports_show_batches_all#|escape}" title="{#reports_show_batches_all#|escape}" onclick="warehouseAvailabilitiesToggleBatches(this, 'all');" class="pointer row_expander" />
              <img id="hide_batches_all" src="{$theme->imagesUrl}small/collapse.png" alt="{#reports_hide_batches_all#|escape}" title="{#reports_hide_batches_all#|escape}" onclick="warehouseAvailabilitiesToggleBatches(this, 'all');" class="pointer row_expander" style="display: none;" />
            {/if}
            <div{if $reports_additional_options.have_batches} style="width: 30px;"{/if}>{#num#|escape}</div>
          </td>
          {if $reports_additional_options.show_code_separate_column}
            <td class="t_border vmiddle"><div style="">{#reports_nomenclature_code#|escape}</div></td>
          {/if}
          <td class="t_border vmiddle"><div style="width: 440px;">{#reports_nomenclature#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 50px;">{#reports_nomenclature_measure#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 65px;">{#reports_free_availability#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 65px;">{#reports_reserved_availability#|escape}</div></td>
          <td style="vertical-align: middle;"{if $hard_coded_columns lt $total_columns} class="t_border"{/if}><div style="width: 65px;">{#reports_availability#|escape}</div></td>
          {if $reports_additional_options.show_prices_columns}
            {if in_array('sell_price', $reports_additional_options.show_prices_columns)}
              <td class="t_border vmiddle"><div style="width: 65px;">{#reports_sell_price#|escape}</div></td>
            {/if}
            {if in_array('last_delivery_price', $reports_additional_options.show_prices_columns)}
              <td class="t_border vmiddle"><div style="width: 65px;">{#reports_last_delivery_price#|escape}</div></td>
            {/if}
            {if in_array('average_weighted_delivery_price', $reports_additional_options.show_prices_columns)}
              <td class="vmiddle{if $hard_coded_columns lt $total_columns} t_border{/if}"><div style="width: 65px;">{#reports_average_weighted_delivery_price#|escape}</div></td>
            {/if}
          {/if}
          {if $reports_additional_options.warehouses}
            {foreach from=$reports_additional_options.warehouses name=warh item=warehouse}
              <td class="vmiddle{if !$smarty.foreach.warh.last} t_border{/if}" colspan="2"><div style="width: 90px;">{$warehouse.name|escape|default:"&nbsp;"}</div></td>
            {/foreach}
          {/if}
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results key=k name=list_res item=result}
          {capture assign='row_class_article'}{cycle name='cycle_articles' values='t_odd,t_even'}{/capture}
          {if $row_class_article eq 't_odd'}
            {assign var='row_color_article' value="#EDEDED"}
          {else}
            {assign var='row_color_article' value="#DFDFDF"}
          {/if}
          <tr class="{$row_class_article} main_row">
            <td class="t_border hright">
              <div class="row_expander_container">
                {if $result.batches}
                  <div>
                    <img id="show_batches_{$k}" src="{$theme->imagesUrl}small/expand.png"   alt="{#reports_show_batches#|escape}" title="{#reports_show_batches#|escape}" onclick="warehouseAvailabilitiesToggleBatches(this, {$k});" class="pointer row_expander" />
                    <img id="hide_batches_{$k}" src="{$theme->imagesUrl}small/collapse.png" alt="{#reports_hide_batches#|escape}" title="{#reports_hide_batches#|escape}" onclick="warehouseAvailabilitiesToggleBatches(this, {$k});" class="pointer row_expander" style="display: none;" />
                  </div>
                {/if}
                <div class="row_expander_num">{counter name='item_counter' print=true}</div>
              </div>
            </td>
            {if $reports_additional_options.show_code_separate_column}
              <td class="t_border">
                {$result.code|escape|default:"&nbsp;"}
              </td>
            {/if}
            <td class="t_border">
              {$result.name|escape|default:"&nbsp;"}{if !$reports_additional_options.show_code_separate_column} ({$result.code|escape|default:"&nbsp;"}){/if}
            </td>
            <td class="t_border hcenter">
              {$result.article_measure|escape|default:"&nbsp;"}
            </td>
            <td class="hright t_border quantity_free">
              {if $result.quantity_free}
                {$result.quantity_free|escape|string_format:"$precision"|default:"-"}
              {else}
                -
              {/if}
            </td>
            <td class="hright t_border quantity_reserved">
              {if $result.quantity_reserved}
                {if $result.colored_reserved_quantity}<span style="color: brown;"><strong>{/if}
                  {$result.quantity_reserved|escape|string_format:"$precision"|default:"-"}
                {if $result.colored_reserved_quantity}</strong></span>{/if}
              {else}
                -
              {/if}
            </td>
            <td class="hright{if $hard_coded_columns<$total_columns} t_border{/if} quantity_data">
              {if $result.quantity}
                {if $result.colored_quantity}<font color="red"><strong>{/if}
                {$result.quantity|escape|string_format:"$precision"|default:"-"}
                {if $result.colored_quantity}</strong></font>{/if}
              {else}
                -
              {/if}
            </td>
            {if $reports_additional_options.show_prices_columns}
              {if in_array('sell_price', $reports_additional_options.show_prices_columns)}
                <td class="t_border hright">
                  {$result.sell_price|escape|string_format:"$precision_value"|default:"0.00"}{if $smarty.const.SHOW_CURRENCIES} {$result.sell_price_currency|default:"&nbsp;"}{/if}
                </td>
              {/if}
              {if in_array('last_delivery_price', $reports_additional_options.show_prices_columns)}
                <td class="t_border hright">
                  {$result.last_delivery_price|escape|string_format:"$precision_value"|default:"0.00"}{if $smarty.const.SHOW_CURRENCIES} {$result.last_delivery_price_currency|default:"&nbsp;"}{/if}
                </td>
              {/if}
              {if in_array('average_weighted_delivery_price', $reports_additional_options.show_prices_columns)}
                <td class="hright{if $hard_coded_columns<$total_columns} t_border{/if}">
                  {$result.average_weighted_delivery_price|escape|string_format:"$precision_value"|default:"0.00"}{if $smarty.const.SHOW_CURRENCIES} {$result.average_weighted_delivery_price_currency|default:"&nbsp;"}{/if}
                </td>
              {/if}
            {/if}
            {if $reports_additional_options.warehouses}
              {foreach from=$reports_additional_options.warehouses name=warh_arr key=warh_key item=warehouse}
                {if $result.warehouses_moving.$warh_key}
                  <td class="hright t_border warehouse_quantities warehouse_quantities_moving{if $result.warehouses_moving.$warh_key.encoded_data} pointer{/if}" title="{#reports_moving_goods#|escape}"
                      {if $result.warehouses_moving.$warh_key.encoded_data} onclick="prepareCustomSearch('{$result.warehouses_moving.$warh_key.encoded_data}');"{/if}>
                    {if $result.warehouses_moving.$warh_key}
                      {$result.warehouses_moving.$warh_key.quantity|escape|string_format:"$precision"|default:"-"}
                    {else}
                      -
                    {/if}
                  </td>
                  <td class="hright{if !$smarty.foreach.warh_arr.last} t_border{/if} warehouse_quantities">
                    {if $result.warehouses.$warh_key}
                      {$result.warehouses.$warh_key|escape|string_format:"$precision"|default:"-"}
                    {else}
                      -
                    {/if}
                  </td>
                {else}
                  <td colspan="2" class="hright{if !$smarty.foreach.warh_arr.last} t_border{/if}">
                    {if $result.warehouses.$warh_key}
                      {$result.warehouses.$warh_key|escape|string_format:"$precision"|default:"-"}
                    {else}
                      -
                    {/if}
                  </td>
                {/if}
              {/foreach}
            {/if}
          </tr>
          {assign var='reset_cycle_batches' value=true}
          {foreach from=$result.batches item='batch'}
            {capture assign='row_class_batch'}{cycle name='cycle_batches' values="t_odd,t_even" reset=$reset_cycle_batches}{/capture}
            {assign var='reset_cycle_batches' value=false}
            {if $row_class_batch eq 't_odd'}
              {assign var='row_color_batch' value="#F8F8F8"}
            {else}
              {assign var='row_color_batch' value="#FFFFFF"}
            {/if}
            <tr class="batches_{$k} {$row_class_batch} batch_row" style="display: none;" >
              {if $reports_additional_options.show_code_separate_column}
                <td class="t_border hright" nowrap="nowrap">
                  &nbsp;
                </td>
              {/if}
              <td class="t_border">
                &nbsp;
              </td>
              <td class="t_border">
                {$batch.batch_code|escape|default:"&nbsp;"}{if $batch.expire ne '0000-00-00'} ({$batch.expire|date_format:#date_short#}){/if}
              </td>
              <td class="t_border">
                &nbsp;
              </td>
              <td class="hright t_border quantity_free_batch">
                {if $batch.quantity_free}
                  {$batch.quantity_free|escape|string_format:"$precision"|default:"-"}
                {else}
                  -
                {/if}
              </td>
              <td class="hright t_border quantity_reserved_batch">
                {if $batch.quantity_reserved}
                  {if $batch.colored_reserved_quantity}<span style="color: brown;"><strong>{/if}
                    {$batch.quantity_reserved|escape|string_format:"$precision"|default:"-"}
                  {if $batch.colored_reserved_quantity}</strong></span>{/if}
                {else}
                  -
                {/if}
              </td>
              <td class="hright quantity_data_batch{if $hard_coded_columns<$total_columns} t_border{/if}">
                {if $batch.quantity_total}
                  {$batch.quantity_total|escape|string_format:"$precision"|default:"-"}
                {else}
                  -
                {/if}
              </td>
              {if $reports_additional_options.show_prices_columns}
                {if in_array('sell_price', $reports_additional_options.show_prices_columns)}
                  <td class="t_border">
                    &nbsp;
                  </td>
                {/if}
                {if in_array('last_delivery_price', $reports_additional_options.show_prices_columns)}
                  <td class="t_border">
                    &nbsp;
                  </td>
                {/if}
                {if in_array('average_weighted_delivery_price', $reports_additional_options.show_prices_columns)}
                  <td{if $hard_coded_columns lt $total_columns} class="t_border"{/if}>
                    &nbsp;
                  </td>
                {/if}
              {/if}
              {foreach from=$reports_additional_options.warehouses name=warh_arr key=warh_key item=warehouse}
                <td colspan="2" class="hright{if !$smarty.foreach.warh_arr.last} t_border{/if}">
                  {if $batch.warehouses_quantities[$warh_key] && $batch.warehouses_quantities[$warh_key] ne 0}
                    {$batch.warehouses_quantities[$warh_key]|escape|string_format:"$precision"|default:"-"}
                  {else}
                    -
                  {/if}
                </td>
              {/foreach}
            </tr>
          {/foreach}
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="{$total_columns}">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="{$total_columns}"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
