<?php
class Ariseco_Design_Dossier Extends Reports {
    public static function buildQuery($registry, $filters = array()) {
        // Prepare array for the results
        $results = [];

        // Require filters
        if (empty($filters['design'])) {
            // Show an error message
            $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));

            // Hide the "Export" button
            $registry->set('hide_export_button', true, true);
        } else {
            // Get the lang
            $lang = $registry['lang'];

            // Get the database object
            $db = $registry['db'];

            // Get report settings
            $settings = self::getReportSettings($registry);

            // Process filters
            $filters['size'] = array_filter($filters['size']);
            $filters['color'] = array_filter($filters['color']);

            /**
             * Get data
             */
            $design = Nomenclatures::searchOne($registry,
                [
                    'model_lang' => $lang,
                    'where' => [
                        'n.id = ' . $filters['design']
                    ],
                    'skip_permissions_check' => true
                ]
            );
            $registry->set('get_old_vars', true, true);
            $design_vars = $design->getAssocVars();

            $registry->set('get_old_vars', false, true);
            $results['design'] = [];
            foreach ($design_vars['brand_name']['options'] as $option) {
                if ($option['option_value'] == $design_vars['brand_name']['value']) {
                    $results['design']['brand_name'] = $option['label'];
                    break;
                }
            }
            $results['design']['name'] = $design->get('code');
            foreach ($design_vars['season_info']['options'] as $option) {
                if ($option['option_value'] == $design_vars['season_info']['value']) {
                    $results['design']['season_info'] = $option['label'];
                    break;
                }
            }
            $results['design']['date_creation'] = $design_vars['date_creation']['value'];
            $results['design']['main_fabric_name'] = trim(substr($design_vars['main_fabric_name']['value'], strrpos($design_vars['main_fabric_name']['value'], ',') + 1));

            $results['design']['colors'] = [];
            $query = "
                SELECT id, code
                  FROM " . DB_TABLE_NOMENCLATURES . "
                  WHERE type = {$settings['nomenclatures_type_color_clothes']}" .
                    (!empty($filters['color']) ? "
                    AND id IN (" . implode(', ', $filters['color']) . ")" : '');
            $color_clothes_codes = $db->GetAssoc($query);

            foreach ($design_vars['sew_colors_id']['value'] as $row => $sew_color_id) {
                if (empty($sew_color_id)) {
                    continue;
                }
                $results['design']['colors'][] = [
                    'code' => $color_clothes_codes[$sew_color_id] ?? '',
                    'name' => $design_vars['sew_colors']['value'][$row] ?? ''
                ];
            }
            $results['design']['product_sketch'] = '';
            if (!empty($design_vars['product_sketch']['value']) && is_object($design_vars['product_sketch']['value'])) {
                $results['design']['product_sketch'] = $design_vars['product_sketch']['value']->get('id');
            }
            $results['design']['picture_front'] = '';
            if (!empty($design_vars['picture_front']['value']) && is_object($design_vars['picture_front']['value'])) {
                $results['design']['picture_front'] = $design_vars['picture_front']['value']->get('id');
            }
            $results['design']['picture_back'] = '';
            if (!empty($design_vars['picture_back']['value']) && is_object($design_vars['picture_back']['value'])) {
                $results['design']['picture_back'] = $design_vars['picture_back']['value']->get('id');
            }
            $results['design']['picture_label_instructions'] = '';
            if (!empty($design_vars['inst_file']['value']) && is_object($design_vars['inst_file']['value'])) {
                $results['design']['picture_label_instructions'] = $design_vars['inst_file']['value']->get('id');
            }
            $results['design']['picture_label_compound'] = '';
            if (!empty($design_vars['label_file']['value']) && is_object($design_vars['label_file']['value'])) {
                $results['design']['picture_label_compound'] = $design_vars['label_file']['value']->get('id');
            }
            $results['design']['picture_packiging'] = '';
            if (!empty($design_vars['pack_file']['value']) && is_object($design_vars['pack_file']['value'])) {
                $results['design']['picture_packiging'] = $design_vars['pack_file']['value']->get('id');
            }
            $results['design']['note_info'] = $design_vars['note_info']['value'];
            $results['design']['reserves_processing'] = ($design_vars['reserves_processing']['value'] ?? '');
            $results['design']['labeling_instructions'] = $design_vars['labeling_instructions']['value'];
            $results['design']['packing_instructions'] = $design_vars['packing_instructions']['value'];
            $results['design']['cut_info'] = $design_vars['cut_info']['value'] ?? '';
            $results['design']['compound_label'] = $design_vars['compound_label']['value'];

            if (!empty($design_vars['main_fabric_id']['value'])) {
                $query = "
                SELECT nc.value
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (n.id = {$design_vars['main_fabric_id']['value']}
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = 'content')
                  JOIN nom_cstm AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.num = 1
                      AND nc.lang = 'en')";
                $results['design']['content_fabric'] = $db->GetOne($query);
            } else {
                $results['design']['content_fabric'] = '';
            }

            foreach ($design_vars['stage_production']['options'] as $option) {
                if ($option['option_value'] == $design_vars['stage_production']['value']) {
                    $results['design']['stage_production'] = $option['label'];
                    break;
                }
            }

            $results['design']['primary_atelier_prod'] = $design_vars['primary_atelier_prod']['value'];

            $results['sizes'] = [];
            $report_filters = $registry->get('report_filters');
            $filters_size = [];
            foreach ($report_filters['size']['options'] as $option) {
                if (!empty($filters['size'])) {
                    if (in_array($option['option_value'], $filters['size'])) {
                        $results['sizes'][] = $option['label'];
                    }
                } else {
                    $results['sizes'][] = $option['label'];
                    $filters_size[] = $option['option_value'];
                }
            }
            if (empty($filters['size'])) {
                $filters['size'] = $filters_size;
            }
            $results['sizes'] = implode(', ', $results['sizes']);

            $results['child_skus'] = [];
            $parentsku_name_ids = [];

            if (!empty($design_vars['parentsku_name_id']['value'])) {
                $parentsku_name_ids = $design_vars['parentsku_name_id']['value'];
            }
            if (!empty($parentsku_name_ids)) {
                $childSKUs_search_params = [
                    'where'                  => [
                        "n.type = {$settings['nomenclatures_type_child_sku']}",
                        "a____parentsku_name_id IN ('" . implode("', '", $parentsku_name_ids) . "')",
                        "a____size_info IN ('" . implode("', '", $filters['size']) . "')",
                    ],
                    'model_lang'             => $lang,
                    'skip_permissions_check' => true
                ];
                if (!empty($filters['color'])) {
                    $childSKUs_search_params['where'][] = "a____color_name_id IN ('" . implode("', '", $filters['color']) . "')";
                }
                $childSKUs = Nomenclatures::search($registry, $childSKUs_search_params);
                $registry->set('get_old_vars', true, true);
                foreach ($childSKUs as $childSKU) {
                    $childSKU->unsanitize();
                    $childSKU_vars = $childSKU->getVarsForTemplateAssoc();
                    $childSKU_product_group = $childSKU_vars['product_group'];
                    $childSKU_product_group_names = array_flip($childSKU_product_group['names']);

                    $childSKU_material_measure_labels = [];
                    if (!empty($childSKU_product_group['material_measure']['options'])) {
                        foreach ($childSKU_product_group['material_measure']['options'] as $material_measure_option) {
                            $childSKU_material_measure_labels[$material_measure_option['option_value']] = $material_measure_option['label'];
                        }
                    }

                    if (!empty($childSKU_product_group['values'])) {
                        // Reset array indexes, because we don't need them them associative (if they are), but we need them to start from 0, because several lines down into the code we use array_splice
                        $childSKU_product_group['values'] = array_values($childSKU_product_group['values']);

                        // Get sub nomenclatures
                        $sub_noms_assoc = [];
                        $product_ids = array_filter(array_column($childSKU_product_group['values'], $childSKU_product_group_names['product_id']));
                        if (!empty($product_ids)) {
                            $product_ids_list = implode(',', $product_ids);
                            $sub_noms = Nomenclatures::search($registry,
                                [
                                    'where'                  => [
                                        "n.type IN ({$settings['nomenclatures_type_cut_sku']}, {$settings['nomenclatures_type_masks_and_filters']})",
                                        "n.id IN ({$product_ids_list})"
                                    ],
                                    'sort'                   => ["FIND_IN_SET(n.id, '{$product_ids_list}')"],
                                    'model_lang'             => $lang,
                                    'skip_permissions_check' => true
                                ]
                            );
                            if (!empty($sub_noms)) {
                                foreach ($sub_noms as $sub_nom) {
                                    $sub_noms_assoc[$sub_nom->get('id')] = $sub_nom;
                                }
                            }
                        }

                        // Go through product_group and from each nomenclature of type "Cut SKU" and "Masks and Filters" extract it's product_group and replace the current row with it
                        $childSKU_product_group_values = [];
                        foreach ($childSKU_product_group['values'] as $childSKU_product_group_row) {
                            $sub_noms_rows_inserted = false;

                            // If the current row nomenclature is among the "Cut SKU" and "Masks and Filters" nomenclatures
                            if (array_key_exists($childSKU_product_group_names['product_id'], $childSKU_product_group_row) && array_key_exists($childSKU_product_group_row[$childSKU_product_group_names['product_id']], $sub_noms_assoc)) {
                                // Get the vars for template
                                $sub_nom_vars = $sub_noms_assoc[$childSKU_product_group_row[$childSKU_product_group_names['product_id']]]->getVarsForTemplateAssoc();
                                // If the sub nomenclature's product_group is not empty
                                if (!empty($sub_nom_vars['product_group']['values'])) {
                                    // Prepare the Child SKU row replacement
                                    $childSKU_product_group_row_replacement = [];
                                    // Get the vars names and keys
                                    $sub_nom_product_group_names = array_flip($sub_nom_vars['product_group']['names']);
                                    // Get the material measure labels
                                    $sub_nom_material_measure_labels = [];
                                    if (!empty($sub_nom_vars['product_group']['material_measure']['options'])) {
                                        foreach ($sub_nom_vars['product_group']['material_measure']['options'] as $material_measure_option) {
                                            $sub_nom_material_measure_labels[$material_measure_option['option_value']] = $material_measure_option['label'];
                                        }
                                    }
                                    // Go through each row
                                    foreach ($sub_nom_vars['product_group']['values'] as $sub_nom_product_group_row) {
                                        // Prepare a new Child SKU product_group row
                                        $new_childSCU_product_group_row = [];
                                        // Go through each Child SKU product_group var (column)
                                        foreach ($childSKU_product_group_names as $childSKU_product_group_name => $childSKU_product_group_name_key) {
                                            // Check if it exists into the sub nomenclatures's product_group
                                            if (array_key_exists($childSKU_product_group_name, $sub_nom_product_group_names)
                                                && array_key_exists($sub_nom_product_group_names[$childSKU_product_group_name], $sub_nom_product_group_row)) {
                                                // Collect each column from the sub nom product_group into the array, which will replace the current row of the Child SKU's product_group
                                                $sub_nom_product_group_row_value = $sub_nom_product_group_row[$sub_nom_product_group_names[$childSKU_product_group_name]];
                                                if ($childSKU_product_group_name == 'material_measure' && array_key_exists($sub_nom_product_group_row_value, $sub_nom_material_measure_labels)) {
                                                    $sub_nom_product_group_row_value = $sub_nom_material_measure_labels[$sub_nom_product_group_row_value];
                                                }
                                                $new_childSCU_product_group_row[$childSKU_product_group_name_key] = $sub_nom_product_group_row_value;
                                            }
                                        }
                                        // If a row is collected
                                        if (count($new_childSCU_product_group_row) > 0) {
                                            // Use it to replace the current product_group row
                                            $childSKU_product_group_values[] = $new_childSCU_product_group_row;
                                            $sub_noms_rows_inserted = true;
                                        }
                                    }
                                }
                            } else {
                                // Replace the material measure with it's label
                                if (array_key_exists($childSKU_product_group_names['material_measure'], $childSKU_product_group_row) && array_key_exists($childSKU_product_group_row[$childSKU_product_group_names['material_measure']], $childSKU_material_measure_labels)) {
                                    $childSKU_product_group_row[$childSKU_product_group_names['material_measure']] = $childSKU_material_measure_labels[$childSKU_product_group_row[$childSKU_product_group_names['material_measure']]];
                                }
                            }

                            // If no sub rows were inserted
                            if (!$sub_noms_rows_inserted) {
                                // Collect the current row
                                $childSKU_product_group_values[] = $childSKU_product_group_row;
                            }
                        }
                        // Replace the product_group rows with the new collected rows
                        $childSKU_product_group['values'] = $childSKU_product_group_values;
                    }

                    $size_filter_position = array_search($childSKU_vars['size_info']['value'], $filters['size']);

                    $results['child_skus'][] = [
                        'id'                   => $childSKU->get('id'),
                        'code'                 => $childSKU->get('code'),
                        'size_filter_position' => $size_filter_position,
                        'product_group'        => $childSKU_product_group,
                        'product_group_names'  => $childSKU_product_group_names
                    ];

                    // Sort Child SKUs
                    usort($results['child_skus'], function ($a, $b) {
                        // Sort by the Child SKU's size (by it's position into the size filter)
                        if ($a['size_filter_position'] < $b['size_filter_position']) {
                            return -1;
                        } else if ($a['size_filter_position'] > $b['size_filter_position']) {
                            return 1;
                        }
                        // Then sort by Child SKU code
                        elseif ($a['code'] < $b['code']) {
                            return -1;
                        } elseif ($a['code'] > $b['code']) {
                            return 1;
                        } else {
                            return 0;
                        }
                    });

                    $childSKU->sanitize();
                }
                $registry->set('get_old_vars', false, true);
            }

            if ($registry->get('action') == 'export') {
                $results['additional_options']['placeholders'] = array(
                    'css_styles'                  => array('name' => 'css_styles'),
                    'result'                      => array('name' => 'result'),
                    'design_name'                 => array('name' => 'design_name'),
                    'size'                        => array('name' => 'size'),
                    'brand_name'                  => array('name' => 'brand_name'),
                    'season_info'                 => array('name' => 'season_info'),
                    'date_creation'               => array('name' => 'date_creation'),
                    'main_fabric_name'            => array('name' => 'main_fabric_name'),
                    'content_fabric'              => array('name' => 'content_fabric'),
                    'stage_production'            => array('name' => 'stage_production'),
                    'primary_atelier_prod'        => array('name' => 'primary_atelier_prod'),
                    'path_product_sketch'         => array('name' => 'path_product_sketch'),
                    'path_picture_front'          => array('name' => 'path_picture_front'),
                    'path_picture_back'           => array('name' => 'path_picture_back'),
                    'note_info'                   => array('name' => 'note_info'),
                    'reserves_processing'         => array('name' => 'reserves_processing'),
                    'labeling_instructions'       => array('name' => 'labeling_instructions'),
                    'packing_instructions'        => array('name' => 'packing_instructions'),
                    'table_technical_description' => array('name' => 'table_technical_description'),
                    'table_child_skus'            => array('name' => 'table_child_skus'),
                    'cut_info'                    => array('name' => 'cut_info'),
                    'compound_label'              => array('name' => 'compound_label'),
                    'path_inst_file'              => array('name' => 'path_inst_file'),
                    'path_pack_file'              => array('name' => 'path_pack_file'),
                    'path_label_file'             => array('name' => 'path_label_file'),
                );
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }

        return $results;
    }
}
