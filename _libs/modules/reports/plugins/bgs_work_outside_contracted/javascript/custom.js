/**
 * Toggles visibility of details of a single result row
 *
 * @param {Object} element
 * @return void
 */
function toggleDetails(element) {
    element = $(element);
    element.up('tr').next().toggle();
    toggleSwitchArrow(element, element.up('tr').next().visible() ? 'collapse' : 'expand');
}

/**
 * Toggles visibility of details of all result rows into the same state
 *
 * @param {Object} element
 * @return void
 */
function toggleDetailsAll(element) {
    element = $(element);
    var mode = element.select('div.switch_expand').length ? 'expand' : 'collapse';
    element.up('table.reports_table').select('td div.pointer[onclick]').each(function(d) {
        if (d.select('div.switch_' + mode).length) {
            d.onclick();
        }
    });
    toggleSwitchArrow(element, mode == 'expand' ? 'collapse' : 'expand');
}

/**
 * Processes selection of checkboxes for customers
 *
 * @param {Object} element - checkbox whose selection state is toggled
 * @return {Boolean} - result of the operation
 */
function toggleCustomerSelected(element) {
    element = $(element);
    var frm = element.up('form');
    if (!frm) {
        return false;
    }
    if (element.id.match(/^items_\d+$/)) {
        if (element.checked) {
            frm.select('span.selected_items_span').each(function(el) {
                el.addClassName('green');
            });
            frm.select('button[type=submit].button').each(function(el) {
                el.removeClassName('hidden');
            });
        } else {
            var num_checked = 0;
            frm.select('input[type=checkbox][name="items[]"]').each(function(el) {
                if (el.checked) {
                    num_checked++;
                    throw $break;
                }
            });

            if (!num_checked) {
                frm.select('span.selected_items_span').each(function(el) {
                    el.removeClassName('green');
                });
                frm.select('button[type=submit].button').each(function(el) {
                    el.addClassName('hidden');
                });
            }
        }
        [element.up('tr'), element.up('tr').next()].each(function(tr) {
            tr[element.checked ? 'addClassName' : 'removeClassName']('selected');
        });
    } else if (element.tagName == 'DIV' && /^check_all_menu_/.test(element.id)) {
        // check all/page/none
        frm.select('input[type=checkbox][name="items[]"]').each(function(el) {
            toggleCustomerSelected(el);
        });
    }
    return true;
}
