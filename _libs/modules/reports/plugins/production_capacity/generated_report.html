<table class="reports_table">
  {assign var='periods_count' value=0}
  {if $reports_additional_options.periods}
    {capture assign="periods_count"}{if is_array($reports_additional_options.periods)}{$reports_additional_options.periods|@count}{else}0{/if}{/capture}
  {/if}
  <tr class="reports_title_row">
    <th rowspan="{if $periods_count}2{else}1{/if}">{#num#|escape}</th>
    <th rowspan="{if $periods_count}2{else}1{/if}">{#reports_workplace_type#|escape}</th>
    {if $periods_count}
      {foreach from=$reports_additional_options.periods item='period'}
      {capture assign='text_production_schedules'}{foreach from=$period.schedules item='ps'}{$ps.full_num|escape}<br />{foreachelse}{#no_items_found#|escape}{/foreach}{/capture}
      <th colspan="3" {help popup_only=true label_content=#reports_production_schedules#|escape text_content=$text_production_schedules}>{#reports_period#|escape} {$period.period_start_report|date_format:#date_short#} - {$period.period_end|date_format:#date_mid#}</th>
      {/foreach}
    {/if}
  </tr>
  {if $periods_count}
  <tr class="reports_title_row2">
    {foreach from=$reports_additional_options.periods item='period'}
    <th>{#reports_workplace_capacity#|escape}</th>
    <th>{#reports_required_capacity#|escape}</th>
    <th>{#reports_difference#|escape}</th>
    {/foreach}
  </tr>
  {/if}
  {foreach from=$reports_results item='result' key='rk' name='ri'}
  <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
    <td class="hright">{$smarty.foreach.ri.iteration}</td>
    <td>{$result.name|escape|default:'&nbsp;'}</td>
    {if $periods_count}
      {foreach from=$reports_additional_options.periods item='period' key='pk' name='pi'}
        <td class="hright">{$result.periods.$pk.available|default:0}</td>
        <td class="hright">{$result.periods.$pk.required|default:0}</td>
        <td class="hright {if $result.periods.$pk.difference gt 0}green{else}red{/if}">{$result.periods.$pk.difference|default:0}</td>
      {/foreach}
    {/if}
  </tr>
  {foreachelse}
  <tr class="t_odd1 t_odd2">
    <td class="error" colspan="{math equation='3*a+2' a=$periods_count}">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
</table>
<br />