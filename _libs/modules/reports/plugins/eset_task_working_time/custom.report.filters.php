<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE EMPLOYEE FILTER
            //get employees' options
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters_customers = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort' => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                       'where' => array('u.active = 1'));
            $employees = Users::search($registry, $filters_customers);

            //prepare documents' types groups
            $options_customers = array();

            foreach($employees as $employee) {
                $options_customers[] = array(
                    'label'         => $employee->get('firstname') . ($employee->get('lastname') ? (' ' . $employee->get('lastname')) : ''),
                    'option_value'  => $employee->get('id')
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee'),
                'options'   => $options_customers,
            );
            $filters['employee'] = $filter;

            //DEFINE PRODUCTION DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE PRODUCTION DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE STATUS FILTER
            $options_statuses = array(
                array ( 'label' => $this->i18n('reports_status_planning'),
                        'option_value' => 'planning'
                       ),
                array ( 'label' => $this->i18n('reports_status_progress'),
                        'option_value' => 'progress'
                       ),
                array ( 'label' => $this->i18n('reports_status_finished'),
                        'option_value' => 'finished'
                       )
            );

            //prepare filters
            $filter = array (
                'custom_id' => 'status',
                'name' => 'status',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_status'),
                'help' => $this->i18n('reports_status'),
                'options' => $options_statuses,
            );
            $filters['status'] = $filter;

            return $filters;
        }
    }
?>