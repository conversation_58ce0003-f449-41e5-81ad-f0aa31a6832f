<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <table border="1" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td nowrap="nowrap" align="center"><div>{#num#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#repotrs_employee_name#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_project#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_task#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_listed_time#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_status#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_late#|escape}</div></td>
    </tr>
    {counter start=0 name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr>
        <td nowrap="nowrap" align="right">
          {counter name='item_counter' print=true}
        </td>
        <td align="left">
          {$result.employee_name|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.project_name|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks&amp;tasks=view&amp;view={$result.id}">{$result.full_num}</a>
        </td>
        <td align="left">
          {if $result.duration}
            {if $result.duration_hours}{$result.duration_hours} {#hours#},{/if}
            {if $result.duration_minutes}{$result.duration_minutes} {#minutes#}{/if}
          {else}
            &nbsp;
          {/if}
        </td>
        <td align="left">
          {capture assign='status_lang_var'}reports_status_{$result.status}{/capture}
          {$smarty.config.$status_lang_var|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {if $result.late_time}
            {if $result.late_hours}{$result.late_hours} {#hours#},{/if}
            {if $result.late_minutes}{$result.late_minutes} {#minutes#}{/if}
          {else}
            &nbsp;
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr>
        <td colspan="7">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
  </table>
  <br />
  <br />
  <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td nowrap="nowrap">{#reports_projects#|escape}</td>
        <td nowrap="nowrap">{#reports_tasks#|escape}</td>
        <td nowrap="nowrap">{#reports_reported_time#|escape}</td>
        <td nowrap="nowrap">{#reports_lates#|escape}</td>
        <td nowrap="nowrap">{#reports_lates_time#|escape}</td>
      </tr>
      <tr>
        <td>
          {$reports_additional_options.total_projects|escape|default:"&nbsp;"}
        </td>
        <td>
          {$reports_additional_options.total_tasks|escape|default:"&nbsp;"}
        </td>
        <td>
          {if $reports_additional_options.total_working_time}
            {if $reports_additional_options.total_working_time_hours}{$reports_additional_options.total_working_time_hours} {#hours#},{/if}
            {if $reports_additional_options.total_working_time_minutes}{$reports_additional_options.total_working_time_minutes} {#minutes#}{/if}
          {else}
            &nbsp;
          {/if}
        </td>
        <td>
          {$reports_additional_options.total_lating_task|escape|default:"&nbsp;"}
        </td>
        <td>
          {if $reports_additional_options.total_late_time}
            {if $reports_additional_options.total_late_time_hours}{$reports_additional_options.total_late_time_hours} {#hours#},{/if}
            {if $reports_additional_options.total_late_time_minutes}{$reports_additional_options.total_late_time_minutes} {#minutes#}{/if}
          {else}
            &nbsp;
          {/if}
        </td>
      </tr>
  </table>
</body>
</html>