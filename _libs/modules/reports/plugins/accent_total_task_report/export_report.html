<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  <style>
  {literal}
    table, td {
        vertical-align: top;
        border-collapse: collapse;
    }
    th {
        font-weight: bold;
        text-align: left;
    }
  {/literal}
  </style>
</head>
<body>
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td>
        <b>{#reports_main_report#}</b>
        <br />
        <table border="1">
          <tr>
            <td nowrap="nowrap"><b>{#num#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_full_num#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_customer#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_project#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_task_name#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_employee#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_observer#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_task_deadline#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_status#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_duration#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_planned_duration#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_late#|escape}</b></td>
            <td nowrap="nowrap"><b>{#reports_comments#|escape}</b></td>
          </tr>
          {counter start=0 name='item_counter' print=false}
          {foreach from=$reports_results item=result name=results}
            <tr>
              <td nowrap="nowrap" width="25">
                {counter name='item_counter' print=true}
              </td>
              <td>
                {$result.full_num|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap">
                {$result.customer_name|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap">
                {$result.project_name|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap">
                {$result.task_name|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap">
                {$result.made_by_name|default:"&nbsp;"}
                {foreach from=$result.assignments.owners item='assignment'}
                  {if $assignment.assignee_name ne $result.made_by_name}
                    <br />{$assignment.assignee_name|escape}
                  {/if}
                {/foreach}
              </td>
              <td nowrap="nowrap">
                {foreach from=$result.assignments.observers item='assignment'}
                    {$assignment.assignee_name|escape}<br />
                {/foreach}
              </td>
              <td style="text-align: right;" nowrap="nowrap">
                {$result.task_deadline|date_format:#date_mid#|default:"&nbsp;"}
              </td>
              <td style="text-align: right;" nowrap="nowrap">
                {capture assign='status_icon'}{$theme->imagesUrl}{$result.record_type}s_{$result.status}.png{/capture}
                {if $result.substatus}{$result.substatus}&nbsp;{/if}
              </td>
              <td style="text-align: right;mso-number-format: '0\.00';">
                {$result.formated_duration|default:"&nbsp;"}
              </td>
              <td style="text-align: right;mso-number-format: '0\.00';">
                {$result.formated_planned_duration|default:"&nbsp;"}
              </td>
              <td style="text-align: right; mso-number-format: \@;">
                {if $result.late <= 0}&nbsp;{else}{$result.formated_late}{/if}
              </td>
              <td style="text-align: right; mso-number-format: '0';">
                {$result.comments|default:"0"}
              </td>
            </tr>
          {foreachelse}
            <tr>
              <td colspan="13">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
        </table>
      </td>
    </tr>
  </table>
</body>
</html>