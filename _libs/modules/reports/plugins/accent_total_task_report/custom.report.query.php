<?php
    Class Accent_Total_Task_Report Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('TASK_TYPE_TASK_ID', 2);

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            /*
             * TASKS' timesheets
             */
            $sql['select'] = 'SELECT t.id as id, t.full_num as full_num, t.active as active, "task" as record_type, ' . "\n" .
                             '  t.type as type, tti18n.name as type_name, t.customer as customer, t.planned_time as planned_duration, ' . "\n" .
                             '  t.planned_finish_date as task_deadline, ti18n.name as task_name, ts.name AS substatus, ' . "\n" .
                             '  CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, pi18n.name as project_name, ' . "\n" .
                             '  t.status as status, t.planned_finish_date as planned_finish_date, t.status_modified as status_modified, ' . "\n" .
                             '  ttime.startperiod AS start_period, ttime.endperiod as end_period, ' . "\n" .
                             '  ttime.user_id as made_by, CONCAT(ui18n.firstname, " ", ui18n.lastname) as made_by_name, ' . "\n" .
                             '  ttime.duration as duration, ttime.period_type as period_type, DATE_FORMAT(ttime.added, "%Y-%m-%d") AS added, ' . "\n" .
                             '  TIMESTAMPDIFF(MINUTE,t.planned_finish_date,ttime.added) AS late' . "\n";

            $sql['from'] =   'FROM ' . DB_TABLE_TASKS . ' AS t' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n' . "\n" .
                             '  ON (t.id=ti18n.parent_id AND ti18n.lang="' . $model_lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_TASKS_TYPES_I18N . ' AS tti18n' . "\n" .
                             '  ON (t.type=tti18n.parent_id AND tti18n.lang="' . $model_lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (t.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                             '  ON (t.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_TASKS_STATUSES . ' AS ts' . "\n" .
                             '  ON (t.substatus=ts.id AND ts.lang="' . $model_lang . '")' . "\n" .
                             'INNER JOIN ' . DB_TABLE_TASKS_TIMESHEETS . ' AS ttime' . "\n" .
                             '  ON (t.id=ttime.task_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                             '  ON (ttime.user_id=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n";

            $where = array();
            $where[] = 't.deleted_by=0';
            $where[] = 't.active=1';
            $where[] = 't.type="' . TASK_TYPE_TASK_ID . '"';
            if (!empty($filters['customer'])) {
                $where[] = 't.customer="' . $filters['customer'] . '"';
            }
            if (!empty($filters['project'])) {
                $where[] = 't.project="' . $filters['project'] . '"';
            }
            if (!empty($filters['employee'])) {
                $where[] = 'ttime.user_id="' . $filters['employee'] . '"';
            }
            if (! isset($filters['date_limiter'])) {
                if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                    $where[] = '((ttime.endperiod >= "' . $filters['from_date'] . '" AND ttime.startperiod <= "' . $filters['to_date'] . '") OR (ttime.startperiod <= "' . $filters['to_date'] . '" AND ttime.endperiod >= "' . $filters['from_date'] . '"))';
                } else if (!empty($filters['from_date'])) {
                    $where[] = 'ttime.endperiod >= "' . $filters['from_date'] . '"';
                } else if (!empty($filters['to_date'])) {
                    $where[] = 'ttime.startperiod <= "' . $filters['to_date'] . '"';
                }
            } else {
                if (!empty($filters['date_limiter'])) {
                    if (!empty($filters['date_limiter_period']) && !empty($filters['date_limiter_period_type'])) {
                        $date_period = $filters['date_limiter_period'] . ' ' . $filters['date_limiter_period_type'];
                    }
                    switch($filters['date_limiter']) {
                        case 'today':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") = CURDATE() OR DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") = CURDATE())';
                            break;
                        case 'yesterday':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") = DATE_SUB(CURDATE(), INTERVAL 1 DAY) OR DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") = DATE_SUB(CURDATE(), INTERVAL 1 DAY))';
                            break;
                        case 'tomorrow':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") = DATE_ADD(CURDATE(), INTERVAL 1 DAY) OR DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") = DATE_ADD(CURDATE(), INTERVAL 1 DAY))';
                            break;
                        case 'current_week':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%x-%v") = DATE_FORMAT(CURDATE(), "%x-%v") OR DATE_FORMAT(ttime.endperiod, "%x-%v") = DATE_FORMAT(CURDATE(), "%x-%v"))';
                            break;
                        case 'current_month':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%Y-%m") = DATE_FORMAT(CURDATE(), "%Y-%m") OR DATE_FORMAT(ttime.endperiod, "%Y-%m") = DATE_FORMAT(CURDATE(), "%Y-%m"))';
                            break;
                        case 'current_year':
                            $where[] = '(DATE_FORMAT(ttime.startperiod, "%Y") = DATE_FORMAT(CURDATE(), "%Y") OR DATE_FORMAT(ttime.endperiod, "%Y") = DATE_FORMAT(CURDATE(), "%Y"))';
                            break;
                        case 'before_x_dwm':
                            $where[] = '((DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") >= DATE_SUB(CURDATE(), INTERVAL ' . $date_period . ') AND DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") <= NOW()) OR (DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") >= DATE_SUB(CURDATE(), INTERVAL ' . $date_period . ') AND DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") <= NOW()))';
                            break;
                        case 'after_x_dwm':
                            $where[] = '((DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") <= DATE_ADD(CURDATE(), INTERVAL ' . $date_period . ') AND DATE_FORMAT(ttime.startperiod, "%Y-%m-%d") >= NOW()) OR (DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") <= DATE_ADD(CURDATE(), INTERVAL ' . $date_period . ') AND DATE_FORMAT(ttime.endperiod, "%Y-%m-%d") >= NOW()))';
                            break;
                        default:
                            break;
                    }
                }
            }

            $sql['where'] = 'WHERE ' . implode(' AND ', $where);

            $sql['order'] = 'ORDER BY t.id, ttime.added';

            //pagination only in dashlets
            if (!empty($filters['paginate']) && $registry['request']->isRequested('dashlet') && $filters['what_to_display'] == 'table') {
                //limit (for pagination)
                $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
            }

            $query = implode("\n", $sql);
            $records_for_tasks = $registry['db']->GetAll($query);

            //get the total count
            if (!empty($sql['limit'])) {
                //get the total number of records for this search
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $all = $registry['db']->GetAll($query);
                if (!empty($all) && is_array($all)) {
                    $total_records = count($all);
                }
            } else {
                //there is no limit set,
                //get the count from the found records
                $total_records = count($records_for_tasks);
            }

            $tasks_ids = array();
            foreach ($records_for_tasks as $key => $recs_tasks) {
                $tasks_ids[] = $recs_tasks['id'];
                if (! empty($recs_tasks['duration'])) {
                    //recalculate the duration according to the period of the report filters (from/to)
                    $recs_tasks['duration'] = self::recalculateTimesheetDuration($recs_tasks, $filters);

                    //format in hours
                    $records_for_tasks[$key]['formated_duration'] = sprintf("%.2F", $recs_tasks['duration']/60);
                }
                if (! empty($recs_tasks['planned_duration'])) {
                    //format in hours
                    $records_for_tasks[$key]['formated_planned_duration'] = sprintf("%.2F", $recs_tasks['planned_duration']/60);
                }

                // calculated late
                // turns the planned finsish date to seconds
                $planned_finished_time = strtotime($recs_tasks['planned_finish_date']);

                $compare_array = array();
                if ($recs_tasks['status'] == 'finished') {
                    $compare_array[] = strtotime($recs_tasks['status_modified']);
                } else {
                    $record_finished_date = 0;
                }
                if (!empty($filters['to_date'])) {
                    $compare_array[] = strtotime($filters['to_date']);
                } else {
                    $criteria_date = 0;
                }
                $compare_array[] = time();

                $compare_time = min($compare_array);
                $actual_task_late = 0;
                $actual_task_late = $compare_time - $planned_finished_time;
                if ($actual_task_late > 0) {
                    $hours = sprintf("%d", $actual_task_late/3600);
                    $minutes = sprintf("%02d", (($actual_task_late%3600)/60));
                    $records_for_tasks[$key]['late'] = sprintf("%d", $actual_task_late/60);
                    $records_for_tasks[$key]['formated_late'] = sprintf("%d:%02d", $hours, $minutes);
                } else {
                    $records_for_tasks[$key]['late'] = 0;
                    $records_for_tasks[$key]['formated_late'] = '0:00';
                }
            }

            //get task assignments + number of comments
            $task_assignments_info = array();
            $num_comments = array();
            if (! empty($tasks_ids)) {
                //prepare the assignments query
                $assignments_query = '';
                $assignments_query = 'SELECT ta.assigned_to AS assignee, ta.assignments_type AS assignments_type, ' . "\n" .
                                     '  CONCAT(ui18n.firstname, " ", ui18n.lastname) AS assignee_name, ta.parent_id as task_id ' . "\n" .
                                     'FROM ' . DB_TABLE_TASKS_ASSIGNMENTS . ' as ta'  . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                                     '  ON (ta.assigned_to=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n";
                $where_clause = array();
                $where_clause[] = 'ta.parent_id IN (' . implode(',', $tasks_ids) . ')';
                $where_clause[] = '(ta.assignments_type="' . PH_ASSIGNMENTS_OWNER . '" OR ta.assignments_type="' . PH_ASSIGNMENTS_OBSERVER . '" OR ta.assignments_type="' . PH_ASSIGNMENTS_RESPONSIBLE . '" OR ta.assignments_type="' . PH_ASSIGNMENTS_DECISION . '")';
                $assignments_query .= 'WHERE ' . implode(' AND ', $where_clause);

                $task_assignments = $registry['db']->GetAll($assignments_query);

                foreach ($task_assignments as $t_assignment) {
                    if (!isset($task_assignments_info[$t_assignment['task_id']])) {
                        $task_assignments_info[$t_assignment['task_id']] = array(
                            'owners'        => array(),
                            'observers'     => array(),
                            'responsibles'  => array(),
                            'decision'      => array()
                        );
                    }

                    if ($t_assignment['assignments_type'] == PH_ASSIGNMENTS_OWNER) {
                        $task_assignments_info[$t_assignment['task_id']]['owners'][$t_assignment['assignee']] = $t_assignment;
                    } else if ($t_assignment['assignments_type'] == PH_ASSIGNMENTS_OBSERVER) {
                        $task_assignments_info[$t_assignment['task_id']]['observers'][$t_assignment['assignee']] = $t_assignment;
                    } else if ($t_assignment['assignments_type'] == PH_ASSIGNMENTS_RESPONSIBLE) {
                        $task_assignments_info[$t_assignment['task_id']]['responsibles'][$t_assignment['assignee']] = $t_assignment;
                    } else if ($t_assignment['assignments_type'] == PH_ASSIGNMENTS_DECISION) {
                        $task_assignments_info[$t_assignment['task_id']]['decision'][$t_assignment['assignee']] = $t_assignment;
                    }
                }

                //get number of comments
                $query_num_comments = 'SELECT model_id AS idx, COUNT(id)' . "\n" .
                                      '  FROM ' . DB_TABLE_COMMENTS . "\n" .
                                      '  WHERE model="Task" AND model_id IN (' . implode(', ', array_unique($tasks_ids)) . ')' . "\n" .
                                      '  GROUP BY model_id';
                $num_comments = $registry['db']->GetAssoc($query_num_comments);
            }

            //set the assignments + number of comments
            foreach ($records_for_tasks as $key => $recs_tasks) {
                $records_for_tasks[$key]['assignments'] = $task_assignments_info[$recs_tasks['id']];
                $records_for_tasks[$key]['comments'] = array_key_exists($records_for_tasks[$key]['id'], $num_comments) ?
                                                       $num_comments[$records_for_tasks[$key]['id']] : 0;
            }


            /*
             * FINAL RESULTS
             */
            $records = $records_for_tasks;


            /*
             * PREPARE CUSTOMERS PARTIAL REPORTS
             */
            $customers_included = array();
            $customers_report = array();

            foreach ($records as $rec) {
                if ($rec['made_by'] && $rec['customer']) {
                    if (! in_array($rec['customer'], $customers_included)) {
                        $customers_included[] = $rec['customer'];
                        $customers_report[$rec['customer']]['customer_id'] = $rec['customer'];
                        $customers_report[$rec['customer']]['customer_name'] = $rec['customer_name'];
                        $customers_report[$rec['customer']]['included_tasks'] = array();
                        $customers_report[$rec['customer']]['duration'] = 0;
                    }
                    if ($rec['record_type'] == 'task') {
                        if ($rec['id'] && (!in_array($rec['id'], $customers_report[$rec['customer']]['included_tasks']))) {
                            $customers_report[$rec['customer']]['included_tasks'][] = $rec['id'];
                        }
                    }
                    $customers_report[$rec['customer']]['duration'] += $rec['duration'];
                }
            }

            foreach($customers_report as $key => $customer_report) {
                $customers_report[$key]['tasks'] = count($customer_report['included_tasks']);
                unset($customers_report[$key]['included_tasks']);

                if (! empty($customer_report['duration'])) {
                    $hours = sprintf("%d", $customer_report['duration']/60);
                    $minutes = sprintf("%02d", $customer_report['duration']%60);
                    $customers_report[$key]['formated_duration'] = sprintf("%d:%02d", $hours, $minutes);
                } else {
                    $customers_report[$key]['formated_duration'] = "0:00";
                }
            }
            ksort($customers_report);

            /*
             * PREPARE CHART
             */
            //first chart
            /* if ((isset($filters['what_to_display']) && $filters['what_to_display'] == 'graph') || !$registry['request']->isRequested('dashlet')) {
                usort($customers_report, array('self', 'sortingResultsByDuration'));
                $chart = new Chart($registry, 'Pie3D', $filters['report_type']);

                //prepare chart_data
                $chart_data = array();

                foreach ($customers_report as $key => $cr) {
                    $chart_data[$key]['label'] = $cr['customer_name'];
                    $chart_data[$key]['value'] = $cr['duration'];
                }

                //prepare chart_main_attributes
                $chart_main_attributes = array();
                $chart_main_attributes['caption'] = $registry['translater']->translate('reports_report_for_customers');
                $chart_main_attributes['xAxisName'] = $registry['translater']->translate('reports_customer_name');
                $chart_main_attributes['yAxisName'] = $registry['translater']->translate('reports_duration_minutes');

                //prepare chart_number_format
                $chart_number_format = array();
                $chart_number_format['formatNumberScale'] = 0;
                $chart_number_format['thousandSeparator'] = " ";
                $chart_number_format['chartLeftMargin'] = '15';
                $chart_number_format['chartRightMargin'] = '15';
                $chart_number_format['chartTopMargin'] = '15';
                $chart_number_format['chartBottomMargin'] = '15';

                $chart_properties = array(
                    'data'                  => $chart_data,
                    'chart_main_attributes' => $chart_main_attributes,
                    'chart_number_format'   => $chart_number_format
                );

                if ($chart->generateXMLChart($chart_properties)) {
                    $records['additional_options']['charts'][] = $chart;
                }
                $records['additional_options']['display_graph'] = true;
            }

            //second chart
            if (!isset($filters['what_to_display']) || !$registry['request']->isRequested('dashlet')) {
                usort($customers_report, array('self', 'sortingResultsByDuration'));
                $chart = new Chart($registry, 'Bar2D', $filters['report_type'], 1);
                $chart->set('top_border', '1px #B5B4B4 solid', true);

                //prepare chart_data
                $chart_data = array();

                foreach ($customers_report as $key => $cr) {
                    $chart_data[$key]['label'] = $cr['customer_name'];
                    $chart_data[$key]['value'] = $cr['duration'];
                }

                //prepare chart_main_attributes
                $chart_main_attributes = array();
                $chart_main_attributes['caption'] = $registry['translater']->translate('reports_report_for_customers');
                $chart_main_attributes['xAxisName'] = $registry['translater']->translate('reports_customer_name');
                $chart_main_attributes['yAxisName'] = $registry['translater']->translate('reports_duration_minutes');
                $chart_main_attributes['height'] = 250;

                //prepare chart_number_format
                $chart_number_format = array();
                $chart_number_format['formatNumberScale'] = 0;
                $chart_number_format['thousandSeparator'] = " ";
                $chart_number_format['chartLeftMargin'] = '15';
                $chart_number_format['chartRightMargin'] = '15';
                $chart_number_format['chartTopMargin'] = '15';
                $chart_number_format['chartBottomMargin'] = '15';

                $chart_properties = array(
                    'data'                  => $chart_data,
                    'chart_main_attributes' => $chart_main_attributes,
                    'chart_number_format'   => $chart_number_format
                );

                if ($chart->generateXMLChart($chart_properties)) {
                    $records['additional_options']['charts'][] = $chart;
                }
            } */

            if (!empty($filters['paginate'])) {
                $results = array($records, $total_records);
            } else {
                $results = $records;
            }

            return $results;
        }

        public static function sortingResultsByDuration($a, $b) {
            return ($a['duration'] < $b['duration']) ? 1 : -1;
        }

        /**
         * Recalculates the part of the timesheet duration according to the report filters
         * This is only done for the timesheets with period_type "period"
         *
         * @param array record - the timesheet data
         * @param array filters - the filters of the report
         * @return int duration - the recaldulated duration in minutes
         */
        public static function recalculateTimesheetDuration($record, $filters) {
            $duration = $record['duration'];
            //part is the remaining number of days outside the report filters
            $part = 0;

            if ($record['period_type'] == 'period') {
                $record['start_period'] = str_replace(' 00:00:00', '', $record['start_period']);
                $record['end_period'] = str_replace(' 00:00:00', '', $record['end_period']);
                if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                    if ($filters['from_date'] > $record['start_period']) {
                        $part += (strtotime($filters['from_date']) - strtotime($record['start_period']))/86400;
                    }
                    if ($record['end_period'] > $filters['to_date']) {
                        $part += (strtotime($record['end_period']) - strtotime($filters['to_date']))/86400;
                    }
                } else if (!empty($filters['from_date'])) {
                    if ($filters['from_date'] > $record['start_period']) {
                        $part += (strtotime($filters['from_date']) - strtotime($record['start_period']))/86400;
                    }
                } else if (!empty($filters['to_date'])) {
                    if ($record['end_period'] > $filters['to_date']) {
                        $part += (strtotime($record['end_period']) - strtotime($filters['to_date']))/86400;
                    }
                }
                if ($part) {
                    //the length of timesheet period in days
                    $period_length = (strtotime($record['end_period']) - strtotime($record['start_period']))/86400 + 1;
                    //add +1 because we should exclude the limit date
                    $proportion = ($period_length - $part)/$period_length;
                    $duration = $duration * $proportion;
                }
            }

            return $duration;
        }
    }
?>
