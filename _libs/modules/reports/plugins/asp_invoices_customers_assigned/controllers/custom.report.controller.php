<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_select_company_offices':
                $this->_selectCompanyOffices();
                break;
            default:
                parent::execute();
        }
    }

    public function _selectCompanyOffices() {
        $offices = Finance_Dropdown::getCompanyOffices(array(0 => $this->registry, 'company_id' => $this->registry['request']->get('company_id')));

        echo json_encode($offices);
        exit;
    }
}

?>
