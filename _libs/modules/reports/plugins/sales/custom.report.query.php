<?php

Class Sales Extends Reports {

    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();

        // Get the report settings in array var (for easier use)
        $settings = Reports::getReportSettings($registry);
        $settings['fir_types'] = preg_split('/\s*,\s*/', $settings['fir_types']);
        if (!empty($settings['incompatible_fir_types'])) {
            $settings['incompatible_fir_types'] = preg_split('/\s*,\s*/', $settings['incompatible_fir_types']);
        }

        // Get the model lang
        $model_lang = (!empty($filters['model_lang']) ? $filters['model_lang'] : $registry['lang']);

        // Get the database object
        $db = &$registry['db'];

        // Check the required filters
        if (empty($filters['period_from']) && empty($filters['period_to']) || empty($filters['fir_types'])) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));
            return self::exitReport($registry, $filters, $final_results, false);
        }

        // Check the incompatible finance incomes reasons types
        if (!empty($settings['incompatible_fir_types']) && count($filtered_incompatible_fir_types = array_intersect($settings['incompatible_fir_types'], $filters['fir_types'])) > 1) {
            $filtered_incompatible_fir_types_names = $db->GetCol("SELECT name FROM " . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . " WHERE lang = '{$model_lang}' AND parent_id IN ('" . implode("', '", $filtered_incompatible_fir_types) . "') ORDER BY name");
            $registry['messages']->setError(sprintf($registry['translater']->translate('error_reports_incompatible_fir_types'), '"' . implode('", "', (is_array($filtered_incompatible_fir_types_names) ? $filtered_incompatible_fir_types_names : array())) . '"'), 'fir_types');
            return self::exitReport($registry, $filters, $final_results, false);
        }

        /**
         * Aplly filters
         */
        $where = array();

        // Filter: period
        if (!empty($filters['period_from'])) {
            if (empty($filters['period_to'])) {
                $where[] = "`fir`.`issue_date` = '{$filters['period_from']}'";
            } else {
                $where[] = "'{$filters['period_from']}' <= `fir`.`issue_date`";
            }
        }
        if (!empty($filters['period_to'])) {
            $where[] = "`fir`.`issue_date` <= '{$filters['period_to']}'";
        }

        // Filter: client
        if (!empty($filters['client'])) {
            $where[] = "`fir`.`customer` = '{$filters['client']}'";
        }

        // process multiple articles filter
        $articles_list = array();
        $articles_filtered = array_filter($filters['article']);
        $articles_filter = false;
        if (!empty($articles_filtered)) {
            $articles_list = array_values(array_unique($articles_filtered));
            $articles_filter = true;
        }
        if (!empty($filters['group'])) {
            $query = 'SELECT `parent_id` FROM '. DB_TABLE_NOM_CATS . ' WHERE `model`="Nomenclature" AND `cat_id`="' . $filters['group'] . '"' . "\n";
            $articles_list = $registry['db']->GetCol($query);

            if (!empty($articles_filtered)) {
                $articles_list = array_intersect($articles_filtered, $articles_list);
            }
            $articles_filter = true;
        }
        if ($articles_filter && empty($articles_list)) {
            $articles_list = array(0);
        }

        // Filter by status
        if (!empty($settings['fir_statuses'])) {
            $settings['fir_statuses'] = preg_split('/\s*,\s*/', $settings['fir_statuses']);
            if (!empty($settings['fir_statuses'])) {
                if (count($settings['fir_statuses']) == 1) {
                    $where[] = "`fir`.`status` = '" . array_shift($settings['fir_statuses']) . "'";
                } else {
                    $where[] = "`fir`.`status` IN ('" . implode("', '", $settings['fir_statuses']) . "')";
                }
            }
        }

        /**
         * Filter by companies, offices, cashboxes and bank accounts
         */
        // Get the accessible records
        $companies_rights_ids     = $registry['currentUser']->get('finance_companies');
        $offices_rights_ids       = $registry['currentUser']->get('finance_offices');
        $cashboxes_rights_ids     = $registry['currentUser']->get('finance_cashboxes');
        $bank_accounts_rights_ids = $registry['currentUser']->get('finance_bank_accounts');
        if (empty($companies_rights_ids) || empty($offices_rights_ids) || empty($cashboxes_rights_ids) && empty($bank_accounts_rights_ids)) {
            return false;
        }
        preg_match('/^(\d+)(_(\d+))*$/', $filters['companies'], $matches);
        $company_ids = isset($matches[1]) ? array_intersect($companies_rights_ids, array($matches[1])) : $companies_rights_ids;
        $office_ids  = isset($matches[3]) ? array_intersect($offices_rights_ids,   array($matches[3])) : $offices_rights_ids;
        if (empty($company_ids) || empty($office_ids)) {
            return false;
        }

        // Filter by company
        $where[] = "fir.company IN (" . implode(', ', $company_ids) . ")";

        // Filter by office
        $where[] = "fir.office IN (" . implode(', ', $office_ids) . ")";

        // Filter by cashboxes and/or bank accounts
        $where_cash_bank = array();
        if (!empty($cashboxes_rights_ids)){
            $where_cash_bank[] = "fir.payment_type = 'cash' AND fir.container_id IN (" . implode(', ', $cashboxes_rights_ids['PKO']['add']) . ")";
        }
        if (!empty($bank_accounts_rights_ids)){
            $where_cash_bank[] = "fir.payment_type = 'bank' AND fir.container_id IN (" . implode(', ', $bank_accounts_rights_ids['BP']['add']) . ")";
        }
        if (!empty($where_cash_bank)) {
            $where[] = '(' . implode(" OR ", $where_cash_bank) . ')';
        }

        // Filter by type
        $where[] = "fir.type IN ('" . implode("', '", $filters['fir_types']) . "')";

        if (!empty($filters['fir_payment_type'])) {
            $where[] = "fir.payment_type IN ('" . implode("', '", $filters['fir_payment_type']) . "')";
        }

        $sql = array();

        // Get the records
        $sql['select'] = "
            SELECT `fir`.`id`                                         AS `fir_id`,
                `fir`.customer                                        AS `customer_id`,
                `fir`.currency                                        AS `fir_currency`,
                `fir`.total                                           AS `total`,
                `fir`.total_with_vat                                  AS `total_with_vat`,
                TRIM(CONCAT(`ci18n`.`name`, ' ', `ci18n`.`lastname`)) AS `customer_name`,
                TRIM(CONCAT(`ei18n`.`name`, ' ', `ei18n`.`lastname`)) AS `employee`,
                `fir`.`num`                                           AS `num`,
                `gd`.`article_id`                                     AS `article_id`,
                `gdi18n`.`article_name`                               AS `article_name`,
                `gd`.`quantity`                                       AS `quantity`,
                `gdi18n`.`article_measure_name`                       AS `article_measure_id`,
                `nm`.`name`                                           AS `article_measure_name`,
                `gd`.`price`                                          AS `price`,
                `gd`.`price_with_discount`                            AS `price_with_discount`,
                `gd`.`subtotal_with_discount`                         AS `subtotal_with_discount`,
                `gd`.`subtotal_profit_no_final_discount`              AS `profit`,
                `gd`.`subtotal_with_vat_with_discount`                AS `subtotal_with_vat_with_discount`";
        $sql['from'] = "
              FROM `" . DB_TABLE_FINANCE_INCOMES_REASONS . "` AS `fir`";
        $sql['join_ci18n'] = "
              JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci18n`
                ON (`ci18n`.`parent_id` = `fir`.`customer`
                  AND `ci18n`.`lang`    = '{$model_lang}')";
        if (!empty($settings['display_customer_type'])) {
            $sql['select'] .= ", cti18n.name AS `customer_type_name`";
            $sql['join_c'] = "
              JOIN `" . DB_TABLE_CUSTOMERS . "` AS `c`
                ON `c`.`id` = `fir`.`customer`";
            $sql['join_cti18n'] = "
              JOIN `" . DB_TABLE_CUSTOMERS_TYPES_I18N . "` AS `cti18n`
                ON (`cti18n`.`parent_id` = `c`.`type`
                  AND `cti18n`.`lang`    = '{$model_lang}')";
        }
        $sql['join_gd'] = "
              JOIN `" . DB_TABLE_GT2_DETAILS . "` AS `gd`
                ON (`gd`.`model`      = 'Finance_Incomes_Reason'
                  AND `gd`.`model_id` = `fir`.`id`" .
            (!empty($articles_list) ? "
                  AND `gd`.`article_id` IN (" . implode(',', $articles_list) . ")" : '') .  ")";
        $sql['join_gdi18n'] = "
              JOIN `" . DB_TABLE_GT2_DETAILS_I18N . "` AS `gdi18n`
                ON (`gdi18n`.`parent_id` = `gd`.`id`
                  AND `gdi18n`.`lang`    = '{$model_lang}')";
        $sql['join_ei18n'] = "
              LEFT JOIN `" . DB_TABLE_USERS . "` AS `ue`
                ON (`fir`.`added_by` = `ue`.`id`)
              LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ei18n`
                ON (`ei18n`.`parent_id` = `ue`.`employee`
                  AND `ei18n`.`lang`    = '{$model_lang}')";
        $sql['join_nm'] = "
              JOIN `" . DB_TABLE_MEASURES . "` AS `nm`
                ON (`nm`.`id`     = `gdi18n`.`article_measure_name`
                  AND `nm`.`lang` = '{$model_lang}')";
        $sql['where'] = "
              WHERE `fir`.`annulled_by` = '0'
                AND `fir`.`active`      = '1'" .
            (!empty($where) ? "
                AND " . implode("
                AND ", $where) : '');
        $sql['order_by'] = "
          ORDER BY `fir`.`id`, `gd`.`id`";

        $query = implode($sql);
        $final_results['records'] = $db->GetAll($query);

        $rates = array();
        $measures = array();
        $price_total = 0;
        $price_with_discount_total = 0;
        $subtotal_with_discount_total = 0;
        $total = 0;
        $total_with_vat = 0;
        $subtotal_with_vat_with_discount_total = 0;
        $rowspan = array();
        $profits = array();
        $is_odd = false;

        $summerized_measures = array();
        $sum_by_articles = array();
        $sum_by_customers = array();

        $ids = array();
        $total_subtotal_with_vat_with_discount = 0;
        $total_subtotal_with_discount = 0;
        $total_total = 0;
        $total_total_with_vat = 0;
        $total_profit = 0;

        if (empty($final_results['records'])) {
            $final_results['additional_options']['dont_show_export_button'] = true;
        } else {
            // get the conversion rate between Currencies
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

            foreach ($final_results['records'] as $key => $record) {
                $rate = (isset($rates[$record['fir_currency']])) ? $rates[$record['fir_currency']] : Finance_Currencies::getRate($registry, $record['fir_currency'], $filters['currency']);

                if ($filters['results'] == 'detailed') {
                    $is_odd = !$is_odd;
                    $rates[$record['fir_currency']] = $rate;
                    if (isset($final_results['records'][$key]['price']) && isset($final_results['records'][$key]['price_with_discount'])) {
                        $final_results['records'][$key]['price'] = $record['price'] * $rate;
                        $final_results['records'][$key]['price_with_discount'] = $record['price_with_discount'] * $rate;
                        $price_total += $final_results['records'][$key]['price'];
                        $price_with_discount_total += $final_results['records'][$key]['price_with_discount'];
                    }
                    $final_results['records'][$key]['subtotal_with_discount'] = $record['subtotal_with_discount'] * $rate;
                    $final_results['records'][$key]['subtotal_with_vat_with_discount'] = $record['subtotal_with_vat_with_discount'] * $rate;
                    $final_results['records'][$key]['total'] = $record['total'] * $rate;
                    $final_results['records'][$key]['total_with_vat'] = $record['total_with_vat'] * $rate;
                    if (!isset($profits[$record['fir_id']])) {
                        $profits[$record['fir_id']] = 0;
                    }
                    $profits[$record['fir_id']] += ($record['profit'] * $rate);
                    $total_profit += ($record['profit'] * $rate);

                    $subtotal_with_discount_total += $final_results['records'][$key]['subtotal_with_discount'];
                    $subtotal_with_vat_with_discount_total += $final_results['records'][$key]['subtotal_with_vat_with_discount'];

                    if (!isset($measures[$record['article_measure_id']])) {
                        $measures[$record['article_measure_id']] = array(
                            'name' => $record['article_measure_name'],
                            'quantity' => $record['quantity']
                        );
                    } else {
                        $measures[$record['article_measure_id']]['quantity'] += $record['quantity'];
                    }

                    if (!isset($rowspan[$record['fir_id']])) {
                        $total += $final_results['records'][$key]['total'];
                        $total_with_vat += $final_results['records'][$key]['total_with_vat'];
                        $rowspan[$record['fir_id']] = 1;
                    } else {
                        $is_odd = !$is_odd;
                        $rowspan[$record['fir_id']] = $rowspan[$record['fir_id']] + 1;
                        unset($final_results['records'][$key]['total']);
                        unset($final_results['records'][$key]['total_with_vat']);
                        unset($final_results['records'][$key]['customer_name']);
                        unset($final_results['records'][$key]['num']);
                        unset($final_results['records'][$key]['profit']);
                    }
                    $final_results['records'][$key]['is_odd'] = $is_odd;
                    $final_results['measures'] = $measures;
                    $final_results['price_total'] = $price_total;
                    $final_results['price_with_discount_total'] = $price_with_discount_total;
                    $final_results['subtotal_with_discount_total'] = $subtotal_with_discount_total;
                    $final_results['subtotal_with_vat_with_discount_total'] = $subtotal_with_vat_with_discount_total;
                    $final_results['total'] = $total;
                    $final_results['total_with_vat'] = $total_with_vat;
                    $final_results['rowspan'] = $rowspan;
                    $final_results['profits'] = $profits;

                } elseif ($filters['results'] == 'summarized_by_articles') {
                    $fir_id = $record['fir_id'];
                    $article_id = $record['article_id'];
                    $article_name = $record['article_name'];
                    $quantity = $record['quantity'];
                    $article_measure_id = $record['article_measure_id'];
                    $article_measure_name = $record['article_measure_name'];
                    $subtotal_with_vat_with_discount = $record['subtotal_with_vat_with_discount'] * $rate;
                    $subtotal_with_discount = $record['subtotal_with_discount'] * $rate;
                    $total = $record['total'] * $rate;
                    $total_with_vat = $record['total_with_vat'] * $rate;
                    $profit = $record['profit'] * $rate;

                    $total_profit += $profit;
                    if (!isset($profits[$article_id])) {
                        $profits[$article_id] = 0;
                    }
                    $profits[$article_id] += $profit;

                    if (!isset($sum_by_articles[$article_id][$article_measure_id])) {
                        $sum_by_articles[$article_id][$article_measure_id] = array(
                            'article_name' => $article_name,
                            'quantity' => $quantity,
                            'article_measure_name' => $article_measure_name,
                            'subtotal_with_discount' => $subtotal_with_discount,
                            'subtotal_with_vat_with_discount' => $subtotal_with_vat_with_discount,
                            'sum_total' => $total,
                            'sum_total_with_vat' => $total_with_vat
                        );
                    } else {
                        if (!isset($ids[$fir_id])) {
                            $sum_by_articles[$article_id][$article_measure_id] = array(
                                'article_name' => $article_name,
                                'quantity' => $quantity + $sum_by_articles[$article_id][$article_measure_id]['quantity'],
                                'article_measure_name' => $article_measure_name,
                                'subtotal_with_discount' => $subtotal_with_discount + $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_discount'],
                                'subtotal_with_vat_with_discount' => $subtotal_with_vat_with_discount + $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_vat_with_discount'],
                                'sum_total' => $total + $sum_by_articles[$article_id][$article_measure_id]['sum_total'],
                                'sum_total_with_vat' => $total_with_vat + $sum_by_articles[$article_id][$article_measure_id]['sum_total_with_vat']
                            );
                        } else {
                            $sum_by_articles[$article_id][$article_measure_id]['quantity'] = $quantity + $sum_by_articles[$article_id][$article_measure_id]['quantity'];
                            $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_discount'] = $subtotal_with_discount + $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_discount'];
                            $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_vat_with_discount'] = $subtotal_with_vat_with_discount + $sum_by_articles[$article_id][$article_measure_id]['subtotal_with_vat_with_discount'];
                        }
                    }

                    if (!isset($ids[$fir_id])) {
                        $ids[$fir_id] = $fir_id;
                        $total_total += $total;
                        $total_total_with_vat += $total_with_vat;
                    }

                    // Total sum of Prelast 2 columns
                    $total_subtotal_with_discount += $subtotal_with_discount;
                    $total_subtotal_with_vat_with_discount += $subtotal_with_vat_with_discount;
                    if (!isset($summerized_measures[$record['article_measure_id']])) {
                        $summerized_measures[$record['article_measure_id']] = array(
                            'article_measure_name' => $record['article_measure_name'],
                            'quantity' => $record['quantity']
                        );
                    } else {
                        $summerized_measures[$record['article_measure_id']]['quantity']+=$record['quantity'];
                    }
                    $final_results['summerized_measures'] = $summerized_measures;
                    $final_results['profits'] = $profits;

                } elseif ($filters['results'] == 'summarized_by_customers') {
                    $c_id = $record['customer_id'];
                    $total = $record['total'] * $rate;
                    $total_with_vat = $record['total_with_vat'] * $rate;
                    $profit = $record['profit'] * $rate;

                    $total_profit += $profit;
                    if (!isset($profits[$c_id])) {
                        $profits[$c_id] = 0;
                    }
                    $profits[$c_id] += $profit;

                    if (!isset($sum_by_customers[$c_id])) {
                        $sum_by_customers[$c_id] = array(
                            'ids' => [$record['fir_id']],
                            'customer_name' => $record['customer_name'],
                            'sum_total' => $total,
                            'sum_total_with_vat' => $total_with_vat
                        );
                    } elseif (!in_array($record['fir_id'], $sum_by_customers[$c_id]['ids'])) {
                        $sum_by_customers[$c_id]['ids'][] = $record['fir_id'];
                        $sum_by_customers[$c_id]['sum_total'] += $total;
                        $sum_by_customers[$c_id]['sum_total_with_vat'] += $total_with_vat;
                    }
                    if (!empty($articles_list) && in_array($record['article_id'], $articles_list)) {
                        if (!array_key_exists('subtotal_with_discount', $sum_by_customers[$c_id])) {
                            $sum_by_customers[$c_id]['subtotal_with_discount'] = 0;
                            $sum_by_customers[$c_id]['subtotal_with_vat_with_discount'] = 0;
                        }
                        $sum_by_customers[$c_id]['subtotal_with_discount'] +=
                            $record['subtotal_with_discount'] * $rate;
                        $sum_by_customers[$c_id]['subtotal_with_vat_with_discount'] +=
                            $record['subtotal_with_vat_with_discount'] * $rate;;
                    }
                    $final_results['profits'] = $profits;
                }
            }
            uasort($sum_by_customers, array('self', 'sortByCustomerName'));
        }

        $final_results['articles_list'] = $articles_list;
        $final_results['summarized_by_articles'] = $sum_by_articles;
        $final_results['summarized_by_customers'] = $sum_by_customers;
        $final_results['sum_total_subtotal_with_discount'] = $total_subtotal_with_discount;
        $final_results['total_subtotal_with_vat_with_discount'] = $total_subtotal_with_vat_with_discount;
        $final_results['total_total'] = $total_total;
        $final_results['total_profit'] = $total_profit;
        $final_results['total_total_with_vat'] = $total_total_with_vat;
        $final_results['display_customer_type'] = $settings['display_customer_type'] ?? false;

        return self::exitReport($registry, $filters, $final_results, true);
    }

    private static function exitReport(&$registry, $filters, $final_results, $success = false, $error = '') {
        if (!$success) {
            $final_results['additional_options']['failed'] = true;
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                $final_results['additional_options']['error'] = $registry['translater']->translate($error);
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }

    public static function sortByCustomerName($a, $b) {
        return ($a['customer_name'] > $b['customer_name']) ? 1 : -1;
    }
}

?>
