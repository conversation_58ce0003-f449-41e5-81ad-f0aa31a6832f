<?php
    Class Colliers_Revenue_Rental_Price_Calculation Extends Reports {

        public static function buildQuery(&$registry, $filters = array()) {

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $table_name = Reports::getTemporaryTableName($registry);
            $settings = Reports::getReportSettings($registry);
            $dependencies = array();
            foreach (preg_split('#\s*,\s*#', $settings['report_dependencies']) as $v) {
                list($k, $v) = preg_split('#\s*\=\>\s*#', $v);
                $dependencies[$k] = $v;
            }

            if ($registry->get('report_type')) {
                $report = $registry->get('report_type');
                $report = Reports::getReports($registry, $report);
                $report = $report[0];
            } else {
                return false;
            }

            if (empty($filters['date_from']) || empty($filters['date_to'])) {
                if (empty($filters['date_from'])) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_no_date_from'));
                }
                if (empty($filters['date_to'])) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_no_date_to'));
                }
                return false;
            }

            //get variables ids
            $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                     'WHERE (name IN ("object_id", "first_date_invoicing", "irp_date_from", "inc_rentalprice") AND model="Contract"'. "\n" .
                     '      AND model_type=' . REPORT_CONTRACT_TYPE . ') OR (name="type_unit" AND model="Nomenclature" AND model_type=2)';
            $var_ids = $registry['db']->GetAssoc($query);


            if ($registry['request']->isRequested('custom_generate') || $registry['action'] == 'dashlet') {
                //the following must be here for this kind of report
                $query = 'DROP TABLE IF EXISTS ' . $table_name;
                $records = $registry['db']->Execute($query);

                $sql['create'] = 'CREATE TABLE IF NOT EXISTS ' . $table_name . "\n" .
                                 '  (idx int(11) AUTO_INCREMENT,' . "\n" .
                                 '   selected tinyint(1),' . "\n" .
                                 '   contract_id int(11),' . "\n" .
                                 '   contract_num varchar(255),' . "\n" .
                                 '   financial_id int(11),' . "\n" .
                                 '   date_start date,' . "\n" .
                                 '   date_validity date,' . "\n" .
                                 '   customer int(11),' . "\n" .
                                 '   customer_name varchar(255),' . "\n" .
                                 '   trademark int(11),' . "\n" .
                                 '   trademark_name varchar(255),' . "\n" .
                                 '   object_id int(11),' . "\n" .
                                 '   object_name varchar(255),' . "\n" .
                                 '   PRIMARY KEY(idx)' . "\n" .
                                 '  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci AS ' . "\n";
                $sql['select'] = 'SELECT NULL as idx, 0 AS selected, c.id AS contract_id, c.num AS contract_num,' . "\n" .
                                 '  c.cstm_financial as financial_id, IF(ccstm2.value!="" AND ccstm2.value IS NOT NULL, ccstm2.value, c.date_start) as date_start, c.date_validity,' . "\n" .
                                 '  c.customer, CONCAT_WS(\' \', ci18n.name, ci18n.lastname) AS customer_name,' . "\n" .
                                 '  c.trademark, ni18n.name AS trademark_name,' . "\n" .
                                 '  ccstm1.value AS object_id, ni18n1.name AS object_name' . "\n";
                $sql['from'] = 'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               '  ON c.customer = ci18n.parent_id AND ci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                               '  ON c.trademark = ni18n.parent_id AND ni18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                               'JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm1' . "\n" .
                               '    ON ccstm1.model_id = c.id AND ccstm1.var_id = ' . $var_ids['object_id'] . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm2' . "\n" .
                               '    ON ccstm2.model_id = c.id AND ccstm2.var_id = ' . $var_ids['first_date_invoicing'] . "\n" .
                               'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm3' . "\n" .
                               '    ON ncstm3.model_id = ccstm1.value AND ncstm3.var_id = ' . $var_ids['type_unit'] . ' AND ncstm3.value != "warehouse_unit"' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n1' . "\n" .
                               '  ON ccstm1.value = ni18n1.parent_id AND ni18n1.lang = \'' . $registry['lang'] . '\'' . "\n";
                $sql['where'][] = 'WHERE c.subtype = "contract" AND c.active=1 AND c.deleted_by=0 AND c.type = ' . REPORT_CONTRACT_TYPE;
                $sql['where'][] = 'c.status = "closed" AND c.substatus != 2';
                $sql['where'][] = 'c.date_start > "0000-00-00" AND c.date_start <= "' . $filters['date_to'] . '"';
                $sql['where'][] = 'c.date_validity >= "' . $filters['date_from'] . '"';

                //add filter for a customer if requested
                if (!empty($filters['customer'])) {
                    $sql['where'][] = 'c.customer = ' . $filters['customer'];
                }

                //add filter for a trademark if requested
                if (!empty($filters['trademark'])) {
                    $sql['where'][] = 'c.trademark = ' . $filters['trademark'];
                }

                $sql['where'] = implode("\nAND ", $sql['where']);

                $sql['having'] = 'HAVING date_start <= "' . $filters['date_to'] . '"';

                $sql['order_by'] = 'ORDER BY object_name';

                $query = implode("\n", $sql);
                $records = $registry['db']->Execute($query);
            }

            $sql = array();
            $sql['select'] = 'SELECT v.*' . "\n";
            $sql['from'] =   'FROM ' . $table_name . ' AS v' . "\n";
            $sql['where'] = '';

            //!!! NO PAGINATION FOR THIS REPORT !!!
            $filters['limit'] = '';
            //$filters['paginate'] = '';

            //limit (for pagination)
            $sql['limit'] = (!empty($filters['limit'])) ? ' LIMIT ' . $filters['limit'] . "\n" : '';

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            //prepare some parameters
            $params = array('articles_ids' =>  preg_split('#\s*,\s*#', REPORT_ARTICLES . ',' . REPORT_ARTICLES_INVOICED));
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $prec = $registry['config']->getSectionParams('precision');

            $t_currency = $registry['config']->getParam('turnovers', 'currency');
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            if (empty($t_currency)) {
                $t_currency = Finance_Currencies::getMain($registry);
            }

            //prepare deposit amount
            //The great function of Iliya is used
            foreach ($records as $key => $record) {
                $params['contracts_ids'] = $record['contract_id'];

                //get percents stairs
                $query = 'SELECT ccstm1.value as `from`, ccstm2.value as `percent`' . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm1' . "\n" .
                         '  ON ccstm1.model_id = c.id AND ccstm1.var_id = ' . $var_ids['irp_date_from'] . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm2' . "\n" .
                         '  ON ccstm2.model_id = c.id AND ccstm2.var_id = ' . $var_ids['inc_rentalprice'] . "\n" .
                         'WHERE c.id = ' . $record['contract_id'] . "\n" .
                         '  AND c.active=1 AND c.deleted_by=0' . "\n" .
                         '  AND ccstm1.value != "" AND ccstm1.value IS NOT NULL' . "\n" .
                         '  AND ccstm2.value != "" AND ccstm2.value IS NOT NULL' . "\n" .
                         '  AND ccstm1.num = ccstm2.num';
                $percents = $registry['db']->GetAll($query);
                if (empty($percents)) {
                    unset($records[$key]);
                    continue;
                }
                //get contract financial contact
                $records[$key]['financial'] = array();
                $contract = new Contract($registry, array('type' => REPORT_CONTRACT_TYPE));
                $contract->set('id', $record['contract_id'], true);
                $contract->set('cstm_financial', $record['financial_id'], true);
                $records[$key]['financial'][] = $contract->getCstmFinancialData();
                $records[$key]['financial'] = array_merge($records[$key]['financial'],
                                                          $contract->getContactCcData('cstm', 'fin'));
                //calculate period length
                if ($record['date_start'] < $filters['date_from']) {
                    $record['date_start'] = $filters['date_from'];
                }
                if ($record['date_validity'] > $filters['date_to']) {
                    $record['date_validity'] = $filters['date_to'];
                }

                foreach ($percents as $per => $cent) {
                    if ($cent['from'] > $record['date_validity'] ||
                      !empty($percents[$per + 1]) && $percents[$per + 1] < $record['date_start']) {
                        unset($percents[$per]);
                        continue;
                    }
                    if (!empty($percents[$per + 1])) {
                        $percents[$per]['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($percents[$per+1]['from'])));
                        if ($percents[$per]['to'] > $record['date_validity']) {
                            $percents[$per]['to'] = $record['date_validity'];
                        }
                    } else {
                        $percents[$per]['to'] = $record['date_validity'];
                    }
                    if ($percents[$per]['to'] < $record['date_start']) {
                        unset($percents[$per]);
                    }
                }
                $percents = array_values($percents);

                // if no percents have left then the contract is unset and
                //the report continues with the next contract
                if (empty($percents)) {
                    unset($records[$key]);
                    continue;
                }

                if ($percents[0]['from'] <  $record['date_start']) {
                    $percents[0]['from'] =  $record['date_start'];
                }

                $records[$key]['base_price'] = 0;
                $records[$key]['invoiced'] = 0;
                $records[$key]['total_days'] = 0;
                $records[$key]['turnovers_price'] = 0;
                $records[$key]['difference'] = 0;
                $records[$key]['incomes_count'] = 0;
                $records[$key]['currency'] = $t_currency;
                $records[$key]['turnover_confirmed'] = 0;
                //invoices have to be issued
                if ($filters['report_for'] == 'invoice') {

                    foreach ($percents as $per => $cent) {
                        $params['date_from'] = $cent['from'];
                        $params['date_to'] = $cent['to'];
                        $gt2_rows = Contracts::getInvoicedAmount($registry,$params);
                        //for each stair of the rental price we calculate how much is the difference
                        $records[$key]['extended'][$per]['date_from'] = $cent['from'];
                        $records[$key]['extended'][$per]['date_to'] = $cent['to'];
                        $records[$key]['extended'][$per]['base_price'] = 0;
                        $records[$key]['extended'][$per]['invoiced'] = 0;
                        if (!empty($gt2_rows)) {
                            foreach ($gt2_rows as $idx => $row) {
                                $c_rate = Finance_Currencies::getRate($registry, $row['invoice_currency'], $t_currency);
                                if (in_array($row['article_id'], preg_split('#\s*,\s*#', REPORT_ARTICLES_INVOICED))) {
                                    $records[$key]['extended'][$per]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                                    $records[$key]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                                } else {
                                    $records[$key]['extended'][$per]['base_price'] += $row['price'] * $row['quantity'] * $c_rate;
                                }
                            }
                            $records[$key]['currency'] = $t_currency;
                        } else {
                            $gt2_contract = $contract->getGT2Vars();
                            foreach ($gt2_contract['values'] as $idx => $row) {
                                if (array_key_exists($row['article_id'], $dependencies)) {
                                    $records[$key]['article_id'] = $row['article_id'];
                                    $records[$key]['currency'] = $t_currency;
                                }
                            }

                        }
                        //get turnovers for the period
                        $query = 'SELECT SUM(total) AS value, COUNT(total) AS count, IF(t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00", 1, 0)  as confirmed' . "\n" .
                             'FROM ' . DB_TABLE_TURNOVERS . ' AS t' . "\n" .
                             'WHERE t.customer = ' . $record['customer'] . "\n" .
                             '  AND t.trademark = ' . $record['trademark'] . "\n" .
                             //IMPORTANT: do not uncomment the following line!!!
                             //the uncofirmed turnovers should be displayed as well
                             //'  AND t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00"' . "\n" .
                             '  AND t.day >= "' . $cent['from'] . '"' . "\n" .
                             '  AND t.day <= "' . $cent['to'] . '"';
                        $incomes = $registry['db']->GetRow($query);
                        //distribute data for the stairs
                        $records[$key]['extended'][$per]['total_days'] = strtotime($cent['to']) - strtotime($cent['from']);
                        $records[$key]['extended'][$per]['total_days'] = round($records[$key]['extended'][$per]['total_days'] /3600 / 24) + 1;
                        $records[$key]['extended'][$per]['percent'] = $cent['percent'];
                        $records[$key]['extended'][$per]['base_price'] = round($records[$key]['extended'][$per]['base_price'], $prec['gt2_total_with_vat']);
                        $records[$key]['extended'][$per]['turnovers_price'] = $incomes['value'] * $cent['percent'] / 100;
                        $records[$key]['extended'][$per]['turnovers_price'] = round($records[$key]['extended'][$per]['turnovers_price'], $prec['gt2_total_with_vat']);
                        $records[$key]['extended'][$per]['difference'] = $records[$key]['extended'][$per]['turnovers_price'] - $records[$key]['extended'][$per]['base_price'] - $records[$key]['extended'][$per]['invoiced'];
                        $records[$key]['extended'][$per]['incomes_count'] = $incomes['count'];
                        $records[$key]['extended'][$per]['turnover_confirmed'] = $incomes['confirmed'];

                        $records[$key]['total_days'] += $records[$key]['extended'][$per]['total_days'];
                        $records[$key]['base_price'] += $records[$key]['extended'][$per]['base_price'];
                        $records[$key]['turnovers_price'] += $records[$key]['extended'][$per]['turnovers_price'];
                        $records[$key]['difference'] += $records[$key]['extended'][$per]['difference'];
                        $records[$key]['incomes_count'] += $incomes['count'];
                        $records[$key]['turnover_confirmed'] += $incomes['confirmed'];
                    }

                    //check if all the periods are confirmed
                    if ($records[$key]['turnover_confirmed'] < count($percents)) {
                        $records[$key]['turnover_confirmed'] = 0;
                    }

                    if (round($records[$key]['difference'], $prec['gt2_total_with_vat']) <= 0) {
                        unset($records[$key]);
                        continue;
                    }
                } elseif ($filters['report_for'] == 'credit') {
                    //credit notices will be issued
                    $params['date_from'] = $filters['date_from'];
                    $params['date_to'] = $filters['date_to'];
                    //get invoiced for the whole period
                    $gt2_rows = Contracts::getInvoicedAmount($registry,$params);

                    //get all model IDS
                    $ids = array();
                    foreach($gt2_rows as $row) {
                        $ids[] = $row['model_id'];
                    }

                    //get relations between the models
                    $query = 'SELECT parent_id, link_to FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                             'WHERE parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                             '  AND link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                             '  AND link_to IN (' . implode(', ', $ids) . ')';
                    $relations = $registry['db']->GetAssoc($query);

                    $ordered = array();
                    //combine the rows in function of the invoice id
                    foreach ($gt2_rows as $row) {
                        if (!empty($relations[$row['model_id']])) {
                            $id = $relations[$row['model_id']];
                        } else {
                            $id = $row['model_id'];
                        }
                        if (empty($ordered[$id])) {
                            $ordered[$id][0]['from'] = $row['date_from'];
                            $ordered[$id][0]['to'] = $row['date_to'];
                            $ordered[$id][0]['invoiced'] = 0;
                            $ordered[$id][0]['base_price'] = 0;
                        } else {
                            if ($row['date_from'] < $ordered[$id][0]['from']) {
                                $ordered[$id][0]['from'] = $row['date_from'];
                            }
                            if ($row['date_to'] > $ordered[$id][0]['to']) {
                                $ordered[$id][0]['to'] = $row['date_to'];
                            }
                        }
                        $c_rate = Finance_Currencies::getRate($registry, $row['invoice_currency'], $t_currency);
                        if (in_array($row['article_id'], preg_split('#\s*,\s*#', REPORT_ARTICLES_INVOICED))) {
                            $ordered[$id][0]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                        } else {
                            $ordered[$id][0]['base_price'] += $row['price'] * $row['quantity'] * $c_rate;
                        }
                    }

                    //distribute percent for the invoices
                    $gt2_rows = $ordered;
                    foreach ($percents as $per => $cent) {
                        foreach($gt2_rows as $r => $rows) {
                            foreach ($rows as $rr => $row) {
                                if ($cent['to'] < $row['from'] || $cent['from'] > $row['to']) {
                                    continue;
                                }
                                if ($cent['from'] <= $row['from']) {
                                    if ($cent['to'] < $row['to']) {
                                        //devide in two
                                        $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                        $index = round((strtotime($cent['to']) - strtotime($row['from'])) / 3600 /24 + 1) / $index;
                                        $gt2_rows[$r][$rr]['to'] = $cent['to'];
                                        $gt2_rows[$r][$rr]['invoiced'] = $row['invoiced'] * $index;
                                        $gt2_rows[$r][$rr]['base_price'] = $row['base_price'] * $index;
                                        $gt2_rows[$r][$rr]['percent'] = $cent['percent'];
                                        $gt2_rows[$r][] = array('from' => General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($cent['to']))),
                                                                'to' => $row['to'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'],
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price']);
                                    } else {
                                        $gt2_rows[$r][$rr]['percent'] = $cent['percent'];
                                    }
                                } else {
                                    $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                    $index = round((strtotime($cent['from']) - strtotime($row['from'])) / 3600 /24) / $index;
                                    $gt2_rows[$r][$rr]['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($cent['from'])));
                                    $gt2_rows[$r][$rr]['invoiced'] = $row['invoiced'] * $index;
                                    $gt2_rows[$r][$rr]['base_price'] = $row['base_price'] * $index;
                                    if ($cent['to'] < $row['to']) {
                                        //devide in three
                                        $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                        $index = round((strtotime($cent['to']) - strtotime($cent['from'])) / 3600 /24 + 1) / $index;
                                        $index = $row['invoiced'] * $index;
                                        $index1 = $row['base_price'] * $index;
                                        $gt2_rows[$r][] = array('from' => $cent['from'],
                                                                'to' => $cent['to'],
                                                                'invoiced' => $index,
                                                                'base_price' => $index1,
                                                                'percent' => $cent['percent']);

                                        $gt2_rows[$r][] = array('from' => General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($cent['to']))),
                                                                'to' => $row['to'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'] - $index,
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price'] - $index1);
                                    } else {
                                        //devide in two
                                        $gt2_rows[$r][] = array('from' => $cent['from'],
                                                                'to' => $row['to'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'],
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price'],
                                                                'percent' => $cent['percent']);
                                    }
                                }
                            }
                        }
                    }

                    foreach ($gt2_rows as $r => $rows) {
                        foreach ($rows as $rr => $row) {
                            $per = $r . '|' . $rr;
                            $records[$key]['extended'][$per]['date_from'] = $row['from'];
                            $records[$key]['extended'][$per]['date_to'] = $row['to'];
                            $records[$key]['extended'][$per]['base_price'] = round($row['base_price'], $prec['gt2_total_with_vat']);
                            $records[$key]['extended'][$per]['invoiced'] = round($row['invoiced'], $prec['gt2_total_with_vat']);

                            $query = 'SELECT SUM(total) AS value, COUNT(total) AS count, IF(t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00", 1, 0)  as confirmed' . "\n" .
                                 'FROM ' . DB_TABLE_TURNOVERS . ' AS t' . "\n" .
                                 'WHERE t.customer = ' . $record['customer'] . "\n" .
                                 '  AND t.trademark = ' . $record['trademark'] . "\n" .
                                 //IMPORTANT: do not uncomment the following line!!!
                                 //the uncofirmed turnovers should be displayed as well
                                 //'  AND t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00"' . "\n" .
                                 '  AND t.day >= "' . $row['from'] . '"' . "\n" .
                                 '  AND t.day <= "' . $row['to'] . '"';
                            $incomes = $registry['db']->GetRow($query);

                            $records[$key]['extended'][$per]['total_days'] = strtotime($row['to']) - strtotime($row['from']);
                            $records[$key]['extended'][$per]['total_days'] = round($records[$key]['extended'][$per]['total_days'] /3600 / 24) + 1;
                            if (empty($row['percent'])) {
                                $row['percent'] = 0;
                            }
                            $records[$key]['extended'][$per]['percent'] = $row['percent'];
                            $records[$key]['extended'][$per]['turnovers_price'] = round($incomes['value'] * $row['percent'] / 100, $prec['gt2_total_with_vat']);
                            $records[$key]['extended'][$per]['difference'] = $records[$key]['extended'][$per]['turnovers_price'] - $records[$key]['extended'][$per]['base_price'] - $records[$key]['extended'][$per]['invoiced'];
                            $records[$key]['extended'][$per]['incomes_count'] = $incomes['count'];
                            $records[$key]['extended'][$per]['turnover_confirmed'] = $incomes['confirmed'];

                            $records[$key]['total_days'] += $records[$key]['extended'][$per]['total_days'];
                            $records[$key]['invoiced'] += $row['invoiced'];
                            $records[$key]['base_price'] += $records[$key]['extended'][$per]['base_price'];
                            $records[$key]['turnovers_price'] += $records[$key]['extended'][$per]['turnovers_price'];
                            $records[$key]['difference'] += $records[$key]['extended'][$per]['difference'];
                            $records[$key]['incomes_count'] += $incomes['count'];
                            $records[$key]['turnover_confirmed'] += $incomes['confirmed'];
                        }

                    }

                    //check if all the periods are confirmed
                    if ($records[$key]['turnover_confirmed'] < count($percents)) {
                        $records[$key]['turnover_confirmed'] = 0;
                    }

                    if (round($records[$key]['difference'], $prec['gt2_total_with_vat']) >= 0) {
                        unset($records[$key]);
                        continue;
                    }

                }
            }

            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                //no pagination required return only the models
                $results = $records;
            }

            return $results;
        }

        public static function getCustomData(&$registry, $filters = array(), $report_filters = array()) {

            $table_name = Reports::getTemporaryTableName($registry);
            $settings = Reports::getReportSettings($registry);
            $dependencies = array();
            foreach (preg_split('#\s*,\s*#', $settings['report_dependencies']) as $v) {
                list($k, $v) = preg_split('#\s*\=\>\s*#', $v);
                $dependencies[$k] = $v;
            }

            $sql = array();
            $sql['select'] = 'SELECT v.*, ' . "\n" .
                             '  c.branch, c.contact_person,' . "\n" .
                             '  c.company, c.office, c.date_validity AS contract_validity, coi18n.name AS contract_name,' . "\n" .
                             '  fci18n.name AS company_name, oi18n.name AS office_name,' . "\n" .
                             '  CONCAT_WS(\' \', ci18n.name, ci18n.lastname) AS customer_name,' . "\n" .
                             '  ccstm1.value as total_vat_rate';
            $sql['from'] =   'FROM ' . $table_name . ' AS v' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                             '  ON v.contract_id = c.id' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                             '  ON v.contract_id = coi18n.parent_id' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON v.customer = ci18n.parent_id AND ci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                             '  ON c.company = fci18n.parent_id AND fci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                             '  ON c.office = oi18n.parent_id AND oi18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                             'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                             '  ON fm1.model = "Contract" AND fm1.model_type = c.type AND fm1.name = "total_vat_rate"' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm1' . "\n" .
                             '  ON ccstm1.model_id = c.id AND ccstm1.var_id = fm1.id' . "\n" ;
            if (empty($filters)) {
                $sql['where'] = 'WHERE v.selected = 1';
            } else {
                $sql['where'] = 'WHERE ' . implode(' AND ', $filters);
            }

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            //prepare some parameters
            $params = array('articles_ids' =>  preg_split('#\s*,\s*#', REPORT_ARTICLES . ',' . REPORT_ARTICLES_INVOICED));
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $prec = $registry['config']->getSectionParams('precision');

            $t_currency = $registry['config']->getParam('turnovers', 'currency');
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            if (empty($t_currency)) {
                $t_currency = Finance_Currencies::getMain($registry);
            }

            $filters = $report_filters;

            //prepare deposit amount
            //The great function of Iliya is used
            foreach ($records as $key => $record) {
                $params['contracts_ids'] = $record['contract_id'];

                //get percents stairs
                $query = 'SELECT ccstm1.value as `from`, ccstm2.value as `percent`' . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                         '  ON fm1.model = "Contract" AND fm1.model_type = c.type AND fm1.name = "irp_date_from"' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm1' . "\n" .
                         '  ON ccstm1.model_id = c.id AND ccstm1.var_id = fm1.id' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                         '  ON fm2.model = "Contract" AND fm2.model_type = c.type AND fm2.name = "inc_rentalprice"' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm2' . "\n" .
                         '  ON ccstm2.model_id = c.id AND ccstm2.var_id = fm2.id' . "\n" .
                         'WHERE c.id = ' . $record['contract_id'] . "\n" .
                         '  AND ccstm1.value != "" AND ccstm1.value IS NOT NULL' . "\n" .
                         '  AND ccstm2.value != "" AND ccstm2.value IS NOT NULL' . "\n" .
                         '  AND ccstm1.num = ccstm2.num';
                $percents = $registry['db']->GetAll($query);
                if (empty($percents)) {
                    unset($records[$key]);
                    continue;
                }
                //get contract financial contact
                $records[$key]['financial'] = array();
                $contract = new Contract($registry, array('type' => REPORT_CONTRACT_TYPE));
                $contract->set('id', $params['contracts_ids'], true);
                $contract->set('cstm_financial', $record['financial_id'], true);
                $records[$key]['financial'][] = $contract->getCstmFinancialData();
                $records[$key]['financial'] = array_merge($records[$key]['financial'],
                                                          $contract->getContactCcData('cstm', 'fin'));

                //calculate period length
                if ($record['date_start'] < $filters['date_from']) {
                    $record['date_start'] = $filters['date_from'];
                }
                if ($record['date_validity'] > $filters['date_to']) {
                    $record['date_validity'] = $filters['date_to'];
                }

                foreach ($percents as $per => $cent) {
                    if ($cent['from'] > $record['date_validity'] ||
                      !empty($percents[$per + 1]) && $percents[$per + 1] < $record['date_start']) {
                        unset($percents[$per]);
                        continue;
                    }
                    if (!empty($percents[$per + 1])) {
                        $percents[$per]['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($percents[$per+1]['from'])));
                        if ($percents[$per]['to'] > $record['date_validity']) {
                            $percents[$per]['to'] = $record['date_validity'];
                        }
                    } else {
                        $percents[$per]['to'] = $record['date_validity'];
                    }
                    if ($percents[$per]['to'] < $record['date_start']) {
                        unset($percents[$per]);
                    }
                }
                $percents = array_values($percents);

                if ($percents[0]['from'] <  $record['date_start']) {
                    $percents[0]['from'] =  $record['date_start'];
                }

                $records[$key]['base_price'] = 0;
                $records[$key]['invoiced'] = 0;
                $records[$key]['total_days'] = 0;
                $records[$key]['turnovers_price'] = 0;
                $records[$key]['difference'] = 0;
                $records[$key]['incomes_count'] = 0;

                //invoices have to be issued
                if ($filters['report_for'] == 'invoice') {

                    foreach ($percents as $per => $cent) {
                        $params['date_from'] = $cent['from'];
                        $params['date_to'] = $cent['to'];
                        $gt2_rows = Contracts::getInvoicedAmount($registry,$params);
                        //for each stair of the rental price we calculate how much is the difference
                        $records[$key]['extended'][$per]['date_from'] = $cent['from'];
                        $records[$key]['extended'][$per]['date_to'] = $cent['to'];
                        $records[$key]['extended'][$per]['base_price'] = 0;
                        $records[$key]['extended'][$per]['invoiced'] = 0;
                        if (!empty($gt2_rows)) {
                            foreach ($gt2_rows as $idx => $row) {
                                $c_rate = Finance_Currencies::getRate($registry, $row['invoice_currency'], $t_currency);
                                if (in_array($row['article_id'], preg_split('#\s*,\s*#', REPORT_ARTICLES_INVOICED))) {
                                    $records[$key]['extended'][$per]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                                    $records[$key]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                                } else {
                                    $records[$key]['extended'][$per]['base_price'] += $row['price'] * $row['quantity'] * $c_rate;
                                }
                            }
                            $records[$key]['article_id'] = $row['article_id'];
                            $records[$key]['currency'] = $t_currency;
                        } else {
                            $gt2_contract = $contract->getGT2Vars();
                            foreach ($gt2_contract['values'] as $idx => $row) {
                                if (array_key_exists($row['article_id'], $dependencies)) {
                                    $records[$key]['article_id'] = $row['article_id'];
                                    $records[$key]['currency'] = $t_currency;
                                }
                            }

                        }
                        //get turnovers for the period
                        $query = 'SELECT SUM(total) AS value, COUNT(total) AS count' . "\n" .
                             'FROM ' . DB_TABLE_TURNOVERS . ' AS t' . "\n" .
                             'WHERE t.customer = ' . $record['customer'] . "\n" .
                             '  AND t.trademark = ' . $record['trademark'] . "\n" .
                             //IMPORTANT: do not COMMENT the following line
                             //this method (getCustomData) is used to get only those turnovers that are confirmed
                             '  AND t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00"' . "\n" .
                             '  AND t.day >= "' . $cent['from'] . '"' . "\n" .
                             '  AND t.day <= "' . $cent['to'] . '"';
                        $incomes = $registry['db']->GetRow($query);
                        //distribute data for the stairs
                        $records[$key]['extended'][$per]['total_days'] = strtotime($cent['to']) - strtotime($cent['from']);
                        $records[$key]['extended'][$per]['total_days'] = round($records[$key]['extended'][$per]['total_days'] /3600 / 24) + 1;
                        $records[$key]['extended'][$per]['percent'] = $cent['percent'];
                        $records[$key]['extended'][$per]['base_price'] = round($records[$key]['extended'][$per]['base_price'], $prec['gt2_total_with_vat']);
                        $records[$key]['extended'][$per]['turnovers_price'] = $incomes['value'] * $cent['percent'] / 100;
                        $records[$key]['extended'][$per]['turnovers_price'] = round($records[$key]['extended'][$per]['turnovers_price'], $prec['gt2_total_with_vat']);
                        $records[$key]['extended'][$per]['difference'] = $records[$key]['extended'][$per]['turnovers_price'] - $records[$key]['extended'][$per]['base_price'] - $records[$key]['extended'][$per]['invoiced'];
                        $records[$key]['extended'][$per]['incomes_count'] = $incomes['count'];

                        $records[$key]['total_days'] += $records[$key]['extended'][$per]['total_days'];
                        $records[$key]['base_price'] += $records[$key]['extended'][$per]['base_price'];
                        $records[$key]['turnovers_price'] += $records[$key]['extended'][$per]['turnovers_price'];
                        $records[$key]['difference'] += $records[$key]['extended'][$per]['difference'];
                        $records[$key]['incomes_count'] += $incomes['count'];

                    }

                    if (round($records[$key]['difference'], $prec['gt2_total_with_vat']) <= 0) {
                        unset($records[$key]);
                        continue;
                    }
                } elseif ($filters['report_for'] == 'credit') {
                    //credit notices will be issued
                    $params['date_from'] = $filters['date_from'];
                    $params['date_to'] = $filters['date_to'];
                    //get invoiced for the whole period
                    $gt2_rows = Contracts::getInvoicedAmount($registry,$params);

                    //get all model IDS
                    $ids = array();
                    foreach($gt2_rows as $row) {
                        $ids[] = $row['model_id'];
                    }

                    //get relations between the models
                    $query = 'SELECT parent_id, link_to FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                             'WHERE parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                             '  AND link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                             '  AND link_to IN (' . implode(', ', $ids) . ')';
                    $relations = $registry['db']->GetAssoc($query);

                    $ordered = array();
                    //combine the rows in function of the invoice id
                    foreach ($gt2_rows as $row) {
                        if (!empty($relations[$row['model_id']])) {
                            $id = $relations[$row['model_id']];
                        } else {
                            $id = $row['model_id'];
                        }
                        if (empty($ordered[$id])) {
                            $ordered[$id][0]['from'] = $row['date_from'];
                            $ordered[$id][0]['to'] = $row['date_to'];
                            $ordered[$id][0]['invoiced'] = 0;
                            $ordered[$id][0]['base_price'] = 0;
                            $ordered[$id][0]['article_id'] = $row['article_id'];
                        } else {
                            if ($row['date_from'] < $ordered[$id][0]['from']) {
                                $ordered[$id][0]['from'] = $row['date_from'];
                            }
                            if ($row['date_to'] > $ordered[$id][0]['to']) {
                                $ordered[$id][0]['to'] = $row['date_to'];
                            }
                        }
                        $c_rate = Finance_Currencies::getRate($registry, $row['invoice_currency'], $t_currency);
                        if (in_array($row['article_id'], preg_split('#\s*,\s*#', REPORT_ARTICLES_INVOICED))) {
                            $ordered[$id][0]['invoiced'] += $row['price'] * $row['quantity'] * $c_rate;
                        } else {
                            $ordered[$id][0]['base_price'] += $row['price'] * $row['quantity'] * $c_rate;
                        }
                    }

                    //distribute percent for the invoices
                    $gt2_rows = $ordered;
                    foreach ($percents as $per => $cent) {
                        foreach($gt2_rows as $r => $rows) {
                            foreach ($rows as $rr => $row) {
                                if ($cent['to'] < $row['from'] || $cent['from'] > $row['to']) {
                                    continue;
                                }
                                if ($cent['from'] <= $row['from']) {
                                    if ($cent['to'] < $row['to']) {
                                        //devide in two
                                        $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                        $index = round((strtotime($cent['to']) - strtotime($row['from'])) / 3600 /24 + 1) / $index;
                                        $gt2_rows[$r][$rr]['to'] = $cent['to'];
                                        $gt2_rows[$r][$rr]['invoiced'] = $row['invoiced'] * $index;
                                        $gt2_rows[$r][$rr]['base_price'] = $row['base_price'] * $index;
                                        $gt2_rows[$r][$rr]['percent'] = $cent['percent'];
                                        $gt2_rows[$r][] = array('from' => General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($cent['to']))),
                                                                'to' => $row['to'],
                                                                'article_id' => $row['article_id'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'],
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price']);
                                    } else {
                                        $gt2_rows[$r][$rr]['percent'] = $cent['percent'];
                                    }
                                } else {
                                    $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                    $index = round((strtotime($cent['from']) - strtotime($row['from'])) / 3600 /24) / $index;
                                    $gt2_rows[$r][$rr]['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($cent['from'])));
                                    $gt2_rows[$r][$rr]['invoiced'] = $row['invoiced'] * $index;
                                    $gt2_rows[$r][$rr]['base_price'] = $row['base_price'] * $index;
                                    if ($cent['to'] < $row['to']) {
                                        //devide in three
                                        $index = round((strtotime($row['to']) - strtotime($row['from'])) / 3600 / 24 + 1);
                                        $index = round((strtotime($cent['to']) - strtotime($cent['from'])) / 3600 /24 + 1) / $index;
                                        $index = $row['invoiced'] * $index;
                                        $index1 = $row['base_price'] * $index;
                                        $gt2_rows[$r][] = array('from' => $cent['from'],
                                                                'to' => $cent['to'],
                                                                'article_id' => $row['article_id'],
                                                                'invoiced' => $index,
                                                                'base_price' => $index1,
                                                                'percent' => $cent['percent']);

                                        $gt2_rows[$r][] = array('from' => General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($cent['to']))),
                                                                'to' => $row['to'],
                                                                'article_id' => $row['article_id'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'] - $index,
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price'] - $index1);
                                    } else {
                                        //devide in two
                                        $gt2_rows[$r][] = array('from' => $cent['from'],
                                                                'to' => $row['to'],
                                                                'article_id' => $row['article_id'],
                                                                'invoiced' => $row['invoiced'] - $gt2_rows[$r][$rr]['invoiced'],
                                                                'base_price' => $row['base_price'] - $gt2_rows[$r][$rr]['base_price'],
                                                                'percent' => $cent['percent']);
                                    }
                                }
                            }
                        }
                    }

                    $records[$key]['currency'] = $t_currency;
                    foreach ($gt2_rows as $r => $rows) {
                        foreach ($rows as $rr => $row) {
                            $records[$key]['extended'][$r][$rr]['date_from'] = $row['from'];
                            $records[$key]['extended'][$r][$rr]['date_to'] = $row['to'];
                            $records[$key]['extended'][$r][$rr]['base_price'] = round($row['base_price'], $prec['gt2_total_with_vat']);
                            $records[$key]['extended'][$r][$rr]['invoiced'] = round($row['invoiced'], $prec['gt2_total_with_vat']);

                            $query = 'SELECT SUM(total) AS value, COUNT(total) AS count' . "\n" .
                                 'FROM ' . DB_TABLE_TURNOVERS . ' AS t' . "\n" .
                                 'WHERE t.customer = ' . $record['customer'] . "\n" .
                                 '  AND t.trademark = ' . $record['trademark'] . "\n" .
                                 //IMPORTANT: do not COMMENT the following line
                                 //this method (getCustomData) is used to get only those turnovers that are confirmed
                                 '  AND t.confirmed IS NOT NULL AND t.confirmed > "0000-00-00 00:00:00"' . "\n" .
                                 '  AND t.day >= "' . $row['from'] . '"' . "\n" .
                                 '  AND t.day <= "' . $row['to'] . '"';
                            $incomes = $registry['db']->GetRow($query);

                            $records[$key]['extended'][$r][$rr]['total_days'] = strtotime($row['to']) - strtotime($row['from']);
                            $records[$key]['extended'][$r][$rr]['total_days'] = round($records[$key]['extended'][$r][$rr]['total_days'] /3600 / 24) + 1;
                            $records[$key]['extended'][$r][$rr]['percent'] = $row['percent'];
                            $records[$key]['extended'][$r][$rr]['turnovers_price'] = round($incomes['value'] * $row['percent'] / 100, $prec['gt2_total_with_vat']);
                            $records[$key]['extended'][$r][$rr]['difference'] = $records[$key]['extended'][$r][$rr]['turnovers_price'] - $records[$key]['extended'][$r][$rr]['base_price'] - $records[$key]['extended'][$r][$rr]['invoiced'];
                            $records[$key]['extended'][$r][$rr]['incomes_count'] = $incomes['count'];
                            $records[$key]['extended'][$r][$rr]['article_id'] = $row['article_id'];

                            $records[$key]['total_days'] += $records[$key]['extended'][$r][$rr]['total_days'];
                            $records[$key]['invoiced'] += $row['invoiced'];
                            $records[$key]['base_price'] += $records[$key]['extended'][$r][$rr]['base_price'];
                            $records[$key]['turnovers_price'] += $records[$key]['extended'][$r][$rr]['turnovers_price'];
                            $records[$key]['difference'] += $records[$key]['extended'][$r][$rr]['difference'];
                            $records[$key]['incomes_count'] += $incomes['count'];
                        }

                    }

                    if (round($records[$key]['difference'], $prec['gt2_total_with_vat']) >= 0) {
                        unset($records[$key]);
                        continue;
                    }

                }
            }

            return $records;
        }

        public static function setRelations(&$registry, &$model) {

            $set = array('parent_id = 0',
                         'contract_id = ' . $model->get('link_to'),
                         'invoice_id = ' . $model->get('id'),
                         '`from` = "' . $model->get('date_from') . '"',
                         '`to` = "' . $model->get('date_to') . '"',
                         'invoice_date = CURDATE()',
                         'issue_date = CURDATE()');

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' SET' . "\n" .
                     implode(",\n", $set);
            $registry['db']->Execute($query);
            if ($registry['db']->ErrorMsg()){
                return false;
            }
            return true;
        }
    }
?>
