<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        $found = false;
        switch($this->action) {
            case 'generate_preview':
                $this->_generatePreview();
                $found = true;
                break;
        }

        if (!$found) {
            parent::execute();
        }
    }

    public function customEmailSend(&$settings) {

        $registry = &$this->registry;
        $request = &$this->registry['request'];
        $filters = $request->get('filters');
        $settings['report_dependencies'] = preg_split('#\s*,\s*#', $settings['report_dependencies']);
        $dependencies = array();
        foreach ($settings['report_dependencies'] as $v) {
            list($k, $v) = preg_split('#\s*\=\>\s*#', $v);
            $dependencies[$k] = $v;
        }
        if ($filters['report_for'] == 'invoice') {
            $settings['create_type'] = PH_FINANCE_TYPE_INVOICE;
        } elseif ($filters['report_for'] == 'credit') {
            $settings['create_type'] = PH_FINANCE_TYPE_CREDIT_NOTICE;
        } else {
            die('Wrong document type! What are you doing here?!?');
        }

        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        if (!$report) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];
        $report_name = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report->get('type'))));
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/custom.report.query.php';
        $records = $report_name::getCustomData($this->registry, array(), $filters);

         $attachments =  $request->get('attached_files');
         if (!is_array($attachments)) {
             $attachments = array($attachments);
         }
         foreach ($attachments as $key => $attachment) {
             if (!$attachment) {
                  unset($attachments[$key]);
             }
         }


         $this->result = array('total_documents' => count($records),
                            'erred_documents' => 0,
                            'total_files' => count($attachments) * count($records),
                            'erred_files' => 0,
                            'total_emails' => count($records),
                            'erred_emails' => 0,
                            'additional_info' => array());

         if ($filters['report_for'] == 'credit') {
             $this->result['total_documents'] = 0;
             foreach ($records as $record) {
                 $this->result['total_documents'] += count($record['extended']);
             }
             $this->result['total_files'] = count($attachments) * $this->result['total_documents'];
         }

        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';

        //prepare gt2 new rows
        $filts = array('where' => array('n.id IN (' . implode(',', array_unique(array_values($dependencies))). ')'),
                         'sanitize' => true);
        $articles = Nomenclatures::search($this->registry, $filts);
        foreach ($articles as $k => $v) {
            unset($articles[$k]);
            $articles[$v->get('id')] = $v;
        }
        foreach ($records as $key => $record) {
            $registry['db']->StartTrans();
            $method = 'create' . preg_replace('#\s#', '', ucwords(preg_replace('#_#', ' ', $settings['create_model'])));

            if (method_exists($this, $method)) {
                //set document custom name
                if ($request->get('email_subject')) {
                    $records[$key]['custom_name'] = $request->get('email_subject');
                } else {
                    $records[$key]['custom_name'] = $report->get('name');
                }
                $record['custom_name'] = $records[$key]['custom_name'];

                $contract_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s - %s</a>',
                                         $_SERVER['PHP_SELF'], $registry->get('module_param'), 'contracts',
                                         'contracts', 'view', 'view', $record['contract_id'],
                                         $record['contract_name'], $record['contract_num']
                                        );

                if ($filters['report_for'] == 'credit') {
                    $files = array();
                    $model = array();
                    foreach ($record['extended'] as $id => $extended) {
                        $gt2 = array('values' => array());
                        foreach($extended as $row) {
                            $new = $articles[$dependencies[$row['article_id']]];
                            $free_text = sprintf($this->i18n('free_text'), $contract_link,
                                                 General::strftime('%d.%m.%Y', strtotime($row['date_from'])),
                                                 General::strftime('%d.%m.%Y', strtotime($row['date_to'])));
                            $gt2['values'][] = array('article_id' => $new->get('id'),
                                                     'article_code' => $new->get('code'),
                                                     'article_name' => $new->get('name'),
                                                     'quantity' => 1,
                                                     'price' => $row['difference'],
                                                     'free_field1' => $record['object_id'],
                                                     'free_text5' => $free_text,
                                                     'discount_value' => 0,
                                                     'average_weighted_delivery_price' => 0,
                                                     'date_from' => $row['date_from'],
                                                     'date_to' => $row['date_to']);
                        }
                        $gt2['plain_values'] = array('currency' => $record['currency'],
                                                     'total' => 0,
                                                     'total_vat_rate' => $record['total_vat_rate'],
                                                     'total_vat' => 0,
                                                     'total_with_vat' => 0);
                        $record['gt2'] = $gt2;
                        $record['status'] = 'finished';

                        $record['issue_by'] = $registry['currentUser']->get('id');
                        $settings['transform_b_issue_by'] = 'issue_by';

                        //get the settings from variables anf invoice groups
                        $filters = array('where' => array('co.id = ' . $record['contract_id']));
                        $contract = Contracts::searchOne($registry, $filters);
                        $contract_default_vars = $contract->getVariablesSettings();

                        //payment type
                        $record['payment_type'] = $contract_default_vars['invoices_payment_type']['value'];
                        $settings['transform_b_payment_type'] = 'payment_type';

                        //the container cashbox/bank account
                        $container = '';
                        if (!empty($contract_default_vars['invoices_container']['value'])) {
                            $container = $contract_default_vars['invoices_container']['value'];
                        } else {
                            //fetch container id from invoice groups
                            $filters = array('where' => array('n.code = \'groupRent\''));
                            $invoice_group = Nomenclatures::searchOne($registry, $filters);
                            $invoice_group_default_vars = $invoice_group->getAssocVars();
                            $container = $invoice_group_default_vars['bank_account_nom']['value'];
                            unset($invoice_group);
                        }
                        $record['container_id'] = $container;
                        $settings['transform_b_container_id'] = 'container_id';

                        //set payment date (the other dates - issue_date, fiscal_event-date should be curent date as they are)
                        $record['date_of_payment_count'] = $contract_default_vars['invoices_payment_date']['value']['count'];
                        $settings['transform_b_date_of_payment_count'] = 'date_of_payment_count';
                        $record['date_of_payment_period_type'] = $contract_default_vars['invoices_payment_date']['value']['period_type'];
                        $settings['transform_b_date_of_payment_period_type'] = 'date_of_payment_period_type';
                        $record['date_of_payment_point'] = $contract_default_vars['invoices_payment_date']['value']['point'];
                        $settings['transform_b_date_of_payment_point'] = 'date_of_payment_point';

                        unset($contract);

                        //creates the requested model(document,finance)
                        $model[$id] = $this->$method($record, $settings);
                        $model[$id]->set('link_to_model_name', 'Finance_Incomes_Reason', true);
                        $model[$id]->set('link_to', $id, true);

                        unset($record['gt2']);

                        if ($model[$id]->get('id')) {
                            $model_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s&%s=%s" target="_blank">%s - %s</a>',
                                                  $_SERVER['PHP_SELF'], $registry->get('module_param'), 'finance',
                                                  $registry->get('controller_param'), 'incomes_reasons',
                                                  'incomes_reasons', 'view', 'view', $model[$id]->get('id'), $model[$id]->get('num'), $model[$id]->get('name')
                                                 );
                            if (empty($record['financial'])) {
                                $this->result['erred_documents'] ++;
                                $this->result['erred_emails'] ++;
                                $this->result['erred_files'] += count($attachments);
                                $this->result['additional_info'][] =
                                    sprintf($this->i18n('error_reports_wrong_email'), $contract_link);
                                $registry['db']->FailTrans();
                                $registry['db']->CompleteTrans();
                                continue;
                            }

                            //set some vars in the report model to get patterns vars
                            $report->set('customer', $model[$id]->get('customer'), true);
                            $report->set('branch', $model[$id]->get('branch'), true);
                            $report->set('contact_person', $model[$id]->get('contact_person'), true);
                            $report->set('created_model_num', $model[$id]->get('num'), true);
                            foreach($record as $k => $v) {
                                $report->set($k, $v, true);
                            }

                            //prepare(generate) attachments
                            foreach ($attachments as  $attachment) {

                                if (preg_match('#file_\d+#', $attachment)) {
                                    $filters = array('where' => array('f.id = ' . preg_replace('#file_#', '', $attachment)),
                                                     'sanitize' => true);
                                    $files[] = Files::searchOne($this->registry, $filters);
                                } elseif (preg_match('#pattern_\d+#', $attachment)) {
                                    $pattern = preg_replace('#pattern_#', '', $attachment);
                                    $request->set('pattern', $pattern, 'all', true);
                                    $filters = array('where' => array('p.id = ' . $pattern,
                                                                      'p.active = 1'),
                                                     'model_lang' => $registry['lang'],
                                                     'sanitize' => true);
                                    $pattern = Patterns::searchOne($this->registry, $filters);

                                    $this->registry->set('pattern', $pattern->get('id'), true);
                                    $placeholders = $model[$id]->getPatternsVars();
                                    $model[$id]->extender = new Extender();
                                    $model[$id]->extender->model_lang = $registry['lang'];
                                    $model[$id]->extender->module = $this->module;
                                    foreach($placeholders as $key => $value) {
                                        $model[$id]->extender->add($key, $value);
                                    }

                                    $file_id = '';
                                    switch ($pattern->get('format')) {
                                        case 'xls':
                                            if ($file_id = $model[$id]->generateXLS()) {
                                                // Write to History
                                                Finance_Incomes_Reasons_History::saveData($registry, array('model' => $model[$id],
                                                                                                           'new_model' => $model[$id],
                                                                                                           'old_model' => $model[$id],
                                                                                                           'action_type'=>'generate',
                                                                                                           'pattern' => $pattern->get('id'),
                                                                                                           'generated_file' => $file_id));
                                            } else {
                                                $registry['messages']->setError($this->i18n('error_report_export_document', array($model_link, $contract_link)));
                                            }
                                            break;
                                        case 'csv':
                                            // TO DO add option fields and also create a pattern which indicates the header columns
                                            break;
                                        case 'pdf':
                                        default:
                                            if ($file_id = $model[$id]->generatePDF(false, true)) {
                                                Finance_Incomes_Reasons_History::saveData($registry, array('model' => $model[$id],
                                                                                                           'new_model' => $model[$id],
                                                                                                           'old_model' => $model[$id],
                                                                                                           'action_type'=>'generate',
                                                                                                           'pattern' => $pattern->get('id'),
                                                                                                           'generated_file' => $file_id));
                                            } else {
                                                $registry['messages']->setError($this->i18n('error_report_generate_document', array($model_link, $contract_link)));
                                            }
                                            break;
                                    }
                                    if ($file_id) {
                                        $files[] = 'file_' . $file_id;
                                    } else {
                                        $this->result['erred_files'] ++;
                                    }
                                }
                            }
                        } else {
                            $model_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                                  $_SERVER['PHP_SELF'], $registry->get('module_param'), 'finance',
                                                  $registry->get('controller_param'), 'incomes_reasons',
                                                  'incomes_reasons', 'view', 'view', $model->get('id'), $model->get('name')
                                                 );
                            $this->result['erred_documents'] ++;
                            $this->result['erred_emails'] ++;
                            $this->result['erred_files'] += count($attachments);
                            $this->result['additional_info'][] =
                                sprintf($this->i18n('error_reports_document_not_generated'), $record['custom_name'], $model_link, $contract_link);
                        }
                    }

                    //get just one model and send the e-mail through it
                    //with all files attached(for all the models)
                    $model = new Finance_Incomes_Reason($this->registry, array());
                    $model->set('type', PH_FINANCE_TYPE_CREDIT_NOTICE, true);

                    //set generated files ids in the model
                    $model->set('attached_files', $files);
                    $recipients = array();
                    foreach ($record['financial'] as $contact) {
                        $recipients[] = $contact['name'] . ' <' . $contact['email'] . '>';
                    }
                    $model->set('customer_email', array(array_shift($recipients)), true);
                    $model->set('customer_email_cc', $recipients, true);
                    $model->set('email_subject', $request->get('email_subject'));

                    if ($request->get('email_template')) {
                        $filters = array('where' => array('e.id = ' . $request->get('email_template')),
                                         'sanitize' => true);
                        $mail = Emails::searchOne($registry, $filters);
                        foreach($record as $key => $value) {
                            $report->set($key, $value, true);
                        }
                        if (!empty($mail)) {
                            $placeholders = $report->getPatternsVars();
                            $model->extender = new Extender();
                            $model->extender->model_lang = $registry['lang'];
                            $model->extender->module = $this->module;
                            foreach($placeholders as $key => $value) {
                                $model->extender->add($key, $value);
                            }
                            $body = $model->extender->expand($mail->get('body'));

                            $model->set('body', $body, true);
                            $model->set('email_template', $mail->get('id'), true);
                            $extra = 'report := ' . $report->get('type') . "\n" . 'customer :=' . $model->get('customer');
                            $model->set('mail_additional_extra', $extra, true);

                            $result = $model->sendAsMail();
                            if (empty($result)) {
                                $result['erred'] = 1;
                            }
                        } else {
                            $result['erred'] = 1;
                        }
                    } else {
                        $result['erred'] = 1;
                    }

                    if (!empty($result['erred'])) {
                        $contact_link = array_shift($record['financial']);
                        $contact_link = $contact_link['name'];
                        $contact_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                                  $_SERVER['PHP_SELF'], $registry->get('module_param'), 'contracts',
                                                  'contracts', 'parties', 'parties', $record['contract_id'], $contact_link
                                                 );
                        $this->result['erred_emails'] ++;
                        $this->result['additional_info'][] =
                            sprintf($this->i18n('error_reports_email_not_sent'), $contact_link, $model_link, $contract_link);
                    }
                    if (isset($model->erred_files)) {
                        $this->result['erred_files'] += $model->erred_files;
                        $this->result['additional_info'][] =
                            sprintf($this->i18n('error_report_generate_document'), $model_link, $contract_link);
                    }
                } else {
                    //prepare gt2 for the incomes reason
                    $gt2 = array('values' => array(),
                                 'plain_values' => array('total' => 0,
                                                         'total_vat_rate' => $record['total_vat_rate'],
                                                         'total_vat' => 0,
                                                         'total_with_vat' => 0,
                                                         'currency' => $record['currency']));

                    $new = $articles[$dependencies[$record['article_id']]];
                    foreach ($record['extended'] as $extended) {
                        $free_text = sprintf($this->i18n('free_text'), $contract_link,
                                             General::strftime('%d.%m.%Y', strtotime($extended['date_from'])),
                                             General::strftime('%d.%m.%Y', strtotime($extended['date_to'])));
                        $gt2['values'][] = array('article_id' => $new->get('id'),
                                               'article_code' => $new->get('code'),
                                               'article_name' => $new->get('name'),
                                               'quantity' => 1,
                                               'price' => $extended['difference'],
                                               'free_field1' => $record['object_id'],
                                               'free_text5' => $free_text,
                                               'discount_value' => 0,
                                               'average_weighted_delivery_price' => 0,
                                               'date_from' => $extended['date_from'],
                                               'date_to' => $extended['date_to']);
                        if (empty($record['from']) || $record['from'] > $extended['date_from']) {
                            $record['from'] = $extended['date_from'];
                        }
                        if (empty($record['to']) || $record['to'] < $extended['date_to']) {
                            $record['to'] = $extended['date_to'];
                        }
                    }

                    $gt2['plain_values']['currency'] = $record['currency'];
                    $record['gt2'] = $gt2;
                    $records[$key]['gt2'] = $gt2;
                    $record['status'] = 'finished';+

                    $record['issue_by'] = $registry['currentUser']->get('id');
                    $settings['transform_b_issue_by'] = 'issue_by';

                    //get the settings from variables anf invoice groups
                    $filters = array('where' => array('co.id = ' . $record['contract_id']));
                    $contract = Contracts::searchOne($registry, $filters);
                    $contract_default_vars = $contract->getVariablesSettings();

                    //payment type
                    $record['payment_type'] = $contract_default_vars['invoices_payment_type']['value'];
                    $settings['transform_b_payment_type'] = 'payment_type';

                    //the container cashbox/bank account
                    $container = '';
                    if (!empty($contract_default_vars['invoices_container']['value'])) {
                        $container = $contract_default_vars['invoices_container']['value'];
                    } else {
                        //fetch container id from invoice groups
                        $filters = array('where' => array('n.code = \'groupRent\''));
                        $invoice_group = Nomenclatures::searchOne($registry, $filters);
                        $invoice_group_default_vars = $invoice_group->getAssocVars();
                        $container = $invoice_group_default_vars['bank_account_nom']['value'];
                        unset($invoice_group);
                    }
                    $record['container_id'] = $container;
                    $settings['transform_b_container_id'] = 'container_id';

                    //set payment date (the other dates - issue_date, fiscal_event-date should be curent date as they are)
                    $record['date_of_payment_count'] = $contract_default_vars['invoices_payment_date']['value']['count'];
                    $settings['transform_b_date_of_payment_count'] = 'date_of_payment_count';
                    $record['date_of_payment_period_type'] = $contract_default_vars['invoices_payment_date']['value']['period_type'];
                    $settings['transform_b_date_of_payment_period_type'] = 'date_of_payment_period_type';
                    $record['date_of_payment_point'] = $contract_default_vars['invoices_payment_date']['value']['point'];
                    $settings['transform_b_date_of_payment_point'] = 'date_of_payment_point';

                    unset($contract);

                    //creates the requested model(document,finance)
                    $model = $this->$method($record, $settings);
                    $model->set('link_to_model_name', 'Contract', true);
                    $model->set('link_to', $record['contract_id'], true);
                    unset($record['gt2']);

                    if ($model->get('id')) {
                        $model_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s&%s=%s" target="_blank">%s - %s</a>',
                                              $_SERVER['PHP_SELF'], $registry->get('module_param'), 'finance',
                                              $registry->get('controller_param'), 'incomes_reasons',
                                              'incomes_reasons', 'view', 'view', $model->get('id'), $model->get('num'), $model->get('name')
                                             );
                        $model->set('date_from', $record['from'], true);
                        $model->set('date_to', $record['to'], true);
                        $res = $report_name::setRelations($this->registry, $model);

                        if (!$res) {
                            $this->result['erred_documents'] ++;
                            $this->result['erred_emails'] ++;
                            $this->result['erred_files'] += count($attachments);
                            $this->result['additional_info'][] =
                                sprintf($this->i18n('error_reports_relations'), $contract_link);
                            $registry['db']->FailTrans();
                            $registry['db']->CompleteTrans();
                            continue;
                        }
                        if (empty($record['financial'])) {
                            $this->result['erred_documents'] ++;
                            $this->result['erred_emails'] ++;
                            $this->result['erred_files'] += count($attachments);
                            $this->result['additional_info'][] =
                                sprintf($this->i18n('error_reports_wrong_email'), $contract_link);
                            $registry['db']->FailTrans();
                            $registry['db']->CompleteTrans();
                            continue;
                        }

                        //set some vars in the report model to get patterns vars
                        $report->set('customer', $model->get('customer'), true);
                        $report->set('branch', $model->get('branch'), true);
                        $report->set('contact_person', $model->get('contact_person'), true);
                        $report->set('created_model_num', $model->get('num'), true);
                        foreach($record as $k => $v) {
                            $report->set($k, $v, true);
                        }

                        //prepare(generate) attachments
                        $files = array();

                        foreach ($attachments as  $attachment) {

                            if (preg_match('#file_\d+#', $attachment)) {
                                $filters = array('where' => array('f.id = ' . preg_replace('#file_#', '', $attachment)),
                                                 'sanitize' => true);
                                $files[] = Files::searchOne($this->registry, $filters);
                            } elseif (preg_match('#pattern_\d+#', $attachment)) {
                                $pattern = preg_replace('#pattern_#', '', $attachment);
                                $request->set('pattern', $pattern, 'all', true);
                                $filters = array('where' => array('p.id = ' . $pattern,
                                                                  'p.active = 1'),
                                                 'model_lang' => $registry['lang'],
                                                 'sanitize' => true);
                                $pattern = Patterns::searchOne($this->registry, $filters);

                                $this->registry->set('pattern', $pattern->get('id'));
                                $placeholders = $model->getPatternsVars();
                                $model->extender = new Extender();
                                $model->extender->model_lang = $registry['lang'];
                                $model->extender->module = $this->module;
                                foreach($placeholders as $key => $value) {
                                    $model->extender->add($key, $value);
                                }

                                $file_id = '';
                                switch ($pattern->get('format')) {
                                    case 'xls':
                                        if ($file_id = $model->generateXLS()) {
                                            // Write to History
                                            Finance_Incomes_Reasons_History::saveData($registry, array('model' => $model,
                                                                                                       'new_model' => $model,
                                                                                                       'old_model' => $model,
                                                                                                       'action_type'=>'generate',
                                                                                                       'pattern' => $pattern->get('id'),
                                                                                                       'generated_file' => $file_id));
                                        } else {
                                            $registry['messages']->setError($this->i18n('error_report_export_document', array($model_link, $contract_link)));
                                        }
                                        break;
                                    case 'csv':
                                        // TO DO add option fields and also create a pattern which indicates the header columns
                                        break;
                                    case 'pdf':
                                    default:
                                        if ($file_id = $model->generatePDF(false, true)) {
                                            Finance_Incomes_Reasons_History::saveData($registry, array('model' => $model,
                                                                                                       'new_model' => $model,
                                                                                                       'old_model' => $model,
                                                                                                       'action_type'=>'generate',
                                                                                                       'pattern' => $pattern->get('id'),
                                                                                                       'generated_file' => $file_id));
                                        } else {
                                            $registry['messages']->setError($this->i18n('error_report_generate_document', array($model_link, $contract_link)));
                                        }
                                        break;
                                }
                                if ($file_id) {
                                    $files[] = 'file_' . $file_id;
                                } else {
                                    $this->result['erred_files'] ++;
                                }
                            }
                        }

                        //set generated files ids in the model
                        $model->set('attached_files', $files);
                        $recipients = array();
                        foreach ($record['financial'] as $contact) {
                            $recipients[] = $contact['name'] . ' <' . $contact['email'] . '>';
                        }
                        $model->set('customer_email', array(array_shift($recipients)), true);
                        $model->set('customer_email_cc', $recipients, true);
                        $model->set('email_subject', $request->get('email_subject'));

                        if ($request->get('email_template')) {
                            $filters = array('where' => array('e.id = ' . $request->get('email_template')),
                                             'sanitize' => true);
                            $mail = Emails::searchOne($registry, $filters);
                            if (!empty($mail)) {
                                $placeholders = $report->getPatternsVars();
                                $model->extender = new Extender();
                                $model->extender->model_lang = $registry['lang'];
                                $model->extender->module = $this->module;
                                foreach($placeholders as $key => $value) {
                                    $model->extender->add($key, $value);
                                }
                                $body = $model->extender->expand($mail->get('body'));

                                $model->set('body', $body, true);
                                $model->set('email_template', $mail->get('id'), true);
                                $extra = 'report := ' . $report->get('type') . "\n" . 'customer :=' . $model->get('customer');
                                $model->set('mail_additional_extra', $extra, true);
                                $result = $model->sendAsMail();
                                if (empty($result)) {
                                    $result['erred'] = 1;
                                }
                            } else {
                                $result['erred'] = 1;
                            }
                        } else {
                            $result['erred'] = 1;
                        }

                        if (!empty($result['erred'])) {
                            $contact_link = array_shift($record['financial']);
                            $contact_link = $contact_link['name'];
                            $contact_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                                      $_SERVER['PHP_SELF'], $registry->get('module_param'), 'contracts',
                                                      'contracts', 'parties', 'parties', $record['contract_id'], $contact_link
                                                     );
                            $this->result['erred_emails'] ++;
                            $this->result['additional_info'][] =
                                sprintf($this->i18n('error_reports_email_not_sent'), $contact_link, $model_link, $contract_link);
                        }
                        if (isset($model->erred_files)) {
                            $this->result['erred_files'] += $model->erred_files;
                            $this->result['additional_info'][] =
                                sprintf($this->i18n('error_report_generate_document'), $model_link, $contract_link);
                        }
                    } else {
                        $model_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                              $_SERVER['PHP_SELF'], $registry->get('module_param'), 'finance',
                                              $registry->get('controller_param'), 'incomes_reasons',
                                              'incomes_reasons', 'view', 'view', $model->get('id'), $model->get('name')
                                             );
                        $this->result['erred_documents'] ++;
                        $this->result['erred_emails'] ++;
                        $this->result['erred_files'] += count($attachments);
                        $this->result['additional_info'][] =
                            sprintf($this->i18n('error_reports_document_not_generated'), $record['custom_name'], $model_link, $contract_link);
                    }
                }
            }

            $registry['db']->CompleteTrans();
        }
    }

    public function prepareAnnouncementContent() {

        $content = '';
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_right_documents'),
                           $this->result['total_documents'] - $this->result['erred_documents']);
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_erred_documents'), $this->result['erred_documents']);
        $content .= sprintf('%s: %s<br /><br />', $this->i18n('reports_total_documents'), $this->result['total_documents']);
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_right_files'),
                           $this->result['total_files'] - $this->result['erred_files']);
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_erred_files'), $this->result['erred_files']);
        $content .= sprintf('%s: %s<br /><br />', $this->i18n('reports_total_files'), $this->result['total_files']);
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_right_emails'),
                           $this->result['total_emails'] - $this->result['erred_emails']);
        $content .= sprintf('%s: %s<br />', $this->i18n('reports_erred_emails'), $this->result['erred_emails']);
        $content .= sprintf('%s: %s<br /><br />', $this->i18n('reports_total_emails'), $this->result['total_emails']);

        $content .= implode("<br />", $this->result['additional_info']);

        return $content;
    }

    private function _generatePreview() {

        $registry = &$this->registry;
        $request = &$this->registry['request'];

        $report = $this->getReportType();
        $report = $report['name'];
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }

        $report_filters = $registry['session']->get('reports_' . $report . '_report');

        require_once PH_MODULES_DIR . 'reports/plugins/' . $report . '/custom.report.query.php';
        $report = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report)));

        $filters = array('idx = ' . $request->get($this->action));
        $record = $report::getCustomData($this->registry, $filters, $report_filters);

        $record = $record[0];

        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $this->registry['translater']->loadFile($i18n_file);

        if (!$request->get('pattern')) {
            //do not remove this trace
            header('Content-Type: text/html; charset=UTF-8');
            die($this->registry['translater']->translate('error_reports_no_pattern'));
        }

        //prepare report settings
        $settings = $report->get('settings');
        $settings = preg_split('#\r\n|\n|\r#', $settings);
        foreach ($settings as $key => $value) {
            unset($settings[$key]);
            if (preg_match('#(^\#)|(^\s*$)#', $value)) continue;
            $value = preg_split('#\s*:=\s*#', $value);
            $settings[trim($value[0])] = trim($value[1]);
        }

        if ($report_filters['report_for'] == 'invoice') {
            $settings['create_type'] = PH_FINANCE_TYPE_INVOICE;
        } elseif ($report_filters['report_for'] == 'credit') {
            $settings['create_type'] = PH_FINANCE_TYPE_CREDIT_NOTICE;
        } else {
            die('Wrong document type! What are you doing here?!?');
        }

        $method = 'create' . preg_replace('#\s#', '', ucwords(preg_replace('#_#', ' ', $settings['create_model'])));
        if (!method_exists($this, $method)) {
            die('Method "' . $method . '" does not exists!');
        }
        //set document custom name
        if ($request->get('email_subject')) {
            $record['custom_name'] = $request->get('email_subject');
        } else {
            $record['custom_name'] = $report->get('name');
        }

        $gt2 = array('values' => array(),
                     'plain_values' => array('total' => 0,
                                             'total_vat' => 0,
                                             'total_vat_rate' => $record['total_vat_rate'],
                                             'total_with_vat' => 0,
                                             'currency' => $record['currency']));
        $contract_link = sprintf('%s - %s', $record['contract_name'], $record['contract_num']);
        $articles = array();
        $dependencies = array();
        foreach (preg_split('#\s*,\s*#', $settings['report_dependencies']) as $v) {
            list($k, $v) = preg_split('#\s*\=\>\s*#', $v);
            $dependencies[$k] = $v;
        }
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        foreach ($record['extended'] as $extended) {
            $free_text = sprintf($this->i18n('free_text'), $contract_link,
                                 General::strftime('%d.%m.%Y', strtotime($extended['date_from'])),
                                 General::strftime('%d.%m.%Y', strtotime($extended['date_to'])));
            if (empty($articles[$dependencies[$record['article_id']]])) {
                $filts = array('where' => array('n.id = ' . $dependencies[$record['article_id']]),
                               'sanitize' => true);
                $new = Nomenclatures::searchOne($registry, $filts);
            } else {
                $new = $articles[$dependencies[$record['articles_id']]];
            }
            $gt2['values'][] = array('article_id' => $new->get('id'),
                                   'article_code' => $new->get('code'),
                                   'article_name' => $new->get('name'),
                                   'quantity' => 1,
                                   'price' => $extended['difference'],
                                   'free_field1' => $record['object_id'],
                                   'free_text5' => $free_text,
                                   'discount_value' => 0,
                                   'average_weighted_delivery_price' => 0,
                                   'date_from' => $extended['date_from'],
                                   'date_to' => $extended['date_to']);
        }
        $record['gt2'] = $gt2;
        //creates the requested model(document,finance) in simulation mode(without saving)
        $settings['simulation'] = true;

        $record['issue_by'] = $registry['currentUser']->get('id');
        $settings['transform_b_issue_by'] = 'issue_by';

        //get the settings from variables anf invoice groups
        $filters = array('where' => array('co.id = ' . $record['contract_id']));
        $contract = Contracts::searchOne($registry, $filters);
        $contract_default_vars = $contract->getVariablesSettings();

        //payment type
        $record['payment_type'] = $contract_default_vars['invoices_payment_type']['value'];
        $settings['transform_b_payment_type'] = 'payment_type';

        //the container cashbox/bank account
        $container = '';
        if (!empty($contract_default_vars['invoices_container']['value'])) {
            $container = $contract_default_vars['invoices_container']['value'];
        } else {
            //fetch container id from invoice groups
            $filters = array('where' => array('n.code = \'groupRent\''));
            $invoice_group = Nomenclatures::searchOne($registry, $filters);
            $invoice_group_default_vars = $invoice_group->getAssocVars();
            $container = $invoice_group_default_vars['bank_account_nom']['value'];
            unset($invoice_group);
        }
        $record['container_id'] = $container;
        $settings['transform_b_container_id'] = 'container_id';

        //set payment date (the other dates - issue_date, fiscal_event-date should be curent date as they are)
        $record['date_of_payment_count'] = $contract_default_vars['invoices_payment_date']['value']['count'];
        $settings['transform_b_date_of_payment_count'] = 'date_of_payment_count';
        $record['date_of_payment_period_type'] = $contract_default_vars['invoices_payment_date']['value']['period_type'];
        $settings['transform_b_date_of_payment_period_type'] = 'date_of_payment_period_type';
        $record['date_of_payment_point'] = $contract_default_vars['invoices_payment_date']['value']['point'];
        $settings['transform_b_date_of_payment_point'] = 'date_of_payment_point';

        unset($contract);

        $model = $this->$method($record, $settings);

        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';

        //set some vars in the report model to get patterns vars
        $report->set('customer', $model->get('customer'), true);
        $report->set('branch', $model->get('branch'), true);
        $report->set('contact_person', $model->get('contact_person'), true);
        $report->set('created_model_num', $model->get('num'), true);
        foreach($record as $k => $v) {
            $report->set($k, $v, true);
        }

        $attachment = $request->get('pattern');
        if (preg_match('#pattern_\d+#', $attachment)) {
            $pattern = preg_replace('#pattern_#', '', $attachment);
            $request->set('pattern', $pattern, 'all', true);
            $filters = array('where' => array('p.id = ' . $pattern,
                                              'p.active = 1'),
                             'model_lang' => $registry['lang'],
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);

            $placeholders = $model->getPatternsVars();

            $model->extender = new Extender();
            $model->extender->model_lang = $registry['lang'];
            $model->extender->module = $this->module;
            foreach($placeholders as $key => $value) {
                $model->extender->add($key, $value);
            }

            $contract_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s - %s</a>',
                                     $_SERVER['PHP_SELF'], $registry->get('module_param'), 'contracts',
                                     'contracts', 'view', 'view', $record['contract_id'],
                                     $record['contract_name'], $record['contract_num']
                                    );
            $model_link = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s&%s=%s" target="_blank">%s - %s</a>',
                                  $_SERVER['PHP_SELF'], $registry->get('module_param'), 'finance',
                                  $registry->get('controller_param'), 'incomes_reasons',
                                  'incomes_reasons', 'view', 'view', $model->get('id'), $model->get('num'), $model->get('name')
                                 );

            $file_id = '';
            switch ($pattern->get('format')) {
                case 'xls':
                    if (!$file_id = $model->generateXLS(true)) {
                        $registry['messages']->setError($this->i18n('error_report_export_document', array($model_link, $contract_link)));
                    }
                    break;
                case 'pdf':
                default:
                    if (!$file_id = $model->generatePDF(true, true)) {
                        $registry['messages']->setError($this->i18n('error_report_generate_document', array($model_link, $contract_link)));
                    }
                    break;
            }
        }

        die;
    }
}

?>
