<?php

class Grifidhotels_Shifts extends Reports
{
    public static function buildQuery(&$registry, $reportFilters)
    {
        $total_results_count = 0;
        $sql = "SELECT *
        FROM gt2_details gt2d
        JOIN gt2_details_i18n AS gt2di18n ON gt2d.id = gt2di18n.parent_id
        WHERE gt2d.model_id != {$reportFilters['schedule']}
        AND gt2d.article_deliverer IN ({$reportFilters['customers']})
        AND gt2d.article_code LIKE '{$reportFilters['date']}'";

        $db = $registry['db'];
        $results = $db->GetAll($sql);
        $real = [];
        foreach ($results as $result) {
            $real[$result['id']] = $result;
        }
        return [
            $real, $total_results_count
        ];
    }
}