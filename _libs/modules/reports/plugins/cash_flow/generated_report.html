<input type="hidden" id="precision" value="{$reports_additional_options.precision}" />
<input type="hidden" id="fer_type_invoice" value="{$reports_additional_options.fer_type_invoice}" />
<input type="hidden" id="fer_type_pro_invoice" value="{$reports_additional_options.fer_type_pro_invoice}" />

<!-- Column fields templates -->
<script id="templateNum" type="text/x-template">
  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={literal}${module}${if(module!==controller)}&amp;controller=${controller}${/if}&amp;${controller}=view&amp;view=${id}" target="_blank">{/literal}
    {literal}${type_name_num}{/literal}
  </a>
</script>

{if array_key_exists('balance', $reports_results)}
  <div
    class="nzEj2Grid"
    id="grid_balance"
    data-balance="{$reports_results.balance|@json_encode|escape}"
    data-balance-grid-columns="{$reports_additional_options.ej2_settings_balance.columns|@json_encode|escape}"
    data-export-file-name="{$reports_additional_options.ej2_settings_balance.export_file_name|escape}"
  ></div>
{/if}

{if array_key_exists('expenses', $reports_results)}
  <div
    class="nzEj2Grid"
    id="grid_expenses"
    data-expenses="{$reports_results.expenses|@json_encode|escape}"
    data-expenses-grid-columns="{$reports_additional_options.ej2_settings_expenses.columns|@json_encode|escape}"
    data-expenses-grid-sort-settings="{$reports_additional_options.ej2_settings_expenses.sort_settings|@json_encode|escape}"
    data-export-file-name="{$reports_additional_options.ej2_settings_expenses.export_file_name|escape}"
  ></div>
{/if}

{if array_key_exists('incomes', $reports_results)}
  <div
    class="nzEj2Grid"
    id="grid_incomes"
    data-incomes="{$reports_results.incomes|@json_encode|escape}"
    data-incomes-grid-columns="{$reports_additional_options.ej2_settings_incomes.columns|@json_encode|escape}"
    data-incomes-grid-sort-settings="{$reports_additional_options.ej2_settings_incomes.sort_settings|@json_encode|escape}"
    data-export-file-name="{$reports_additional_options.ej2_settings_incomes.export_file_name|escape}"
  ></div>
{/if}
