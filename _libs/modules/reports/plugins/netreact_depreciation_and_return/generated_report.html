<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border vmiddle">{#reports_nom_types#|escape}</td>
          <td class="t_border vmiddle">{#reports_name_code#|escape}</td>
          <td class="t_border vmiddle">{#reports_delivery_price#|escape}</td>
          <td class="t_border vmiddle">{#reports_depreciation#|escape}</td>
          <td class="t_border vmiddle">{#reports_current_depreciation#|escape}</td>
          <td class="t_border vmiddle">{#reports_rest_amount#|escape}</td>
          {if $reports_results.show eq 1}
          <td class="vmiddle" colspan="{$reports_results.max_cols}">{#reports_depreciation_plan#|escape}</td>
          {else}
          <td class="t_border vmiddle">{#reports_depreciation_month#|escape}</td>
          <td class="t_border vmiddle">{#reports_month_income#|escape}</td>
          <td class="t_border vmiddle">{#reports_current_income#|escape}</td>
          <td class="vmiddle">{#reports_return#|escape}</td>
          {/if}
        </tr>
        {foreach from=$reports_results.results key=nom_id item=result name=i}
        {cycle values='t_odd,t_even' assign=c1}
        <tr class="{$c1}">
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap" style="text-align: center;">
            {$result.type_name}
          </td>
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$nom_id}">{$result.code} {$result.name}</a>
          </td>
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap" style="text-align: right;">
            {$result.assets_acquisition_cost|string_format:'%.2f'}
          </td>
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap" style="text-align: right;">
            {$result.assets_amortiozation_periods}
          </td>
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap" style="text-align: right;">
            {$result.total_depreciation|string_format:'%.2f'}
          </td>
          <td{if $reports_results.show eq 1} rowspan="2"{/if} class="t_border" nowrap="nowrap" style="text-align: right;">
            {$result.rest_cost|string_format:'%.2f'}
          </td>
          {if $reports_results.show eq 1}
            {capture assign="cnt"}{if is_array($result.plan)}{$result.plan|@count}{else}0{/if}{/capture}
            {foreach from=$result.plan key=date item=date name=rp1}
              <td class="hcenter t_bottom_border{if $smarty.foreach.rp1.iteration < $reports_results.max_cols} t_border{/if}">{$date}</td>
            {/foreach}
            {if $cnt < $reports_results.max_cols}
              <td colspan="{$reports_results.max_cols-$cnt}">&nbsp;</td>
            {/if}
          <tr class="{$c1}">
            {foreach from=$result.plan key=date item=amount name=rp2}
              <td class="hright t_bottom_border{if $smarty.foreach.rp2.iteration < $reports_results.max_cols} t_border{/if}">{$amount}</td>
            {/foreach}
            {if $cnt < $reports_results.max_cols}
              <td colspan="{$reports_results.max_cols-$cnt}">&nbsp;</td>
            {/if}
          {else}
            <td class="t_border" nowrap="nowrap" style="text-align: right;">
              {$result.single_period_cost|string_format:'%.2f'}
            </td>
            <td class="t_border" nowrap="nowrap" style="text-align: right;">
              {$result.assets_month_income|string_format:'%.2f'}
            </td>
            <td class="t_border" nowrap="nowrap" style="text-align: right;">
              {$result.total_incomes|string_format:'%.2f'}
            </td>
            <td nowrap="nowrap" style="text-align: right;">
              {$result.returns|string_format:'%.2f'}
            </td>
          {/if}
        </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="{if $reports_results.show eq 1}{$reports_results.max_cols+6}{else}10{/if}"></td>
        </tr>
      </table>
      <br /><br />
    </td>
  </tr>
</table>