<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if !empty($reports_results)}
    <table border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td><h1>{#reports_daily_activities#|escape}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="1" cellpadding="0" cellspacing="0">
            <tr align="center">
              <td><strong>{#reports_activity#|escape}</strong></td>
              <td><strong>{#reports_area#|escape}</strong></td>
            </tr>
            {foreach from=$reports_results.daily item='act' name='ai' key='ak'}
            <tr>
              <td>{$act.activity_schedule_name|escape}</td>
              <td>{$act.zone_schedule_name|escape}</td>
            </tr>
            {foreachelse}
            <tr>
              <td colspan="2" style="color: #F50504;">{#no_items_found#|escape}</td>
            </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_periodical_activities#|escape}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="1" cellpadding="0" cellspacing="0">
            <tr align="center">
              <td><strong>{#reports_activity#|escape}</strong></td>
              <td><strong>{#reports_area#|escape}</strong></td>
              <td><strong>{#reports_frequency#|escape}</strong></td>
              <td><strong>{#reports_due_execute#|escape}</strong></td>
              <td><strong>{#reports_real_execute#|escape}</strong></td>
            </tr>
            {foreach from=$reports_results.periodical item='act' name='ai' key='ak'}
            <tr{if $act.real_execute >= $act.due_execute && $act.frequency_schedule neq 'mixed'} style="text-decoration: line-through;"{/if}>
              <td>{$act.activity_schedule_name|escape}</td>
              <td>{$act.zone_schedule_name|escape}</td>
              {if $act.frequency_schedule eq 'mixed'}
              <td colspan="2">
                {#warning_reports_different_frequency#|escape}
              </td>
              {else}
              <td>
                {capture assign='frequency_name'}reports_frequency_{$act.frequency_schedule|replace:"fr_":""}{/capture}
                {foreach from=$act.timeline item='t' key='tk' name='ti'}
                  {if is_array($act.timeline) && $act.timeline|@count eq 1}
                    {$act.schedule_execute.$tk} {if $act.schedule_execute.$tk == 1}{#reports_time_sg#|escape}{else}{#reports_time_pl#|escape}{/if} {$smarty.config.$frequency_name|escape}{if !empty($report_filters.from_date.value) && $t gt $report_filters.from_date.value} ({#from#|mb_lower} {$t|date_format:#date_short#}){/if}
                  {else}
                    {$act.schedule_execute.$tk|default:0} {if $act.schedule_execute.$tk == 1}{#reports_time_sg#|escape}{else}{#reports_time_pl#|escape}{/if} {$smarty.config.$frequency_name|escape} ({#from#|mb_lower} {$t|date_format:#date_short#})<br />
                  {/if}
                {/foreach}
              </td>
              <td align="right">{$act.due_execute}</td>
              {/if}
              <td align="right">{$act.real_execute}{if $act.num_problems}<span style="color: #F50504;">{repeat string='!' num=$act.num_problems}</span>{/if}</td>
            </tr>
            {foreachelse}
            <tr>
              <td colspan="5" style="color: #F50504;">{#no_items_found#|escape}</td>
            </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>
    {/if}
  </body>
</html>