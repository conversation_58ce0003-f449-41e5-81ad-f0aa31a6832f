/**
 * custom javascript messages
 */
if (env.current_lang == 'bg') {
    i18n['messages']['error_queisser_visit_planning_position'] = 'Моля, попълнете стойности за поредност на посещенията, които не се дублират помежду си или с тези на вече добавени посещения към дневния план!';
} else if (env.current_lang == 'ro') {
    i18n['messages']['error_queisser_visit_planning_position'] = 'Vă rugăm să specificați valorile pentru ordinea întâlnirilor, care sunt unice și nu au duplicate printre valorile pentru întâlnirile deja adăugate la programul zilnic!';
} else if (env.current_lang == 'de') {
    i18n['messages']['error_queisser_visit_planning_position'] = 'Bitte geben Sie Werte für die Reihenfolge der Meetings an, die einzigartig sind und keine Duplikate unter den Werten für bereits zum Tagesplan hinzugefügte Meetings aufweisen!';
} else {
    i18n['messages']['error_queisser_visit_planning_position'] = 'Please, specify values for order of meetings which are unique and have no duplicates among values for meetings already added to daily schedule!';
}

/**
 * Toggle visiblity of date and period filters
 *
 * @param element - selected 'search_by' radio option
 */
function toggleDateFilters(element) {
    if (element.value == 1) {
        switchVisibleRows('date_from,date_to', 'date');
    } else if (element.value == 2) {
        switchVisibleRows('date', 'date_from,date_to');
    }
    // toggle visibility of 'create_daily_schedule'
    toggleCreateDailySchedule();
}

/**
 * Toggle disabled state of options and optgroups when selected user is changed
 *
 * @param element - sales rep dropdown
 */
function toggleRays(element) {
    var rays = $$('select[id^="ray_"]'),
        regex = new RegExp('(^|\\\s)user_' + element.value + '($|\\\s)');

    for (var j=0; j<rays.length; j++) {
        opts = rays[j].options;
        // remove selection unless it is an option for selected user
        if (element.value && !rays[j][rays[j].selectedIndex].className.match(regex)) {
            rays[j].selectedIndex = 0;
            toggleUndefined(rays[j]);
        }
        for (var i = 0; i < opts.length; i++) {
            if (opts[i].className.match(/undefined/)) {
                continue;
            }
            if (!element.value || opts[i].className.match(regex)) {
                removeClass(opts[i], 'hidden');
                removeClass(opts[i], 'inactive_option');
                opts[i].removeAttribute('disabled');
                if (opts[i].parentNode.tagName == 'OPTGROUP' && opts[i].parentNode.className.match(/inactive_option/)) {
                    removeClass(opts[i].parentNode, 'hidden');
                    removeClass(opts[i].parentNode, 'inactive_option');
                }
            } else {
                addClass(opts[i], 'hidden');
                addClass(opts[i], 'inactive_option');
                opts[i].setAttribute('disabled', 'disabled');
                if (opts[i].parentNode.tagName == 'OPTGROUP' && !opts[i].parentNode.className.match(/inactive_option/) && !$(opts[i].parentNode).select('option:not([disabled])').length) {
                    addClass(opts[i].parentNode, 'hidden');
                    addClass(opts[i].parentNode, 'inactive_option');
                }
            }
        }
    }

    // toggle visibility of 'create_daily_schedule'
    toggleCreateDailySchedule();
}

/**
 * Toggle visibility of 'create_daily_schedule' filter row
 */
function toggleCreateDailySchedule() {
    var cds = $('create_daily_schedule_1');
    if (cds) {
        var search_by_date = $('search_by_1') != null ? $('search_by_1').checked : false;
        var agent = $('agent') != null ? $('agent').value : '';

        rays_selected = 0;
        var rays = $$('select[id^="ray_"]');
        for (var i=0; i<rays.length; i++) {
            rays_selected += (rays[i].value ? 1 : 0);
        }

        if (search_by_date && rays_selected && agent) {
            switchVisibleRows('', 'create_daily_schedule');
        } else {
            cds.checked = false;
            switchVisibleRows('create_daily_schedule', '');
        }
    }
}

/**
 * Function to process selected records in the queisser_visit_planning report
 *
 * @param object element - the button element which calls the function
 * @param string action - the action which will be triggered
 */
function queisserCreateDailySchedule(element, action) {
    // get the form
    var form = element.form;

    // check selected rows
    var fields = $$('input[id^="items_"][type="text"]:not([disabled])');
    var positions = [];
    var invalid = false;
    for (var j = 0; j < fields.length; j++) {
        removeClass(fields[j], 'erred');
        if (fields[j].value) {
            var val = parseInt(fields[j].value);
            if (isNaN(val) || val <= 0 || positions.indexOf(val) > -1 || existing_positions.indexOf(val) > -1) {
                invalid = true;
                addClass(fields[j], 'erred');
            } else {
                positions.push(val);
            }
        }
    }

    // if no rows are selected or there are invalid values, no action could be taken
    if (positions.length > 0 && !invalid) {
        var form_action = env.base_url + '?' + env.module_param + '=reports&reports=' + action + '&report_type=' + $('report_type').value;
        form.action = form_action;
        return true;
    } else {
        alert(i18n['messages']['error_queisser_visit_planning_position']);
        return false;
    }
}

/**
 * Load results for selected ray from dashlet
 *
 * @param element - dropdown with ray options
 * @return {Boolean}
 */
loadRayResults = function (element) {
    var container = $('visit_planning_container');
    if (!container) {
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            if (result.errors) {
                var messages = '';
                if (result.errors) {
                    messages += '<ul class="error" style="padding: 0 0 0 27px;">\n';
                    for (var err in result.errors) {
                        if (typeof result.errors[err] == 'string') {
                            messages += '<li>' + result.errors[err] + '</li>\n';
                        }
                    }
                    messages += '</ul>';
                }
                container.innerHTML = messages;
            } else {
                container.innerHTML = result.data || '';
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_load_ray_results&report_type=' + $(element.form).down('[name="report_type"]').value;

    new Ajax.Request(url, opt);
};

/**
 * Function to load another report with selected comments
 *
 * @param customer_id - id of the customer which we want to see comments for
 */
openCustomerComments = function (customer_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            window.open(t.responseText, '_blank');
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_prepare_redirect_link&report_type=' +  $('report_type').value + '&customer=' + customer_id;

    new Ajax.Request(url, opt);
};
