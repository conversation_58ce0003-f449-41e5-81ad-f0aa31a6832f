<?php

/**
 * Plugin for conversion of sell price in alternative unit of measure into
 * selected currency in GT2 table.
 * Used in ELSIM installation.
 */
class Auom_Autocompleters extends Autocompleters {
    /**
     * Method for conversion of sell price in alternative unit of measure into
     * selected currency in GT2 table
     *
     * @param Registry $registry - the main registry
     * @param array $autocomplete - autocompleter settings
     * @param array $data - data of selected option
     * @return array - data of selected option after processing
     */
    public static function preparePrice(Registry &$registry, array &$autocomplete, array &$data) {
        $db = &$registry['db'];

        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $rate = 1;
        $main_currency = Finance_Currencies::getMain($registry);

        if ($autocomplete['type'] == 'nomenclatures' &&
        !empty($autocomplete['id_var']) && !empty($data['$' . $autocomplete['id_var']]) &&
        !empty($autocomplete['plugin_params']['sell_price_field'])) {

            // we have to check currency in nomenclature
            $sell_price_currency = $db->GetOne("
                SELECT sell_price_currency FROM " . DB_TABLE_NOMENCLATURES . "
                WHERE id = '{$data['$' . $autocomplete['id_var']]}'
            ");
            // if nomenclature sell price currency is different from the main currency,
            // we have to convert prices because default autocompleter functionality
            // has recalculated them from sell price currency into GT2 currency.
            // but it is assumed that nomenclature prices are all in the main
            // currency no matter what the value of sell price currency is.
            if ($sell_price_currency && $sell_price_currency != $main_currency) {
                $rate = Finance_Currencies::getRate(
                    $registry,
                    $main_currency,
                    $sell_price_currency
                );
            }
        } elseif ($autocomplete['type'] = 'autocompleters') {
            // source currency is main currency of installation, destination currency is the one currently selected in GT2
            $rate =
                empty($autocomplete['plugin_params']['currency']) ?
                0 :
                Finance_Currencies::getRate(
                    $registry,
                    $main_currency,
                    $autocomplete['plugin_params']['currency']
                );
        }
        $prec = $registry['config']->getParam('precision', 'nom_sell_price');
        $pfs = sprintf('%%.%dF', $prec);

        // recalculate alternative sell price field
        if (!empty($autocomplete['plugin_params']['alt_sell_price_field']) && isset($data['$' . $autocomplete['plugin_params']['alt_sell_price_field']])) {
            $data['$' . $autocomplete['plugin_params']['alt_sell_price_field']] =
                sprintf(
                    $pfs,
                    round($data['$' . $autocomplete['plugin_params']['alt_sell_price_field']] * $rate, $prec)
                );
        }
        // if autocompleter is custom, recalculate sell price field as well
        if (!empty($autocomplete['plugin_params']['sell_price_field']) && isset($data['$' . $autocomplete['plugin_params']['sell_price_field']])) {
            $data['$' . $autocomplete['plugin_params']['sell_price_field']] =
                sprintf(
                    $pfs,
                    round($data['$' . $autocomplete['plugin_params']['sell_price_field']] * $rate, $prec)
                );
        }

        return $data;
    }
}
