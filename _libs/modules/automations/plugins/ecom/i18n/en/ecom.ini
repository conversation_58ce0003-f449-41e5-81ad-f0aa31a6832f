automation_ecom_call = Call
automation_ecom_no_user_with_this_phone = The call is to the number %s. There is no customer with this phone in the database.

automation_ecom_task_name = Expiring deadline of %s from order %s
automation_ecom_service = service

error_automation_more_than_one_subscription_for_one_period = Not allowed more than one subscription for the same customer for the same period!
error_automation_more_than_one_subscription_for_customer = This customer already has the same or matching period for an order! Please correct the period!
error_automation_renew_subscription = Please check the selected subscription. Renew subscription has to be checked!
error_automation_new_subscription = Please check the selected subscription. New subscription has to be checked!
error_automation_subscription_groups = Two or more of the selected articles are from the same group and the same type! The selected articles should be from different groups and type!

error_automation_validate_task_period = The task period cannot be more than %s days. Please change the date for the final performance!

error_automation_validate_document_customer_responsible_missing_required_settings = Missing required settings for automatic action%s!
error_automation_validate_document_customer_responsible_add = Cannot add order for %s, because %s and %s are responsible! Please, contact Dimiter <PERSON><PERSON> to be assigned!
error_automation_validate_document_customer_responsible_client_not_new = At line %s is selected the service "%s", which is of type "%s", but the client "%s" is not new, because there are already <a href="%s" target="_blank">orders</ a> for him!
error_automation_validate_document_customer_responsible_client_responsible_is_someone_else = At line %s is selected the service "%s", which is of type "%s", but for the client "%s" responsible for %s is %s!
error_automation_validate_document_customer_responsible_client_responsible_is_no_one = At line %s is selected the service "%s", which is of type "%s", but for the client "%s" there is no responsible for %s!
error_automation_validate_document_customer_responsible_page = page
error_automation_validate_document_customer_responsible_banners = banners
error_automation_validate_document_customer_responsible_no_active_tasks = At line %s is selected the service "%s", which is of type "%s", but you don't have active tasks of type "%s" for client "%s"!
error_automation_validate_document_customer_responsible_periods_overlap = At line %s for the service "%s", which is of type "%s", there is a selected period, which overlaps with the period of the same type service selected in <a href="%s" target="_blank">previous orders</a>!
error_automation_validate_document_customer_responsible_prev_order_is_yours_too = At line %s is selected the service "%s", which is of kind "%s", but the previous order <a href="%s" target="_blank">"%s"</a> is from you too!
error_automation_validate_document_customer_responsible_has_non_expired_order = At line %s is selected the service "%s", which is of type "%s", but there are <a href="%s" target="_blank">orders</a> for the client, which have not expired yet!
error_automation_validate_document_customer_responsible_invalid_renew = At line %s is selected the service "%s", which is of type "%s" and kind "%s", but for the client "%s" there are no previous orders for this type of service!
error_automation_validate_document_customer_responsible_invalid_period_from = At line %s you have set the start date of the activity period to be %s y., but the activity period from <a href="%s" target="_blank">the previous order</a> expires as %s y.!

error_automation_task_assigned_users_exceeded = More than %s user selected! Please select no more than %1$s!
error_automation_task_assigned_to_not_available_role = Selected user must be from the roles %s! Please select user from these roles!
error_automation_task_assigned_to_not_available_role_pl = Selected users must be from the roles %s! Please select users from these roles!

automation_finish_order_tasks_success = Successfully changed status of <a href="%s" target="_blank">"%s"</a>
error_automation_finish_order_tasks_failed = A technical problem has occurred while trying to automaticaly finish the tasks of type "Task banner" or "Task for re subscription"!