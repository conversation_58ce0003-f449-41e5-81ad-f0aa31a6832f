automation_gig_validateleaverequest_required_settings = Required settings are missing for automatic action and because of this the leave request can't be validated!
automation_gig_validateleaverequest_no_report = Can't check how many leave days can be used!
automation_gig_validateleaverequest_no_data_for_customer = Can't find employee data and because of this the leave request can't be validated!
automation_gig_validateleaverequest_no_data_for_year = There are no unused days for %s!
automation_gig_validateleaverequest_not_enough_day = It's requested %s day, but for %s y. there are %s!
automation_gig_validateleaverequest_not_enough_days = There are requested %s days, but for %s y. there are %s!
automation_gig_validateleaverequest_period_older_than_leave_year = The leave request period can't be before "%s"!
automation_gig_validateleaverequest_report_failed = The leave request can't be validated becaus of a problem with te leave requests report!
automation_gig_validateleaverequest_different_years_start_end = The start date and the and date can't be in different years!
automation_gig_validateleaverequest_overlap_denied_one = The employee in the current leave request has a previous <a target="_blank" href="%s">denied vacation</a>, which overlaps with the selected period!
automation_gig_validateleaverequest_overlap_denied_several = The employee in the current leave request has previous <a target="_blank" href="%s">denied vacations</a>, which overlaps with the selected period!
automation_gig_validateleaverequest_overlap_undenied_one = The employee in the current leave request has a previous <a target="_blank" href="%s">requested/used vacation</a>, which overlaps with the selected period!
automation_gig_validateleaverequest_overlap_undenied_several = The employee in the current leave request has previous <a target="_blank" href="%s">requested/used vacations</a>, which overlaps with the selected period!