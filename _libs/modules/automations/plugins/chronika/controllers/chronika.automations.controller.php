<?php

class Chronika_Automations_Controller extends Automations_Controller {
    /**
     * Plugin for creation of documents of "Monthly report" type.
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function createMonthlyReports($params) {
        set_time_limit(3600);

        $db = &$this->registry['db'];

        $lang = $this->registry->get('lang');

        // get customers
        $query = 'SELECT c.id AS idx, b.id AS branch' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                 '  ON c.id=d.customer' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS b' . "\n" .
                 '  ON b.subtype=\'branch\' AND c.id=b.parent_customer AND b.is_main=1 AND c.is_company=1' . "\n" .
                 'WHERE c.subtype=\'normal\' AND c.active=1 AND c.deleted_by=0' . "\n" .
                 '  AND d.type=\'' . $this->settings['doc_type_activity_base'] . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                 // search only for specific customers
                 (!empty($this->settings['specific_customers']) ? '  AND c.id IN (' . $this->settings['specific_customers'] . ')' . "\n" : '') .
                 'GROUP BY c.id';
        $customers = $db->GetAssoc($query);
        // add own company id(s)
        if (!empty($this->settings['own_company'])) {
            $query = 'SELECT c.id AS idx, b.id AS branch' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS b' . "\n" .
                     '  ON b.subtype=\'branch\' AND c.id=b.parent_customer AND b.is_main=1 AND c.is_company=1' . "\n" .
                     'WHERE c.id IN (' . $this->settings['own_company'] . ')' . "\n" .
                     'GROUP BY c.id';
            $customers = array_unique($customers + $db->GetAssoc($query));
        }

        $result = true;
        if ($customers) {

            //load the language files for documents
            $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $lang . '/documents.ini');

            // get type
            require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
            $filters = array('where' => array('dt.id = \'' . $params['start_model_type'] . '\'',
                                              'dt.active = 1',
                                              'dt.inheritance = 0'),
                             'sanitize' => true);
            $type = Documents_Types::searchOne($this->registry, $filters);

            // get counter model just once, assuming its settings will not be
            // changed during automation execution
            // IMPORTANT: next_number is always taken from db (with locking)
            // when number of document is defined
            $counter = null;
            if ($type) {
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $doc_tmp = new Document($this->registry, array('type' => $params['start_model_type']));
                $doc_tmp->getCounter();
                if (!empty($doc_tmp->counter) && is_object($doc_tmp->counter)) {
                    $counter = clone $doc_tmp->counter;
                }
                unset($doc_tmp);
            }

            if (!$type) {
                // invalid type
                $this->executionErrors[] = $this->i18n('error_invalid_type');
            } elseif (!$counter) {
                // missing counter
                $this->executionErrors[] = $this->i18n('error_no_counter_for_this_document_type');
            } else {
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                // common properties
                $props = array(
                    'type'                  => $params['start_model_type'],
                    'type_code'             => $type->get('code'),
                    'type_name'             => $type->get('name'),
                    'generate_system_task'  => $type->get('generate_system_task'),
                    'direction'             => $type->get('direction'),
                    'name'                  => $type->get('default_name'),
                    'department'            => $type->getDefaultDepartment(),
                    'group'                 => $type->getDefaultGroup(),
                    'media'                 => $type->get('default_media'),
                    'media_name'            => $type->get('default_media_name'),
                    'active'                => '1'
                );

                foreach ($customers as $customer => $branch) {

                    $db->StartTrans();

                    $doc_props = $props + array('customer' => $customer, 'branch' => $branch);
                    $document = new Document($this->registry, $doc_props);
                    // set counter
                    $document->counter = $counter;

                    if ($document->save()) {

                        $old_document = new Document($this->registry);
                        $old_document->sanitize();

                        $filters = array('where' => array('d.id = ' . $document->get('id')),
                                         'model_lang' => $document->get('model_lang'),
                                         'sanitize' => true);
                        $new_document = Documents::searchOne($this->registry, $filters);

                        $audit_parent = Documents_History::saveData($this->registry,
                                                                    array('model' => $document,
                                                                          'action_type' => 'add',
                                                                          'new_model' => $new_document,
                                                                          'old_model' => $old_document));
                        if (!$audit_parent) {
                            $this->executionErrors[] = $this->_prepareErrorMessage('Error saving history for document with ID: ' . $new_document->get('id') . ' (%s)');
                        }

                        unset($old_document);
                        unset($new_document);

                    } else {
                        $this->executionErrors[] = $this->_prepareErrorMessage('Error adding document for customer with ID: ' . $doc_props['customer'] . ' (%s)');
                        $db->FailTrans();
                    }

                    $result = !$db->HasFailedTrans();

                    $db->CompleteTrans();
                }
            }
        }

        $this->updateAutomationHistory($params, 0, intval($result));

        return true;
    }

    /**
     * Prepares error message from error messages from database and registry.
     *
     * @param string $error_message_text - text for error message with placeholder
     * @return string - error message text with collected error messages filled in
     */
    private function _prepareErrorMessage($error_message_text = '%s') {
        $error_messages = array();

        $db = &$this->registry['db'];
        // DB error message
        if ($db->ErrorMsg()) {
            $error_messages[] = 'DB error message: ' . $db->ErrorMsg();
        }

        //check for error messages from execution
        $error_messages = array_merge($error_messages, $this->registry['messages']->getErrors());
        $this->registry['messages']->flush();

        return sprintf($error_message_text, implode('; ', $error_messages));
    }

    /**
     * Plugin for creation of documents based on the selected data in the gruop tables for clients acticity
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function createRegister($params) {
        $db = &$this->registry['db'];

        $lang = $this->registry->get('lang');

        // parse the settings to find the included grouping tables
        $included_gt = preg_split('#\s*,\s*#', $this->settings['included_group_tables']);
        $included_gt = array_filter($included_gt);
        unset($this->settings['included_group_tables']);

        $date_fields = array();
        $ordered_settings = array();
        foreach ($included_gt as $idx => $inc_gt) {
            $set = array();
            foreach ($this->settings as $key => $setting) {
                if (!preg_match('#^' . $inc_gt . '_#', $key)) {
                    // if the setting does not start with the name of the grouping table that setting is skipped
                    continue;
                }

                // strip the table name to find which is the settings that has to be set
                $key = preg_replace('#^' . $inc_gt . '_#', '', $key);

                //  if the key is date_field this means that this field will be used
                // to find out if a document has to be created based on the date in this field
                if ($key == 'date_field') {
                    $date_fields[$inc_gt] = $setting;
                }

                // prepared the settings as an ordered array
                if (preg_match('#^set_(.*)$#', $key, $matches)) {
                    $set[$matches[1]] = $setting;
                    continue;
                }
                $ordered_settings[$inc_gt][$key] = $setting;
            }
            if (empty($set) || empty($ordered_settings[$inc_gt]['date_field'])) {
                unset($ordered_settings[$inc_gt]);
                unset($date_fields[$inc_gt]);
                continue;
            }
            $ordered_settings[$inc_gt]['set'] = $set;
        }

        // if no processed settings then the automation stops its execution
        if (empty($ordered_settings)) {
            return true;
        }

        // get the grouping index of the needed grouping tables
        $sql = 'SELECT fm.name, fm.grouping' . "\n" .
               'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
               'WHERE fm.name IN ("' . implode('","', array_keys($ordered_settings)) . '") AND fm.model_type="' . $params['start_model_type'] . '" AND fm.model="Document" AND fm.type="group"' . "\n";
        $groups_data = $db->GetAssoc($sql);

        // get the data concerning the needed date fields to find the information about the dates
        $sql = 'SELECT fm.id, fm.name, fm.grouping' . "\n" .
               'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
               'WHERE fm.name IN ("' . implode('","', $date_fields) . '") AND fm.grouping IN (' . implode(',', $groups_data) . ')' . "\n";
        $dates_data = $db->GetAssoc($sql);

        // find the rows and models which contains date equal to the current one
        $sql = 'SELECT var_id, GROUP_CONCAT(CONCAT(num, "_",  model_id))' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS_CSTM . "\n" .
               'WHERE var_id IN ("' . implode('","', array_keys($dates_data)) . '") AND DATE_FORMAT(value, "%Y-%m-%d")=CURDATE()' . "\n" .
               'GROUP BY model_id, var_id';
        $rows_data = $db->GetAssoc($sql);

        // if no rows for the current date then the automation stops its execution
        if (empty($rows_data)) {
            return true;
        }

        /*
         * process the row data in the order:
         * [date_field_id] =>
         *      [model_id] =>
         *          array(row_nums)
         */
        $rows_definitions = array();
        foreach ($rows_data as $key => $value) {
            $value = array_filter(preg_split('#\s*,\s*#', $value));
            foreach ($value as $val) {
                $val = explode('_', $val);
                if (!isset($rows_definitions[$key][$val[1]])) {
                    $rows_definitions[$key][$val[1]] = array();
                }
                $rows_definitions[$key][$val[1]][] = $val[0];
            }
        }

        // get the needed document rows and all the required additional data
        $sql = 'SELECT fm.name as field_name, fm.grouping as field_grouping, d_cstm.model_id, d_cstm.num as row_num, d_cstm.var_id, d_cstm.value as field_value, d.*, di18n.* ' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               '  ON (d.id=d_cstm.model_id)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
               '  ON (d.id=di18n.parent_id AND di18n.lang="' . $lang . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
               '  ON (d_cstm.var_id=fm.id)' . "\n" .
               'WHERE ';
        $where = array();
        foreach ($rows_definitions as $key => $values) {
            foreach ($values as $idx => $val) {
                $date_field_grouping_name = array_search($dates_data[$key]['name'], $date_fields);
                $where[] = sprintf('d_cstm.model_id="%d" AND d_cstm.num IN ("%s") AND CONCAT("a_", fm.name) IN ("%s")',
                                   $idx,
                                   implode('","', $val),
                                   implode('","', $ordered_settings[$date_field_grouping_name]['set']));
            }
        }
        $sql .= '(' . implode(") OR\n(", $where) . ') AND d.id IS NOT NULL' . "\n" .
                'ORDER BY d_cstm.model_id, fm.grouping, d_cstm.num';
        $full_rows_data = $db->GetAll($sql);


        $prepared_rows = array();
        // unique key to define the row
        $frd_key = '';

        // the grouping index of the previous processed ggrouping table
        $old_g = '';

        foreach ($full_rows_data as $frd) {
            $frd_key = sprintf('%s_%s_%s', $frd['model_id'], $frd['field_grouping'], $frd['row_num']);
            if (!isset($prepared_rows[$frd_key])) {
                // find the name of the grouping table
                $gt_name = array_search($frd['field_grouping'], $groups_data);

                // get the settings for the grouping table
                $set = $ordered_settings[$gt_name]['set'];

                $prepared_rows[$frd_key] = array(
                    'settings' => $set,
                    'data'     => array(),
                );
            }
            $prepared_rows[$frd_key]['data'][$frd['field_name']] = $frd;
        }

        $db->StartTrans();
        foreach ($prepared_rows as $prep_row) {
            $this->_createRegisterDocument($prep_row['data'], $prep_row['settings']);
        }
        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        $this->updateAutomationHistory($params, 0, $result);

        return $result;
    }

    /*
     * Function to create document for createRegister automation
     *
     * @param array $rows_data - the row data which will be created a document for
     * @param array $set - options which point which where to be set each var in the new document
     *
     * @return boolean - result of operation
     */
    private function _createRegisterDocument($rows_data, $set) {
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        $first_var = reset($rows_data);
        $assign = array();
        $properties = array();
        $additional_vars = array();
        foreach ($set as $var => $source) {
            $var_type_dest = '';
            $var_type_src = '';
            $dest_type = '';
            $src_type = '';

            if (preg_match('#^a_#', $var)) {
                $var_type_dest = preg_replace('#^a_#', '', $var);
                $var_container = &$additional_vars;
            } elseif ($var == 'assign') {
                $var_type_dest = 'assign';
                $var_container = &$assign;
            } else {
                $var_type_dest = $var;
                $var_container = &$properties;
            }

            if (preg_match('#^a_#', $source)) {
                $var_type_src = preg_replace('#^a_#', '', $source);
                $src_type = 'additional';
            } else {
                $var_type_src = $source;
                $src_type = 'basic';
            }

            if ($var_type_dest == 'type') {
                $var_container[$var_type_dest] = $var_type_src;
            } else {
                if ($src_type == 'basic') {
                    // take the first var
                    if ($var_type_dest == 'assign') {
                        $var_container[] = (!empty($first_var[$var_type_src]) ? $first_var[$var_type_src] : '');
                    } else {
                        $var_container[$var_type_dest] = (!empty($first_var[$var_type_src]) ? $first_var[$var_type_src] : '');
                    }
                } else {
                    if ($var_type_dest == 'assign') {
                        $var_container[] = (!empty($rows_data[$var_type_src]['field_value']) ? $rows_data[$var_type_src]['field_value'] : '');
                    } else {
                        $var_container[$var_type_dest] = (!empty($rows_data[$var_type_src]['field_value']) ? $rows_data[$var_type_src]['field_value'] : '');
                    }
                }
            }

            if ($var_type_dest == 'deadline' && !empty($var_container[$var_type_dest])) {
                if (preg_match('#^\d{4}-\d{2}-\d{2}$#', $var_container[$var_type_dest])) {
                    $var_container[$var_type_dest] .= ' 17:00:00';
                }
            }
        }

        // clear the assign array and additional vars array in case there are empty settings
        $assign = array_filter($assign);
        $additional_vars = array_filter($additional_vars);

        // get the customer from the vars
        if (empty($properties['customer'])) {
            $properties['customer'] = $first_var['customer'];
        }

        // set the group and department in case they are set in the automation settings
        if (!empty($this->settings['default_group'])) {
            $properties['group'] = $this->settings['default_group'];
        }
        if (!empty($this->settings['default_department'])) {
            $properties['department'] = $this->settings['default_department'];
        }
        $properties['active'] = 1;

        // create the new model
        $document = new Document($this->registry, $properties);

        // get type to take the default name
        $filters = array('where' => array('dt.id = \'' . $document->get('type') . '\'',
                                          'dt.active = 1'),
                                          'sanitize' => true);
        $doc_type = Documents_Types::searchOne($this->registry, $filters);
        $document->set('name', $doc_type->get('default_name'), true);

        // in case the additional vars has to be set perform getVars and replace needed var values
        if (!empty($additional_vars)) {
            $document->getVars();
            $vars = $document->get('vars');
            foreach ($vars as $idx => $add_var) {
                if (isset($additional_vars[$add_var['name']])) {
                    $vars[$idx]['value'] = $additional_vars[$add_var['name']];
                }
            }
            $document->set('vars', $vars, true);
        }

        // save the document
        if ($document->save()) {
            $old_document = new Document($this->registry);
            $old_document->sanitize();

            $filters = array('where' => array('d.id = ' . $document->get('id')),
                             'model_lang' => $document->get('model_lang'),
                             'sanitize' => true);
            $new_document = Documents::searchOne($this->registry, $filters);

            // write history
            Documents_History::saveData($this->registry,
                                        array('model'       => $document,
                                              'action_type' => 'add',
                                              'new_model'   => $new_document,
                                              'old_model'   => $old_document));

            unset($old_document);
            unset($document);

            // if there is users to assign then they are assigned
            if (!empty($assign)) {
                //set or clear (if id < 0) document decision maker assignment
                $old_document = clone $new_document;
                $new_document->set('assignments_owner', $assign, true);
                $new_document->unsanitize();

                if ($new_document->assign(false)) {
                    $filters = array('where'         => array('d.id = ' . $new_document->get('id')),
                                     'model_lang'    => $new_document->get('model_lang'));
                    $assigned_document = Documents::searchOne($this->registry, $filters);

                    // save history
                    Documents_History::saveData($this->registry,
                                                array('model' => $assigned_document,
                                                      'action_type' => 'assign',
                                                      'new_model' => $assigned_document,
                                                      'old_model' => $old_document));
                } else {
                    $row_num = $first_var['row_num'];
                    $field_grouping = $first_var['field_grouping'];
                    $this->executionErrors[] = sprintf('An error occurred while trying to assign the new created document which source is the row %d in grouping table (index %d) in document with id %d!', $first_var['row_num'], $first_var['field_grouping'], $first_var['id']);
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            $row_num = $first_var['row_num'];
            $field_grouping = $first_var['field_grouping'];
            $this->executionErrors[] = sprintf('An error occurred while trying to add document for row %d in grouping table (index %d) for the document with id %d!', $first_var['row_num'], $first_var['field_grouping'], $first_var['id']);
            $errors = $this->registry['messages']->getErrors();
            foreach ($errors as $err) {
                $this->executionErrors[] = '- ' . $err;
            }
            $this->registry['messages']->flush();
            $this->registry['db']->FailTrans();
        }
    }

    public function finishCustomerReport($params) {

        $db = $this->registry['db'];
        $db->StartTrans();

        $vars = $params['model']->getAssocVars();

        $vids = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model = "Document" AND model_type = 1 AND `grouping` = 24';
        $vids = $db->GetAssoc($vids);
        //process tasks
        $vv = sprintf('%d, %d, %d', $vids['time_to_invoice'], $vids['time_correction_four'], $vids['invoiced_time']);
        foreach (array($vars['task_u_key']['value'], $vars['nad_u_key']['value']) as $p => $values) {
            foreach ($values as $k => $v) {
                $v = explode('_', $v);
                $query = 'SELECT dc.var_id, dc.*' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' dc' . "\n" .
                         'WHERE model_id = ' . $v[1] . ' AND var_id IN (' . $vv . ') AND num = ' . $v[2];
                $current = $db->GetAssoc($query);
                if ($p == 0) {
                    $measure = $vars['task_measure']['value'][$k];
                    $tmp1 = explode(':', $vars['task_time']['value'][$k]);
                } else {
                    $measure = $vars['nad_measure']['value'][$k];
                    if ($measure == 1) {
                        $tmp1 = explode(':', $vars['nad_time']['value'][$k]);
                    }
                }
                if ($measure == 1) {
                    // time
                    $tmp1[0] = str_pad($tmp1[0], 2, 0, STR_PAD_LEFT);
                    $tmp1[1] = str_pad($tmp1[1], 2, 0, STR_PAD_LEFT);
                    if ($current[$vids['invoiced_time']]['value']) {
                        $tmp = explode(':', $current[$vids['invoiced_time']]['value']);
                        $tmp = date('Y-m-d ') . str_pad($tmp[0], 2, 0, STR_PAD_LEFT) . ':' . str_pad($tmp[1], 2, 0, STR_PAD_LEFT) . ':00';
                        $tmp = date_add(date_create($tmp), new DateInterval('PT' . $tmp1[0] . 'H' . $tmp1[1] . 'M'))->format('H:i');
                    } else {
                        $tmp = $tmp1[0] . ':' . $tmp1[1];
                    }
                    $current[$vids['invoiced_time']]['value'] = $tmp;
                    $tmp1 = explode(':', $tmp);
                    $tmp = explode(':', $current[$vids['time_correction_four']]['value']);
                    $current[$vids['time_correction_four']]['value'] = str_pad($tmp[0], 2, 0, STR_PAD_LEFT) . ':' . str_pad($tmp[1], 2, 0, STR_PAD_LEFT);
                    $tmp = date('Y-m-d ') . $current[$vids['time_correction_four']]['value'] . ':00';
                    $tmp = date_sub(date_create($tmp), new DateInterval('PT' . $tmp1[0] . 'H' . $tmp1[1] . 'M'))->format('H:i');
                    $current[$vids['time_to_invoice']]['value'] = $tmp;
                    if ($tmp == '00:00' || $tmp > $current[$vids['time_correction_four']]['value']) {
                        $current[$vids['time_to_invoice']]['value'] = '';
                    }
                } else {
                    // pcs
                    $current[$vids['time_to_invoice']]['value'] = '';
                    $current[$vids['invoiced_time']]['value'] = $vars['nad_time']['value'][$k];
                }

                foreach($current as $var => $data) {
                    $query = 'UPDATE ' . DB_TABLE_DOCUMENTS_CSTM . ' SET' . "\n" .
                             '  value = "' . $data['value'] . '",' . "\n" .
                             '  modified = NOW(),' . "\n" .
                             '  modified_by = ' . $this->registry['originalUser']->get('id') . "\n" .
                             'WHERE model_id = ' . $data['model_id'] . ' AND var_id = ' . $var . ' AND num = ' . $data['num'];
                    $db->Execute($query);
                }
                if ($current[$vids['time_to_invoice']]['value'] == '') {
                    //set task as invoiced
                    $query = 'UPDATE ' . DB_TABLE_FIELDS_META . ' fm' . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc' . "\n" .
                             '  ON dc.model_id = ' . $vars['task_activity_id']['value'][$k] . ' AND dc.var_id = fm.id' . "\n" .
                             'SET dc.value = 1, dc.modified = NOW(), dc.modified_by = ' . $this->registry['originalUser']->get('id') . "\n" .
                             'WHERE fm.model = "Document" AND fm.model_type = 26 AND fm.name = "field_billing"';
                    $db->Execute($query);
                }
            }
        }
        // process expenses
        foreach($vars['exp_u_key']['value'] as $k => $v) {
            $v = explode('_', $v);
            if ($v[0] == 'doc') {
                $mod = 'Document';
                $field = 'free_field5';
            } elseif ($v[0] == 'fer') {
                $mod = 'Finance_Expenses_Reason';
                $field = 'free_field1';
            }
            $query = 'UPDATE ' . DB_TABLE_GT2_DETAILS . ' SET' . "\n" .
                     $field . ' = ' . $params['model']->get('id') . ",\n" .
                     '  modified = NOW(),' . "\n" .
                     '  modified_by = ' . $this->registry['originalUser']->get('id') . "\n" .
                     'WHERE id = ' . $v[2];
            $db->Execute($query);
        }

        $result = !$db->HasFailedTrans();
        $this->updateAutomationHistory(array('id' => $params['id']), $params['model'], $result);
        $db->CompleteTrans();
        return $result;
    }

    /**
     * Plugin for copying documents for the next year and deactivating the old ones
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function copyDocumentsForNextYear($params) {
        // Check required settings
        $required_settings = array('customer_type', 'substatus_copied', 'date_fields');
        $existing_settings = array_intersect_key(array_filter($this->settings), array_flip($required_settings));
        if (count($existing_settings) != count($required_settings)) {
            $this->executionErrors[] = 'Required settings are missing: ' . implode(', ', array_diff($required_settings, array_keys($existing_settings)));
            return false;
        }

        // Parse settings
        $this->settings['date_fields'] = preg_split('/\s*,\s*/', $this->settings['date_fields']);
        if (!isset($this->settings['copy_days_before_min_date']) || $this->settings['copy_days_before_min_date'] == '') {
            $this->settings['copy_days_before_min_date'] = '0';
        }

        // Get the date fields
        $query = "
            SELECT name, id
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model = 'Document'
                AND model_type = '{$params['start_model_type']}'
                AND name IN ('" . implode("', '", $this->settings['date_fields']) . "')";
        $date_fields = $this->registry['db']->GetAssoc($query);
        if (count($date_fields) != count($this->settings['date_fields'])) {
            $this->executionErrors[] = 'Required fields are missing: ' . implode(', ', array_diff($this->settings['date_fields'], array_keys($date_fields)));
            return false;
        }

        // Load the language file for documents
        $this->registry['translater']->loadFile(PH_MODULES_DIR . "documents/i18n/{$this->registry->get('lang')}/documents.ini");

        /*
         * Copy the documents
         */
        // Get the ids of the documents which have to be copied
        $query = "
            SELECT d.id
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_CUSTOMERS . " AS c
                ON (c.id = d.customer
                  AND c.`type` = '{$this->settings['customer_type']}'
                  AND c.deleted_by = 0
                  AND c.active = 1)
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (dc.model_id = d.id
                  AND dc.var_id IN (" . implode(',', $date_fields) . ")
                  AND dc.value != '')
              WHERE d.deleted_by = 0
                AND d.active = 1
                AND d.`type` = '{$params['start_model_type']}'
                AND d.status = 'opened'
              GROUP BY d.id
              HAVING DATE_ADD(
                  CONCAT(
                    DATE_FORMAT(MIN(dc.value), '%Y') + 1,
                    '-',
                    DATE_FORMAT(MIN(dc.value), '%m'),
                    '-',
                    IF(DATE_FORMAT(MIN(dc.value), '%m') = '02' AND DATE_FORMAT(MIN(dc.value), '%d') = '29',
                      '28',
                      DATE_FORMAT(MIN(dc.value), '%d'))
                  ),
                  INTERVAL -{$this->settings['copy_days_before_min_date']} DAY
                ) <= CURDATE()";
        $documents_to_copy = $this->registry['db']->GetCol($query);

        // If there are any documents to be copied
        if (!empty($documents_to_copy)) {
            // Get the old documents
            $filters = array(
                'where'    => array('d.id IN (' . implode(',', $documents_to_copy) . ')'),
                'lang'     => $this->registry->get('lang'),
                'sanitize' => true
            );
            $old_documents = Documents::search($this->registry, $filters);
            if (!empty($old_documents)) {
                // Get the substatus which have to be set for the old documents
                $query = "
                    SELECT status, name
                      FROM " . DB_TABLE_DOCUMENTS_STATUSES . "
                      WHERE doc_type = '{$params['start_model_type']}'
                        AND id = '{$this->settings['substatus_copied']}'
                        AND lang = '{$this->registry->get('lang')}'";
                $substatus_copied = $this->registry['db']->GetRow($query);
                if (empty($substatus_copied)) {
                    $this->executionErrors[] = "Can't find substatus definded from settings with id: {$this->settings['substatus_copied']}";
                    return false;
                }

                // Go through each old document
                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                foreach ($old_documents as $old_document) {
                    // Go through each additional var
                    $old_document->unsanitize();
                    $old_document->getVars();
                    $new_document = clone $old_document;
                    $vars = $new_document->get('vars');
                    foreach ($vars as $var_key => $var) {
                        // If the current additional var is one of the date fields which date have to be changed
                        if (in_array($var['name'], $this->settings['date_fields']) && is_array($var['value'])) {
                            // Go through each row for this date field
                            foreach ($var['value'] as $date_key => $date) {
                                // If the date field is not empty
                                if (!empty($date)) {
                                    // Add 1 year to this date
                                    $y = General::strftime('%Y', $date);
                                    $m = General::strftime('%m', $date);
                                    $d = General::strftime('%d', $date);
                                    $y = $y + 1;
                                    if ($m == '02' && $d == '29') {
                                        $d = '28';
                                    }
                                    $vars[$var_key]['value'][$date_key] = "{$y}-{$m}-{$d}";
                                }
                            }
                        }
                    }

                    // Set the changed additional vars back into the object
                    // and remove the id and the number which should re-generated for the new document
                    $new_document->set('vars',     $vars, true);
                    $new_document->set('id',       null, true);
                    $new_document->set('num',      null, true);
                    $new_document->set('full_num', null, true);

                    // Start a transaction for this new document
                    $this->registry['db']->StartTrans();

                    // Try to add the new document
                    if ($new_document->save()) {
                        // Write history
                        $new_document_old = new Document($this->registry);
                        $new_document_old->sanitize();
                        $filters = array(
                            'where'      => array("d.id = {$new_document->get('id')}"),
                            'model_lang' => $new_document->get('model_lang'),
                            'sanitize'   => false
                        );
                        $new_document_new = Documents::searchOne($this->registry, $filters);
                        $new_document_new->getVars();
                        Documents_History::saveData(
                            $this->registry,
                            array(
                                'action_type' => 'add',
                                'old_model'   => $new_document_old,
                                'model'       => $new_document,
                                'new_model'   => $new_document_new,
                            )
                        );

                        // Make a relation between the old and the now documents
                        $query = "
                            INSERT IGNORE INTO " . DB_TABLE_DOCUMENTS_RELATIVES . "
                              SET parent_id = {$new_document->get('id')},
                                parent_model_name = 'Document',
                                link_to = {$old_document->get('id')},
                                link_to_model_name = 'Document',
                                origin = 'cloned',
                                multi_index = 0,
                                group_index = '';";
                        $this->registry['db']->Execute($query);

                        // Set the status of the old document to "Copied"
                        $old_document_old = clone $old_document;
                        $old_document_old->sanitize();
                        $old_document->set('status', $substatus_copied['status'], true);
                        $old_document->set('substatus', "{$substatus_copied['status']}_{$this->settings['substatus_copied']}", true);
                        $old_document->set('substatus_name', $substatus_copied['name'], true);
                        // Try to save the new status
                        if ($old_document->setStatus()) {
                            // Write history
                            $filters = array(
                                'where'      => array("d.id = {$old_document->get('id')}"),
                                'model_lang' => $old_document->get('model_lang'),
                                'sanitize'   => true
                            );
                            $old_document_new = Documents::searchOne($this->registry, $filters);
                            Documents_History::saveData(
                                $this->registry,
                                array(
                                    'action_type' => 'status',
                                    'old_model'   => $old_document_old,
                                    'model'       => $old_document,
                                    'new_model'   => $old_document_new,
                                )
                            );
                        } else {
                            // If failed to set the status, then fail the transaction for the new document
                            $this->registry['db']->FailTrans();
                        }
                    } else {
                        // If failed to add the new document then fail the transaction for the new document
                        $this->registry['db']->FailTrans();
                    }

                    // Complete the transaction
                    $has_failed_trans = $this->registry['db']->HasFailedTrans();
                    $this->registry['db']->CompleteTrans();

                    // Prepare an error message if the transaction has failed
                    if ($has_failed_trans) {
                        $this->executionErrors[] = "Failed to copy document with id: {$old_document->get('id')}";
                    }
                }
                $this->registry->set('get_old_vars', $get_old_vars, true);
            }
        }
        unset($old_documents);

        /*
         * Deactivate the old documents
         */
        // Get the documents which have to be deactivated
        $query = "
            SELECT d.id
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_CUSTOMERS . " AS c
                ON (c.id = d.customer
                  AND c.`type` = '{$this->settings['customer_type']}')
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (dc.model_id = d.id
                  AND dc.var_id IN (" . implode(',', $date_fields) . ")
                  AND dc.value != '')
              WHERE d.deleted_by = 0
                AND d.active = 1
                AND d.`type` = '{$params['start_model_type']}'
                AND d.substatus = '{$this->settings['substatus_copied']}'
              GROUP BY d.id
              HAVING DATE(MAX(dc.value)) < CURDATE()";
        $documents_to_deactivate = $this->registry['db']->GetCol($query);

        // If there are any documents to deactivate
        if (!empty($documents_to_deactivate)) {
            // Try to deactivate the documents
            if (Documents::changeStatus($this->registry, $documents_to_deactivate, 'deactivate')) {
                // Write history
                $filters = array(
                    'where'    => array('d.id IN (' . implode(',', $documents_to_deactivate) . ')'),
                    'lang'     => $this->registry->get('lang'),
                    'sanitize' => true
                );
                $deactivated_documents = Documents::search($this->registry, $filters);
                if (!empty($deactivated_documents)) {
                    foreach ($deactivated_documents as $deactivated_document) {
                        Documents_History::saveData($this->registry, array('model' => $deactivated_document, 'action_type' => 'deactivate'));
                    }
                }
            } else {
                $this->executionErrors[] = 'Failed to deactivate documents: ' . implode(', ', $documents_to_deactivate);
            }
        }

        // Get the result depending on the execution errors
        if (empty($this->executionErrors)) {
            $result = 1;
        } else {
            $result = 0;
        }

        // Writes history
        $this->updateAutomationHistory($params, 0, $result);

        return $result;
    }
}
