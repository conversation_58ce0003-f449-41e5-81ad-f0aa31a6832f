outlooks_module_name = Module
outlooks_action = Action
outlooks_title = Title
outlooks_subtitle = Subtitle

outlooks_section_type = For section / type
outlooks_list_search = For list / search
outlooks_role_user = For roles / users
outlooks_assignments_roles = For roles / For all
outlooks_assignments_users = For users
outlooks_user = User

outlooks_documents = Documents
outlooks_projects = Projects
outlooks_customers = Customers
outlooks_notes = Notes
outlooks_tasks = Tasks
outlooks_nomenclatures_pricelists = Nomenclatures - price lists
outlooks_nomenclatures_categories = Nomenclatures - categories
outlooks_documents_types = Documents' types
outlooks_documents_sections = Documents' sections
outlooks_documents_medias = Documents' medias
outlooks_documents_counters = Documents' counters
outlooks_documents_statuses = Documents' statuses
outlooks_tasks_types = Tasks' types
outlooks_tasks_statuses = Tasks' statuses
outlooks_tasks_counters = Tasks' counters
outlooks_stages = Stages
outlooks_stages_phases = Phases
outlooks_customers_types = Customers' types
outlooks_offices = Offices
outlooks_users = Users
outlooks_roles = Roles
outlooks_departments = Departments
outlooks_groups = Groups
outlooks_emails = E-mails
outlooks_patterns = Patterns
outlooks_patterns_parts = Headers and footers
outlooks_layouts = Layouts
outlooks_outlooks = Outlooks
outlooks_helps = Help texts
outlooks_tags = Tags
outlooks_equal = coincides with

outlook = Outlook
outlook_for = Outlook for
outlook_for_model = For module
outlook_for_type = Type
outlooks = Outlooks
outlooks_module = Module
outlooks_type_about = Type
outlooks_document = Document
outlooks_customer = Client
outlooks_project = Project
outlooks_task = Task
outlooks_shown_fields = Fields displayed
outlooks_document = Document
outlooks_customer = Contractor
outlooks_project = Project
outlooks_section = For section
outlooks_type = For type
outlooks_no_section_type = Without section or type
outlooks_about = Outlook
outlooks_origin = Origin
outlooks_column_width = Width
outlooks_basic_var = Basic Variable
outlooks_additional_var = Additional Variable
outlooks_columns = Columns

outlooks_search_modul = Module

outlooks_add_for = Add outlook
outlooks_add = Add outlook
outlooks_add_documents = Documents
outlooks_add_customers = Contractors
outlooks_add_projects = Projects
outlooks_add_tasks = Tasks

outlooks_add_option_type_about = Outlook for
outlooks_add_option_type_about_legend = Select outlook subject
outlooks_add_option_type = Pattern
outlooks_add_option_type_legend = Pattern type
outlooks_add_search_template = for search
outlooks_add_list_template = for list
outlooks_add_all_template = for all
outlooks_add_role_template = for roles
outlooks_add_user_template = for users

outlooks_edit = Edit outlook
outlooks_edit_field_order = Order of visualization

outlooks_view_outlook = Outlook view

outlooks_translate = Translate outlook

outlooks_module_users = Users
outlooks_module_projects = Projects
outlooks_module_customers = Contractors
outlooks_module_employees = Employees
outlooks_module_offices = Offices
outlooks_module_documents = Documents
outlooks_module_tasks = Tasks
outlooks_module_nomenclatures = Nomenclatures
outlooks_module_events = Events
outlooks_module_finance_incomes_reasons = Finance Incomes Reasons
outlooks_module_finance_expenses_reasons = Finance Expenses Reasons
outlooks_module_finance_warehouses_documents = Finance Warehouses Documents
outlooks_module_finance_annulments = Annulments
outlooks_module_finance_repayment_plans = Finance Repayment Plans
outlooks_module_finance_payments = Finance Payments
outlooks_module_contracts = Contracts

message_outlooks_add_success = Settings changed successfully.
message_outlooks_edit_success = Settings changed successfully.
message_outlook_translate_success = Settings translated successfully.
message_outlooks_resave_success = Outlooks resaved successfully.
message_outlooks_selected_resave_success = Selected outlooks resaved successfully.

error_outlooks_add_exist = Outlook already existing.
error_outlooks_edit_exist = Outlook without assignments already exists.
error_outlooks_add_failed = Error in adding outlook
error_outlooks_edit_failed = Error in editing outlook
error_outlooks_translate_failed = Error translating an outlook!
error_no_type_specified = No model selected for this outlook
error_no_type_about_specified = No pattern type selected
error_no_such_outlook = This record is not available for you!
error_invalid_assignments = The outlook exists for the following
error_outlooks_resave_failed = Error resaving the outlooks

outlooks_field_full_num = Document No.
outlooks_field_custom_num = No. by contractor
outlooks_field_name_docs = About
outlooks_field_type = Type
outlooks_field_customer = Contractor
outlooks_field_group = Group
outlooks_field_status = Status
outlooks_field_stages = Stages
outlooks_field_date = Date
outlooks_field_name = Name
outlooks_field_kind = Kind
outlooks_field_priority = Priority
outlooks_field_manager_name = Manager
outlooks_field_count_documents = Documents
outlooks_field_count_tasks = Tasks
outlooks_field_department = Department
outlooks_field_employee = Employee
outlooks_field_office = Office
outlooks_field_project = Project
outlooks_field_media = Document media
outlooks_field_owner = Assigned to
outlooks_field_responsible = Supervisor
outlooks_field_observer = Inform
outlooks_field_decision = Decision maker
outlooks_field_deadline = Deadline for treatment
outlooks_field_validity_term = Validity term
outlooks_field_code = Code
outlooks_field_contact_person = Contact person
outlooks_field_phone = Phone
outlooks_field_fax = Fax
outlooks_field_gsm = GSM
outlooks_field_web = Web
outlooks_field_email = E-mail
outlooks_field_address = Address
outlooks_field_company_name = Full company name
outlooks_field_in_dds = Statistical Registration No. (VAT ID)
outlooks_field_eik = EIK
outlooks_field_registration_file = Company case No.
outlooks_field_registration_volume = Volume/Page/Register
outlooks_field_registration_number = Court of Registration
outlooks_field_registration_address = Registration address
outlooks_field_position = Position
outlooks_field_mol = Representative
outlooks_field_ucn = Personal ID No.
outlooks_field_identity_num = Identify Card No.
outlooks_field_identity_date = Identify Card issued on
outlooks_field_identity_by = Identify card issued by
outlooks_field_date_start = Start
outlooks_field_date_end = End
outlooks_field_parent_project = Subproject of
outlooks_field_finished_part = % execution
outlooks_field_budget = Budget planned
outlooks_field_work_period = Man-hours
outlooks_field_relatives_children = Source of
outlooks_field_relatives_parent = Created from
outlooks_field_name_code = [Code] Name

outlooks_col_name = Column name
outlooks_position = Position
outlooks_user = User

#Help SECTION for label info
help_outlooks_module = Outlook module
help_outlooks_list_search = If the outlook is for list or for search
help_outlooks_section_type = If the outlook is for type or for section
help_outlooks_role_user = If the outlook is for a role, for a user or for all
help_outlooks_type_about = Type, for which the module refers to
help_outlooks_shown_fields = Pattern fields to be visualized in the order set
help_outlooks_edit_field_order = The columns can be moved by drag and drop to the desired position
help_outlooks_add_for = Select subject of the new outlook
help_outlooks_about = Outlook subject
help_outlooks_user = If the outlook is for a user, please select
