<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=outlooks&amp;outlooks=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="outlooks" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=outlooks" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.title.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.title.link}">{#outlooks_title#|escape}</div></td>
          <td class="t_caption t_border {$sort.module.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.module.link}">{#outlooks_module#|escape}</div></td>
          <td class="t_caption t_border {$sort.section.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.section.link}">{#outlooks_about#|escape}</div></td>
          <td class="t_caption t_border {$sort.model_id.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.model_id.link}">{#outlooks_section_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.assignments.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.assignments.link}">{#outlooks_role_user#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>

      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$outlooks item='outlook'}
      {strip}
      {capture assign='info'}
        <strong>{#outlook_for_model#|escape}:</strong> {$outlook->get('model_name')|escape|default:"&nbsp;"}<br />
        <strong>{#outlooks_section_type#|escape}:</strong> {if $outlook->get('model_id')}{$outlook->get('type_name')|escape|default:"&nbsp;"}{else}-{/if}<br />
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if $outlook->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});"
                   type="checkbox"
                   name='items[]'
                   value="{$outlook->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($outlook->get('id'), $selected_items.ids) ||
                       (@$selected_items.select_all eq 1 && @!in_array($outlook->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
            </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.title.isSorted}">{$outlook->get('title')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.module.isSorted}">{$outlook->get('model_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.section.isSorted}">{$outlook->get('section_type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.model_id.isSorted}">{$outlook->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.assignments.isSorted}">
            <div style="float: left">
              {if $outlook->get('assignments_type') == 'Roles'}
                {#outlooks_add_role_template#|mb_ucfirst}:
              {elseif $outlook->get('assignments_type') == 'Users'}
                {#outlooks_add_user_template#|mb_ucfirst}:
              {else}
                {#outlooks_add_all_template#|mb_ucfirst}
              {/if}
            </div>
            {if $outlook->get('assignments_type') ne 'All'}
            {if $outlook->get('assignments')|@count gt 1}
              <div style="float: right">
                <img src="{$theme->imagesUrl}expand1.png" onclick="toggleOutlookAssignments(this, 'assignments_{$outlook->get('id')}');" alt="" class="pointer" />
              </div>
              <div class="clear"></div>
              <div class="collapsed" id="assignments_{$outlook->get('id')}" style="max-height: 25px; overflow: hidden;">
            {else}
              <div class="clear"></div>
            {/if}
              <ul style="margin: 0px; padding-left: 20px;">
                {foreach from=$outlook->get('assignments') item='assignment'}
                  {if $assignment.assigned_to_name}
                    <li>{$assignment.assigned_to_name|escape|default:"&nbsp;"}</li>
                  {/if}
                {/foreach}
                </ul>
            {if $outlook->get('assignments')|@count gt 1}
              </div>
            {/if}
            {/if}
          </td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$outlook}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="8">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="8"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html session_param=$session_param|default:$pagination.session_param exclude='multiedit, activate, deactivate, restore'}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
