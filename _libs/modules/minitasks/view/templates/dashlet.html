
<form name="minitasks" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=minitasks" method="post">
  <div id="communication_messages_container{$dashlet_id}"></div>
  <table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
    <tr>
      {foreach from=$minitasks_columns item='column'}
      <th class="t_caption t_border {$sort.$column.class}" nowrap="nowrap" style="width: {$column_widths.$column|default:""};{if !in_array($column, $columns)} display: none;{/if}">
        <div onclick="{$sort.$column.link}">
          {capture assign='column_name'}minitasks_{$column}{/capture}
          {$smarty.config.$column_name|escape}
        </div>
      </th>
      {/foreach}
      <td class="t_caption hright" nowrap="nowrap" style="vertical-align: middle; width: 104px;">
        {if $currentUser->checkRights('minitasks', 'add')}
        <span class="nz-icon-button" onclick="insertNewMinitaskRow(this, {$dashlet_id});" title="{#minitasks_add_new#|escape}">add</span>
        {else}
        <span class="nz-icon-button" onclick="alert('{#error_add_notallowed#|escape:'quotes'|escape}')" title="{#minitasks_add_new#|escape}">add</span>
        {/if}
      </td>
    </tr>
    {* row index of add row should be next after that of last mini task from list *}
    {assign var='row_index' value=$minitasks|@count}
    <tr class="t_selected_row_for_edit row_data_{$row_index}" id="row_data_{$row_index}" style="display: none">
      {include file="`$theme->templatesDir`_minitasks_edit.html" minitask=$empty_minitask row_index=$row_index real_module=$module real_controller=$controller real_action=$action autocomplete_filters=$customer_autocomplete_filters}
    </tr>
    {foreach name='i' key='row_index' from=$minitasks item='minitask'}
    {strip}
    {capture assign='info'}
      <strong>{#added#|escape}:</strong> {$minitask->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('added_by_name')|escape}<br />
      <strong>{#modified#|escape}:</strong> {$minitask->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('modified_by_name')|escape}<br />
      <strong>{#status_modified#|escape}:</strong> {$minitask->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$minitask->get('status_modified_by_name')|escape}<br />
      {if $minitask->get('status') neq 'opened'}
        <strong>{#comment#}</strong>: {$minitask->get('comment')|escape|nl2br|default:"&nbsp;"}<br />
      {/if}
    {/capture}
    {/strip}
      <tr class="{cycle values='t_odd,t_even'} row_data_{$row_index} {$minitask->get('severity')}" id="row_data_{$row_index}">
        {foreach from=$minitasks_columns item='column'}
        <td class="t_border {$sort.$column.isSorted}"{if !in_array($column, $columns)} style="display: none;"{/if}>
        {if $column eq 'for_record'}
          {if $minitask->get('model_id')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$minitask->get('module')}&amp;{if $minitask->get('controller')}{$controller_param}={$minitask->get('controller')}&amp;{$minitask->get('controller')}{else}{$minitask->get('module')}{/if}=view&amp;view={$minitask->get('model_id')}{if $minitask->get('archive')}&amp;archive=1{/if}"
               title="{$minitask->get('record_name')|escape|default:'&nbsp;'}">{if $minitask->get('record_num')}[{$minitask->get('record_num')|escape}]{elseif $minitask->get('model') eq 'Contract'}<i>{#minitasks_unfinished_contract#|escape}</i>{/if} {$minitask->get('record_name')|escape|default:"&nbsp;"}</a>
          {else}
            &nbsp;
          {/if}
        {elseif $column eq 'customer'}
          {if $minitask->get('customer')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$minitask->get('customer')}"
                                            title="{#view#|escape}: {$minitask->get('customer_name')|escape}">{$minitask->get('customer_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
        {elseif $column eq 'description'}
          {$minitask->get('description')|escape|nl2br|url2href}
        {elseif $column eq 'deadline'}
          {$minitask->get('deadline')|date_format:#date_short#|default:"&nbsp;"}
        {elseif $column eq 'assigned_to'}
          {$minitask->get('assigned_to_name')|escape|default:"&nbsp;"}
        {else}
          {$minitask->get($all_columns.$column)|escape|default:"&nbsp;"}
        {/if}
        </td>
       {/foreach}
        <td class="hcenter nz-buttons-wrapper" nowrap="nowrap">
          {if $minitask->checkPermissions('edit')}
            <span class="nz-icon-button" id="img_edit_{$row_index}" name="img_edit[{$row_index}]" title="{#edit#}"
                  onclick="manageMinitask(this.form, '{$module}', '{$action}', 'edit', this, {$minitask->get('id')}); return false;"
            >{$theme->getIconForAction('edit')}</span>
          {else}
            <span class="nz-icon-button" id="img_edit_{$row_index}" name="img_edit[{$row_index}]"
                  onclick="alert('{#error_edit_notallowed#|escape:'quotes'|escape}');">{$theme->getIconForAction('edit')}</span>
          {/if}
          {if $minitask->checkPermissions('setstatus')}
            <span class="nz-icon-button img_finished_{$row_index}" id="img_finished_{$row_index}" name="img_finished[{$row_index}]" title="{#minitasks_finish#}"
                  onclick="changeMinitaskStatus(this.className, '{$module}', '{$action}', {$minitask->get('id')}, 'finished'); return false;">{$theme->getIconForAction('communications_minitasks_finish')}</span>
            <span class="nz-icon-button img_failed_{$row_index}" id="img_failed_{$row_index}" name="img_failed[{$row_index}]"  title="{#minitasks_cancel#}"
                  onclick="changeMinitaskStatus(this.className, '{$module}', '{$action}', {$minitask->get('id')}, 'failed'); return false;">{$theme->getIconForAction('communications_minitasks_fail')}</span>
          {else}
          {if $minitask->get('status') != 'failed'}
            <span class="nz-icon-button nz--finished" id="img_finished_{$row_index}" name="img_finished[{$row_index}]" title="{if $minitask->get('status') eq 'finished'}{#minitasks_status_finished#|escape}{else}{#minitasks_finish#}{/if}"
                  onclick="alert('{#error_changestatus_notallowed#|escape:'quotes'|escape}');">{$theme->getIconForAction('communications_minitasks_finish')}</span>
          {/if}
          {if $minitask->get('status') != 'finished'}
            <span class="nz-icon-button nz--failed" id="img_failed_{$row_index}" name="img_failed[{$row_index}]" title="{if $minitask->get('status') eq 'failed'}{#minitasks_status_failed#|escape}{else}{#minitasks_cancel#}{/if}"
                  onclick="alert('{#error_changestatus_notallowed#|escape:'quotes'|escape}');">{$theme->getIconForAction('communications_minitasks_fail')}</span>
          {/if}
          {/if}
          <div class="nz-tooltip-content" id="minitask_info_{$minitask->get('id')}">{$info}</div>
          <span class="nz-tooltip-trigger nz-tooltip-autoinit"
                data-tooltip-position="panel: bottom center at: top center"
                data-tooltip-title="{#system_info#|escape}"
                data-tooltip-element="#minitask_info_{$minitask->get('id')}"
          ><i class="nz-tooltip--icon material-icons">info</i></span>
          {if $minitask->checkPermissions('edit') && $minitask->get('status') eq 'opened'}
          <span id="img_severity_{$row_index}" class="nz-icon-button img_severity_{$row_index}{if $dashlet_id}_{$dashlet_id}{/if} {$minitask->get('severity')}" name="img_severity[{$row_index}]" title="{#minitasks_severity#|escape}"
                onclick="showSeveritySlider(this, '{$action}', {$minitask->get('id')}); return false;">{$theme->getIconForAction('communications_minitasks_severity')}</span>
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="{math equation='count+1' count=$columns|@count}">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
  </table>
  <div class="pagemenu">
    {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=minitasks&amp;minitasks=dashlet&amp;dashlet={$dashlet_id}&amp;page={/capture}
    {capture assign='container'}content_dashlet_{$dashlet_id}{/capture}
    {include file="`$theme->templatesDir`pagination.html"
    found=$pagination.found
    total=$pagination.total
    rpp=$pagination.rpp
    page=$pagination.page
    pages=$pagination.pages
    target=$container
    link=$link
    use_ajax=1
    hide_rpp=1
    hide_stats=1
    }
  </div>
  {include file="`$theme->templatesDir`_severity_legend.html" prefix='minitasks'}
</form>
