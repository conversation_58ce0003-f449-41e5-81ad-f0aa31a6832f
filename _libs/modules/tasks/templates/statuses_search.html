<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=tasks&amp;controller=statuses&amp;statuses=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="tasks_status" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks&amp;controller=statuses" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall" style="width: 15px">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.task_type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.task_type.link}">{#tasks_statuses_task_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#tasks_statuses_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#tasks_statuses_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.sequence.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.sequence.link}">{#tasks_statuses_sequence#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$tasks_statuses item='tasks_status'}
      {strip}
      {capture assign='info'}
        <strong>{#tasks_statuses_name#|escape}:</strong> {$tasks_status->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$tasks_status->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$tasks_status->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$tasks_status->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$tasks_status->get('modified_by_name')|escape}<br />
        {if $tasks_status->isDeleted()}<strong>{#deleted#|escape}:</strong> {$tasks_status->get('deleted')|date_format:#date_mid#|escape}{if $tasks_status->get('deleted_by_name')} {#by#|escape} {$tasks_status->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$tasks_status->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $tasks_status->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$tasks_status->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$tasks_status->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="4" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$tasks_status disabled='edit,delete,view'}
          </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$tasks_status->get('active')} t_inactive{/if}{if $tasks_status->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$tasks_status->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($tasks_status->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($tasks_status->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.task_type.isSorted}">{$tasks_status->get('type_name')|escape|default:"&nbsp;"}</td>
          {capture assign='status_name'}tasks_status_{$tasks_status->get('status')}{/capture}
          <td class="t_border {$sort.status.isSorted}">{$smarty.config.$status_name|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted}">
            {if $tasks_status->get('icon_name')}<img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$tasks_status->get('icon_name')}" alt="" title="{$tasks_status->get('name')|escape}" />{/if}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=statuses&amp;{$action_param}=view&amp;view={$tasks_status->get('id')}">{$tasks_status->get('name')|escape}</a>
          </td>
          <td class="t_border hright {$sort.sequence.isSorted}">{$tasks_status->get('sequence')|escape|default:"&nbsp;"}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$tasks_status}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
