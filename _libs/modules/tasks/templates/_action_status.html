{if $available_action.show_form}
  <form method="{$available_action.options.form_method|default:'get'}" action="{$smarty.server.PHP_SELF}" id="{$available_action.action}_form">
    <input type="hidden" name="{$available_action.module_param}" value="{$available_action.module}" />
    <input type="hidden" name="{$available_action.module}" value="{$available_action.action}" />
    {if $available_action.model_id}
      <input type="hidden" name="{$available_action.action}" value="{$available_action.model_id}" />
    {/if}
    {if $available_action.model_lang}
      <input type="hidden" name="model_lang" value="{$available_action.model_lang}" />
    {/if}
    {if $available_action.name eq 'search' || $available_action.name eq 'filter'}
      <input type="hidden" name="{$available_action.session_param}" value="1" />
      <input type="hidden" name="{$available_action.name}_module" value="{$available_action.module}" />
      <input type="hidden" name="{$available_action.name}_controller" value="{$available_action.controller}" />
      {if $event}
      <input type="hidden" name="event" value="{$event}" />
      {/if}
      {if $form_name}
      <input type="hidden" name="form_name" value="{$form_name}" />
      {/if}
    {/if}
    {assign var='lb_suffix' value='_'}
{/if}

  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0">
          <tr>
            {if !$hide_status_label}
              <td class="labelbox"><a name="error_status"><label for="status"{if $messages->getErrors('status')} class="error"{/if}>{help label='status'}</label></a></td>
              <td>&nbsp;</td>
            {/if}
            <td class="databox" nowrap="nowrap">
              {capture assign='current_status'}{$task->get('status')}{/capture}
              {capture assign='current_substatus'}{$task->get('substatus')}{/capture}
              {assign var='substatuses' value=$task->get('substatuses')}
              {assign var='inactive_statuses' value=$task->get('inactive_statuses')}
              <input type="hidden" name="current_status_base" value="{$current_status}" id="current_status_base" class="current_status_base" />
              <input type="hidden" name="current_substatus_base" value="{$current_substatus}" id="current_substatus_base" class="current_substatus_base" />
              <input type="hidden" name="statuses_unlock" value="{$task->checkPermissions('setstatus_unlock')}" id="statuses_unlock" class="statuses_unlock" />
              <input type="hidden" name="current_selected_status" value="{if $current_substatus}substatus_{$current_substatus}{/if}" id="current_selected_status" class="current_selected_status" />
              <input type="radio" name="status" id="status_planning{$lb_suffix}"{if $inactive_statuses.planning[0]} class="disabled status status_planning{if ($current_status eq 'planning' or !$current_status) && empty($inactive_statuses.planning[$current_substatus])} dimmed"{else}" disabled="disabled"{/if} onclick="return false;"{else} class="status status_planning" onclick="if (validateTaskStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');"{/if} value="planning"{if $task->get('status') eq 'planning' or !$task->get('status')} checked="checked"{/if} />
              <input type="hidden" name="requires_comment_planning" id="requires_comment_planning" class="requires_comment_planning" value="{$task->get('planning_requires_comment')}" />
              <label for="status_planning{$lb_suffix}" class="tasks_status planning">{#tasks_status_planning#|escape}</label><br />
              {if $substatuses.planning}
                <blockquote id="substatus_planning" class="block_quote_status substatus_planning">
                  {foreach from=$substatuses.planning item='substat_properties'}
                    <input type="radio" name="substatus" id="substatus_{$substat_properties.id}{$lb_suffix}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $task->get('substatus') eq $substat_properties.id} checked="checked"{/if}{if $inactive_statuses.planning[$substat_properties.id]} class="substatus_{$substat_properties.id} disabled" disabled="disabled"{else} class="substatus_{$substat_properties.id}"{/if} />
                    {if $substat_properties.icon_name}<img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$substat_properties.icon_name}" alt="" title="" style="width: 14px; height: 14px;" />{/if}<label for="substatus_{$substat_properties.id}{$lb_suffix}" class="tasks_status_substatus">{$substat_properties.name}</label><br />
                    <input type="hidden" name="requires_comment_planning_{$substat_properties.id}" id="requires_comment_planning_{$substat_properties.id}" class="requires_comment_planning_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                  {/foreach}
                </blockquote>
              {/if}
              <input type="radio" name="status" id="status_progress{$lb_suffix}"{if $inactive_statuses.progress[0]} class="disabled status status_progress{if $current_status eq 'progress' && empty($inactive_statuses.progress[$current_substatus])} dimmed"{else}" disabled="disabled"{/if} onclick="return false;"{else} class="status status_progress" onclick="if (validateTaskStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');"{/if} value="progress"{if $task->get('status') eq 'progress'} checked="checked"{/if} />
              <input type="hidden" name="requires_comment_progress" id="requires_comment_progress" class="requires_comment_progress" value="{$task->get('progress_requires_comment')}" />
              <label for="status_progress{$lb_suffix}" class="tasks_status progress">{#tasks_status_progress#|escape}</label><br />
              {if $substatuses.progress}
                <blockquote id="substatus_progress" class="block_quote_status substatus_progress">
                  {foreach from=$substatuses.progress item='substat_properties'}
                    <input type="radio" name="substatus" id="substatus_{$substat_properties.id}{$lb_suffix}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $task->get('substatus') eq $substat_properties.id} checked="checked"{/if}{if $inactive_statuses.progress[$substat_properties.id]} class="substatus_{$substat_properties.id} disabled" disabled="disabled"{else} class="substatus_{$substat_properties.id}"{/if} />
                    {if $substat_properties.icon_name}<img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$substat_properties.icon_name}" alt="" title="" style="width: 14px; height: 14px;" />{/if}<label for="substatus_{$substat_properties.id}{$lb_suffix}" class="tasks_status_substatus">{$substat_properties.name}</label><br />
                    <input type="hidden" name="requires_comment_progress_{$substat_properties.id}" id="requires_comment_progress_{$substat_properties.id}" class="requires_comment_progress_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                  {/foreach}
                </blockquote>
              {/if}
              <input type="radio" name="status" id="status_finished{$lb_suffix}"{if $inactive_statuses.finished[0]} class="disabled status status_finished{if $current_status eq 'finished' && empty($inactive_statuses.finished[$current_substatus])} dimmed"{else}" disabled="disabled"{/if} onclick="return false;"{else} class="status status_finished" onclick="if (validateTaskStatusChange(this)){ldelim}toggleStatuses(this);{rdelim} else alert('{#error_status_change#|escape:'quotes'|escape}');"{/if} value="finished"{if $task->get('status') eq 'finished'} checked="checked"{/if} />
              <input type="hidden" name="requires_comment_finished" id="requires_comment_finished" class="requires_comment_finished" value="{$task->get('finished_requires_comment')}" />
              <label for="status_finished{$lb_suffix}" class="tasks_status finished">{#tasks_status_finished#|escape}</label><br />
              {if $substatuses.finished}
                <blockquote id="substatus_finished" class="block_quote_status substatus_finished">
                  {foreach from=$substatuses.finished item='substat_properties'}
                    <input type="radio" name="substatus" id="substatus_{$substat_properties.id}{$lb_suffix}" value="{$substat_properties.parent_status}_{$substat_properties.id}" onclick="if (! validateSubstatusChange(this, '{$module}')) alert('{#error_substatus_change#|escape:'quotes'|escape}');"{if $task->get('substatus') eq $substat_properties.id} checked="checked"{/if}{if $inactive_statuses.finished[$substat_properties.id]} class="substatus_{$substat_properties.id} disabled" disabled="disabled"{else} class="substatus_{$substat_properties.id}"{/if} />
                    {if $substat_properties.icon_name}<img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$substat_properties.icon_name}" alt="" title="" style="width: 14px; height: 14px;" />{/if}<label for="substatus_{$substat_properties.id}{$lb_suffix}" class="tasks_status_substatus">{$substat_properties.name}</label><br />
                    <input type="hidden" name="requires_comment_finished_{$substat_properties.id}" id="requires_comment_finished_{$substat_properties.id}" class="requires_comment_finished_{$substat_properties.id}" value="{$substat_properties.requires_comment}" />
                  {/foreach}
                </blockquote>
              {/if}
            </td>
          </tr>
        </table>
      </td>
      <td align="right" style="vertical-align: top;">
        {capture assign='current_status_requires_comment_name'}{$task->get('status')}_requires_comment{/capture}
        {capture assign='current_status_requires_comment'}{$task->get($current_status_requires_comment_name)}{/capture}
        {capture assign='current_substatus_requires_comment'}{if $task->get('substatus')}{foreach from=$substatuses.$current_status item="substatus"}{if $substatus.id == $task->get('substatus')}{$substatus.requires_comment}{/if}{/foreach}{else}0{/if}{/capture}
        <table id="available_comment_table" class="available_comment_table" style="visibility: {if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment' || $current_substatus_requires_comment == 'optional_comment'}visible{else}hidden{/if}{else}{if $current_status_requires_comment == 'requires_comment' || $current_status_requires_comment == 'optional_comment'}visible{else}hidden{/if}{/if};">
          <tr>
            <td class="required required_comment" id="required_comment" rowspan="2" style="visibility: {if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment'}visible{else}hidden{/if}{else}{if $current_status_requires_comment == 'requires_comment'}visible{else}hidden{/if}{/if};">{#required#}<input type="hidden" name="requires_comment" id="requires_comment" class="requires_comment" value="{if $current_substatus_requires_comment}{if $current_substatus_requires_comment == 'requires_comment'}1{else}0{/if}{else}{if $current_status_requires_comment == 'requires_comment'}1{else}0{/if}{/if}" /></td>
            <td class="labelbox"><a name="error_comment"><label for="comment{$lb_suffix}"{if $messages->getErrors('comment')} class="error"{/if}>{help label='comment'}</label></a></td>
          </tr>
          <tr>
            <td>
              <textarea class="areabox comment" name="comment" id="comment{$lb_suffix}" style="height: {math equation='(2+x+y+z)*18' x=$substatuses.planning|@count y=$substatuses.progress|@count z=$substatuses.finished|@count}px;"></textarea>
              {if $include_portal_users_option && !$currentUser->get('is_portal')}
                <br />
                {capture assign="is_portal_suffix"}_{uniqid}{/capture}
                <input type="radio" name="is_portal" id="is_portal1{$is_portal_suffix}" value="1" title="{#is_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !isset($available_action.default_portal_comment) || $available_action.default_portal_comment} checked="checked"{/if} /><label for="is_portal1{$is_portal_suffix}">{#is_portal#|escape}</label>
                <input type="radio" name="is_portal" id="is_portal2{$is_portal_suffix}" value="0" title="{#is_not_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if isset($available_action.default_portal_comment) && !$available_action.default_portal_comment} checked="checked"{/if} /><label for="is_portal2{$is_portal_suffix}">{#is_not_portal#|escape}</label>
              {/if}
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="2">
        <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.options.label}" onclick="return checkRequiredComment(this)">{$available_action.options.label}</button>
      </td>
    </tr>
  </table>

{if $available_action.show_form}
  </form>
{/if}
