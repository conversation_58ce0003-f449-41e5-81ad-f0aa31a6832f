tasks_types = Type of tasks
tasks_types_name = Name
tasks_types_name_plural = Name (for list and menu)
tasks_types_status = Status
tasks_types_section = Section
tasks_types_status_active = Active
tasks_types_status_inactive = Inactive
tasks_types_added_by = Added by
tasks_types_modified_by = Modified by
tasks_types_added = Added on
tasks_types_modified = Modified on
tasks_types_add = Add task type data
tasks_types_edit = Edit task type data
tasks_types_translate = Translate task type data
tasks_types_view = View task type data
tasks_types_description = Description
tasks_types_counter = Counter
tasks_types_template_vars = Fields to save in template
tasks_types_planning_requires_comment = Comment on status Planning
tasks_types_progress_requires_comment = Comment on status Progress
tasks_types_finished_requires_comment = Comment on status Finished
tasks_types_validate = Additional required fields
tasks_types_validate_unique = Unique fields
tasks_types_validate_unique_current_year = For current year only
tasks_types_assignment_types = Assignment types
tasks_types_requires_completed_minitasks = Requires completion of mini tasks
tasks_types_related_customers_types = Customers types for AC
tasks_types_default_department = Default department
tasks_types_default_group = Default group

tasks_types_default_user_group = [Default user group]
tasks_types_default_user_department = [Default user department]

tasks_layouts_name = About
tasks_layouts_customer = Contractor
tasks_layouts_status = Status
tasks_layouts_substatus = Substatus
tasks_layouts_project = Project
tasks_layouts_planned_start_date = Planned start
tasks_layouts_planned_finish_date = Planned end
tasks_layouts_planned_time = Time planned (minutes)
tasks_layouts_severity = Priority
tasks_layouts_progress = % of completeness
tasks_layouts_equipment = Equipment
tasks_layouts_task_field = Test field
tasks_layouts_source = Task source
tasks_layouts_description = Description
tasks_layouts_notes = Notes
tasks_layouts_department = Department

message_tasks_types_add_success = Type task data successfully added!
message_tasks_types_edit_success = Type task data successfully edited!
message_tasks_types_translate_success = Type task data successfully translated!

error_tasks_types_edit_failed = Type task data not edited:
error_tasks_types_add_failed = Type task data not added:
error_tasks_types_translate_failed = Type task data not translated:

error_no_such_task = This record is not available for you!
error_no_typename_specified = Please, specify name!
error_no_typename_plural_specified = Please, specify name (for list and menu)!
error_no_type_specified = No type selected!
error_no_counter_specified = Choose a counter!

#Help SECTION for label info 

help_tasks_types_name = 
help_tasks_types_name_plural = 
help_tasks_types_status = 
help_tasks_types_status_active = 
help_tasks_types_status_inactive = 
help_tasks_types_added_by = 
help_tasks_types_modified_by = 
help_tasks_types_counter = 
help_tasks_types_planning_requires_comment = Requires comment when enter in status Planning
help_tasks_types_progress_requires_comment = Requires comment when enter in status Progress
help_tasks_types_finished_requires_comment = Requires comment when enter in status Finished
help_tasks_types_validate = 
help_tasks_types_validate_unique = 
help_tasks_types_validate_unique_current_year = Whether set fields are validated for uniqueness against records added in current year or against all records.
help_tasks_types_assignment_types = 
help_tasks_types_template_vars = Select the fields you wish to be saved when saving a template for a task for this type.
help_tasks_types_requires_completed_minitasks = Requires that all mini tasks for record are completed before it can be finished.
help_tasks_types_related_customers_types = 
