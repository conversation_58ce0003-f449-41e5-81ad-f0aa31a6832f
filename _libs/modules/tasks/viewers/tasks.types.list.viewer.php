<?php

class Tasks_Types_List_Viewer extends Viewer {
    public $template = 'types_list.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('name', 'name', 'description', 'added');

    public function prepare() {
        require_once $this->modelsDir . 'tasks.types.factory.php';

        $filters = Tasks_Types::saveSearchParams($this->registry);
        $filters['where'][] = 'tt.system IS NOT NULL';
        list($tasks_types, $pagination) = Tasks_Types::pagedSearch($this->registry, $filters);

        $this->data['tasks_types'] = $tasks_types;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tasks_types');
        $this->data['title'] = $title;
    }
}

?>
