<?php

/**
 * Tasks_Status model class
 */
Class Tasks_Status extends Model {
    public $modelName = 'Tasks_Status';

    public $width = 16;

    public $height = 16;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_statusname_specified', 'name');
        }
        if ($this->isDefined('task_type') && !$this->get('task_type')) {
            $this->raiseError('error_no_task_type_specified', 'task_type');
        }
        if ($this->isDefined('status') && !$this->get('status')) {
            $this->raiseError('error_no_status_specified', 'status');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //prepare main data from post
        $query12 = 'SELECT MAX( id ) AS new_id FROM ' . DB_TABLE_TASKS_STATUSES . "\n";
        $id = $db->GetOne($query12)+1;

        $set = $this->prepareMainData();
        $set['id']          = sprintf("id=%d", $id);
        $set['added']       = sprintf("added=now()");
        $set['added_by']    = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_TASKS_STATUSES . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new tasks status base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        $this->set('id', $id, true);
        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];
        $set = $this->prepareMainData();

        $insert = $set;
        $insert['id']          = sprintf("id=%d",$this->get('id'));
        $insert['added']       = sprintf("added=now()");
        $insert['added_by']    = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        $query1 = 'INSERT INTO ' . DB_TABLE_TASKS_STATUSES . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $set);

        $db->Execute($query1);

        if ($this->isDefined('active') || $this->isDefined('group') || $this->isDefined('sequence') || $this->isDefined('requires_comment')) {
            $update = array();
            if ($this->isDefined('active')) {
                $update[] = sprintf('`active`=%d', $this->get('active'));
            }
            if ($this->isDefined('group')) {
                $update[] = sprintf('`group`=%d', $this->get('group'));
            }
            if ($this->isDefined('sequence')) {
                $update[] = sprintf('`sequence`=%d', $this->get('sequence'));
            }
            if ($this->isDefined('requires_comment')) {
                $update[] = sprintf('`requires_comment`="%s"', $this->get('requires_comment'));
            }

            //update all the records for the languages
            $query2 = 'UPDATE ' . DB_TABLE_TASKS_STATUSES . "\n" .
                      'SET ' . implode(', ', $update) . "\n" .
                      'WHERE id=' . $this->get('id');

            $db->Execute($query2);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('task_type')) {
            $set['task_type'] = sprintf("task_type=%d", $this->get('task_type'));
        }
        if ($this->isDefined('sequence')) {
            $set['sequence'] = sprintf("sequence=%d", $this->get('sequence'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('name')) {
            $set['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')) {
            $set['description'] = sprintf("description='%s'", $this->get('description'));
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("status='%s'", $this->get('status'));
        }
        if ($this->isDefined('requires_comment')) {
            $set['requires_comment'] = sprintf("requires_comment='%s'", $this->get('requires_comment'));
        }
        if ($this->isDefined('model_lang')) {
            $set['lang'] = sprintf("lang='%s'", $this->get('model_lang'));
        }
        $set['modified'] = 'modified=now()';
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Checks model translations
     *
     * @return bool - array of available languages
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT ct.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_TASKS_STATUSES . ' AS ct' . "\n";

        //where clause
        $sql['where'] = 'WHERE ct.id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY ct.lang' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Checks if image is selected and uploads it to the server
     *
     * @return bool - if the action was successful or not
     */
    public function imageCreate() {
        if (! empty($_FILES['icon_file']['name'])) {
            $tmp_file_name = $_FILES['icon_file']['tmp_name'];
            $file_info = getimagesize($tmp_file_name);
            $file_extension = FilesLib::getImageType($file_info[2]);
            if ($file_extension) {
                $id = $this->get('id');
                if (! is_dir(PH_TASKS_STATUSES_DIR)) {
                    FilesLib::createDir(PH_TASKS_STATUSES_DIR, 0777, true);
                }
                $filename = 'status_' . $id . '.' . $file_extension;
                $restrictions = array('max_width' => $this->width, 'max_height' => $this->height);
                $result_upload = FilesLib::uploadFile($_FILES['icon_file'], PH_TASKS_STATUSES_DIR, $filename, $restrictions);
                if ($result_upload) {
                    $result = $this->updateTableIcon($id, $file_extension);
                    chmod(PH_TASKS_STATUSES_DIR . $filename, 0777);
                    if ($result) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * Updates tasks_statuses table when an icon is uploaded or deleted.
     *
     * @param int $id - the id of the status to be updated
     * @param string $file_extension - the file extension of the uploaded file
     * @param bool $delete_file - whether file is deleted or modified
     * @return bool - result of the operation
     */
    public function updateTableIcon($id, $file_extension='', $delete_file='') {
        if ($delete_file) {
            $file_name = '';
        } else {
            $file_name = 'status_' . $id . '.' . $file_extension;
        }

        $db = $this->registry['db'];
        $db->StartTrans();
        $query = 'UPDATE ' . DB_TABLE_TASKS_STATUSES . "\n" .
                 ' SET icon_name="' . $file_name . '"' . "\n" .
                 ' WHERE id="' . $id . '"';
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        if ($result && $delete_file) {
            $icon_file_delete = PH_TASKS_STATUSES_DIR . $delete_file;
            unlink($icon_file_delete);
        }

        return $result;
    }

}

?>
