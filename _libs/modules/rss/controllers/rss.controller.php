<?php

class Rss_Controller extends Controller {

    /**
     * Class constructor
     * @param mixed $registry
     */
    public function __construct($registry) {

        parent::__construct($registry);

        //ensure that the user has logged in via his RSS client
        if (empty($registry['currentUser'])) {
            Auth::authenticateHTTP($registry);
        }

        if (empty($this->registry['currentUser'])) {
            exit;
        }
    }

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        default:
            $this->_list();
        }
    }

    /**
     * Frontend home page
     */
    public function _list() {
        //everything is in the viewer
        return true;
    }

}

?>
