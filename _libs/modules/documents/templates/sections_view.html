<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='sections_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$documents_section->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_position'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$documents_section->get('position')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$documents_section->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='sections_icon'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $documents_section->get('icon_name')}
              <img src="{$smarty.const.PH_DOCUMENTS_SECTIONS_URL}{$documents_section->get('icon_name')}" alt="" />
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_section}
</div>
