<?php

class Documents_Multiedit_Viewer extends Viewer {
    public $template = 'multiedit.html';

    public function prepare() {
        $this->data['documents'] = $this->registry['documents'];
        $this->data['type_name'] = $this->registry['doctype']->get('name');

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action
                            );
        $this->data['submitLink'] = $this->submitLink;

        if ($this->theme->isModern()) {
            $this->data['dont_wrap_content'] = true;
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('documents_multiedit');
        $this->data['title'] = $title;
    }
}

?>
