<?php

class Documents_Translate_Viewer extends Viewer {
    public $template = 'translate.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        // GET GROUPS
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('d.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Documents::searchOne($this->registry, $filters);

        // get additional required fields
        $fields = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('type'));
        $this->data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('documents_translate'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
