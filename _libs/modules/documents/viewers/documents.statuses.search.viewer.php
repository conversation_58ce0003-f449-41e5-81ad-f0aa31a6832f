<?php

class Documents_Statuses_Search_Viewer extends Viewer {
    public $template = 'statuses_search.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'documents.statuses.factory.php';
        $filters = Documents_Statuses::saveSearchParams($this->registry, array(), 'search_');
        list($documents_statuses, $pagination) = Documents_Statuses::pagedSearch($this->registry, $filters);

        $this->data['documents_statuses'] = $documents_statuses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('documents_statuses');
        $this->data['title'] = $title;
    }
}

?>
