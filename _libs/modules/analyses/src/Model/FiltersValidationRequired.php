<?php

namespace Module\Analyses\Model;

use Exception;

class FiltersValidationRequired extends AbstractFiltersValidation implements FiltersValidationInterface
{
    /**
     * @param array $validationParams
     * @param array $filterParams
     * @return FilterValidationResult
     * @throws Exception
     */
    public function run(array $validationParams, array $filterParams) : FilterValidationResult
    {
        // TODO: Filters values could be an object, so we don't copy the entire values array over and over again.
        $filtersValues = $this->getFiltersValues();
        $i18n = $this->getI18n();

        try {
            $errorLevel = $this->readErrorLevel($validationParams['error_level']??'error');
        } catch (Exception $e) {
            throw new Exception('Reading error level failed for validation: ' . json_encode($validationParams), $e->getCode(), $e);
        }

        // If empty
        if (!array_key_exists('name', $filterParams)
                || !array_key_exists($filterParams['name'], $filtersValues)
                || $filtersValues[$filterParams['name']] === '') {
            $defaultMessageTemplate = $i18n->translate('analyses_filters_validation_fill_required_filter');
            $filterName = $filterParams['name']??'';

            try {
                $messageText = $this->generateValidationMessage($defaultMessageTemplate, $validationParams, $filterParams);
            } catch (Exception $e) {
                throw new Exception("Message failed for validation: " . json_encode($validationParams), $e->getCode(), $e);
            }

            return new FilterValidationResult(false, $errorLevel, $messageText, $filterName);
        }

        return new FilterValidationResult(true);
    }
}
