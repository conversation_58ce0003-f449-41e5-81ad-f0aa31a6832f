<?php

require_once PH_MODULES_DIR . 'soap/controllers/soap.controller.php';

class Custom_Soap_Controller extends Soap_Controller {

    /**
     * Array with permited test actions for the client
     */
    public $actions = array('testGetExaminationResults', 'testPrintExamination');

    /**
     * Actions which will need user's credentials and we cannot use
     * automated system of nZoom
     * @var array
     */
    public $authenticateActions = array('handle'/*, 'getdocs'*/);

    /**
    * WSDL data including login details
    */
    public static $wsdlData = array(
             'trace'          => true,
             'cache_wsdl'     => WSDL_CACHE_NONE,
             'features'       => SOAP_SINGLE_ELEMENT_ARRAYS,
             'authentication' => SOAP_AUTHENTICATION_DIGEST);

    /**
     * Test function for 'nzGetInstallationInfo' function
     */
    public function testGetExaminationResults() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = $this->registry['currentUser']->get('username');
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = $this->registry['currentUser']->get('password');
        }

        $params = new stdClass();
        if ($this->registry['request']->isRequested('RID')) {
            $params->RID = $this->registry['request']->get('RID');
        }
        if ($this->registry['request']->isRequested('UCN')) {
            $params->UCN = $this->registry['request']->get('UCN');
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzGetExaminationResults', $params, self::$wsdlData);
    }

    /**
     * Test function for 'nzSetInstallationInfo' function
     */
    public function testPrintExamination() {

        //check if username is requested
        if ($this->registry['request']->isRequested('username')) {
            self::$wsdlData['login'] = $this->registry['request']->get('username');
        } else {
            self::$wsdlData['login'] = $this->registry['currentUser']->get('username');
        }

        //check if password is requested
        if ($this->registry['request']->isRequested('password')) {
            self::$wsdlData['password'] = $this->registry['request']->get('password');
        } else {
            self::$wsdlData['password'] = $this->registry['currentUser']->get('password');
        }

        $params = new stdClass();
        if ($this->registry['request']->isRequested('sExaminationId')) {
            include_once $this->pluginModelsDir . 'custom.soap.handler.php';
            $params->sExaminationId = General::encrypt($this->registry['request']->get('sExaminationId'), Custom_Soap_Handler::$encryptionKey, 'xtea');
        }

        require_once $this->pluginModelsDir . 'custom.soap.handler.php';
        $this->_test('nzPrintExamination', $params, self::$wsdlData);
    }
}
