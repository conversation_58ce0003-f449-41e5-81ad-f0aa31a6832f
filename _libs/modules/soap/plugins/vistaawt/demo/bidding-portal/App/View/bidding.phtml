<?php
$thumbnailMaxWidthAndHeight =
    '&maxwidth='
    . \BgService\Config::getProperty('pic_thumbnail_width')
    . '&maxheight='
    . \BgService\Config::getProperty('pic_thumbnail_height');
$picMaxWidthAndHeight =
    '&maxwidth='
    . \BgService\Config::getProperty('pic_max_width')
    . '&maxheight='
    . \BgService\Config::getProperty('pic_max_height');

?>

<div class="header row">
    <div class="header-static col-xs-8 text-danger">Продажба активи с тайно наддаване</div>
    <div class="header-dynamic clearfix col-xs-4">
      <div>
        <img src="<?php echo $this->getPublicUrl(); ?>/img/user.png">
        <span class="header-name ">Здравей, <?php echo $this->data->currentUser; ?>!</span>
        <a class="header-exit btn btn-default" href="?action=logout" role="button">ИЗХОД</a>
      </div>
    </div>
</div>

<?php if ( ! $this->data->success) { ?>
<div class="bidding-page-fail alert alert-danger fade in alert-dismissable">
  <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
  <span><?php echo $this->data->reason; ?></span>
</div>
<?php } ?>

<div class="top-buffer">
  <?php if (empty($this->data->nzOffers)) { ?>
  <h3>В момента няма активни оферти!</h3>
  <?php
  } else {
  foreach ($this->data->nzOffers as $index => $offer) {
    $currentUserIsWinner =
      ($offer->outbidUserId == $this->data->currentUserId); ?>
    <div class="row offer-element"
         id="offer_<?php echo $index; ?>"
         data-deadline="<?php echo $offer->deadline; ?>"
         data-offerId="<?php echo $offer->id; ?>"
         data-priceStart="<?php echo $offer->price; ?>"
         data-priceStep="<?php echo $offer->priceStep; ?>"
         data-winnerId="<?php echo $offer->outbidUserId; ?>"
         data-title="<?php echo $offer->title; ?>"
    >
        <div class="col-sm-3 offer-images">
            <?php
            if (!isset($offer->images)) {
                echo '<img class="img-thumbnail img-responsive" alt="No image" src="' . $this->getPublicUrl() . '/img/no-image-small.png">';
            } else {
                $carouselImages = 'carousel-images-' . $offer->id . '-' . $index;?>
              <img
                  data-toggle="modal"
                  data-target="#<?php echo $carouselImages; ?>-modal"
                  class="open-carousel img-thumbnail img-responsive "
                  alt="<?php echo $offer->images[0]->title; ?>"
                  src="<?php echo $offer->images[0]->location . $thumbnailMaxWidthAndHeight; ?>"
              >
            <?php } ?>
        </div>
        <div class="col-sm-6 data">
            <h3 class="offer-title"><?php echo $offer->title; ?></h3>
            <div class="offer-description" >
          <?php echo $offer->description; ?></div><?php
            $dots = count(explode('<br/>', $offer->description));
            if (strlen($offer->description) > 300 || $dots > 4) {
              echo '<a class="expand-description" expand="false">...</a>';
            }
      ?></div>
        <div class="col-sm-3 clearfix offer-other">
          <div class="offer-other-deadline">
            <img src="<?php echo $this->getPublicUrl(); ?>/img/clock.png">
            <span><?php echo $offer->deadline; ?></span>
          </div>
          <div class="offer-other-price">
            <div class="clearfix"><?php
            if ( ! $offer->isExpired && $currentUserIsWinner) { ?>
              <img src="<?php echo $this->getPublicUrl(); ?>/img/check.png" >
            <?php } ?>
            <span><?php echo $offer->price; ?> лв.</span>
            </div>
            <div class="offer-bidding<?php
            if ($offer->isExpired) {
                ?>-expired">Наддаването е приключило!<?php
            } else if ($currentUserIsWinner) {
                ?>-success">Твоето предложение е най-високо!<?php
            } else {
                ?>"><button
                            class="btn-lg btn btn-primary"
                            data-toggle="modal"
                            data-target="#bidding-popup-modal"
                >Наддавам</button><?php
            } ?></div>
          </div>
        </div>
        <?php if (isset($offer->images)) { ?>
          <!-- Modal Galery -->
          <div class="modal fade" id="<?php echo $carouselImages; ?>-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document">
              <div class="modal-content gallery-popup">
                  <div class="modal-body">
                    <div class="carousel-popup">
                      <span class="close-carousel glyphicon glyphicon-remove" data-dismiss="modal"></span>
                      <div id="<?php echo $carouselImages; ?>"
                           class="carousel slide"
                           data-interval="false"
                           data-ride="carousel" >

                        <!-- Indicators -->
                        <ol class="carousel-indicators">
                            <?php
                            foreach ($offer->images as $key => $value) {
                                echo '<li data-target="#'
                                    . $carouselImages
                                    . '" data-slide-to="'
                                    . $key
                                    . '" ';
                                if ($key == 0) {
                                    echo ' class="active" ';
                                }
                                echo '></li>';

                            } ?>
                        </ol>

                        <!-- Wrapper for slides -->
                        <div class="carousel-inner" role="listbox">
                            <?php foreach ($offer->images as $key => $value) { ?>
                              <div class="item <?php if ($key == 0) echo 'active'; ?>">
                                <img
                                        src="<?php echo $value->location . $picMaxWidthAndHeight; ?>"
                                        alt="<?php echo $value->title; ?>">
                                <div class="carousel-caption"><?php echo $value->title; ?></div>
                              </div>
                            <?php } ?>

                          <!-- Controls -->
                          <a class="left carousel-control" href="#<?php echo $carouselImages; ?>" role="button" data-slide="prev">
                            <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                            <span class="sr-only">Previous</span>
                          </a>
                          <a class="right carousel-control" href="#<?php echo $carouselImages; ?>" role="button" data-slide="next">
                            <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                            <span class="sr-only">Next</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
            </div>
          </div>
        <?php } ?>
    </div>
  <?php } ?>
<?php $this->loadPartial('pagination'); ?>
<?php } ?>
</div>

<!-- Modal -->
<div class="modal fade" id="bidding-popup-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content bidding-popup">

    <form method="post" id="new_bidding" action="#">
      <div class="modal-header">
        <div class="clearfix">
          <span class="col-xs-6 title-container"><span class="title"></span></span>
          <span class="col-xs-4 deadline-container">
            <img src="<?php echo $this->getPublicUrl(); ?>/img/clock.png">
            <span class="deadline"></span>
          </span>
        </div>
      </div>
      <div class="modal-body">
        <div class="bidding-reason-fail hidden alert alert-danger fade in alert-dismissable">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        <span></span>
      </div>
        <h1 class="clearfix">
          <span class="current-price"></span>
          <img class="disable-bid bid-decrease" src="<?php echo $this->getPublicUrl(); ?>/img/minus.png">
          <img class="bid-increase" src="<?php echo $this->getPublicUrl(); ?>/img/plus.png">
        </h1>
      </div>
      <div class="modal-footer">
        <span class="price-step-container">Стъпка на наддаване: <span class="price-step"></span> лв.</span>
        <div class="send-buttons clearfix">
          <input class="disable-bid btn btn-primary" type="submit" value="Потвърждавам" name="commit" id="bid-submit"/>
          <button type="button" class="bid-close btn btn-danger" data-dismiss="modal">Откажи</button>
        </div>
        <div class="bidding-loading">
          <img src="<?php echo $this->getPublicUrl(); ?>/img/loading.gif">
        </div>
      </div>
    </form>
    </div>
  </div>
</div>

<div class="max-width row footer">Този портал е за употреба само от служителите на Виста АВТ.</div>

