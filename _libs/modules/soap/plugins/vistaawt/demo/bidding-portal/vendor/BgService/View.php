<?php

namespace BgService;

/**
 * Class View
 * @package BgService
 */
class View
{
    private $content;
    protected $templatePath;
    private $data;
    private $fileName;

    /**
     * View constructor.
     *
     * @param string    $fileName
     * @param \StdClass $data
     */
    public function __construct($fileName = '', \StdClass $data)
    {
        $this->data = $data;
        $this->fileName = $fileName;

        $this->templatePath = Config::getProperty('templatePath')
            . strtolower($fileName)
            . '.phtml';
    }

    /**
     * @throws \Exception
     */
    public function render()
    {
        if (file_exists($this->templatePath)) {
            ob_start();
            include_once $this->templatePath;
            $this->content = ob_get_clean();
        } else {
            throw new \Exception('File template does not exists: ' . $this->templatePath);
        }

        $this->tryLoadMasterLayout();
    }

    /**
     * Loads master layout template
     */
    private function tryLoadMasterLayout()
    {
        $file = Config::getProperty('masterLayout');

        if (file_exists($file)) {
            include_once $file;
        } else {
            echo $this->content;
        }
    }

    /**
     * Loads partial template
     *
     * @param $nameFile
     */
    public function loadPartial($nameFile)
    {
        $file = Config::getProperty('templatePath') . DIRECTORY_SEPARATOR . 'helper' . DIRECTORY_SEPARATOR . $nameFile .'.phtml';

        if (file_exists($file)) {
            include $file;
        }
    }

    /**
     * @return mixed|null
     */
    private function getPublicUrl()
    {
        return Config::getProperty('public_url_noprotocol');
    }
}