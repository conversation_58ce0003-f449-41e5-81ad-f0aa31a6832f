<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=contracts&amp;contracts=filter&amp;{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="contracts" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts" method="post" enctype="multipart/form-data">
      {if $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          {if !$autocomplete_params || $autocomplete_params.select_multiple}
            {include file="`$theme->templatesDir`_select_items.html"
              pages=$pagination.pages
              total=$pagination.total
              session_param=$session_param|default:$pagination.session_param
            }
          {else}
            {assign var='hide_selection_stats' value=true}
          {/if}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.num.link}">{#contracts_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.custom_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.custom_num.link}">{#contracts_custom_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.subtype.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.subtype.link}">{#contracts_subtype#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#contracts_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#contracts_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{#contracts_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{#contracts_department#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#contracts_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#contracts_added#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$contracts item='contract'}
      {strip}
      {capture assign='info'}
        <strong><u>{#contracts_num#|escape}:</u></strong> {if $contract->get('num')}{if $contract->get('num') eq 'system'}{#contracts_system_num#}{else}{$contract->get('num')}{/if}{else}<i>{#contracts_unfinished_contract#}</i>{/if}<br />
        <strong>{#contracts_name#|escape}:</strong> {$contract->get('name')|escape}<br />
        <strong>{#contracts_type#|escape}:</strong> {$contract->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$contract->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$contract->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$contract->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$contract->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$contract->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$contract->get('status_modified_by_name')|escape}<br />
        {if $contract->isDeleted()}<strong>{#deleted#|escape}:</strong> {$contract->get('deleted')|date_format:#date_mid#|escape}{if $contract->get('deleted_by_name')} {#by#|escape} {$contract->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$contract->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $contract->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='contract_status'}
        {if $contract->get('status') eq 'opened'}
          {#help_contracts_status_opened#}
        {elseif $contract->get('status') eq 'locked'}
          {#help_contracts_status_locked#}
        {elseif $contract->get('status') eq 'closed'}
          {#help_contracts_status_closed#}
        {/if}
        {if $contract->get('substatus_name')}
          <br />
          {#help_contracts_substatus#}{$contract->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$contract->get('active')} t_inactive{/if}{if $contract->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$contract->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
            {else}
                <input type="checkbox"
                   onclick="setCheckAllBox(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                           {rdelim});"
                   name='items[]' 
                   value="{$contract->get('id')}"
                   title="{#check_to_include#|escape}" />
           {/if}
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.num.isSorted}">
            {if $contract->get('num')}{if $contract->get('num') eq 'system'}{#contracts_system_num#}{else}{$contract->get('num')}{/if}{else}<i>{#contracts_unfinished_contract#}</i>{/if}
            <div id="rf{$contract->get('id')}" style="display: none">{if $contract->get('num')}{if $contract->get('num') eq 'system'}{#contracts_system_num#}{else}[{$contract->get('num')}]{/if}{else}<i>{#contracts_unfinished_contract#}</i>{/if} {$contract->get('name')|escape|default:"&nbsp;"}</div>
          </td>
          <td class="t_border {$sort.custom_num.isSorted}">{$contract->get('custom_num')|default:"&nbsp;"}</td>
          <td class="t_border {$sort.subtype.isSorted}">{capture assign='subtype_label'}contracts_{$contract->get('subtype')}{/capture}{$smarty.config.$subtype_label}</td>
          <td class="t_border {$sort.name.isSorted}">{$contract->get('name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted}">{$contract->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.customer.isSorted}">{$contract->get('customer_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.department.isSorted}">{$contract->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$contract_status|escape caption=#help_contracts_status#|escape width=250}
          {/capture}
          {if $contract->get('substatus_name')}
            {if $contract->get('icon_name')}
              <img src="{$smarty.const.PH_CONTRACTS_STATUSES_URL}{$contract->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}contracts_{$contract->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$contract->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}contracts_{$contract->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}contracts_status_{$contract->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.added.isSorted}" nowrap="nowrap">{$contract->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$contract exclude='edit,delete,view'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="12">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
      <br />
      <br />
      <table border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td>
          {if $smarty.request.autocomplete_filter}
            {if $autocomplete_params.select_multiple}
              <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
            {/if}
            <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
          {else}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 0);{rdelim}, this, '{#confirm_link_contracts#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_contracts#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 1);{rdelim}, this, '{#confirm_link_contracts#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_contracts#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          {/if}
          </td>
        </tr>
      </table>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
