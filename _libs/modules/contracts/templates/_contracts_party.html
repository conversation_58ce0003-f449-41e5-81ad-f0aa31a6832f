<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="labelbox">{help label=$party.type}</td>
    <td>&nbsp;</td>
    <td colspan="2" class="strong">
        {$party.cname|escape}
        {if $party.prefix eq 'cstm'}
        <input type="hidden" id="{$party.prefix}_is_company" value="{$party.is_company}" />
        <input type="hidden" id='can_add' value="{$currentUser->checkRights('customers_contactpersons', 'add')}" />
        {/if}
    </td>
  </tr>
  <tr>
    <td class="labelbox">{help label='party_address'}</td>
    <td>&nbsp;</td>
    <td colspan="2">{$party.address|escape|nl2br|default:"&nbsp;"}</td>
  </tr>
  <tr>
    <td class="labelbox strong">{help label='party_contacts'}</td>
    <td>&nbsp;</td>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td class="labelbox">
      {help label='party_administrative'}
      {if $party.prefix eq 'cstm'}
      {capture assign='error_administrative'}error_{$party.prefix}_administrative{/capture}
      <a name="{$error_administrative}"></a>
      {/if}
      {capture assign='error_adm_email'}error_{$party.prefix}_adm_email{/capture}
      <a name="{$error_adm_email}"></a>
    </td>
    <td>&nbsp;</td>
    <td colspan="2" style="white-space: nowrap;">
    {if $party.prefix eq 'cstm' && empty($party.administrative.options)}
      {include file=`$theme->templatesDir`input_text.html
               name=`$party.prefix`_administrative
               standalone=true
               required=1
               width=150
               label=#contracts_party_name#
               show_placeholder='help'
               help="[`$smarty.config.input`]"
      }
      {include file=`$theme->templatesDir`input_text.html
               name=`$party.prefix`_adm_email
               standalone=true
               required=1
               width=150
               label=#contracts_party_email#
               show_placeholder='help'
               help="[`$smarty.config.input`]"
      }
      <input type="hidden" name="cstm_administrative_isCustom" id="cstm_administrative_isCustom" value="1" />
      <input type="hidden" name="cstm_adm_email_isCustom" id="cstm_adm_email_isCustom" value="1" />
    {else}
      {if $party.prefix eq 'cstm' && $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add')}
        {assign var=field_type value=combobox}
      {else}
        {assign var=field_type value=dropdown}
      {/if}
      {include file=`$theme->templatesDir`input_`$field_type`.html
               name=`$party.prefix`_administrative
               options=$party.administrative.options
               optgroups=$party.administrative.optgroups
               value=$party.administrative.selected
               onchange='changeContactEmails(this)'
               standalone=true
               width=150
               label=#contracts_party_name#
      }
      <script type="text/javascript">
        {$party.prefix}_administrative_options = {json encode=$party.administrative.options};
      </script>
      <select name="{$party.prefix}_adm_email" id="{$party.prefix}_adm_email" class="selbox{if !$party.administrative.selected || !$party.administrative.email_selected} undefined{/if}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="{#contracts_party_email#|escape}">
        {if $party.administrative.selected || $party.administrative.email_selected}
        <option value="" class="undefined">{if $party.prefix eq 'cstm' && $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add') && $party.administrative.isCustom}[{#select_or_input#|escape}]{else}[{#please_select#|escape}]{/if}</option>
        {/if}
      {if $party.administrative.selected}
        {foreach from=$party.administrative.options item=contact}
          {if $contact.option_value eq $party.administrative.selected}
            {foreach from=$contact.emails item=mail}
              {if $mail}
                <option value="{$mail}"{if $mail eq $party.administrative.email_selected} selected="selected"{/if}>{$mail}</option>
              {/if}
            {/foreach}
          {/if}
        {/foreach}
      {*else}
        {capture assign=e_selected}{$party.prefix}_adm_email{/capture}
        {capture assign=e_selected}{$contract->get($e_selected)}{/capture}
        {foreach from=$party.administrative.options.0.emails item=mail}
          <option value="{$mail}"{if $mail eq $e_selected}selected="selected"{/if}>{$mail}</option>
        {/foreach*}
      {/if}
      </select>
      {if $party.prefix eq 'cstm' && $currentUser->checkRights('customers_contactpersons', 'add') && $party.administrative.isCustom}
        <script type="text/javascript">
          {$party.prefix}_adm_email = new toCombo('{$party.prefix}_adm_email');
        </script>
        <input type="hidden" name="cstm_administrative_isCustom" id="cstm_administrative_isCustom" value="1" />
        <input type="hidden" name="cstm_adm_email_isCustom" id="cstm_adm_email_isCustom" value="1" />
      {/if}
    {/if}
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      {help label='party_cc'}
      {if $party.prefix eq 'cstm'}
      {capture assign='error_administrative_cc'}error_{$party.prefix}_administrative_cc{/capture}
      <a name="{$error_administrative_cc}"></a>
      {/if}
      {capture assign='error_adm_email_cc'}error_{$party.prefix}_adm_email_cc{/capture}
      <a name="{$error_adm_email_cc}"></a>
    </td>
    <td>&nbsp;</td>
    <td style="padding-right: 2px;">
      <table id="{$party.type}_adm_container" cellspacing="0" cellpadding="0" border="0">
        <tr style="display: none;"><td colspan="2"></td></tr>
        {if $party.prefix eq 'self'}
          {assign var='contacts_cc' value=$contract->get('self_adm_cc')}
        {else}
          {assign var='contacts_cc' value=$contract->get('cstm_adm_cc')}
        {/if}
        {foreach from=$contacts_cc item='contact_cc' key='ck' name='ci'}
        <tr id="{$party.type}_adm_container_{$smarty.foreach.ci.iteration}">
          <td style="display: none;">
            <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if $contacts_cc|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('{$party.type}_adm_container','{$smarty.foreach.ci.iteration}'); {rdelim}, this);" />
            <a href="javascript: disableField('{$party.type}_adm_container','{$smarty.foreach.ci.iteration}');">{$smarty.foreach.ci.iteration}</a>
          </td>
          <td nowrap="nowrap" style="padding: 5px 0px;">
          {if $party.prefix eq 'cstm' && empty($party.administrative.options)}
            {include file=`$theme->templatesDir`input_text.html
                     name=`$party.prefix`_administrative_cc
                     index=$smarty.foreach.ci.iteration
                     standalone=true
                     required=1
                     width=150
                     label=#contracts_party_name#
                     show_placeholder='help'
                     help="[`$smarty.config.input`]"
            }
            {include file=`$theme->templatesDir`input_text.html
                     name=`$party.prefix`_adm_email_cc
                     index=$smarty.foreach.ci.iteration
                     standalone=true
                     required=1
                     width=150
                     label=#contracts_party_email#
                     show_placeholder='help'
                     help="[`$smarty.config.input`]"
            }
            <input type="hidden" name="cstm_administrative_cc_isCustom[{$ck}]" id="cstm_administrative_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
            <input type="hidden" name="cstm_adm_email_cc_isCustom[{$ck}]" id="cstm_adm_email_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
          {else}
            {if $party.prefix eq 'cstm' && $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add')}
              {assign var=field_type value=combobox}
            {else}
              {assign var=field_type value=dropdown}
            {/if}
            {include file=`$theme->templatesDir`input_`$field_type`.html
                     name=`$party.prefix`_administrative_cc
                     index=$smarty.foreach.ci.iteration
                     options=$party.administrative.options
                     optgroups=$party.administrative.optgroups
                     value=$contact_cc.customer
                     onchange='changeContactEmails(this)'
                     standalone=true
                     width=150
                     label=#contracts_party_name#
            }
            {capture assign='isCustom_cc'}{if $party.is_company && (!empty($party.administrative.isCustom_cc.$ck) || $contacts_cc|@count eq 1 && !$contact_cc.customer)}1{else}0{/if}{/capture}
            <select name="{$party.prefix}_adm_email_cc[{$ck}]" id="{$party.prefix}_adm_email_cc_{$smarty.foreach.ci.iteration}" class="selbox{if !$contact_cc.customer || !$contact_cc.email} undefined{/if}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="{#contracts_party_email#|escape}">
            {if $contact_cc.customer || $contact_cc.email}
              <option value="" class="undefined">{if $party.prefix eq 'cstm' && $currentUser->checkRights('customers_contactpersons', 'add') && $isCustom_cc}[{#select_or_input#|escape}]{else}[{#please_select#|escape}]{/if}</option>
            {/if}
            {if $contact_cc.customer}
              {foreach from=$party.administrative.options item=contact}
                {if $contact.option_value eq $contact_cc.customer}
                  {foreach from=$contact.emails item=mail}
                    {if $mail}
                      <option value="{$mail}"{if $mail eq $contact_cc.email} selected="selected"{/if}>{$mail}</option>
                    {/if}
                  {/foreach}
                {/if}
              {/foreach}
            {/if}
            </select>
            {if $party.prefix eq 'cstm' && $currentUser->checkRights('customers_contactpersons', 'add') && $isCustom_cc}
              <script type="text/javascript">
                {$party.prefix}_adm_email_cc_{$smarty.foreach.ci.iteration} = new toCombo('{$party.prefix}_adm_email_cc_{$smarty.foreach.ci.iteration}');
              </script>
              <input type="hidden" name="cstm_administrative_cc_isCustom[{$ck}]" id="cstm_administrative_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
              <input type="hidden" name="cstm_adm_email_cc_isCustom[{$ck}]" id="cstm_adm_email_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
            {/if}
          {/if}
          </td>
        </tr>
        {/foreach}
      </table>
    </td>
    <td style="padding: 10px 5px 0 0;" valign="top">
      <div class="t_buttons" style="width: 30px;">
        <div id="{$party.type}_adm_container_plusButton" onclick="addField('{$party.type}_adm_container', false, false, true);" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
        <div id="{$party.type}_adm_container_minusButton"{if $contacts_cc|@count le 1} class="disabled"{/if} onclick="removeField('{$party.type}_adm_container');" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
      </div>
    </td>
  </tr>
  <tr>
    <td colspan="4">&nbsp;</td>
  </tr>
  <tr>
    <td class="labelbox">
      {help label='party_financial'}
      {if $party.prefix eq 'cstm'}
      {capture assign='error_financial'}error_{$party.prefix}_financial{/capture}
      <a name="{$error_financial}"></a>
      {/if}
      {capture assign='error_fin_email'}error_{$party.prefix}_fin_email{/capture}
      <a name="{$error_fin_email}"></a>
    </td>
    <td{if $party.prefix eq 'self'} class="required">{#required#}{else}>&nbsp;{/if}</td>
    <td colspan="2" style="white-space: nowrap;">
    {if $party.prefix eq 'cstm' && empty($party.financial.options)}
      {include file=`$theme->templatesDir`input_text.html
               name=`$party.prefix`_financial
               standalone=true
               required=1
               width=150
               label=#contracts_party_name#
               show_placeholder='help'
               help="[`$smarty.config.input`]"
      }
      {include file=`$theme->templatesDir`input_text.html
               name=`$party.prefix`_fin_email
               standalone=true
               required=1
               width=150
               label=#contracts_party_email#
               show_placeholder='help'
               help="[`$smarty.config.input`]"
      }
      <input type="hidden" name="cstm_financial_isCustom" id="cstm_financial_isCustom" value="1" />
      <input type="hidden" name="cstm_fin_email_isCustom" id="cstm_fin_email_isCustom" value="1" />
    {else}
      {if $party.prefix eq 'cstm' && $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add')}
        {assign var=field_type value=combobox}
      {else}
        {assign var=field_type value=dropdown}
      {/if}
      {capture assign='required'}{if $party.prefix eq 'self'}1{else}0{/if}{/capture}
      {include file=`$theme->templatesDir`input_$field_type.html
               name=`$party.prefix`_financial
               options=$party.financial.options
               optgroups=$party.financial.optgroups
               value=$party.financial.selected
               onchange='changeContactEmails(this)'
               standalone=true
               required=$required
               width=150
               label=#contracts_party_name#
      }
      <script type="text/javascript">
        {$party.prefix}_financial_options = {json encode=$party.financial.options};
      </script>
      {if $party.prefix eq 'self'}
        {if $party.financial.selected}
          {foreach from=$party.financial.options item=contact}
            {if $contact.option_value eq $party.financial.selected}
              <span id="{$party.prefix}_fin_email" title="{#contracts_party_email#|escape}">{$contact.emails.0}</span>
            {/if}
          {/foreach}
        {else}
          <span id="{$party.prefix}_fin_email"></span>
        {/if}
      {else}
        <select name="{$party.prefix}_fin_email" id="{$party.prefix}_fin_email" class="selbox{if !$party.financial.selected || !$party.financial.email_selected} undefined{/if}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="{#contracts_party_email#|escape}">
        {if $party.financial.selected || $party.financial.email_selected}
          <option value="" class="undefined">{if $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add') && $party.financial.isCustom}[{#select_or_input#|escape}]{else}[{#please_select#|escape}]{/if}</option>
        {/if}
        {if $party.financial.selected}
          {foreach from=$party.financial.options item=contact}
            {if $contact.option_value eq $party.financial.selected}
              {foreach from=$contact.emails item=mail}
                {if $mail}
                  <option value="{$mail}"{if $mail eq $party.financial.email_selected} selected="selected"{/if}>{$mail}</option>
                {/if}
              {/foreach}
            {/if}
          {/foreach}
        {*else}
          {foreach from=$party.financial.options.0.emails item=mail}
            <option value="{$mail}"{if $mail eq $e_selected} selected="selected"{/if}>{$mail}</option>
          {/foreach*}
        {/if}
        </select>
        {if $party.prefix eq 'cstm' && $currentUser->checkRights('customers_contactpersons', 'add') && $party.financial.isCustom}
          <script type="text/javascript">
            {$party.prefix}_fin_email = new toCombo('{$party.prefix}_fin_email');
          </script>
          <input type="hidden" name="cstm_financial_isCustom" id="cstm_financial_isCustom" value="1" />
          <input type="hidden" name="cstm_fin_email_isCustom" id="cstm_fin_email_isCustom" value="1" />
        {/if}
      {/if}
    {/if}
    </td>
  </tr>
  <tr>
    {if $party.prefix eq 'cstm'}
    <td class="labelbox">
      {help label='party_cc'}
      <a name="error_cstm_financial_cc"></a>
      <a name="error_cstm_fin_email_cc"></a>
    </td>
    <td>&nbsp;</td>
    <td style="padding-right: 2px;">
      <table id="{$party.type}_fin_container" cellspacing="0" cellpadding="0" border="0">
        <tr style="display: none;"><td colspan="2"></td></tr>
        {assign var='contacts_cc' value=$contract->get('cstm_fin_cc')}
        {foreach from=$contacts_cc item='contact_cc' key='ck' name='ci'}
        <tr id="{$party.type}_fin_container_{$smarty.foreach.ci.iteration}">
          <td style="display: none;">
            <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if $contacts_cc|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('{$party.type}_fin_container','{$smarty.foreach.ci.iteration}'); {rdelim}, this);" />
            <a href="javascript: disableField('{$party.type}_fin_container','{$smarty.foreach.ci.iteration}');">{$smarty.foreach.ci.iteration}</a>
          </td>
          <td nowrap="nowrap" style="padding: 5px 0px;">
          {if empty($party.financial.options)}
            {include file=`$theme->templatesDir`input_text.html
                     name=`$party.prefix`_financial_cc
                     index=$smarty.foreach.ci.iteration
                     standalone=true
                     required=1
                     width=150
                     label=#contracts_party_name#
                     show_placeholder='help'
                     help="[`$smarty.config.input`]"
            }
            {include file=`$theme->templatesDir`input_text.html
                     name=`$party.prefix`_fin_email_cc
                     index=$smarty.foreach.ci.iteration
                     standalone=true
                     required=1
                     width=150
                     label=#contracts_party_email#
                     show_placeholder='help'
                     help="[`$smarty.config.input`]"
            }
            <input type="hidden" name="cstm_financial_cc_isCustom[{$ck}]" id="cstm_financial_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
            <input type="hidden" name="cstm_fin_email_cc_isCustom[{$ck}]" id="cstm_fin_email_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
          {else}
            {if $party.is_company && $currentUser->checkRights('customers_contactpersons', 'add')}
              {assign var=field_type value=combobox}
            {else}
              {assign var=field_type value=dropdown}
            {/if}
            {include file=`$theme->templatesDir`input_$field_type.html
                     name=`$party.prefix`_financial_cc
                     index=$smarty.foreach.ci.iteration
                     options=$party.financial.options
                     optgroups=$party.financial.optgroups
                     value=$contact_cc.customer
                     onchange='changeContactEmails(this)'
                     standalone=true
                     width=150
                     label=#contracts_party_name#
            }
            {capture assign='isCustom_cc'}{if $party.is_company && (!empty($party.financial.isCustom_cc.$ck) || $contacts_cc|@count eq 1 && !$contact_cc.customer)}1{else}0{/if}{/capture}
            <select name="{$party.prefix}_fin_email_cc[{$ck}]" id="{$party.prefix}_fin_email_cc_{$smarty.foreach.ci.iteration}" class="selbox{if !$contact_cc.customer || !$contact_cc.email} undefined{/if}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="{#contracts_party_email#|escape}">
            {if $contact_cc.customer || $contact_cc.email}
              <option value="" class="undefined">{if $currentUser->checkRights('customers_contactpersons', 'add') && $isCustom_cc}[{#select_or_input#|escape}]{else}[{#please_select#|escape}]{/if}</option>
            {/if}
            {if $contact_cc.customer}
              {foreach from=$party.financial.options item=contact}
                {if $contact.option_value eq $contact_cc.customer}
                  {foreach from=$contact.emails item=mail}
                    {if $mail}
                      <option value="{$mail}"{if $mail eq $contact_cc.email} selected="selected"{/if}>{$mail}</option>
                    {/if}
                  {/foreach}
                {/if}
              {/foreach}
            {/if}
            </select>
            {if $currentUser->checkRights('customers_contactpersons', 'add') && $isCustom_cc}
            <script type="text/javascript">
              {$party.prefix}_fin_email_cc_{$smarty.foreach.ci.iteration} = new toCombo('{$party.prefix}_fin_email_cc_{$smarty.foreach.ci.iteration}');
            </script>
            <input type="hidden" name="cstm_financial_cc_isCustom[{$ck}]" id="cstm_financial_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
            <input type="hidden" name="cstm_fin_email_cc_isCustom[{$ck}]" id="cstm_fin_email_cc_isCustom_{$smarty.foreach.ci.iteration}" value="1" />
            {/if}
          {/if}
          </td>
        </tr>
        {/foreach}
      </table>
    </td>
    <td style="padding: 10px 5px 0 0;" valign="top">
      <div class="t_buttons" style="width: 30px;">
        <div id="{$party.type}_fin_container_plusButton" onclick="addField('{$party.type}_fin_container', false, false, true);" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
        <div id="{$party.type}_fin_container_minusButton"{if $contacts_cc|@count le 1} class="disabled"{/if} onclick="removeField('{$party.type}_fin_container');" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
      </div>
    </td>
    {else}
    <td colspan="4"></td>
    {/if}
  </tr>
</table>