<?php

class Contracts_Statuses_Edit_Viewer extends Viewer {
    public $template = 'statuses_edit.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare contracts types
        require_once(PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php');
        $filters = array(
            'sanitize'  => true,
            'where'     => array('cot.id="' . $this->model->get('contract_type') . '"')
        );
        $contract_type = Contracts_Types::searchOne($this->registry,$filters);
        $this->data['type_name'] = $contract_type->get('name');

        $statuses = array('opened' => $this->i18n('contracts_status_opened'),
                          'locked' => $this->i18n('contracts_status_locked'),
                          'closed' => $this->i18n('contracts_status_closed'));
        $this->data['status_name'] = $statuses[$this->model->get('status')];

        //prepare require comment options
        $required_comment_options = array(
            array ( 'label' => $this->i18n('required_statuses_option_without_comment'),
                    'option_value' => 'without_comment'
                   ),
            array ( 'label' => $this->i18n('required_statuses_option_requires_comment'),
                    'option_value' => 'requires_comment'
                   ),
            array ( 'label' => $this->i18n('required_statuses_option_optional_comment'),
                    'option_value' => 'optional_comment'
                   )
        );

        $requiring_comment = array (
            'custom_id' => 'requires_comment',
            'name'      => 'requires_comment',
            'type'      => 'dropdown',
            'value'     => $this->model->get('requires_comment'),
            'required'  => 1,
            'label'     => $this->i18n('contracts_statuses_requires_comment'),
            'help'      => $this->i18n('help_contracts_statuses_requires_comment'),
            'options'   => $required_comment_options
        );
        $this->data['requiring_comment'] = $requiring_comment;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('contracts_statuses_edit');
        $this->data['title'] = $title;
    }
}

?>
