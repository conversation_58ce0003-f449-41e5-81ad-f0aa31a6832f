<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='counters_name'}</td>
          <td class="required">{#required#}</td>
          <td>
            {mb_truncate_overlib text=$customers_counter->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_next_number'}</td>
          <td class="required">{#required#}</td>
          <td>
            {if $customers_counter->get('next_number')}{$customers_counter->get('next_number')}{else}1{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_formula'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$customers_counter->get('formula')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_description'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$customers_counter->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_count_customers'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$customers_counter->get('count_customers')|escape|default:0}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='counters_types_used'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $customers_counter->get('types')}
              {foreach name='i' from=$customers_counter->get('types') item='type_name' key='type_id'}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=types&amp;types=view&amp;view={$type_id}" target="_blank">{$smarty.foreach.i.iteration}. {$type_name}</a><br />
              {/foreach}
            {else}
              <span class="error">{#error_no_types_used#|escape}</span>
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$customers_counter}
</div>
