customers_counters = Vetragspartnerzähler
customers_counters_name = Name
customers_counters_formula = Formel
customers_counters_description = Beschreibung
customers_counters_next_number = Nächste Nummer
customers_counters_count_customers = Anzahl der Vetragspartnern
customers_counters_types_used = Verwendet in Typen
customers_counters_status = Status
customers_counters_status_active = Aktiv
customers_counters_status_inactive = Nicht aktiv
customers_counters_added_by = Hinzugefügt von
customers_counters_modified_by = Geändert von
customers_counters_added = Hinzugefügt am
customers_counters_modified = Geändert am

customers_counters_add = Vetragspartnerzähler hinzufügen
customers_counters_edit = Vetragspartnerzähler bearbeiten
customers_counters_view = Vetragspartnerzähler einsehen
customers_counters_translate = Vetragspartnerzähler übersetzen

message_customers_counters_add_success = Die Daten des Zählers wurden erfolgreich hinzugefügt!
message_customers_counters_edit_success = Die Daten des Zählers wurden erfolgreich bearbeitet!
message_customers_counters_translate_success = Der Zähler wurde erfolgreich übersetzt!

error_customers_counters_edit_failed = Die Daten des Zählers wurden nicht erfolgreich bearbeitet:
error_customers_counters_add_failed = Die Daten des Zählers wurden nicht hinzugefügt:
error_customers_counters_translate_failed = Der Zähler wurde nicht erfolgreich übersetzt:

error_no_such_customer_counter = Dieser Eintrag ist für Sie nicht verfügbar!
error_no_counter_name_specified = Name nicht eingegeben!
error_no_counter_formula_specified = Formel nicht eingegeben!
error_invalid_next_number = Bitte nächste Zählernummer eingeben, die nur aus Ziffern größer als 0 besteht!

error_no_types_used = Kein einziger Vetragspartnertyp wurde verwendet

customers_counters_formula_delimiter = Trennzeichen
customers_counters_empty_delimiter = ohne Trennzeichen
customers_counters_formula_leading_zeroes = Anzahl der führenden Nullen
customers_counters_formula_date_format = Format
customers_counters_formula_date_delimiter = mit Divider

customers_counters_formula_date_format_year = JJJJ
customers_counters_formula_date_format_year_short = JJ
customers_counters_formula_date_format_month = MM
customers_counters_formula_date_format_day = TT

customers_counters_formula_date_format1 = JJJJ
customers_counters_formula_date_format2 = MM/JJJJ
customers_counters_formula_date_format3 = MM/JJ
customers_counters_formula_date_format4 = JJJJ/MM
customers_counters_formula_date_format5 = JJ/MM
customers_counters_formula_date_format6 = TT/MM/JJJJ
customers_counters_formula_date_format7 = TT/MM/JJ
customers_counters_formula_date_format8 = MM/TT/JJJJ
customers_counters_formula_date_format9 = MM/TT/JJ
customers_counters_formula_date_format10 = JJJJ/TT/MM
customers_counters_formula_date_format11 = JJ/TT/MM
customers_counters_formula_date_format12 = JJJJ/MM/TT
customers_counters_formula_date_format13 = JJ/MM/TT
customers_counters_formula_date_format14 = JJ
customers_counters_formula_date_format15 = JJJ/MM

customers_counters_formula_legend = Legende zum Ausfüllen der Zählerformel
customers_counters_formula_prefix = Präfix
customers_counters_formula_num = Nummer der Nomenklatur
customers_counters_formula_user_code = Benutzercode
customers_counters_formula_assigned_code = Supervisorcode
customers_counters_formula_added = Datum der Nomenklatur

customers_counters_formula_prefix_descr = direkt 2-3 Buchstaben ausfüllen, z.B. CLI für Kunde.
customers_counters_formula_num_descr = die laufende Nummer des Vetragspartner eintragen.
customers_counters_formula_assigned_code_descr = füllt den Kode des Supervisor des Vetragspartner. Ist kein Supervisor gewählt, so wird kein Kode eingetragen.
customers_counters_formula_user_code_descr = füllt den Kode des Benutzers aus, der den Vertragsparter erstellt hatte.
customers_counters_formula_added_descr = Datum des Hinzufügens des Vetragspartners.

customers_counters_formula_note = <strong>HINSWEIS:</strong> <strong>Nur 5 Elemente</strong> können zur Zählerformel hinzugefügt werden

help_customers_counters_next_number = Nächste Nummer. Bitte dieses Feld verwenden, um eine Nummer anzugeben, von der an der Zähler zu zählen beginnt.
