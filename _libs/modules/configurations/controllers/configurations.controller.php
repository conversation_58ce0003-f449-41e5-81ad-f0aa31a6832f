<?php

/**
 * Configurations Controller class
 */
class Configurations_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Configuration';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Configurations';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array('edit');

    /**
     * Default action definitions (add and edit)
     */
    public $afterActionDefinitions = array('edit');

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'list':
        case 'edit':
            $this->registry->set('action', 'edit', true);
            $this->action = 'edit';
            $this->_edit();
            break;
        default:
            // if action is anything else besides the default or edit redirect to index page
            // - someone is already editing the config

            // insert the errors again the session because we have double redirects in this case
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect('index');
            break;
        }
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];
        // check if details are submitted via POST
        if ($request->isPost()) {
            // build the model from the POST
            $configuration = Configurations::buildModel($this->registry);

            if ($configuration->save()) {
                // show message: success
                $this->registry['messages']->setMessage($this->i18n('message_configurations_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                // the model was successfully saved
                // set action as completed
                $this->actionCompleted = true;
            } else {
                // show message: failed
                $this->registry['messages']->setError($this->i18n('error_configurations_edit_failed'),'',-1);
                // register the model, with all the posted details
                $this->registry->set('configuration', $configuration);
            }

        } else {
            // If no POST then build new Configuration model
            $configuration = new Configuration($this->registry);
            if (!$this->registry->isRegistered('configuration')) {
                $this->registry->set('configuration', $configuration->sanitize());
            }

            // Check for concurental access to the settings
            //   and lock the model if already used
            $this->checkAccessOwnership($configuration);
        }

        return true;
    }
}

?>
