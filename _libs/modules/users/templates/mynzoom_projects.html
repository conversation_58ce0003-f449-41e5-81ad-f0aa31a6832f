    <input type="hidden" name="layout" id="layout" value="{$layout}" />
    <table cellspacing="0" cellpadding="0" border="0" class="t_table">
      <tr>
        <td colspan="5" class="t_caption3">
          <div class="t_caption2_title">
            <img src="{$theme->imagesUrl}small/info.png" alt="{#system_info#|escape}" {popup text=$layouts.$layout.description caption=#system_info#} />
            {$layouts.$layout.name|escape}
          </div>
        </td>
      </tr>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title">
            {#users_mynzoom_default_assignments#}
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <table cellspacing="0" cellpadding="3" border="0" id="default_assignments" width="100%">
            <tr>
              <td width="20">&nbsp;</td>
              <td class="strong" nowrap="nowrap" width="200">{#users_mynzoom_assignments_type#}</td>
              <td class="strong" nowrap="nowrap">{#users_mynzoom_assignments_participant#}</td>
              <td>
                <img src="{$theme->imagesUrl}plus.png" border="0" class="pointer" alt="{#users_mynzoom_add_new_default_assignment#|escape}" onclick="insertNewDefaultAssignmentRow(this, 'project')" title="{#users_mynzoom_add_new_default_assignment#|escape}" />
              </td>
            </tr>
            {foreach name='assign_opt' from=$default_assignments_options item='assignments_options' key='row'}
              {assign var=row value=$row+1}
              <tr style="vertical-align: top;"{if $assignments_options.colored} class="row_{$assignments_options.colored}"{/if} id="default_assignments_{$row}">
                <td>
                  <input type="checkbox" name="included[]" value="{$row}" id="included_{$row}" class="included_row" title="{#check_to_include#|escape}"{if $assignments_options.included} checked="checked"{/if} />
                </td>
                <td>
                  <select class="selbox" name="project_type_{$row}" id="project_type_{$row}" onfocus="highlight(this)" onblur="unhighlight(this)">
                    {foreach from=$projects_types item='project_type'}
                      <option value="{$project_type->get('id')}"{if $project_type->get('id') eq $assignments_options.type} selected="selected"{/if}>{$project_type->get('name')}</option>
                    {/foreach}
                  </select>
                </td>
                <td style="padding-top: 0px;">
                  <div>
                    <input type="hidden" name="assignments_type_{$row}" id="assignments_type_{$row}" value="{if $assignments_options.assignments_type}{$assignments_options.assignments_type}{else}Departments{/if}" />
                    {* List with users *}
                    <table id="users_assignments_{$row}" border="0" cellpadding="0" cellspacing="0" style="width:650px;{if !$assignments_options.assignments_type || $assignments_options.assignments_type eq 'Departments'} display:none;{/if}">
                      <tr id="users_tabs_{$row}">
                        <td class="m_header_menu t_table" style="width:180px;!important; padding-top: 0px!important; height: 15px!important; background: none!important;">
                          <ul style="padding-top: 0px;">
                            <li>
                              <span><a onclick="$('users_assignments_{$row}').style.display = 'none';
                                                $('departments_assignments_{$row}').style.display = '';
                                                $('assignments_type_{$row}').value = 'Departments';">{#menu_departments#|escape}</a></span>
                            </li>
                            <li>
                              <span class="selected"><a onclick="">{#menu_users#|escape}</a></span>
                            </li>
                          </ul>
                        </td>
                        <td align="right">
                          <span class="pointer" onclick="selectDeselectAllOptions(this, 'project', 1)" style="color: #666666;">{#users_mynzoom_select_all#|escape}</span> |
                          <span class="pointer" onclick="selectDeselectAllOptions(this, 'project', 0)" style="color: #666666;">{#users_mynzoom_diselect_all#|escape}</span>
                        </td>
                      </tr>
                      <tr{if $hidden} style="display: none"{/if}>
                        {* Element Cell *}
                        <td nowrap="nowrap" colspan="2">
                          <div class="scroll_box" style="width:680px!important; height:100px!important;">
                            {foreach from=$users_options item='user_opt'}
                              <input type="checkbox"
                                     name="assign_user_{$row}[]"
                                     id="assign_user_{$user_opt.id}_{$row}"
                                     value="{$user_opt.id}"
                                     title="{$user_opt.name|escape}"
                                     style="margin: 2px;"
                                     {if @in_array($user_opt.id, $assignments_options.users)}checked="checked"{/if}
                              />
                              <label for="assign_user_{$user_opt.id}_{$row}">{$user_opt.name|escape|mb_wordwrap|default:'&nbsp;'}</label>
                              <br />
                            {/foreach}
                          </div>
                        </td>
                      </tr>
                    </table> 

                    {* Tree with departments *} 
                    <table id="departments_assignments_{$row}" border="0" cellpadding="0" cellspacing="0" style="width:650px; {if $assignments_options.assignments_type eq 'Users'}display:none{/if}">  
                      <tr id="departments_tabs_{$row}">
                        <td class="m_header_menu t_table" style="width:180px;!important; padding-top: 0px!important; height: 15px!important; background: none!important;">
                          <ul style="padding-top: 0px;">
                            <li>
                              <span class="selected"><a>{#menu_departments#|escape}</a></span>
                            </li>
                            <li>
                              <span><a onclick="$('departments_assignments_{$row}').style.display='none';
                                                $('users_assignments_{$row}').style.display='';
                                                $('assignments_type_{$row}').value = 'Users';">{#menu_users#|escape}</a></span>
                            </li>
                          </ul>
                        </td>
                        <td align="right">
                          <span class="pointer" onclick="selectDeselectAllOptions(this, 'project', 1)" style="color: #666666;">{#users_mynzoom_select_all#|escape}</span> |
                          <span class="pointer" onclick="selectDeselectAllOptions(this, 'project', 0)" style="color: #666666;">{#users_mynzoom_diselect_all#|escape}</span>
                        </td>
                      </tr>
                      <tr{if $hidden} style="display: none"{/if}>
                        <td nowrap="nowrap" colspan="2">
                          <div id="assign_departments_{$row}" class="scroll_box" style="width:680px!important; height:100px!important;">
                            {foreach from=$departments item='department'}
                              <input type="checkbox"
                                     name="assign_department_{$row}[]"
                                     id="assign_department_{$department.id}_{$row}"
                                     value="{$department.id}"
                                     title="{$department.name|escape}"
                                     style="margin: 2px;"
                                     {if @in_array($department.id, $assignments_options.departments)}checked="checked"{/if}
                              />
                              <label for="assign_department_{$department.id}_{$row}"{if $department.deleted || !$department.active} class="inactive_option" title="{#inactive_option#}"{/if}>{$department.name|escape|default:"&nbsp;"|indent:$department.level:"---"}</label><br />
                            {/foreach}
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
                <td nowrap="nowrap">
                  &nbsp;
                </td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    </table>