<?php /* Smarty version 2.6.33, created on 2025-02-28 17:17:00
         compiled from /var/www/Nzoom-Evolution/_libs/modules/events/view/templates/_event_color_styles.html */ ?>
<style><?php echo ''; ?><?php $_from = $this->_tpl_vars['types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['t'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['t']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['type']):
        $this->_foreach['t']['iteration']++;
?><?php echo ''; ?><?php ob_start(); ?><?php echo 'color_'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('mine_color_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'background_color_'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('mine_background_color_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['calendar_settings'][$this->_tpl_vars['mine_color_name']]): ?><?php echo '#events-list a.nz-chip.nz-types__'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo ':not(.nz--active):link,#events-list a.nz-chip.nz-types__'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo ':not(.nz--active):visited '; ?>{<?php echo '--chip-color: '; ?><?php echo $this->_tpl_vars['calendar_settings'][$this->_tpl_vars['mine_color_name']]; ?><?php echo '!important;--chip-background-color: '; ?><?php echo $this->_tpl_vars['calendar_settings'][$this->_tpl_vars['mine_background_color_name']]; ?><?php echo '!important;'; ?><?php echo ''; ?>}<?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['calendar_settings'][$this->_tpl_vars['mine_background_color_name']]): ?><?php echo ''; ?><?php echo '#events-list .nz-event-ownsership__mine.nz-types__'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo ',#events-list .nz-event-ownsership__mine.nz-types__'; ?><?php echo $this->_tpl_vars['type']->get('id'); ?><?php echo '>td '; ?>{<?php echo '--chip-background-color: '; ?><?php echo $this->_tpl_vars['calendar_settings'][$this->_tpl_vars['mine_background_color_name']]; ?><?php echo '!important;'; ?><?php echo ''; ?>}<?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>
</style>