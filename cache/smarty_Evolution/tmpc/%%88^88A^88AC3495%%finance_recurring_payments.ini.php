<?php $_config_vars = array (
  'finance_recurring_payment_audit' => 'recurring payment',
  'finance_recurring_payments' => 'Recurring payments',
  'finance_recurring_payments_name' => 'About',
  'finance_recurring_payments_reason_type' => 'Type of finance incomes/expenses reason',
  'finance_recurring_payments_type' => 'Type',
  'finance_recurring_payments_start_date' => 'Start date',
  'finance_recurring_payments_end_date' => 'End date',
  'finance_recurring_payments_customer' => 'Contractor',
  'finance_recurring_payments_project' => 'Project',
  'finance_recurring_payments_contract' => 'Contract',
  'finance_recurring_payments_offset' => 'Repeat offset',
  'finance_recurring_payments_payment_type' => 'Payment type',
  'finance_recurring_payments_container_id' => 'Cashbox/Bank account',
  'finance_recurring_payments_employee' => 'Employee',
  'finance_recurring_payments_reason_editable' => 'Reason can be edited',
  'finance_recurring_payments_add_invoice' => 'Invoice will be issued',
  'finance_recurring_payments_send_to_customer' => 'Automatically send to contractor',
  'finance_recurring_payments_remind_before' => 'Days before payment to remind',
  'finance_recurring_payments_company' => 'Company',
  'finance_recurring_payments_office' => 'Office',
  'finance_recurring_payments_company_data' => 'Cashbox/Bank account',
  'finance_recurring_payments_currency' => 'Currency',
  'finance_recurring_payments_description' => 'Description',
  'finance_recurring_payments_cashbox' => 'Cashbox',
  'finance_recurring_payments_bank_account' => 'Bank account',
  'finance_recurring_payments_recurrence_type' => 'Recurrence',
  'finance_recurring_payments_payment_days' => 'Payment days',
  'finance_recurring_payments_pattern' => 'Pattern',
  'finance_recurring_payments_send_file' => 'File',
  'finance_recurring_payments_add' => 'Add recurring payment',
  'finance_recurring_payments_edit' => 'Edit recurring payment',
  'finance_recurring_payments_view' => 'View recurring payment',
  'finance_recurring_payments_translate' => 'Translate recurring payment',
  'finance_recurring_payments_history' => 'Recurring payment history',
  'finance_recurring_payments_log_add' => '%s adds %s (amount: %s)',
  'finance_recurring_payments_log_edit' => '%s edits %s (amount: %s)',
  'finance_recurring_payments_log_translate' => '%s translates %s (amount: %s)',
  'finance_recurring_payments_logtype_add' => 'Add',
  'finance_recurring_payments_logtype_edit' => 'Edit',
  'finance_recurring_payments_logtype_translate' => 'Translate',
  'message_finance_recurring_payments_add_success' => 'Recurring payment was added successfully',
  'message_finance_recurring_payments_edit_success' => 'Recurring payment was edited successfully',
  'message_finance_recurring_payments_translate_success' => 'Recurring payment was translated successfully',
  'error_finance_recurring_payments_add_failed' => 'Error while adding recurring payment',
  'error_finance_recurring_payments_edit_failed' => 'Error while editing recurring payment',
  'error_finance_recurring_payments_translate_failed' => 'Error while translating recurring payment',
  'error_no_such_finance_recurring_payment' => 'This record is not available for you!',
  'error_no_name_specified' => 'No name specified',
  'error_finance_recurring_payments_no_container_id' => 'Please, select cashbox/bank account!',
); ?>