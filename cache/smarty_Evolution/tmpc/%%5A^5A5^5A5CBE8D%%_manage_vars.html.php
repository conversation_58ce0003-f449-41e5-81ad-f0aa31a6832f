<?php /* Smarty version 2.6.33, created on 2024-08-13 18:34:03
         compiled from /var/www/Nzoom-Evolution/_libs/modules/customers/view/templates/_manage_vars.html */ ?>
            <?php $this->assign('layout_id', $this->_tpl_vars['layout']['id']); ?>
            <?php $this->assign('vars', $this->_tpl_vars['layouts_vars'][$this->_tpl_vars['layout_id']]); ?>
            <?php if ($this->_tpl_vars['layout']['id']): ?>
            <tr<?php if ($this->_tpl_vars['layout']['view']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?> id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_box"<?php if ($this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
              <td class="nopadding" colspan="3">
                <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <?php endif; ?>
            <?php $_from = $this->_tpl_vars['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
              <?php if ($this->_tpl_vars['var']['type']): ?>
                <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

                <?php if ($this->_tpl_vars['layout']['edit']): ?>
                                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'value' => $this->_tpl_vars['var']['value'],'value_id' => $this->_tpl_vars['var']['value_id'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'sequences' => $this->_tpl_vars['var']['sequences'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => $this->_tpl_vars['var']['source'],'onchange' => $this->_tpl_vars['var']['onchange'],'map_params' => $this->_tpl_vars['var']['map_params'],'width' => $this->_tpl_vars['var']['width'],'hidden' => $this->_tpl_vars['var']['hidden'],'really_required' => $this->_tpl_vars['var']['required'],'required' => $this->_tpl_vars['var']['required'],'disabled' => $this->_tpl_vars['var']['disabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'deleteid' => $this->_tpl_vars['var']['deleteid'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php else: ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "view_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php endif; ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <?php if ($this->_tpl_vars['layout']['id']): ?>
                </table>
              </td>
            </tr>
            <?php endif; ?>