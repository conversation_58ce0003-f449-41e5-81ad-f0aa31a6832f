<?php /* Smarty version 2.6.33, created on 2024-07-05 11:18:42
         compiled from /var/www/Nzoom-Evolution/_libs/modules/documents/templates/_types_transform_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/_types_transform_list.html', 4, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/_types_transform_list.html', 14, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/_types_transform_list.html', 12, false),)), $this); ?>
<h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
list.png" width="16" height="16" border="0" alt="<?php echo $this->_tpl_vars['title']; ?>
" /> <?php echo $this->_config[0]['vars']['transformations']; ?>
</h1>
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['transform'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_source<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_transform_type<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['kind'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption"          nowrap="nowrap"><div class="t_caption_title"><?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_transform_method<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
<?php $_from = $this->_tpl_vars['transformations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['varname'] => $this->_tpl_vars['transformation']):
        $this->_foreach['i']['iteration']++;
?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['transformation']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['transformation']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
    <td class="t_border hright"><?php echo $this->_foreach['i']['iteration']; ?>
</td>
    <td class="t_border       "><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['transformation']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
    <td class="t_border <?php if (stripos ( $this->_tpl_vars['module'] , $this->_tpl_vars['transformation']->get('source_model') ) === 0 && $this->_tpl_vars['transformation']->get('current_type') == $this->_tpl_vars['transformation']->get('source_type')): ?>databox2<?php endif; ?>"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['transformation']->get('source_names'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
    <td class="t_border <?php if (stripos ( $this->_tpl_vars['module'] , $this->_tpl_vars['transformation']->get('destination_model') ) === 0 && $this->_tpl_vars['transformation']->get('current_type') == $this->_tpl_vars['transformation']->get('destination_type')): ?>databox2<?php else: ?><?php endif; ?>"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['transformation']->get('destination_names'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
    <td class="t_border legend"><?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_transform_<?php echo $this->_tpl_vars['transformation']->get('transform_type'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('transform_type', ob_get_contents());ob_end_clean(); ?><?php echo $this->_config[0]['vars'][$this->_tpl_vars['transform_type']]; ?>
</td>
    <td class="legend         "><?php echo $this->_tpl_vars['transformation']->get('method'); ?>
</td>
  </tr>
<?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="6"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endif; unset($_from); ?>
  <tr>
    <td class="t_footer" colspan="6"></td>
  </tr>
</table>