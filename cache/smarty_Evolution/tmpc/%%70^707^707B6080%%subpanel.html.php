<?php /* Smarty version 2.6.33, created on 2024-10-31 14:26:09
         compiled from /var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 11, false),array('function', 'counter', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 18, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 39, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 13, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 14, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/nomenclatures/view/templates/subpanel.html', 25, false),)), $this); ?>
<?php ob_start(); ?><?php if (preg_match ( '#trademark_nomenclature$#' , $this->_tpl_vars['session_param'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('trademark_nomenclature', ob_get_contents());ob_end_clean(); ?>
<?php if ($this->_tpl_vars['trademark_nomenclature']): ?>
<div id="messages_container_<?php echo $this->_tpl_vars['session_param']; ?>
">
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layout/messages_lists.html', 'smarty_include_vars' => array('messages' => $this->_tpl_vars['messages'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>
<?php endif; ?>
<table border="0" cellpadding="0" cellspacing="0" class="" width="100%">
  <tr>
    <?php if ($this->_tpl_vars['trademark_nomenclature']): ?>
    <th class=" " width="15">&nbsp;</th>
    <th class=" " width="15"><?php echo smarty_function_help(array('text_content' => $this->_config[0]['vars']['main_trademark']), $this);?>
</th>
    <?php endif; ?>
    <th class="r" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
    <th class=" <?php echo $this->_tpl_vars['sort']['code']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['code']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['code'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_code']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_code'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
    <th class=" <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
    <th class="" width="80">&nbsp;</th>
  </tr>
<?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

<?php $_from = $this->_tpl_vars['nomenclatures']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['nomenclature']):
        $this->_foreach['i']['iteration']++;
?>
<?php echo ''; ?><?php ob_start(); ?><?php echo '<strong><u>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['code'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_code']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_code'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</u></strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['nomenclatures_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['nomenclatures_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['nomenclature']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['nomenclature']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['nomenclature']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['nomenclature']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

<?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['nomenclature'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['nomenclature']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['nomenclature']->get('deleted_by')): ?> t_deleted<?php endif; ?><?php if ($this->_tpl_vars['trademark_nomenclature'] && ! $this->_tpl_vars['nomenclature']->get('parent_id')): ?> attention<?php endif; ?>">
    <?php if ($this->_tpl_vars['trademark_nomenclature']): ?>
    <td class="t_border">
      <input type="image" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" onclick="return confirmAction('delete_row', function() { updateTrademarks(null, {action: 'delete', nomenclature: '<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
'}); }, this);" />
    </td>
    <td class="t_border">
      <input onclick="updateTrademarks(null, {action: 'default', nomenclature: '<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
', the_element: this});"
             type="checkbox"
             name='items[]'
             value="<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
"
             title="<?php echo $this->_config[0]['vars']['check_to_set_default']; ?>
"
             <?php if ($this->_tpl_vars['nomenclature']->get('is_default')): ?>checked="checked"<?php endif; ?> />
    </td>
    <?php endif; ?>
    <td class="t_border hright" nowrap="nowrap">
      <?php if ($this->_tpl_vars['nomenclature']->get('files_count')): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=attachments&amp;attachments=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>>
          <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
               onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
)"
               onmouseout="mclosetime()" />
        </a>
      <?php endif; ?>
      <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

    </td>
    <td class="t_border <?php echo $this->_tpl_vars['sort']['code']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a></td>
    <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a></td>
    <td class="hcenter" nowrap="nowrap">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['nomenclature'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
<?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="6"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endif; unset($_from); ?>
  <tr>
    <td class="t_footer" colspan="6"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=subpanel&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('search_link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['search_link'],'use_ajax' => $this->_tpl_vars['use_ajax'],'hide_selection_stats' => true,'session_param' => $this->_tpl_vars['session_param'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>