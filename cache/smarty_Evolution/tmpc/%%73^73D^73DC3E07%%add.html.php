<?php /* Smarty version 2.6.33, created on 2025-03-12 18:37:44
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 6, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 35, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 90, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 235, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 389, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 389, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 417, false),array('modifier', 'replace', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 434, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 74, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/add.html', 80, false),)), $this); ?>
<?php if (! $_GET['reload']): ?>
<div class="nz-page-wrapper<?php if (! empty ( $this->_tpl_vars['side_panels'] ) && count ( $this->_tpl_vars['side_panels'] )): ?> nz--has-side-panel<?php endif; ?>">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">

    <?php if ($this->_tpl_vars['action'] != 'ajax_add'): ?>
    <div class="nz-page-title"><h1 id="taksMainTitle"><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
      <div class="nz-page-title-tools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['general'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['general'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
      <div class="nz-page-title-sidetools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['quick'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['quick'],'onlyIcons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
    </div>
    <div class="nz-page-actions">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['context'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
  <?php else: ?>
    <div id="lightbox_messages_container" class="collapsible" style="display: none;"></div>
    <?php if ($this->_tpl_vars['assignments_settings']): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/assignments/templates/_assignments_configurator_panel.html", 'smarty_include_vars' => array('config_templates' => $this->_tpl_vars['assignments_settings']['config_templates'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <script type="text/javascript" src="<?php echo @PH_MODULES_URL; ?>
assignments/javascript/assignments.js?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" defer="defer"></script>
    <?php endif; ?>
  <?php endif; ?>

  <div id="form_container" class="nz-page-content main_panel_container">
<?php endif; ?>

    <form name="tasks" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
      <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['task']->get('id'); ?>
" />
      <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
      <input type="hidden" name="create_from_document" id="create_from_document" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('create_from_document'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
      <input type="hidden" name="create_from_customer" id="create_from_customer" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('create_from_customer'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
      <?php if ($this->_tpl_vars['task']->get('event_id')): ?>
        <input type="hidden" name="event_id" id="event_id" value="<?php echo $this->_tpl_vars['task']->get('event_id'); ?>
" />
      <?php endif; ?>
      <?php if ($this->_tpl_vars['action'] == 'ajax_add' && $this->_tpl_vars['assignments_settings']): ?>
        <input type="hidden" name="model_name" id="model_name" value="<?php echo $this->_tpl_vars['task']->modelName; ?>
" />
        <input type="hidden" name="model_type" id="model_type" value="<?php echo $this->_tpl_vars['task']->get('type'); ?>
" />
      <?php endif; ?>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <?php if ($this->_tpl_vars['action'] == 'ajax_add'): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layouts_index.html', 'smarty_include_vars' => array('display' => 'abs_div')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <script type="text/javascript" defer="defer">positionAbsoluteDivs();</script>
              <?php endif; ?>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['task']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

                <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" onclick="toggleViewLayouts(this)" id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch">
                      <a name="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>

                <?php if ($this->_tpl_vars['lkey'] == 'configurator'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td colspan="3">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_tasks_configurator_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                      <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] && $this->_tpl_vars['action'] != 'ajax_add'): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['action'] == 'ajax_add' && count($this->_tpl_vars['tasks_types']) > 1): ?>
                      <select class="selbox" name="type" id="type" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="reloadTask(this);">
                      <?php $_from = $this->_tpl_vars['tasks_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['task_type']):
?>
                        <option value="<?php echo $this->_tpl_vars['task_type']->get('id'); ?>
"<?php if ($this->_tpl_vars['task_type']->get('id') == $this->_tpl_vars['task']->get('type')): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task_type']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
                      <?php endforeach; endif; unset($_from); ?>
                      </select>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['task']->get('type'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['task']->get('customer'),'value_code' => $this->_tpl_vars['task']->get('customer_code'),'value_name' => $this->_tpl_vars['task']->get('customer_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <span id="branch_container" style="display: <?php if ($this->_tpl_vars['task']->get('customer_is_company')): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('tasks_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('help_tasks_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox<?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?> missing_records<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('tasks_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                          <?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('empty_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <?php $_from = $this->_tpl_vars['customer_branches']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['customer_branch']):
?>
                              <?php if (( ! $this->_tpl_vars['customer_branch']->isDeleted() && $this->_tpl_vars['customer_branch']->isActivated() ) || $this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['task']->get('branch')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer_branch']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['task']->get('branch')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['customer_branch']->isDeleted() || ! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['customer_branch']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('tasks_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="branch">
                        <?php if ($this->_tpl_vars['task']->get('branch')): ?>
                          <span<?php if (! $this->_tpl_vars['task']->get('branch_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="branch" id="branch" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('branch'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                    <span id="contact_person_container" style="display: <?php if ($this->_tpl_vars['task']->get('customer_is_company') && ( $this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['currentUser']->get('is_portal') || $this->_tpl_vars['task']->get('contact_person') )): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('help_tasks_contact_person'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="contact_person" id="contact_person" class="selbox<?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?> missing_records<?php elseif (! $this->_tpl_vars['task']->get('contact_person')): ?> undefined<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                          <?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['empty_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <option value="" class="undefined"<?php if (! $this->_tpl_vars['task']->get('contact_person')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                            <?php $_from = $this->_tpl_vars['contact_persons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact_person']):
?>
                              <?php if (( ! $this->_tpl_vars['contact_person']->isDeleted() && $this->_tpl_vars['contact_person']->isActivated() ) || $this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['task']->get('contact_person')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contact_person']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['task']->get('contact_person')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['contact_person']->isDeleted() || ! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if ($this->_tpl_vars['contact_person']->get('lastname')): ?> <?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('lastname'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="contact_person">
                        <?php if ($this->_tpl_vars['task']->get('contact_person')): ?>
                          <span<?php if (! $this->_tpl_vars['task']->get('contact_person_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="contact_person" id="contact_person" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('contact_person'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['task']->get('trademark'),'value_name' => $this->_tpl_vars['task']->get('trademark_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if ($this->_tpl_vars['project_required'] || in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['task']->get('project'),'value_code' => $this->_tpl_vars['task']->get('project_code'),'value_name' => $this->_tpl_vars['task']->get('project_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                      <span class="help" <?php echo smarty_function_help(array('label' => 'phase','popup_only' => '1'), $this);?>
>&nbsp;</span>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'phase','options' => $this->_tpl_vars['phases'],'no_select_records_label' => $this->_config[0]['vars']['project_phase'],'first_option_label' => $this->_config[0]['vars']['project_phase'],'width' => 100,'value' => $this->_tpl_vars['task']->get('phase'),'label' => $this->_config[0]['vars']['tasks_phase'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php if ($this->_tpl_vars['task']->get('phase')): ?> <span class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_phase']), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('phase_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                      <input type="hidden" name="phase" id="phase" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('phase'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'planned_start_date'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_planned_start_date"><label for="planned_start_date"<?php if ($this->_tpl_vars['messages']->getErrors('planned_start_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['task']->isDefined('planned_start_date')): ?>
                      <?php $this->assign('planned_start_date', $this->_tpl_vars['task']->get('planned_start_date')); ?>
                    <?php else: ?>
                      <?php $this->assign('planned_start_date', ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))); ?>
                    <?php endif; ?>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'planned_start_date','width' => 200,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['planned_start_date'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('planned_start_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                      <input type="hidden" name="planned_start_date" id="planned_start_date" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['planned_start_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'planned_finish_date'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_planned_finish_date"><label for="planned_finish_date"<?php if ($this->_tpl_vars['messages']->getErrors('planned_finish_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'width' => 200,'name' => 'planned_finish_date','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['task']->get('planned_finish_date'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('planned_finish_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                      <input type="hidden" name="planned_finish_date" id="planned_finish_date" value="<?php echo $this->_tpl_vars['task']->get('planned_finish_date'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'planned_time'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_planned_time"><label for="planned_time"<?php if ($this->_tpl_vars['messages']->getErrors('planned_time')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox short hright" name="planned_time" id="planned_time" value="<?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['task']->get('planned_time'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onkeydown="return changeKey(this, event, insertOnlyDigits)" /> <?php echo $this->_config[0]['vars']['minutes']; ?>

                    <?php else: ?>
                      <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['task']->get('planned_time'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo $this->_config[0]['vars']['minutes']; ?>

                      <input type="hidden" name="planned_time" id="planned_time" value="<?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['task']->get('planned_time'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'timesheet_time'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_timesheet_time"><label for="timesheet_time"<?php if ($this->_tpl_vars['messages']->getErrors('timesheet_time')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('timesheet_time_formatted'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php if ($this->_tpl_vars['task']->get('timesheet_time') > 0): ?>
                      (<?php echo $this->_config[0]['vars']['total']; ?>
: <?php echo $this->_tpl_vars['task']->get('timesheet_time'); ?>
 <?php echo $this->_config[0]['vars']['minutes']; ?>
)
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'severity'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_severity"><label for="severity"<?php if ($this->_tpl_vars['messages']->getErrors('severity')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select class="selbox" name="severity" id="severity" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                        <?php $_from = $this->_tpl_vars['severitys']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['item']):
?>
                          <option value="<?php echo $this->_tpl_vars['item']['option_value']; ?>
"<?php if ($this->_tpl_vars['item']['option_value'] == $this->_tpl_vars['task']->get('severity')): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['item']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                        <?php endforeach; endif; unset($_from); ?>
                      </select>
                    <?php else: ?>
                      <?php $_from = $this->_tpl_vars['severitys']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['item']):
?>
                        <?php if ($this->_tpl_vars['item']['option_value'] == $this->_tpl_vars['task']->get('severity')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                      <input type="hidden" name="severity" id="severity" value="<?php echo $this->_tpl_vars['task']->get('severity'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'progress'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_progress"><label for="progress"<?php if ($this->_tpl_vars['messages']->getErrors('progress')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select name="progress" id="progress" class="selbox short" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                        <?php unset($this->_sections['i']);
$this->_sections['i']['name'] = 'i';
$this->_sections['i']['loop'] = is_array($_loop=101) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['i']['step'] = ((int)10) == 0 ? 1 : (int)10;
$this->_sections['i']['show'] = true;
$this->_sections['i']['max'] = $this->_sections['i']['loop'];
$this->_sections['i']['start'] = $this->_sections['i']['step'] > 0 ? 0 : $this->_sections['i']['loop']-1;
if ($this->_sections['i']['show']) {
    $this->_sections['i']['total'] = min(ceil(($this->_sections['i']['step'] > 0 ? $this->_sections['i']['loop'] - $this->_sections['i']['start'] : $this->_sections['i']['start']+1)/abs($this->_sections['i']['step'])), $this->_sections['i']['max']);
    if ($this->_sections['i']['total'] == 0)
        $this->_sections['i']['show'] = false;
} else
    $this->_sections['i']['total'] = 0;
if ($this->_sections['i']['show']):

            for ($this->_sections['i']['index'] = $this->_sections['i']['start'], $this->_sections['i']['iteration'] = 1;
                 $this->_sections['i']['iteration'] <= $this->_sections['i']['total'];
                 $this->_sections['i']['index'] += $this->_sections['i']['step'], $this->_sections['i']['iteration']++):
$this->_sections['i']['rownum'] = $this->_sections['i']['iteration'];
$this->_sections['i']['index_prev'] = $this->_sections['i']['index'] - $this->_sections['i']['step'];
$this->_sections['i']['index_next'] = $this->_sections['i']['index'] + $this->_sections['i']['step'];
$this->_sections['i']['first']      = ($this->_sections['i']['iteration'] == 1);
$this->_sections['i']['last']       = ($this->_sections['i']['iteration'] == $this->_sections['i']['total']);
?>
                          <option value="<?php echo $this->_sections['i']['index']; ?>
"<?php if ($this->_sections['i']['index'] == $this->_tpl_vars['task']->get('progress')): ?> selected="selected"<?php endif; ?>><?php echo $this->_sections['i']['index']; ?>
%</option>
                        <?php endfor; endif; ?>
                      </select>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('progress'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
%
                      <input type="hidden" name="progress" id="progress" value="<?php echo $this->_tpl_vars['task']->get('progress'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'equipment'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_equipment"><label for="equipment"<?php if ($this->_tpl_vars['messages']->getErrors('equipment')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="equipment" id="equipment" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('equipment'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('equipment'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="equipment" id="equipment" value="<?php echo $this->_tpl_vars['task']->get('equipment'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'task_field'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_task_field"><label for="task_field"<?php if ($this->_tpl_vars['messages']->getErrors('task_field')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="task_field" id="task_field" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('task_field'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('task_field'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="task_field" id="task_field" value="<?php echo $this->_tpl_vars['task']->get('task_field'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'source'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_source"><label for="source"<?php if ($this->_tpl_vars['messages']->getErrors('source')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select name="source" id="source" class="selbox<?php if (! $this->_tpl_vars['task']->get('source')): ?> undefined<?php endif; ?>" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                        <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <option value="email"<?php if ($this->_tpl_vars['task']->get('source') == 'email'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                        <option value="phone"<?php if ($this->_tpl_vars['task']->get('source') == 'phone'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_phone'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                        <option value="meeting"<?php if ($this->_tpl_vars['task']->get('source') == 'meeting'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_meeting'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                        <option value="inside"<?php if ($this->_tpl_vars['task']->get('source') == 'inside'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_inside'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                        <option value="other"<?php if ($this->_tpl_vars['task']->get('source') == 'other'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_other'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                      </select>
                    <?php else: ?>
                      <?php if ($this->_tpl_vars['task']->get('source')): ?>
                        <?php ob_start(); ?>tasks_<?php echo $this->_tpl_vars['task']->get('source'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('source_lang_var', ob_get_contents());ob_end_clean(); ?>
                        <?php echo $this->_config[0]['vars'][$this->_tpl_vars['source_lang_var']]; ?>

                      <?php else: ?>
                        &nbsp;
                      <?php endif; ?>
                      <input type="hidden" name="source" id="source" value="<?php echo $this->_tpl_vars['task']->get('source'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled higher" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="description" id="description" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_notes"><label for="notes"<?php if ($this->_tpl_vars['messages']->getErrors('notes')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled higher" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="notes" id="notes" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select class="selbox<?php if (! $this->_tpl_vars['task']->get('department')): ?> undefined<?php endif; ?>" name="department" id="department" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
                        <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                          <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['task']->get('department')): ?>
                          <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['task']->get('department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      </select>
                    <?php else: ?>
                      <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                        <?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['task']->get('department')): ?>
                          <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>

                          <input type="hidden" name="department" id="department" value="<?php echo $this->_tpl_vars['task']->get('department'); ?>
" />
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif (strpos ( $this->_tpl_vars['lkey'] , 'assignments_' ) === 0): ?>
                <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?> class="nz-form-input">
                  <td class="labelbox">
                    <?php $this->assign('at', ((is_array($_tmp=$this->_tpl_vars['layout']['keyword'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'assignments_', '') : smarty_modifier_replace($_tmp, 'assignments_', ''))); ?>
                    <a name="error_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"><label for="<?php echo $this->_tpl_vars['at']; ?>
_ac"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['layout']['keyword'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/assignments/templates/_assignments_group.html", 'smarty_include_vars' => array('type' => $this->_tpl_vars['at'],'users' => $this->_tpl_vars['assignments_settings'][$this->_tpl_vars['at']]['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php if ($this->_tpl_vars['at'] == 'owner'): ?>
                    <script type="text/javascript" defer="defer">
                      Event.observe('department', 'change', updateAssignmentsParams);
                    </script>
                    <?php endif; ?>
                  </td>
                  <td class="unrequired">&nbsp;</td>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/assignments/templates/_assign.html", 'smarty_include_vars' => array('type' => $this->_tpl_vars['at'],'data' => $this->_tpl_vars['assignments_settings'][$this->_tpl_vars['at']],'borderless' => true,'model' => $this->_tpl_vars['task'],'width' => 200,'display_always' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </tr>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3">
                    <?php echo ''; ?><?php if ($this->_tpl_vars['task']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['task']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['action'] == 'ajax_add'): ?><?php echo '<button type="button" name="saveButton1" class="nz-form-button nz-button-primary" onclick="ajaxSaveTask(\''; ?><?php echo $this->_tpl_vars['action']; ?><?php echo '\', this.form)">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="button" name="cancel" class="nz-form-button nz-button-cancel" onclick="confirmAction(\'cancel\', function() '; ?>{<?php echo ' lb.deactivate(); '; ?>}<?php echo ', this);">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php else: ?><?php echo '<button type="submit" name="saveButton1" class="nz-form-button nz-button-primary">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['task'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php if ($this->_tpl_vars['action'] != 'ajax_add'): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>

      <?php if (! $_GET['reload']): ?>
    </form>
  </div>
</div>
<?php endif; ?>