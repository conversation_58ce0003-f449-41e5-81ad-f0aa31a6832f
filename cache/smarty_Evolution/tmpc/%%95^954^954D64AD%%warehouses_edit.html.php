<?php /* Smarty version 2.6.33, created on 2025-01-08 17:54:30
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html', 10, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html', 80, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html', 17, false),array('function', 'array', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_edit.html', 75, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<form name="finance" enctype="multipart/form-data" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['warehouse']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['warehouse']->get('id'); ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_name'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['warehouse']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_warehouses_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_code"><label for="code"<?php if ($this->_tpl_vars['messages']->getErrors('code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_code'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <input type="text" class="txtbox" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['warehouse']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_warehouses_code'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_company"><label for="company"<?php if ($this->_tpl_vars['messages']->getErrors('company')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_company'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['warehouse']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_office"><label for="office"<?php if ($this->_tpl_vars['messages']->getErrors('office')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_office'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'office','options' => $this->_tpl_vars['offices'],'value' => $this->_tpl_vars['warehouse']->get('office'),'required' => 1,'really_required' => 1,'width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['finance_warehouses_office'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_location"><label for="location"<?php if ($this->_tpl_vars['messages']->getErrors('location')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_location'), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox" name="location" id="location" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['warehouse']->get('location'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_warehouses_location'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_description'), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_warehouses_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['warehouse']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_employees"><label for="employees"<?php if ($this->_tpl_vars['messages']->getErrors('employees')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'warehouses_employees'), $this);?>
</label></a></td>
          <td colspan="2" class="nopadding">
            <table id="employees_container" cellspacing="0" cellpadding="2" border="0" class="floatl">
              <tr style="display: none;">
                <td colspan="2"></td>
              </tr>
              <?php $this->assign('employees', $this->_tpl_vars['warehouse']->getEmployees()); ?>
              <?php if (! $this->_tpl_vars['employees']): ?>
                <?php echo smarty_function_array(array('assign' => 'employees','eval' => 'array(array(\'id\' => \'\', \'code\' => \'\', \'name\' => \'\'))'), $this);?>

              <?php endif; ?>
              <?php $_from = $this->_tpl_vars['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['j'] => $this->_tpl_vars['employee']):
        $this->_foreach['i']['iteration']++;
?> 
              <tr id="employees_container_<?php echo $this->_foreach['i']['iteration']; ?>
">
                <td style="text-align: center; padding-right: 3px;">
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (count($this->_tpl_vars['employees']) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('employees_container','<?php echo $this->_foreach['i']['iteration']; ?>
'); }, this);" />
                  <a href="javascript: disableField('employees_container','<?php echo $this->_foreach['i']['iteration']; ?>
');" style="display: none;"><?php echo $this->_foreach['i']['iteration']; ?>
</a>
                </td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employees','index' => $this->_foreach['i']['iteration'],'autocomplete' => $this->_tpl_vars['autocomplete_employee'],'autocomplete_var_type' => 'basic','value' => $this->_tpl_vars['employee']['id'],'value_code' => $this->_tpl_vars['employee']['code'],'value_name' => $this->_tpl_vars['employee']['name'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['employee'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php endforeach; endif; unset($_from); ?>
            </table>
            <div class="floatl" style="padding: 5px; width: 30px;">
              <div class="t_buttons">
                <div id="employees_container_plusButton" onclick="addField('employees_container', false, false, true);" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                <div id="employees_container_minusButton"<?php if (count($this->_tpl_vars['employees']) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeField('employees_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['warehouse'],'exclude' => 'is_portal')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>