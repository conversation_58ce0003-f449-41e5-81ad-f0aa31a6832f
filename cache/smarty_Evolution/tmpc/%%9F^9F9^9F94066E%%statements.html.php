<?php /* Smarty version 2.6.33, created on 2024-08-06 18:28:49
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 3, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 57, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 96, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 31, false),array('modifier', 'trim', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 54, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/statements.html', 150, false),)), $this); ?>
<?php if ($this->_tpl_vars['visible_statements_screen_1']): ?>
  <?php echo ''; ?><?php echo smarty_function_math(array('equation' => "x+1",'x' => $this->_tpl_vars['week_days_number'],'assign' => 'table_cols'), $this);?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['monday_start'] && $this->_tpl_vars['calendar']->day_of_week == 0): ?><?php echo '7'; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['calendar']->day_of_week; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_day_of_week', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?><?php echo ''; ?><?php echo ''; ?><?php $this->assign('timesheet_cell_height', 12); ?><?php echo ''; ?><?php $this->assign('timesheet_all_day_height', 20); ?><?php echo ''; ?><?php $this->assign('timesheet_allday_cell_height', 15); ?><?php echo ''; ?><?php if ($this->_tpl_vars['week_days_number'] == 7): ?><?php echo ''; ?><?php $this->assign('timesheet_cell_width', 120); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['week_days_number'] == 6): ?><?php echo ''; ?><?php $this->assign('timesheet_cell_width', 140); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['week_days_number'] == 5): ?><?php echo ''; ?><?php $this->assign('timesheet_cell_width', 168); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->assign('timesheet_overlapping', 10); ?><?php echo ''; ?><?php $this->assign('timesheet_area_offset_left', 0); ?><?php echo ''; ?><?php $this->assign('timesheet_area_border_width', 1); ?><?php echo ''; ?><?php $this->assign('timesheet_area_border_padding', 1); ?><?php echo ''; ?>

  <h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
  <div id="form_container" style="width: <?php echo smarty_function_math(array('equation' => 'x*y+36+x*2','x' => $this->_tpl_vars['week_days_number'],'y' => $this->_tpl_vars['timesheet_cell_width']), $this);?>
px; float:left">

  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

  <table border="0" cellpadding="0" cellspacing="0" class="t_table">
    <tr>
      <td colspan="<?php echo $this->_tpl_vars['table_cols']; ?>
">
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
          <tr>
            <td class="t_caption4 t_border2 hcenter" style="width: 32px">
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=tasks&amp;tasks=statements&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay(-7); ?>
" class="strong" title="<?php echo $this->_config[0]['vars']['calendars_previous_week']; ?>
">&laquo;</a>
            </td>
            <td class="cal_title_bar t_caption2 hcenter<?php if (! $this->_tpl_vars['hide_next_week_button']): ?> t_border<?php endif; ?>">
              <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['first_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 - <?php echo ((is_array($_tmp=$this->_tpl_vars['last_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 (<?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
)</div>
            </td>
            <td class="<?php if (! $this->_tpl_vars['hide_next_week_button']): ?>t_caption3<?php else: ?>t_caption2<?php endif; ?> hcenter" style="width: 32px">
              <?php if (! $this->_tpl_vars['hide_next_week_button']): ?>
                <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=tasks&amp;tasks=statements&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay(7); ?>
" class="strong" title="<?php echo $this->_config[0]['vars']['calendars_next_week']; ?>
">&raquo;</a>
              <?php else: ?>
                &nbsp;
              <?php endif; ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td class="t_caption2 t_border_double cal_hour">&nbsp;</td>
      <?php unset($this->_sections['weekdays']);
$this->_sections['weekdays']['name'] = 'weekdays';
$this->_sections['weekdays']['loop'] = is_array($_loop=$this->_tpl_vars['week_days_number']+$this->_tpl_vars['monday_start']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['weekdays']['start'] = (int)$this->_tpl_vars['monday_start'];
$this->_sections['weekdays']['show'] = true;
$this->_sections['weekdays']['max'] = $this->_sections['weekdays']['loop'];
$this->_sections['weekdays']['step'] = 1;
if ($this->_sections['weekdays']['start'] < 0)
    $this->_sections['weekdays']['start'] = max($this->_sections['weekdays']['step'] > 0 ? 0 : -1, $this->_sections['weekdays']['loop'] + $this->_sections['weekdays']['start']);
else
    $this->_sections['weekdays']['start'] = min($this->_sections['weekdays']['start'], $this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] : $this->_sections['weekdays']['loop']-1);
if ($this->_sections['weekdays']['show']) {
    $this->_sections['weekdays']['total'] = min(ceil(($this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] - $this->_sections['weekdays']['start'] : $this->_sections['weekdays']['start']+1)/abs($this->_sections['weekdays']['step'])), $this->_sections['weekdays']['max']);
    if ($this->_sections['weekdays']['total'] == 0)
        $this->_sections['weekdays']['show'] = false;
} else
    $this->_sections['weekdays']['total'] = 0;
if ($this->_sections['weekdays']['show']):

            for ($this->_sections['weekdays']['index'] = $this->_sections['weekdays']['start'], $this->_sections['weekdays']['iteration'] = 1;
                 $this->_sections['weekdays']['iteration'] <= $this->_sections['weekdays']['total'];
                 $this->_sections['weekdays']['index'] += $this->_sections['weekdays']['step'], $this->_sections['weekdays']['iteration']++):
$this->_sections['weekdays']['rownum'] = $this->_sections['weekdays']['iteration'];
$this->_sections['weekdays']['index_prev'] = $this->_sections['weekdays']['index'] - $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['index_next'] = $this->_sections['weekdays']['index'] + $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['first']      = ($this->_sections['weekdays']['iteration'] == 1);
$this->_sections['weekdays']['last']       = ($this->_sections['weekdays']['iteration'] == $this->_sections['weekdays']['total']);
?>
        <?php ob_start(); ?>weekday_<?php echo $this->_sections['weekdays']['index']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_label', ob_get_contents());ob_end_clean(); ?>
        <?php if ($this->_sections['weekdays']['index'] == 7): ?>
          <?php ob_start(); ?>weekday_0<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_label', ob_get_contents());ob_end_clean(); ?>
        <?php endif; ?>
        <?php $this->assign('offset_day', $this->_sections['weekdays']['index']-$this->_tpl_vars['monday_start']); ?>
        <?php $this->assign('day', $this->_tpl_vars['calendar']->getDayInWeek($this->_tpl_vars['offset_day'],$this->_tpl_vars['week_num'])); ?>
        <td class="t_caption2<?php if (! $this->_sections['weekdays']['last']): ?> t_border_double<?php endif; ?><?php if ($this->_tpl_vars['calendar']->day_of_week == $this->_sections['weekdays']['index']%7): ?> t_caption3<?php endif; ?><?php if ($this->_tpl_vars['offset_day']+1 != $this->_tpl_vars['week_days_number']): ?> t_border_double<?php endif; ?> hcenter strong" style="width: <?php echo $this->_tpl_vars['timesheet_cell_width']; ?>
px;">
          <?php echo $this->_config[0]['vars'][$this->_tpl_vars['week_label']]; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['day']->dateISO)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)
          <?php if ($this->_tpl_vars['current_date_formated'] >= $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_day'])): ?>
            <div style="float: right;">
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/plus3.png" style="cursor: pointer;" border="0" width="11" height="11" alt="" title="" onclick="addTimesheet('dates', '<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_day']); ?>
')" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_day']))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)),'text_content' => $this->_config[0]['vars']['tasks_add_day_timesheet'],'popup_only' => 1), $this);?>
 />
            </div>
          <?php endif; ?>
        </td>
      <?php endfor; endif; ?>
    </tr>
    <?php echo smarty_function_math(array('equation' => "((adwtc+2)*tach)+((2*adwtc*tabp)+(2*adwtc*tabw))+1",'tabp' => $this->_tpl_vars['timesheet_area_border_padding'],'tabw' => $this->_tpl_vars['timesheet_area_border_width'],'tach' => $this->_tpl_vars['timesheet_allday_cell_height'],'adwtc' => $this->_tpl_vars['all_day_week_timesheets_count'],'assign' => 'week_timesheets_height'), $this);?>

    <tr style="height:<?php echo $this->_tpl_vars['week_timesheets_height']; ?>
px;">
      <td class="cal_hour t_border_double t_v_border">
        <?php ob_start(); ?><?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_num_text', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?><?php echo $this->_config[0]['vars']['tasks_add_timesheet']; ?>
 <?php echo $this->_config[0]['vars']['tasks_timesheets_by_period']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('add_week_event_content', ob_get_contents());ob_end_clean(); ?>
        <div style="float: right;">
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/plus2.png" style="cursor: pointer;" border="0" width="14" height="14" alt="" title="" onclick="addTimesheet('period', '<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']); ?>
')" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['week_num_text'])) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)),'text_content' => $this->_tpl_vars['add_week_event_content'],'popup_only' => 1), $this);?>
 />
        </div>
      </td>
      <?php unset($this->_sections['week_day_full_day_timesheets']);
$this->_sections['week_day_full_day_timesheets']['name'] = 'week_day_full_day_timesheets';
$this->_sections['week_day_full_day_timesheets']['loop'] = is_array($_loop=$this->_tpl_vars['week_days_number']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['week_day_full_day_timesheets']['show'] = true;
$this->_sections['week_day_full_day_timesheets']['max'] = $this->_sections['week_day_full_day_timesheets']['loop'];
$this->_sections['week_day_full_day_timesheets']['step'] = 1;
$this->_sections['week_day_full_day_timesheets']['start'] = $this->_sections['week_day_full_day_timesheets']['step'] > 0 ? 0 : $this->_sections['week_day_full_day_timesheets']['loop']-1;
if ($this->_sections['week_day_full_day_timesheets']['show']) {
    $this->_sections['week_day_full_day_timesheets']['total'] = $this->_sections['week_day_full_day_timesheets']['loop'];
    if ($this->_sections['week_day_full_day_timesheets']['total'] == 0)
        $this->_sections['week_day_full_day_timesheets']['show'] = false;
} else
    $this->_sections['week_day_full_day_timesheets']['total'] = 0;
if ($this->_sections['week_day_full_day_timesheets']['show']):

            for ($this->_sections['week_day_full_day_timesheets']['index'] = $this->_sections['week_day_full_day_timesheets']['start'], $this->_sections['week_day_full_day_timesheets']['iteration'] = 1;
                 $this->_sections['week_day_full_day_timesheets']['iteration'] <= $this->_sections['week_day_full_day_timesheets']['total'];
                 $this->_sections['week_day_full_day_timesheets']['index'] += $this->_sections['week_day_full_day_timesheets']['step'], $this->_sections['week_day_full_day_timesheets']['iteration']++):
$this->_sections['week_day_full_day_timesheets']['rownum'] = $this->_sections['week_day_full_day_timesheets']['iteration'];
$this->_sections['week_day_full_day_timesheets']['index_prev'] = $this->_sections['week_day_full_day_timesheets']['index'] - $this->_sections['week_day_full_day_timesheets']['step'];
$this->_sections['week_day_full_day_timesheets']['index_next'] = $this->_sections['week_day_full_day_timesheets']['index'] + $this->_sections['week_day_full_day_timesheets']['step'];
$this->_sections['week_day_full_day_timesheets']['first']      = ($this->_sections['week_day_full_day_timesheets']['iteration'] == 1);
$this->_sections['week_day_full_day_timesheets']['last']       = ($this->_sections['week_day_full_day_timesheets']['iteration'] == $this->_sections['week_day_full_day_timesheets']['total']);
?>
                <?php $this->assign('day_week', $this->_tpl_vars['calendar']->getDayInWeek($this->_sections['week_day_full_day_timesheets']['index'],$this->_tpl_vars['week_num'])); ?>
        <td style="width: <?php echo $this->_tpl_vars['timesheet_cell_width']; ?>
px;" class="cal_week_day">
          <div style="position:relative">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_statements_timesheets_week.html", 'smarty_include_vars' => array('timesheets' => $this->_tpl_vars['all_day_week_timesheets'][$this->_sections['week_day_full_day_timesheets']['index']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <table cellpadding="0" cellspacing="0" width="100%">
              <tr style="height:<?php echo $this->_tpl_vars['week_timesheets_height']; ?>
px;">
                <td class="t_v_border<?php if (! $this->_sections['week_day_full_day_timesheets']['last']): ?> t_border_double<?php endif; ?>">&nbsp;</td>
              </tr>
            </table>
          </div>
        </td>
      <?php endfor; endif; ?>
    </tr>
    <tr>
      <td>
        <div style="position:relative">
          <table cellpadding="0" cellspacing="0" border="0">
                        <?php unset($this->_sections['time']);
$this->_sections['time']['name'] = 'time';
$this->_sections['time']['loop'] = is_array($_loop=$this->_tpl_vars['day_hours']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['time']['show'] = true;
$this->_sections['time']['max'] = $this->_sections['time']['loop'];
$this->_sections['time']['step'] = 1;
$this->_sections['time']['start'] = $this->_sections['time']['step'] > 0 ? 0 : $this->_sections['time']['loop']-1;
if ($this->_sections['time']['show']) {
    $this->_sections['time']['total'] = $this->_sections['time']['loop'];
    if ($this->_sections['time']['total'] == 0)
        $this->_sections['time']['show'] = false;
} else
    $this->_sections['time']['total'] = 0;
if ($this->_sections['time']['show']):

            for ($this->_sections['time']['index'] = $this->_sections['time']['start'], $this->_sections['time']['iteration'] = 1;
                 $this->_sections['time']['iteration'] <= $this->_sections['time']['total'];
                 $this->_sections['time']['index'] += $this->_sections['time']['step'], $this->_sections['time']['iteration']++):
$this->_sections['time']['rownum'] = $this->_sections['time']['iteration'];
$this->_sections['time']['index_prev'] = $this->_sections['time']['index'] - $this->_sections['time']['step'];
$this->_sections['time']['index_next'] = $this->_sections['time']['index'] + $this->_sections['time']['step'];
$this->_sections['time']['first']      = ($this->_sections['time']['iteration'] == 1);
$this->_sections['time']['last']       = ($this->_sections['time']['iteration'] == $this->_sections['time']['total']);
?>
              <?php echo smarty_function_math(array('equation' => "x/60+y",'x' => $this->_tpl_vars['day_start'],'y' => $this->_sections['time']['index'],'assign' => 'grid_time','format' => '%02d'), $this);?>

              <?php echo smarty_function_math(array('equation' => "4*x",'x' => $this->_tpl_vars['timesheet_cell_height'],'assign' => 'current_cell_height'), $this);?>

              <?php ob_start(); ?><?php echo $this->_config[0]['vars']['calendars_add_event']; ?>
 <b><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->dateISO)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 <?php echo $this->_tpl_vars['grid_time']; ?>
</b><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('grid_time_info', ob_get_contents());ob_end_clean(); ?>
              <tr style="height:<?php echo $this->_tpl_vars['current_cell_height']; ?>
px" class="<?php echo smarty_function_cycle(array('values' => 'cal_odd,cal_even'), $this);?>
">
                <td class="cal_hour t_border_double t_v_border" style="border-collapse: collapse;">
                  <?php echo $this->_tpl_vars['grid_time']; ?>
:00
                </td>
              </tr>
            <?php endfor; endif; ?>
          </table>
        </div>
      </td>
    <?php unset($this->_sections['week_day']);
$this->_sections['week_day']['name'] = 'week_day';
$this->_sections['week_day']['loop'] = is_array($_loop=$this->_tpl_vars['week_days_number']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['week_day']['show'] = true;
$this->_sections['week_day']['max'] = $this->_sections['week_day']['loop'];
$this->_sections['week_day']['step'] = 1;
$this->_sections['week_day']['start'] = $this->_sections['week_day']['step'] > 0 ? 0 : $this->_sections['week_day']['loop']-1;
if ($this->_sections['week_day']['show']) {
    $this->_sections['week_day']['total'] = $this->_sections['week_day']['loop'];
    if ($this->_sections['week_day']['total'] == 0)
        $this->_sections['week_day']['show'] = false;
} else
    $this->_sections['week_day']['total'] = 0;
if ($this->_sections['week_day']['show']):

            for ($this->_sections['week_day']['index'] = $this->_sections['week_day']['start'], $this->_sections['week_day']['iteration'] = 1;
                 $this->_sections['week_day']['iteration'] <= $this->_sections['week_day']['total'];
                 $this->_sections['week_day']['index'] += $this->_sections['week_day']['step'], $this->_sections['week_day']['iteration']++):
$this->_sections['week_day']['rownum'] = $this->_sections['week_day']['iteration'];
$this->_sections['week_day']['index_prev'] = $this->_sections['week_day']['index'] - $this->_sections['week_day']['step'];
$this->_sections['week_day']['index_next'] = $this->_sections['week_day']['index'] + $this->_sections['week_day']['step'];
$this->_sections['week_day']['first']      = ($this->_sections['week_day']['iteration'] == 1);
$this->_sections['week_day']['last']       = ($this->_sections['week_day']['iteration'] == $this->_sections['week_day']['total']);
?>
      <td style="width: <?php echo $this->_tpl_vars['timesheet_cell_width']; ?>
px;" class="cal_week_day">
        <div style="position:relative">
                    <?php $this->assign('day', $this->_tpl_vars['calendar']->getDayInWeek($this->_sections['week_day']['index'],$this->_tpl_vars['week_num'])); ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_statements_timesheets_day.html", 'smarty_include_vars' => array('timesheets' => $this->_tpl_vars['week_timesheets'][$this->_sections['week_day']['index']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

          <?php echo smarty_function_math(array('equation' => "4*y",'y' => $this->_tpl_vars['timesheet_cell_height'],'assign' => 'inner_cell_height'), $this);?>

          <?php $this->assign('week_index', $this->_sections['week_day']['index']); ?>
          <?php ob_start(); ?>cycle_<?php echo $this->_sections['week_day']['index']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('cycle_name', ob_get_contents());ob_end_clean(); ?>
          <table cellpadding="0" cellspacing="0" width="100%">
                        <?php unset($this->_sections['time']);
$this->_sections['time']['name'] = 'time';
$this->_sections['time']['loop'] = is_array($_loop=$this->_tpl_vars['day_hours']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['time']['show'] = true;
$this->_sections['time']['max'] = $this->_sections['time']['loop'];
$this->_sections['time']['step'] = 1;
$this->_sections['time']['start'] = $this->_sections['time']['step'] > 0 ? 0 : $this->_sections['time']['loop']-1;
if ($this->_sections['time']['show']) {
    $this->_sections['time']['total'] = $this->_sections['time']['loop'];
    if ($this->_sections['time']['total'] == 0)
        $this->_sections['time']['show'] = false;
} else
    $this->_sections['time']['total'] = 0;
if ($this->_sections['time']['show']):

            for ($this->_sections['time']['index'] = $this->_sections['time']['start'], $this->_sections['time']['iteration'] = 1;
                 $this->_sections['time']['iteration'] <= $this->_sections['time']['total'];
                 $this->_sections['time']['index'] += $this->_sections['time']['step'], $this->_sections['time']['iteration']++):
$this->_sections['time']['rownum'] = $this->_sections['time']['iteration'];
$this->_sections['time']['index_prev'] = $this->_sections['time']['index'] - $this->_sections['time']['step'];
$this->_sections['time']['index_next'] = $this->_sections['time']['index'] + $this->_sections['time']['step'];
$this->_sections['time']['first']      = ($this->_sections['time']['iteration'] == 1);
$this->_sections['time']['last']       = ($this->_sections['time']['iteration'] == $this->_sections['time']['total']);
?>
            <?php echo smarty_function_math(array('equation' => "x/60+y",'x' => $this->_tpl_vars['day_start'],'y' => $this->_sections['time']['index'],'assign' => 'grid_time','format' => '%02d'), $this);?>

              <tr style="height:<?php echo $this->_tpl_vars['inner_cell_height']; ?>
px" class="<?php echo smarty_function_cycle(array('name' => $this->_tpl_vars['cycle_name'],'values' => 'cal_odd,cal_even'), $this);?>
">
                <td class="t_v_border<?php if (! $this->_sections['week_day']['last']): ?> t_border_double<?php endif; ?>"<?php if ($this->_tpl_vars['day']->dateISO <= $this->_tpl_vars['current_date_formated']): ?> onclick="addTimesheet('dates', '<?php echo $this->_tpl_vars['day']->dateISO; ?>
'<?php if ($this->_tpl_vars['current_date_formated'] == $this->_tpl_vars['day']->dateISO): ?>, '', '<?php echo $this->_tpl_vars['grid_time']; ?>
:00:00'<?php endif; ?>)"<?php endif; ?>>&nbsp;</td>
              </tr>
            <?php endfor; endif; ?>
          </table>
        </div>
      </td>
    <?php endfor; endif; ?>
    </tr>
    <tr style="height:20px;">
      <td><strong><?php echo $this->_config[0]['vars']['tasks_statements_total']; ?>
</strong></td>
      <?php unset($this->_sections['week_day_total']);
$this->_sections['week_day_total']['name'] = 'week_day_total';
$this->_sections['week_day_total']['loop'] = is_array($_loop=$this->_tpl_vars['week_days_number']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['week_day_total']['show'] = true;
$this->_sections['week_day_total']['max'] = $this->_sections['week_day_total']['loop'];
$this->_sections['week_day_total']['step'] = 1;
$this->_sections['week_day_total']['start'] = $this->_sections['week_day_total']['step'] > 0 ? 0 : $this->_sections['week_day_total']['loop']-1;
if ($this->_sections['week_day_total']['show']) {
    $this->_sections['week_day_total']['total'] = $this->_sections['week_day_total']['loop'];
    if ($this->_sections['week_day_total']['total'] == 0)
        $this->_sections['week_day_total']['show'] = false;
} else
    $this->_sections['week_day_total']['total'] = 0;
if ($this->_sections['week_day_total']['show']):

            for ($this->_sections['week_day_total']['index'] = $this->_sections['week_day_total']['start'], $this->_sections['week_day_total']['iteration'] = 1;
                 $this->_sections['week_day_total']['iteration'] <= $this->_sections['week_day_total']['total'];
                 $this->_sections['week_day_total']['index'] += $this->_sections['week_day_total']['step'], $this->_sections['week_day_total']['iteration']++):
$this->_sections['week_day_total']['rownum'] = $this->_sections['week_day_total']['iteration'];
$this->_sections['week_day_total']['index_prev'] = $this->_sections['week_day_total']['index'] - $this->_sections['week_day_total']['step'];
$this->_sections['week_day_total']['index_next'] = $this->_sections['week_day_total']['index'] + $this->_sections['week_day_total']['step'];
$this->_sections['week_day_total']['first']      = ($this->_sections['week_day_total']['iteration'] == 1);
$this->_sections['week_day_total']['last']       = ($this->_sections['week_day_total']['iteration'] == $this->_sections['week_day_total']['total']);
?>
        <td style="width: <?php echo $this->_tpl_vars['timesheet_cell_width']; ?>
px; text-align: center;">
          <?php echo $this->_tpl_vars['total_time_per_day'][$this->_sections['week_day_total']['index']]; ?>

        </td>
      <?php endfor; endif; ?>
    </tr>
    <tr>
      <td class="t_footer" colspan="<?php echo $this->_tpl_vars['table_cols']; ?>
"></td>
    </tr>
  </table>
  </div>
  <div class="clear"></div>
  <br />
  <br />
  <br />
<?php endif; ?>
<?php if ($this->_tpl_vars['visible_statements_screen_2'] || $this->_tpl_vars['visible_statements_screen_3']): ?>
  <div class="action_tabs">
    <ul>
      <?php if ($this->_tpl_vars['visible_statements_screen_2']): ?>
        <li><span<?php if ($this->_tpl_vars['selected_subpanel'] == 'list_timesheets_models'): ?> class="selected"<?php endif; ?>><a onclick="toggleStatementsTab(this)" id="list_timesheets_models" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_list_timesheets_models'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['visible_statements_screen_3']): ?>
        <li><span<?php if ($this->_tpl_vars['selected_subpanel'] == 'free_add_timesheets'): ?> class="selected"<?php endif; ?>><a onclick="toggleStatementsTab(this)" id="free_add_timesheets" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_free_adding_timesheets'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
      <?php endif; ?>
    </ul>
  </div>
  <div class="clear"></div>
  <?php if ($this->_tpl_vars['week_days_number'] && $this->_tpl_vars['timesheet_cell_width']): ?>
    <?php echo smarty_function_math(array('equation' => 'x*y+36+x*2','x' => $this->_tpl_vars['week_days_number'],'y' => $this->_tpl_vars['timesheet_cell_width'],'assign' => 'subpanel_div_width'), $this);?>

  <?php else: ?>
    <?php $this->assign('subpanel_div_width', 800); ?>
  <?php endif; ?>
  <div id="statements_subpanel_div" style="width: 1200px;">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['statements_subpanel'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div>
<?php endif; ?>