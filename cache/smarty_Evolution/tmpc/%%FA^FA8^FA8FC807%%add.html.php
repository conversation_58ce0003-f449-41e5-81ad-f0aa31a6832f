<?php /* Smarty version 2.6.33, created on 2024-10-10 16:54:44
         compiled from /var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 13, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 335, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 363, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 363, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 391, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 43, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/add.html', 71, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <form name="contracts" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" enctype="multipart/form-data" onsubmit="return calculateBeforeSubmit(this);">
                <input type="hidden" name="model_id" id="model_id" value="<?php echo $this->_tpl_vars['model_id']; ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <?php if ($this->_tpl_vars['contract']->get('transform_params')): ?>
          <input type="hidden" name="transform_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('transform_params'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
        <?php endif; ?>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td class="t_footer"></td>
          </tr>
          <tr>
            <td>
              <?php $this->assign('layouts_vars', $this->_tpl_vars['contract']->get('vars')); ?>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['contract']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

                <?php ob_start(); ?>include_<?php echo $this->_tpl_vars['lkey']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('type_include_date', ob_get_contents());ob_end_clean(); ?>
                <?php if ($this->_tpl_vars['layout']['system'] && ( ! ( $this->_tpl_vars['lkey'] == 'system' || preg_match ( '#^date_(sign|start|end)_subtype$#' , $this->_tpl_vars['lkey'] ) ) || $this->_tpl_vars['contract']->get($this->_tpl_vars['type_include_date']) && preg_match ( '#^date_(sign|start|validity|end)$#' , $this->_tpl_vars['lkey'] ) ) || $this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
                      <a name="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>

                <?php if ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['contract']->get('type'); ?>
" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_custom_num"><label for="custom_num"<?php if ($this->_tpl_vars['messages']->getErrors('custom_num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="custom_num" id="custom_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" maxlength="30" />
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="custom_num" id="custom_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                      <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['contract']->get('customer'),'value_code' => $this->_tpl_vars['contract']->get('customer_code'),'value_name' => $this->_tpl_vars['contract']->get('customer_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <span id="branch_container" style="display: <?php if ($this->_tpl_vars['contract']->get('customer_is_company')): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('help_contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox<?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?> missing_records<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                          <?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('empty_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <?php $_from = $this->_tpl_vars['customer_branches']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['customer_branch']):
?>
                              <?php if (( ! $this->_tpl_vars['customer_branch']->isDeleted() && $this->_tpl_vars['customer_branch']->isActivated() ) || $this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['contract']->get('branch')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer_branch']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['contract']->get('branch')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['customer_branch']->isDeleted() || ! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['customer_branch']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="branch">
                        <?php if ($this->_tpl_vars['contract']->get('branch')): ?>
                          <span<?php if (! $this->_tpl_vars['contract']->get('branch_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="branch" id="branch" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('branch'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                    <span id="contact_person_container" style="display: <?php if ($this->_tpl_vars['contract']->get('customer_is_company') && ( $this->_tpl_vars['layout']['edit'] || $this->_tpl_vars['contract']->get('contact_person') )): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['contracts_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('help_contracts_contact_person'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="contact_person" id="contact_person" class="selbox<?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?> missing_records<?php elseif (! $this->_tpl_vars['contract']->get('contact_person')): ?> undefined<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                          <?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['empty_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <option value="" class="undefined"<?php if (! $this->_tpl_vars['contract']->get('contact_person')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                            <?php $_from = $this->_tpl_vars['contact_persons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact_person']):
?>
                              <?php if (( ! $this->_tpl_vars['contact_person']->isDeleted() && $this->_tpl_vars['contact_person']->isActivated() ) || $this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['contract']->get('contact_person')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contact_person']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['contract']->get('contact_person')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['contact_person']->isDeleted() || ! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if ($this->_tpl_vars['contact_person']->get('lastname')): ?> <?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('lastname'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['contracts_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="contact_person">
                        <?php if ($this->_tpl_vars['contract']->get('contact_person')): ?>
                          <span<?php if (! $this->_tpl_vars['contract']->get('contact_person_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="contact_person" id="contact_person" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('contact_person'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['contract']->get('trademark'),'value_name' => $this->_tpl_vars['contract']->get('trademark_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['contract']->get('project'),'value_code' => $this->_tpl_vars['contract']->get('project_code'),'value_name' => $this->_tpl_vars['contract']->get('project_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'company'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_company"><label for="company"<?php if ($this->_tpl_vars['messages']->getErrors('company')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <input type="hidden" name="company" id="company" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('company'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_office"><label for="office"<?php if ($this->_tpl_vars['messages']->getErrors('office')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <div style="width: 200px;">
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'office','custom_id' => 'office','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['contract']->get('office'),'options' => $this->_tpl_vars['offices'],'required' => 1,'really_required' => 1,'width' => 200,'onchange' => "updateAvailableQuantities($('company').value + '_' + this.value + '_bank_0')",'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      </div>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('office_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="office" id="office" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('office'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_employee"><label for="employee"<?php if ($this->_tpl_vars['messages']->getErrors('employee')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employee','autocomplete_type' => 'customers','stop_customer_details' => 1,'autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['contract']->get('employee'),'value_name' => $this->_tpl_vars['contract']->get('employee_name'),'filters_array' => $this->_tpl_vars['autocomplete_employee_filters'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="employee" id="employee" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('employee'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_sign' && $this->_tpl_vars['contract']->get('include_date_sign')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_date_sign"><label for="date_sign"<?php if ($this->_tpl_vars['messages']->getErrors('date_sign')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_formula.html", 'smarty_include_vars' => array('standalone' => true,'source' => 'date','name' => 'date_sign','formulas' => $this->_tpl_vars['contract']->get('formulas'),'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['contract']->get('date_sign'),'formula_value' => $this->_tpl_vars['contract']->get('date_sign_formula'),'width' => 220,'show_calendar_icon' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_start' && $this->_tpl_vars['contract']->get('include_date_start')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_date_start"><label for="date_start"<?php if ($this->_tpl_vars['messages']->getErrors('date_start')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_formula.html", 'smarty_include_vars' => array('standalone' => true,'source' => 'date','name' => 'date_start','formulas' => $this->_tpl_vars['contract']->get('formulas'),'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['contract']->get('date_start'),'formula_value' => $this->_tpl_vars['contract']->get('date_start_formula'),'width' => 220,'show_calendar_icon' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_validity' && $this->_tpl_vars['contract']->get('include_date_validity')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_date_validity"><label for="date_validity"<?php if ($this->_tpl_vars['messages']->getErrors('date_validity')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_formula.html", 'smarty_include_vars' => array('standalone' => true,'source' => 'date','name' => 'date_validity','formulas' => $this->_tpl_vars['contract']->get('formulas'),'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['contract']->get('date_validity'),'formula_value' => $this->_tpl_vars['contract']->get('date_validity_formula'),'width' => 220,'show_calendar_icon' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_end' && $this->_tpl_vars['contract']->get('include_date_end')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_date_end"><label for="date_end"<?php if ($this->_tpl_vars['messages']->getErrors('date_end')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_formula.html", 'smarty_include_vars' => array('standalone' => true,'source' => 'date','name' => 'date_end','formulas' => $this->_tpl_vars['contract']->get('formulas'),'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['contract']->get('date_end'),'formula_value' => $this->_tpl_vars['contract']->get('date_end_formula'),'width' => 220,'show_calendar_icon' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'referers'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_referers"><label for="referers"<?php if ($this->_tpl_vars['messages']->getErrors('referers')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <input type="hidden" name="update_relatives" id="update_relatives" value="1" />
                    <div id="referers">
                      <div id="toggleCheckboxes" style="width: 300px; display: <?php if (is_array ( $this->_tpl_vars['contract']->get('referers') ) && count($this->_tpl_vars['contract']->get('referers')) > 4): ?>block<?php else: ?>none<?php endif; ?>;">
                        <span onclick="toggleCheckboxes(this, 'referers', true)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
                        <span onclick="toggleCheckboxes(this, 'referers', false)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </div>
                      <?php if ($this->_tpl_vars['contract']->get('referers')): ?>
                        <?php $_from = $this->_tpl_vars['contract']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                          <input type="checkbox" name="referers[]" id="ref<?php echo $this->_tpl_vars['ref']['id']; ?>
" value="<?php echo $this->_tpl_vars['ref']['id']; ?>
" checked="checked" /><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
" target="_blank"><?php if ($this->_tpl_vars['ref']['num']): ?>[<?php echo $this->_tpl_vars['ref']['num']; ?>
]<?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?>&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                    </div>
                    <button type="button" name="filterButton" class="button" onclick="var popUrl='<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['module']; ?>
=filter&amp;open_from=<?php echo $this->_tpl_vars['module']; ?>
&amp;customer=' + $('customer').value; pop(popUrl, 820, 580);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['link'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
...</button>
                    <?php else: ?>
                      <?php if ($this->_tpl_vars['contract']->get('referers')): ?>
                        <?php $_from = $this->_tpl_vars['contract']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                          <?php echo $this->_foreach['i']['iteration']; ?>
. <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
" target="_blank"><?php if ($this->_tpl_vars['ref']['num']): ?>[<?php echo $this->_tpl_vars['ref']['num']; ?>
]<?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?>&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <textarea class="areabox doubled" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;"><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_notes"><label for="notes"<?php if ($this->_tpl_vars['messages']->getErrors('notes')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <textarea class="areabox doubled" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;"><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select class="selbox<?php if (! $this->_tpl_vars['contract']->get('department')): ?> undefined<?php endif; ?>" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                        <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                          <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['contract']->get('department')): ?>
                          <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['contract']->get('department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      </select>
                    <?php else: ?>
                      <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                        <?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['contract']->get('department')): ?>
                          <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>

                          <input type="hidden" name="department" id="department" value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
" />
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <!-- Contract Additional Vars -->
                <?php $this->assign('layout_id', $this->_tpl_vars['layout']['id']); ?>
                <?php $this->assign('vars', $this->_tpl_vars['layouts_vars'][$this->_tpl_vars['layout_id']]); ?>
                <?php if ($this->_tpl_vars['layout']['id']): ?>
                <tr id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_box"<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="nopadding" colspan="3">
                    <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                <?php endif; ?>
                <?php $_from = $this->_tpl_vars['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
                  <?php if ($this->_tpl_vars['var']['type'] == 'gt2'): ?>
                    <tr><td colspan="3">
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_edit.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['var'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['var'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                    </td></tr>
                  <?php elseif ($this->_tpl_vars['var']['type']): ?>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo $this->_tpl_vars['var']['help']; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['label']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'value' => $this->_tpl_vars['var']['value'],'value_id' => $this->_tpl_vars['var']['value_id'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'sequences' => $this->_tpl_vars['var']['sequences'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => ((is_array($_tmp=@$this->_tpl_vars['var']['formula_type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['source']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['source'])),'onchange' => $this->_tpl_vars['var']['onchange'],'map_params' => $this->_tpl_vars['var']['map_params'],'width' => $this->_tpl_vars['var']['width'],'hidden' => $this->_tpl_vars['var']['hidden'],'really_required' => $this->_tpl_vars['var']['required'],'required' => $this->_tpl_vars['var']['required'],'disabled' => $this->_tpl_vars['var']['disabled'],'formulas' => $this->_tpl_vars['contract']->get('formulas'),'indexes' => $this->_tpl_vars['contract']->get('indexes'),'formula_value' => $this->_tpl_vars['var']['formula'],'include_index' => $this->_tpl_vars['var']['include_index'],'index_value' => $this->_tpl_vars['var']['index'],'index_date' => $this->_tpl_vars['var']['index_date'],'index_formula' => $this->_tpl_vars['var']['index_formula'],'options_align' => $this->_tpl_vars['var']['options_align'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'deleteid' => $this->_tpl_vars['var']['deleteid'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "view_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php if ($this->_tpl_vars['contract']->get('transform_params')): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'value' => $this->_tpl_vars['var']['value'],'value_id' => $this->_tpl_vars['var']['value_id'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'sequences' => $this->_tpl_vars['var']['sequences'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => ((is_array($_tmp=@$this->_tpl_vars['var']['formula_type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['source']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['source'])),'onchange' => $this->_tpl_vars['var']['onchange'],'hidden' => 1,'hidden_all' => 1,'really_required' => $this->_tpl_vars['var']['required'],'required' => $this->_tpl_vars['var']['required'],'width' => $this->_tpl_vars['var']['width'],'disabled' => $this->_tpl_vars['var']['disabled'],'formulas' => $this->_tpl_vars['contract']->get('formulas'),'indexes' => $this->_tpl_vars['contract']->get('indexes'),'formula_value' => $this->_tpl_vars['var']['formula'],'include_index' => $this->_tpl_vars['var']['include_index'],'index_value' => $this->_tpl_vars['var']['index'],'index_date' => $this->_tpl_vars['var']['index_date'],'index_formula' => $this->_tpl_vars['var']['index_formula'],'options_align' => $this->_tpl_vars['var']['options_align'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'deleteid' => $this->_tpl_vars['var']['deleteid'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php $this->assign('hidden_all', 0); ?>
                      <?php endif; ?>
                    <?php endif; ?>
                  <?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
                <?php if ($this->_tpl_vars['layout']['id']): ?>
                    </table>
                  </td>
                </tr>
                <?php endif; ?>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3" style="padding: 10px;">
                    <?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['contract']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo '<button type="submit" name="saveButton1" class="button">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>

                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['contract'],'exclude' => 'is_portal')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </form>
      </div>
    </td>
  </tr>
</table>