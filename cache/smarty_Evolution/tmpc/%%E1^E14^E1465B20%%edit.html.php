<?php /* Smarty version 2.6.33, created on 2024-10-14 15:16:58
         compiled from /var/www/Nzoom-Evolution/_libs/modules/indexes/templates/edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/indexes/templates/edit.html', 10, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/indexes/templates/edit.html', 22, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/indexes/templates/edit.html', 19, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<form name="indexes" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['varindex']->get('id'); ?>
" />
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['varindex']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="subject"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['varindex']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['indexes_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="model"><?php echo smarty_function_help(array('label' => 'model'), $this);?>
</label></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap"><?php echo $this->_config[0]['vars']['indexes_model_contract']; ?>

            <input type="hidden" name="model" id="model" value="Contract" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_model_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('model_type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'model_type'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('options' => $this->_tpl_vars['contracts_types'],'value' => $this->_tpl_vars['varindex']->get('model_type'),'standalone' => true,'name' => 'model_type','width' => 200)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="data"<?php if ($this->_tpl_vars['messages']->getErrors('data')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'data'), $this);?>
</label></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            &nbsp;
          </td>
        </tr>
        <tr>
          <td colspan="3" class="nopadding">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_data.html", 'smarty_include_vars' => array('data' => $this->_tpl_vars['varindex']->get('data'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <?php echo smarty_function_help(array('label' => 'operation'), $this);?>
<br /><br />
            *&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;<?php echo $this->_config[0]['vars']['indexes_multi_legend']; ?>
<br />
            +&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;<?php echo $this->_config[0]['vars']['indexes_plus_leged']; ?>
<br />
            =&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;<?php echo $this->_config[0]['vars']['indexes_equal_legend']; ?>
<br />
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['varindex'],'exclude' => 'is_portal groups')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>