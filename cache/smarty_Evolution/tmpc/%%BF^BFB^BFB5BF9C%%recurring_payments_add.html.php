<?php /* Smarty version 2.6.33, created on 2025-01-08 12:03:37
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_add.html', 9, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_add.html', 22, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_add.html', 19, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div class="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<form name="finance" enctype="multipart/form-data" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" onsubmit="return gt2calc('submit_gt2');">
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_recurring_payment']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['finance_recurring_payment']->get('type'); ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_reason_type'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_recurring_payment']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_name'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_recurring_payment']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <?php ob_start(); ?><?php if ($this->_tpl_vars['finance_recurring_payment']->get('company_data') && $this->_tpl_vars['finance_recurring_payment']->get('company_data') != '0_0_0_0'): ?><?php echo $this->_tpl_vars['finance_recurring_payment']->get('company_data'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('company_data_value', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => false,'name' => 'company_data','label' => $this->_config[0]['vars']['finance_recurring_payments_company_data'],'index' => 0,'optgroups' => $this->_tpl_vars['companies_data'],'value' => $this->_tpl_vars['company_data_value'],'required' => 1,'really_required' => 1,'readonly' => 0,'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <tr>
          <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_customer'), $this);?>
</label></a></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_recurring_payment']->get('customer'),'value_code' => $this->_tpl_vars['finance_recurring_payment']->get('customer_code'),'value_name' => $this->_tpl_vars['finance_recurring_payment']->get('customer_name'),'readonly' => 0,'width' => 266,'standalone' => true,'label' => $this->_config[0]['vars']['finance_recurring_payments_customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['trademark']), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['finance_recurring_payment']->get('trademark'),'value_name' => $this->_tpl_vars['finance_recurring_payment']->get('trademark_name'),'readonly' => 0,'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['trademark'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_project'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_recurring_payment']->get('project'),'value_code' => $this->_tpl_vars['finance_recurring_payment']->get('project_code'),'value_name' => $this->_tpl_vars['finance_recurring_payment']->get('project_name'),'width' => 266,'standalone' => true,'label' => $this->_config[0]['vars']['finance_recurring_payments_project'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_description'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <textarea class="areabox" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['finance_recurring_payment']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
          </td>
        </tr>
        <tr id="employee_id_row">
          <td class="labelbox"><a name="error_employee1"><label for="employee1"<?php if ($this->_tpl_vars['messages']->getErrors('employee1')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_employee'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employee1','autocomplete_type' => 'customers','stop_customer_details' => 1,'autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['finance_recurring_payment']->get('employee1'),'value_name' => $this->_tpl_vars['finance_recurring_payment']->get('employee1_name'),'filters_array' => $this->_tpl_vars['autocomplete_employee_filters'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['finance_recurring_payments_employee'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_start_date"><label for="start_date"<?php if ($this->_tpl_vars['messages']->getErrors('start_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_start_date'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'start_date','label' => $this->_config[0]['vars']['finance_recurring_payments_start_date'],'help' => $this->_config[0]['vars']['help_finance_recurring_payments_start_date'],'value' => $this->_tpl_vars['finance_recurring_payment']->get('start_date'),'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_end_date"><label for="end_date"<?php if ($this->_tpl_vars['messages']->getErrors('end_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_end_date'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'end_date','label' => $this->_config[0]['vars']['recurring_payments_end_date'],'help' => $this->_config[0]['vars']['help_recurring_payments_end_date'],'value' => $this->_tpl_vars['finance_recurring_payment']->get('end_date'),'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_recurrence_type"><label for="recurrence_type"<?php if ($this->_tpl_vars['messages']->getErrors('recurrence_type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_recurrence_type'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <select name="recurrence_type" id="recurrence_type" class="selbox" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="changeOffset(this.value, 'offset')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_recurrence_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                            <?php $_from = $this->_tpl_vars['recurrence_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                <option value="<?php echo $this->_tpl_vars['option']['option_value']; ?>
"<?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['finance_recurring_payment']->get('recurrence_type')): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['option']['label']; ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_offset"><label for="offset"<?php if ($this->_tpl_vars['messages']->getErrors('offset')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_offset'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <select name="offset" id="offset" class="selbox small" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_offset'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                            <?php $_from = $this->_tpl_vars['offsets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                <option value="<?php echo $this->_tpl_vars['option']; ?>
"<?php if ($this->_tpl_vars['option'] == $this->_tpl_vars['finance_recurring_payment']->get('offset')): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['option']; ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_payment_days"><label for="payment_days"<?php if ($this->_tpl_vars['messages']->getErrors('payment_days')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_payment_days'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox small" name="payment_days" id="payment_days" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_recurring_payment']->get('payment_days'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_payment_days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_reason_editable"><label for="reason_editable"<?php if ($this->_tpl_vars['messages']->getErrors('reason_editable')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_reason_editable'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <input type="checkbox" name="reason_editable" id="reason_editable" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reason_editable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if ($this->_tpl_vars['finance_recurring_payment']->get('reason_editable')): ?> checked="checked"<?php endif; ?> />
          </td>
        </tr>
      <?php if ($this->_tpl_vars['finance_recurring_payment']->get('reason_type') == 'Finance_Incomes_Reason'): ?>
        <tr>
          <td class="labelbox"><a name="error_add_invoice"><label for="add_invoice"<?php if ($this->_tpl_vars['messages']->getErrors('add_invoice')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_add_invoice'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <input type="checkbox" name="add_invoice" id="add_invoice" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_add_invoice'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if ($this->_tpl_vars['finance_recurring_payment']->get('add_invoice')): ?> checked="checked"<?php endif; ?> onclick="if(this.checked) $('send_invoice').style.display=''; else $('send_invoice').style.display='none'" />
          </td>
        </tr>
        <tr id="send_invoice"<?php if (! $this->_tpl_vars['finance_recurring_payment']->get('add_invoice')): ?> style="display:none"<?php endif; ?>>
          <td class="labelbox"><a name="error_send_to_customer"><label for="send_to_customer"<?php if ($this->_tpl_vars['messages']->getErrors('send_to_customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_send_to_customer'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <input type="checkbox" name="send_to_customer" id="send_to_customer" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_send_to_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if ($this->_tpl_vars['finance_recurring_payment']->get('send_to_customer')): ?> checked="checked"<?php endif; ?> />
            <?php echo smarty_function_help(array('label' => 'recurring_payments_pattern'), $this);?>

            <select class="selbox" name="pattern" id="pattern" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_pattern'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
              <?php $_from = $this->_tpl_vars['patterns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?>
                <option value="<?php echo $this->_tpl_vars['pattern']->get('id'); ?>
"<?php if ($this->_tpl_vars['pattern']->get('id') == $this->_tpl_vars['finance_recurring_payment']->get('pattern')): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
                        <input type="hidden"  name="send_file" id="send_file" value="pdf" />
          </td>
        </tr>
      <?php endif; ?>
        <tr>
          <td class="labelbox"><a name="error_remind_before"><label for="remind_before"<?php if ($this->_tpl_vars['messages']->getErrors('remind_before')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'recurring_payments_remind_before'), $this);?>
</label></a></td>
          <td class="unirequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox small" name="remind_before" id="remind_before" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_recurring_payment']->get('remind_before'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_remind_before'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
        <tr>
          <td colspan="3" style="padding: 5px;">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_edit.html", 'smarty_include_vars' => array('model' => $this->_tpl_vars['finance_recurring_payment'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td colspan="3" style="padding: 15px;">
            <button type="submit" name="saveButton1" id="submit_gt2" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['finance_recurring_payment'],'exclude' => "groups, is_portal")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>