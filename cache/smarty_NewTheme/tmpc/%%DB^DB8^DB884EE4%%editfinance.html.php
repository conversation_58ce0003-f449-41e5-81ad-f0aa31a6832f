<?php /* Smarty version 2.6.33, created on 2024-10-15 17:18:39
         compiled from /var/www/Nzoom-Evolution/_libs/modules/contracts/templates/editfinance.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/editfinance.html', 14, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/editfinance.html', 23, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/editfinance.html', 27, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <div id="form_container" class="main_panel_container">

          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <form name="contracts" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['contract']->get('id'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <tr>
            <td colspan="3" class="t_bottom_border"></td>
          </tr>
          <tr>
            <td colspan="3" class="nopadding">
              <fieldset style="width: 500px; margin: 5px;">
                <legend><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add_new_element'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</legend>
                <table cellpadding="1" cellspacing="1" border="0" class="t_borderless">
                  <tr class="vtop">
                  <?php $this->assign('templates_types', $this->_tpl_vars['contract']->get('templates_types')); ?>
                    <td><?php if (! $this->_tpl_vars['templates_types']['hidden']): ?><label for="template_type"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['templates_types']['label'],'text_content' => $this->_tpl_vars['templates_types']['help']), $this);?>
</label><?php else: ?>&nbsp;<?php endif; ?></td>
                    <td>
                      <?php ob_start(); ?>$('add_finance').innerHTML='';addFinance(<?php echo $this->_tpl_vars['contract']->get('id'); ?>
, 'add_finance',$('template_type').value);<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('onchange_func', ob_get_contents());ob_end_clean(); ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['templates_types']['type']).".html", 'smarty_include_vars' => array('standalone' => true,'name' => 'template_type','var_id' => 'template_type','options' => $this->_tpl_vars['templates_types']['options'],'optgroups' => $this->_tpl_vars['templates_types']['optgroups'],'label' => $this->_tpl_vars['templates_types']['label'],'readonly' => $this->_tpl_vars['templates_types']['readonly'],'hidden' => $this->_tpl_vars['templates_types']['hidden'],'width' => 140,'onchange' => $this->_tpl_vars['onchange_func'],'origin' => 'finance_add')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </td>
                    <td align="right"><input type="image" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/download.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['configurator_group_save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['configurator_group_save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="addFinance(<?php echo $this->_tpl_vars['contract']->get('id'); ?>
, 'add_finance',$('template_type').value);return false;" class="pointer" /></td>
                  </tr>
                </table>
              </fieldset>
              <div id="add_finance"></div>
              <div id="list_finance">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_finance_list.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </div>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </form>
      </div>
    </td>
  </tr>
</table>