<?php /* Smarty version 2.6.33, created on 2024-10-15 10:20:11
         compiled from /var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 24, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 29, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 41, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 86, false),array('function', 'counter', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 36, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/types_list.html', 54, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;controller=types&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="contracts_type" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;controller=types" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['party']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['party']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_types_party'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['inheritance']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['inheritance']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_types_inheritance'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['count_contracts']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['count_contracts']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_types_count_contracts'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['contracts_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contracts_type']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['contracts_type']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['contracts_type']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['contracts_type']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['contracts_type']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php if (! $this->_tpl_vars['contracts_type']->checkPermissions('list')): ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="<?php echo $this->_tpl_vars['contracts_type']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td colspan="4" class="t_border dimmed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['contracts_type'],'disabled' => 'edit,delete,view')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['contracts_type']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['contracts_type']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
          <td class="t_border">
            <input onclick="sendIds(params = {
                                            the_element: this,
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            session_param: '<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
',
                                            total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                           });" 
                   type="checkbox"
                   name='items[]'
                   value="<?php echo $this->_tpl_vars['contracts_type']->get('id'); ?>
"
                   title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                   <?php if (@ in_array ( $this->_tpl_vars['contracts_type']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['contracts_type']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                     checked="checked"
                   <?php endif; ?> />
          </td>
          <td class="t_border hright"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;controller=types&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['contracts_type']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['contracts_type']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['party']['isSorted']; ?>
">
            <?php if ($this->_tpl_vars['contracts_type']->get('party') == 'principal'): ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_types_principal'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>

            <?php elseif ($this->_tpl_vars['contracts_type']->get('party') == 'executor'): ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_types_executor'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['inheritance']['isSorted']; ?>
">
            <?php if ($this->_tpl_vars['contracts_type']->get('inheritance') == 0): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['contracts_types_inheritance_primary'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php else: ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['contracts_types_inheritance_secondary'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['count_contracts']['isSorted']; ?>
 hright"><?php if ($this->_tpl_vars['contracts_type']->get('count_contracts') > 0): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;type=<?php echo $this->_tpl_vars['contracts_type']->get('id'); ?>
"><?php echo $this->_tpl_vars['contracts_type']->get('count_contracts'); ?>
</a><?php else: ?>0<?php endif; ?></td>
          <td class="hcenter" nowrap="nowrap">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['contracts_type'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php endif; ?>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="7"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('exclude' => 'multiedit','session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>