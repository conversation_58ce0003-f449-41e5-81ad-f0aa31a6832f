<?php /* Smarty version 2.6.33, created on 2024-03-13 18:34:14
         compiled from _timesheets_edit_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_timesheets_edit_panel.html', 11, false),array('modifier', 'default', '_timesheets_edit_panel.html', 65, false),array('function', 'help', '_timesheets_edit_panel.html', 18, false),)), $this); ?>

  <?php $this->assign('tid', $this->_tpl_vars['timesheet']->get('id')); ?>
  <table border="0" cellpadding="3" cellspacing="0" class="t_layout_table t_borderless">

<?php if ($this->_tpl_vars['statements']): ?>
  <?php if ($_REQUEST['inline_add']): ?>
    <tr>
      <td colspan="3" class="m_header_menu" style="border-bottom: 1px solid #DDDDDD; padding: 0; background: none;">
        <ul>
          <li><span<?php if (! $this->_tpl_vars['timesheet']->get('period_type') || $this->_tpl_vars['timesheet']->get('period_type') == 'period'): ?> class="selected"<?php endif; ?>><a onclick="toggleTimesheetPeriod(this)" id="period" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_by_period'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
          <li><span<?php if ($this->_tpl_vars['timesheet']->get('period_type') == 'dates'): ?> class="selected"<?php endif; ?>><a onclick="toggleTimesheetPeriod(this)" id="dates" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_by_dates'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
        </ul>
      </td>
    </tr>
  <?php elseif (! $this->_tpl_vars['timesheet']->get('task_id')): ?>
    <tr>
      <td class="labelbox"><label for="customer_autocomplete"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_customer']), $this);?>
</label></td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','width' => 222,'autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'clear','autocomplete_buttons_hide' => 'search','execute_after' => 'changeTimesheetsRecords','standalone' => true,'label' => $this->_config[0]['vars']['tasks_customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><label for="timesheet_records"<?php if ($this->_tpl_vars['messages']->getErrors('record')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_record']), $this);?>
</label></td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'width' => '200','name' => 'timesheet_records','onchange' => 'manageTimesheet(this);','value' => '','label' => $this->_config[0]['vars']['tasks_record'],'no_select_records_label' => $this->_config[0]['vars']['tasks_empty_records'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php endif; ?>
<?php else: ?>
    <tr>
      <td colspan="3" class="t_caption"><div class="t_caption_title"><?php if (! $this->_tpl_vars['tid']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_add_timesheet'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_edit_timesheet'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div></td>
    </tr>
  <?php if (! $this->_tpl_vars['tid']): ?>
    <tr>
      <td colspan="3">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_timesheets_configurator_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php endif; ?>
    <tr>
      <td colspan="3" class="m_header_menu" style="border-bottom: 1px solid #DDDDDD; padding: 0; background: none;">
        <ul>
          <li><span<?php if (! $this->_tpl_vars['timesheet']->get('period_type') || $this->_tpl_vars['timesheet']->get('period_type') == 'period'): ?> class="selected"<?php endif; ?>><a onclick="toggleTimesheetPeriod(this)" id="period" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_by_period'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
          <li><span<?php if ($this->_tpl_vars['timesheet']->get('period_type') == 'dates'): ?> class="selected"<?php endif; ?>><a onclick="toggleTimesheetPeriod(this)" id="dates" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_by_dates'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
        </ul>
        <input type="hidden" id="period_type" name="period_type" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['timesheet']->get('period_type'))) ? $this->_run_mod_handler('default', true, $_tmp, 'period') : smarty_modifier_default($_tmp, 'period')); ?>
" />
      </td>
    </tr>
<?php endif; ?>

    <!-- Timesheet By Period -->
    <?php ob_start(); ?><?php if ($this->_tpl_vars['timesheet']->get('period_type') == 'dates'): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('period_hidden', ob_get_contents());ob_end_clean(); ?>
    <?php $this->assign('startperiod_allowed_min_date', $this->_tpl_vars['timesheet']->getStartperiodAllowedMinDate()); ?>
    <?php $this->assign('event_start_date', $this->_tpl_vars['timesheet']->get('event_start_date')); ?>
        <?php $this->assign('ddbefore', ''); ?>
        <?php $this->assign('ddafter', '1'); ?>
        <?php if ($this->_tpl_vars['event_start_date'] != ''): ?>
                <?php $this->assign('ddbefore', $this->_tpl_vars['event_start_date']); ?>
        <?php $this->assign('ddafter', $this->_tpl_vars['event_start_date']); ?>
    <?php elseif (( empty ( $this->_tpl_vars['task'] ) || ! $this->_tpl_vars['task']->checkPermissions('editalltimesheets') ) && $this->_tpl_vars['startperiod_allowed_min_date'] != ''): ?>
                <?php $this->assign('ddbefore', $this->_tpl_vars['startperiod_allowed_min_date']); ?>
    <?php endif; ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'startperiod_period','label' => $this->_config[0]['vars']['tasks_timesheets_startperiod'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_startperiod_period'],'value' => $this->_tpl_vars['timesheet']->get('startperiod_period'),'required' => 1,'disallow_date_before' => $this->_tpl_vars['ddbefore'],'disallow_date_after' => $this->_tpl_vars['ddafter'],'hidden' => $this->_tpl_vars['period_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'endperiod_period','label' => $this->_config[0]['vars']['tasks_timesheets_endperiod'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_endperiod_period'],'value' => $this->_tpl_vars['timesheet']->get('endperiod_period'),'required' => 1,'disallow_date_before' => $this->_tpl_vars['ddbefore'],'disallow_date_after' => $this->_tpl_vars['ddafter'],'hidden' => $this->_tpl_vars['period_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'duration','label' => $this->_config[0]['vars']['tasks_timesheets_duration'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_duration'],'back_label' => $this->_config[0]['vars']['minutes'],'value' => $this->_tpl_vars['timesheet']->get('duration'),'required' => 1,'restrict' => 'insertOnlyDigits','custom_class' => 'small hright','hidden' => $this->_tpl_vars['period_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

    <!-- Timesheet By Dates and Time -->
    <?php ob_start(); ?><?php if ($this->_tpl_vars['timesheet']->get('period_type') != 'dates'): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('period_hidden', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'startperiod_dates','label' => $this->_config[0]['vars']['tasks_timesheets_startperiod'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_startperiod_dates'],'value' => $this->_tpl_vars['timesheet']->get('startperiod_dates'),'required' => 1,'disallow_date_before' => $this->_tpl_vars['ddbefore'],'disallow_date_after' => $this->_tpl_vars['ddafter'],'hidden' => $this->_tpl_vars['period_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'endperiod_dates','label' => $this->_config[0]['vars']['tasks_timesheets_endperiod'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_endperiod_dates'],'value' => $this->_tpl_vars['timesheet']->get('endperiod_dates'),'required' => 1,'disallow_date_before' => $this->_tpl_vars['ddbefore'],'disallow_date_after' => $this->_tpl_vars['ddafter'],'hidden' => $this->_tpl_vars['period_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'duration_billing','label' => $this->_config[0]['vars']['tasks_timesheets_duration_billing'],'help' => $this->_config[0]['vars']['help_tasks_timesheets_duration_billing'],'back_label' => $this->_config[0]['vars']['minutes'],'value' => $this->_tpl_vars['timesheet']->get('duration_billing'),'restrict' => 'insertOnlyDigits','custom_class' => 'small hright')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <tr>
      <td colspan="3">&nbsp;</td>
    </tr>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['timesheet']->get('user_id')): ?><?php echo $this->_tpl_vars['timesheet']->get('user_id'); ?>
<?php else: ?><?php echo $this->_tpl_vars['currentUser']->get('id'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('timesheet_user_id', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'user_id','options' => $this->_tpl_vars['user_id'],'value' => $this->_tpl_vars['timesheet_user_id'],'required' => 1,'really_required' => 1,'onchange' => 'manageTimesheet(this);','label' => $this->_config[0]['vars']['tasks_timesheets_completed_by'],'help' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_completed_by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['timesheet']->get('office')): ?><?php echo $this->_tpl_vars['timesheet']->get('office'); ?>
<?php else: ?><?php echo $this->_tpl_vars['currentUser']->get('office'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('timesheet_office', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'office','options' => $this->_tpl_vars['offices'],'value' => $this->_tpl_vars['timesheet_office'],'label' => $this->_config[0]['vars']['tasks_timesheets_office'],'help' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_office'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <tr<?php if (( $this->_tpl_vars['timesheet']->get('event_id') && ! $this->_tpl_vars['event_found'] || ! $this->_tpl_vars['events'] ) && ! $this->_tpl_vars['timesheet']->get('event_start_date')): ?> class="hidden"<?php endif; ?>>
      <td class="labelbox"><a name="error_event_id"></a><label for="event_id"<?php if ($this->_tpl_vars['messages']->getErrors('event_id')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_planned_time'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_planned_time'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></td>
      <td class="unrequired">&nbsp;</td>
      <td>
        <?php if ($this->_tpl_vars['timesheet']->get('event_id') && ! $this->_tpl_vars['event_found']): ?>
          <?php if ($this->_tpl_vars['timesheet']->get('event_start_date')): ?>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('event_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'plannedtime','value' => '1')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'event_id','value' => $this->_tpl_vars['timesheet']->get('event_id'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php else: ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'plannedtime','value' => '1')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'width' => '200','name' => 'event_id','options' => $this->_tpl_vars['events'],'value' => $this->_tpl_vars['timesheet']->get('event_id'),'onchange' => 'manageTimesheet(this);','label' => $this->_config[0]['vars']['tasks_timesheets_planned_time'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <span id="span_finish_event" class="<?php if (! $this->_tpl_vars['timesheet']->get('event_id')): ?>hidden<?php endif; ?>">
            <input type="checkbox" name="finish_event" id="finish_event" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_finish_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="margin: 0px 3px;"<?php if ($this->_tpl_vars['timesheet']->get('event_id') && $this->_tpl_vars['timesheet']->get('finish_event')): ?> checked="checked"<?php endif; ?> onclick="manageTimesheet(this);" /> <label for="finish_event"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_finish_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_finish_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'label_sufix' => ''), $this);?>
</label>
          </span>
        <?php endif; ?>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><a name="error_activity"></a><label for="activity"<?php if ($this->_tpl_vars['messages']->getErrors('activity')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_timesheets_activity'],'help_content' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_activity'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></td>
      <td class="unrequired">&nbsp;</td>
      <td>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'width' => '200','name' => 'activity','options' => $this->_tpl_vars['activities'],'value' => $this->_tpl_vars['timesheet']->get('activity'),'label' => $this->_config[0]['vars']['tasks_timesheets_activity'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php if (! $this->_tpl_vars['statements'] && ! $this->_tpl_vars['tid'] && $this->_tpl_vars['currentUser']->checkRights('events','add') && $this->_tpl_vars['task'] && $this->_tpl_vars['task']->checkPermissions('timesheet_add_event')): ?>
          <span id="span_add_event"<?php if ($this->_tpl_vars['timesheet']->get('event_id')): ?> class="hidden"<?php endif; ?>>
            <input type="checkbox" name="add_event" id="add_event" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_add_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="margin: 0px 3px;"<?php if ($this->_tpl_vars['timesheet']->get('add_event')): ?> checked="checked"<?php endif; ?> /> <label for="add_event"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_add_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_config[0]['vars']['help_tasks_timesheets_add_event'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'label_sufix' => ''), $this);?>
</label>
          </span>
        <?php endif; ?>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><a name="error_subject"></a><label for="subject"<?php if ($this->_tpl_vars['messages']->getErrors('subject')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_timesheets_subject']), $this);?>
</label></td>
      <td class="unrequired">&nbsp;</td>
      <td>
        <input type="text" class="txtbox doubled" name="subject" id="subject" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('subject'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
      </td>
    </tr>
    <tr>
      <td class="labelbox"><a name="error_content"></a><label for="content"<?php if ($this->_tpl_vars['messages']->getErrors('content')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_timesheets_content']), $this);?>
</label></td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td>
        <textarea class="areabox doubled higher" name="content" id="content" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('content'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
      </td>
    </tr>
    <?php if (! $this->_tpl_vars['statements'] && ! $this->_tpl_vars['tid'] && ! $this->_tpl_vars['has_unfinished_events'] && $this->_tpl_vars['task'] && $this->_tpl_vars['task']->checkPermissions('finishtaskfromtimesheet')): ?>
    <tr id="span_finish_task" class="<?php if ($this->_tpl_vars['last_event'] && ! ( $this->_tpl_vars['last_event'] == $this->_tpl_vars['timesheet']->get('event_id') && $this->_tpl_vars['timesheet']->get('finish_event') )): ?>hidden<?php endif; ?>">
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td>
        <input type="checkbox" name="finish_task" id="finish_task" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_finish_task'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['timesheet']->get('finish_task') && ! $this->_tpl_vars['has_unfinished_events'] && ( $this->_tpl_vars['timesheet']->get('finish_event') || ! $this->_tpl_vars['timesheet']->get('event_id') || $this->_tpl_vars['timesheet']->get('event_id') && ! $this->_tpl_vars['event_found'] )): ?> checked="checked"<?php endif; ?> /> <label for="finish_task"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_finish_task'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
        <?php if ($this->_tpl_vars['last_event']): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'last_event','value' => $this->_tpl_vars['last_event'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </td>
    </tr>
    <?php endif; ?>
    <tr>
      <td colspan="3">
        <?php if ($this->_tpl_vars['tid']): ?><input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['tid']; ?>
" /><?php endif; ?>
        <button type="submit" class="button" name="addTimeSheet" id="addTimeSheet"><?php if (! $this->_tpl_vars['tid']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></button><?php if ($this->_tpl_vars['statements']): ?><button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() { lb.deactivate(); }, this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php endif; ?>
      </td>
    </tr>
  </table>