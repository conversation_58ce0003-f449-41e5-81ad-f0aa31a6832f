<?php /* Smarty version 2.6.33, created on 2023-07-18 17:21:04
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Default/templates/_menu.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'regex_replace', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_menu.html', 6, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_menu.html', 6, false),array('modifier', 'replace', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_menu.html', 6, false),)), $this); ?>
        <ul id="main_menu<?php if ($this->_tpl_vars['postfix']): ?>_<?php echo $this->_tpl_vars['postfix']; ?>
<?php endif; ?>" class="zpHideOnLoad">
          <?php $_from = $this->_tpl_vars['menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['g'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['g']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['menu_option']):
        $this->_foreach['g']['iteration']++;
?>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['postfix']; ?>
_<?php echo $this->_foreach['g']['iteration']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('postfix', ob_get_contents());ob_end_clean(); ?>
            <li id="menu_item<?php echo $this->_tpl_vars['postfix']; ?>
"<?php if ($this->_tpl_vars['menu_option']['selected']): ?> class="menu-path"<?php endif; ?>>
              <img src="<?php echo $this->_tpl_vars['menu_option']['icon']; ?>
" width="14" height="14" alt="" border="0" />
              <a href="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['menu_option']['url'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('replace', true, $_tmp, '&amp;', '&') : smarty_modifier_replace($_tmp, '&amp;', '&')); ?>
"<?php if ($this->_tpl_vars['menu_option']['target']): ?> target="<?php echo $this->_tpl_vars['menu_option']['target']; ?>
"<?php endif; ?> title="<?php echo $this->_tpl_vars['menu_option']['legend']; ?>
"><?php echo $this->_tpl_vars['menu_option']['i18n']; ?>
</a>
              <?php if ($this->_tpl_vars['menu_option']['options']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_menu.html", 'smarty_include_vars' => array('menu' => $this->_tpl_vars['menu_option']['options'],'postfix' => $this->_tpl_vars['postfix'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php endif; ?>
            </li>
          <?php endforeach; endif; unset($_from); ?>
        </ul>