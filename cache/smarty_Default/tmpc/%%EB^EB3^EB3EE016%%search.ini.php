<?php $_config_vars = array (
  'add_filter' => 'Add new filter',
  'remove_filter' => 'Remove last filter',
  'add_sort' => 'Add sort definition',
  'remove_sort' => 'Remove last sort definitions',
  'absolute' => 'Absolute comparisons',
  'relative' => 'Relative comparisons',
  'and' => 'and',
  'or' => 'or',
  'filters_not_defined' => 'Search filters has not been defined',
  'sort_not_defined' => 'Sort definitions has not been defined',
  'basic_vars' => 'Basic variables',
  'additional_vars' => 'Additional variables',
  'befor' => 'before',
  'before' => 'before',
  'after' => 'after',
  'exactly_on' => 'exactly on',
  'different_from' => 'different from',
  'today' => 'today',
  'yesterday' => 'yesterday',
  'tomorrow' => 'tomorrow',
  'week_day' => 'day of the week',
  'month_x' => 'in month',
  'year_x' => 'in year',
  'day_x' => 'in day',
  'current_week' => 'current week',
  'current_month' => 'current month',
  'current_year' => 'current year',
  'before_x_dwm' => 'previous',
  'after_x_dwm' => 'next',
  'until_today' => 'until today',
  'until_yesterday' => 'until yesterday',
  'until_tomorrow' => 'until tomorrow',
  'until_x_dwm' => 'until',
  'after_today' => 'after today',
  'after_yesterday' => 'after yesterday',
  'after_tomorrow' => 'after tomorrow',
  'date_day' => 'day',
  'date_week' => 'week',
  'date_month' => 'month',
  'date_days' => 'days',
  'date_weeks' => 'weeks',
  'date_months' => 'months',
  'coincide' => 'coincides with',
  'contain' => 'contains',
  'not_contain' => 'not contains',
  'starts_with' => 'starts with',
  'ends_with' => 'ends with',
  'equal' => 'equals to',
  'not_equal' => 'not equals to',
  'greater' => 'greater than',
  'greater_equal' => 'greater or equal than',
  'less' => 'less than',
  'less_equal' => 'less or equal than',
  'assignment_responsible' => 'Supervisor',
  'assignment_observer' => 'Inform',
  'assignment_decision' => 'Decision',
  'assignment_owner' => 'Owner',
); ?>