<?php /* Smarty version 2.6.33, created on 2024-03-08 12:28:31
         compiled from /var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 2, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 41, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 86, false),array('modifier', 'nl2br', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 202, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 202, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 215, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 215, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 222, false),array('modifier', 'mb_count_characters', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 238, false),array('modifier', 'mb_html_substr', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 249, false),array('function', 'json', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 30, false),array('function', 'counter', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 71, false),array('function', 'array', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 72, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/resources/outlooks/customers/list_type_12_Users_341.html', 102, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<?php if ($this->_tpl_vars['subtitle']): ?><h2><?php echo ((is_array($_tmp=$this->_tpl_vars['subtitle'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2><?php endif; ?>

<table border="0" cellpadding="0" cellspacing="0">
  <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
    <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=filter&amp;<?php if ($this->_tpl_vars['relation']): ?>relation=<?php echo $this->_tpl_vars['relation']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['event']): ?>event=<?php echo $this->_tpl_vars['event']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['mynzoom_settings_table']): ?>mynzoom_settings_table=<?php echo $this->_tpl_vars['mynzoom_settings_table']; ?>
&amp;<?php endif; ?><?php if ($_GET['autocomplete_filter']): ?>autocomplete_filter=session&amp;<?php endif; ?><?php if ($_REQUEST['uniqid']): ?>uniqid=<?php echo $_REQUEST['uniqid']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['session_param']): ?>session_param=<?php echo $this->_tpl_vars['session_param']; ?>
&amp;<?php endif; ?>page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>

  <?php else: ?>
    <tr>
      <td class="pagemenu">
        <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['controller']; ?>
=<?php echo $this->_tpl_vars['action']; ?>
<?php if ($this->_tpl_vars['type'] && is_numeric ( $this->_tpl_vars['type'] )): ?>&amp;type=<?php echo $this->_tpl_vars['type']; ?>
<?php endif; ?>&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="customers" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>" method="post" enctype="multipart/form-data">
      <?php if ($this->_tpl_vars['action'] == 'filter' && $_REQUEST['autocomplete_filter']): ?>
        <?php $this->assign('uniqid', $_REQUEST['uniqid']); ?>
        <?php $this->assign('autocomplete_params', $_SESSION['autocomplete_params'][$this->_tpl_vars['uniqid']]); ?>
        <?php echo smarty_function_json(array('assign' => 'autocomplete_params_json','encode' => $this->_tpl_vars['autocomplete_params']), $this);?>

        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['autocomplete_params_json'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
      <?php endif; ?>
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php if (! $this->_tpl_vars['autocomplete_params'] || $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <?php $this->assign('hide_selection_stats', true); ?>
              <?php endif; ?>
            <?php else: ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['eik']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['eik']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['eik']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['eik'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_eik']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_eik'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['city']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['city']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['city']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['city'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_city']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_city'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['address']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['address']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['address']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['address'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_address']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_address'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['phone']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['phone']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['phone']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['phone'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_phone']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_phone'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['gsm']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['gsm']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['gsm']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['gsm'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_gsm']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_gsm'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['email']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['email']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['email']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['email'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_email']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_email'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['a__type_delivery']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['a__type_delivery']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['a__type_delivery']['link']; ?>
"<?php endif; ?>><?php echo $this->_tpl_vars['add_vars_labels']['12']['type_delivery']; ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['tags']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['tags']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['tags']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['tags'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_tags']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_tags'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['a__customer_branch']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['a__customer_branch']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['a__customer_branch']['link']; ?>
"<?php endif; ?>><?php echo $this->_tpl_vars['add_vars_labels']['12']['customer_branch']; ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['type']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['type']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['is_company']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['is_company']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php echo smarty_function_array(array('assign' => 'background_colors'), $this);?>

      <?php $_from = $this->_tpl_vars['customers']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['single']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php $this->assign('salutation', ''); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['single']->get('is_company') && $this->_tpl_vars['single']->get('salutation')): ?><?php echo ''; ?><?php $this->assign('layout_salutation', $this->_tpl_vars['single']->getLayoutsDetails('salutation')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['layout_salutation']['view']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'salutation_'; ?><?php echo $this->_tpl_vars['single']->get('salutation'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('salutation', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['salutation']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('salutation', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['salutation']; ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['single']->get('is_company')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['customers_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['customers_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' ('; ?><?php if ($this->_tpl_vars['single']->get('is_company')): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ')<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['single']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['single']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['single']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['single']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
      <div id="rf<?php echo $this->_tpl_vars['single']->get('id'); ?>
" style="display: none"><?php echo $this->_tpl_vars['salutation']; ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! $this->_tpl_vars['single']->get('is_company')): ?> <?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div>
      <?php if ($this->_tpl_vars['single']->modelName != 'Event' && ! $this->_tpl_vars['single']->checkPermissions('list')): ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td colspan="12" class="t_border dimmed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'],'disabled' => 'edit,delete,view')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php else: ?>
        <tr <?php if (! $this->_tpl_vars['background_style']): ?>class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['single']->isDefined('active') && ! $this->_tpl_vars['single']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['single']->get('archived_by')): ?> attention<?php endif; ?><?php if ($this->_tpl_vars['single']->get('deleted_by')): ?> t_deleted<?php endif; ?><?php if ($this->_tpl_vars['single']->get('annulled_by') || $this->_tpl_vars['single']->get('subtype_status') == 'failed'): ?> <?php if ($this->_tpl_vars['single']->modelName == 'Contract'): ?>strike<?php else: ?>t_strike<?php endif; ?><?php endif; ?><?php if ($this->_tpl_vars['single']->get('severity')): ?> <?php echo $this->_tpl_vars['single']->get('severity'); ?>
<?php endif; ?>"<?php else: ?>class="t_row<?php if ($this->_tpl_vars['single']->get('annulled_by') || $this->_tpl_vars['single']->get('subtype_status') == 'failed'): ?> t_strike<?php endif; ?><?php if ($this->_tpl_vars['single']->isDefined('active') && ! $this->_tpl_vars['single']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['single']->get('deleted_by')): ?> t_deleted<?php endif; ?>"<?php if ($this->_tpl_vars['single']->isDefined('active') && $this->_tpl_vars['single']->get('active') || ! $this->_tpl_vars['single']->isDefined('active')): ?> <?php echo $this->_tpl_vars['background_style']; ?>
<?php endif; ?><?php endif; ?>>
          <td class="t_border">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php if ($this->_tpl_vars['autocomplete_params'] && ! $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
                <input type="checkbox" name='items[]' value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="return clickOnce(this);" />
              <?php else: ?>
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {
                                                the_element: this,
                                                module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                                controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                                action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                                button_id: '<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall_1'
                                               });"
                       name='items[]'
                       value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['module'] == 'customers' && $this->_tpl_vars['relation']): ?>_<?php if ($this->_tpl_vars['single']->get('is_company')): ?>company<?php else: ?>person<?php endif; ?><?php endif; ?>"
                       title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php endif; ?>
            <?php else: ?>
              <input onclick="sendIds(params = {
                                              the_element: this,
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              session_param: '<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
',
                                              total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                             });"
                     type="checkbox"
                     name='items[]'
                     value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
"
                     title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                     <?php if (@ in_array ( $this->_tpl_vars['single']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['single']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                       checked="checked"
                     <?php endif; ?> />
            <?php endif; ?>
          </td>
          <td class="t_border hright" nowrap="nowrap">
            <?php if ($this->_tpl_vars['single']->get('files_count')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
&amp;<?php echo $this->_tpl_vars['controller']; ?>
<?php else: ?>&amp;<?php echo $this->_tpl_vars['module']; ?>
<?php endif; ?>=attachments&amp;attachments=<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>">
               <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                     onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>, '', 1<?php endif; ?>)"
                     onmouseout="mclosetime()" />
              </a>
            <?php endif; ?>
            <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['single']->get('id'); ?>
"><?php echo $this->_tpl_vars['salutation']; ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! $this->_tpl_vars['single']->get('is_company')): ?> <?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['eik']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('eik'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['city']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['address']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['phone']['isSorted']; ?>
<?php if (! $this->_tpl_vars['use_asterisk']): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>"<?php if (! $this->_tpl_vars['use_asterisk']): ?> <?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?>>
            <?php $_from = $this->_tpl_vars['single']->get('phone'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cdi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cdi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['phone']):
        $this->_foreach['cdi']['iteration']++;
?>
              <?php if ($this->_tpl_vars['use_asterisk'] && $this->_tpl_vars['phone']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => 'phone','number' => $this->_tpl_vars['phone'],'label' => $this->_config[0]['vars']['customers_phone'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['phone'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['cdi']['iteration'] == $this->_foreach['cdi']['total'])): ?><br /><?php endif; ?>
              <?php endif; ?>
            <?php endforeach; else: ?>
              &nbsp;
            <?php endif; unset($_from); ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['gsm']['isSorted']; ?>
<?php if (! $this->_tpl_vars['use_asterisk']): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>"<?php if (! $this->_tpl_vars['use_asterisk']): ?> <?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?>>
            <?php $_from = $this->_tpl_vars['single']->get('gsm'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cdi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cdi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['gsm']):
        $this->_foreach['cdi']['iteration']++;
?>
              <?php if ($this->_tpl_vars['use_asterisk'] && $this->_tpl_vars['gsm']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => 'gsm','number' => $this->_tpl_vars['gsm'],'label' => $this->_config[0]['vars']['customers_phone'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gsm'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['cdi']['iteration'] == $this->_foreach['cdi']['total'])): ?><br /><?php endif; ?>
              <?php endif; ?>
            <?php endforeach; else: ?>
              &nbsp;
            <?php endif; unset($_from); ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['email']['isSorted']; ?>
<?php if (! $this->_tpl_vars['single']->get('email')): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>"<?php if (! $this->_tpl_vars['single']->get('email')): ?> <?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?>>
            <?php if (is_array ( $this->_tpl_vars['single']->get('email') )): ?>
              <?php $_from = $this->_tpl_vars['single']->get('email'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cdi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cdi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['email']):
        $this->_foreach['cdi']['iteration']++;
?>
                <a href="mailto:<?php echo $this->_tpl_vars['email']; ?>
" target="_self"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php if (! ($this->_foreach['cdi']['iteration'] == $this->_foreach['cdi']['total'])): ?><br /><?php endif; ?>
              <?php endforeach; else: ?>
                &nbsp;
              <?php endif; unset($_from); ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
          <?php $this->assign('var_values', $this->_tpl_vars['single']->getSelectedAddVarCheckboxLabels('type_delivery')); ?>
          <?php ob_start(); ?><?php echo $this->_tpl_vars['add_vars_back_labels']['12']['type_delivery']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_back_label', ob_get_contents());ob_end_clean(); ?>
          <td class="t_border <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
>
            <?php $_from = $this->_tpl_vars['var_values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb_val'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb_val']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['checkbox_value']):
        $this->_foreach['cb_val']['iteration']++;
?>
              <?php if (trim ( $this->_tpl_vars['checkbox_value'] ) !== ''): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['checkbox_value'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

              <?php else: ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" style="margin-left: 5px;" />
              <?php endif; ?>
              <?php if (! ($this->_foreach['cb_val']['iteration'] == $this->_foreach['cb_val']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; else: ?>
              &nbsp;
            <?php endif; unset($_from); ?>
            <?php if ($this->_tpl_vars['var_values']): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var_back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
          </td>
          <?php echo ''; ?><?php if (preg_match ( '#^Finance_.*$#i' , $this->_tpl_vars['single']->modelName )): ?><?php echo ''; ?><?php $this->assign('_module', 'finance'); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '#^Finance_(.*)$#i', '$1') : smarty_modifier_regex_replace($_tmp, '#^Finance_(.*)$#i', '$1')))) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?><?php echo 's'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_controller', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?><?php echo 's'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_module', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_controller', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          <td class="t_border <?php echo $this->_tpl_vars['sort']['tags']['isSorted']; ?>
" <?php if ($this->_tpl_vars['single']->getModelTags() && $this->_tpl_vars['single']->get('available_tags_count') > 0 && $this->_tpl_vars['single']->checkPermissions('tags_view') && $this->_tpl_vars['single']->checkPermissions('tags_edit')): ?> onclick="changeTags(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
'<?php if ($this->_tpl_vars['redirect_to_url'] && $this->_tpl_vars['update_target']): ?>, '<?php echo $this->_tpl_vars['redirect_to_url']; ?>
' + ($$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page').length ? $$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page')[0].innerHTML : 1)<?php endif; ?>)" style="cursor: pointer;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tags_change'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php endif; ?>>
            <?php if ($this->_tpl_vars['single']->get('model_tags') && is_array ( $this->_tpl_vars['single']->get('model_tags') ) && count($this->_tpl_vars['single']->get('model_tags')) > 0 && $this->_tpl_vars['single']->checkPermissions('tags_view')): ?>
              <?php $_from = $this->_tpl_vars['single']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
                <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span><?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?><br /><?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
          <?php ob_start(); ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , 'customer_branch' )): ?>customer_branch<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?><?php echo $this->_tpl_vars['single']->getVarValue('customer_branch',0); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_value', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?><?php echo $this->_tpl_vars['add_vars_back_labels']['12']['customer_branch']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_back_label', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?><?php if (! $this->_tpl_vars['asterisk_contact'] && ! preg_match ( str_replace ( ' ' , '' , '#(< a\s|onclick=)#' ) , $this->_tpl_vars['var_value'] )): ?> <?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_row_link', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php if ($this->_tpl_vars['var_value']): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => ((is_array($_tmp=$this->_tpl_vars['asterisk_contact'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')),'number' => $this->_tpl_vars['var_value'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?><?php else: ?><?php echo $this->_tpl_vars['var_value']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?><?php if ($this->_tpl_vars['content'] !== ''): ?><?php echo $this->_tpl_vars['content']; ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var_back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php else: ?>&nbsp;<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content', ob_get_contents());ob_end_clean(); ?>
          <?php $this->assign('long_content', false); ?>
          <?php ob_start(); ?>dropdown<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_type', ob_get_contents());ob_end_clean(); ?>
          <?php if ($this->_tpl_vars['var_type'] == 'textarea' && ((is_array($_tmp=$this->_tpl_vars['content'])) ? $this->_run_mod_handler('mb_count_characters', true, $_tmp, true) : smarty_modifier_mb_count_characters($_tmp, true)) > 130): ?>
            <?php $this->assign('long_content', true); ?>
            <?php $this->assign('single_id', $this->_tpl_vars['single']->get('id')); ?>
            <?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['full_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['full_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="toggleContent(\'add_var_customer_branch\', '; ?><?php echo $this->_tpl_vars['single_id']; ?><?php echo ');" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('show_full', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['part_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['part_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="toggleContent(\'add_var_customer_branch\', '; ?><?php echo $this->_tpl_vars['single_id']; ?><?php echo ');" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('show_part', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

            <?php ob_start(); ?><div id="add_var_customer_branch_part_<?php echo $this->_tpl_vars['single_id']; ?>
"><span<?php echo $this->_tpl_vars['var_row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['content'])) ? $this->_run_mod_handler('mb_html_substr', true, $_tmp, 130, "...") : smarty_modifier_mb_html_substr($_tmp, 130, "...")))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>
</span><?php echo $this->_tpl_vars['show_full']; ?>
</div><div id="add_var_customer_branch_full_<?php echo $this->_tpl_vars['single_id']; ?>
" style="display: none;"><span<?php echo $this->_tpl_vars['var_row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['content'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>
</span><?php echo $this->_tpl_vars['show_part']; ?>
</div><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content', ob_get_contents());ob_end_clean(); ?>
          <?php else: ?>
            <?php ob_start(); ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['content'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content', ob_get_contents());ob_end_clean(); ?>
          <?php endif; ?>
          <td class="t_border <?php if (is_numeric ( $this->_tpl_vars['var_value'] )): ?> hright<?php endif; ?> <?php echo $this->_tpl_vars['sort']['a__customer_branch']['isSorted']; ?>
<?php if ($this->_tpl_vars['var_row_link']): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>" <?php if (! $this->_tpl_vars['long_content']): ?><?php echo $this->_tpl_vars['var_row_link']; ?>
<?php endif; ?>><?php echo $this->_tpl_vars['content']; ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['type']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['is_company']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php if ($this->_tpl_vars['single']->get('is_company')): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></td>

          <td class="hcenter" nowrap="nowrap">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'],'exclude' => "edit, view, delete")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="15"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="15"></td>
        </tr>
      </table>
      <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
        <br />
        <?php echo ''; ?><?php if ($_REQUEST['autocomplete_filter']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['autocomplete_params']['select_multiple']): ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: false'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: true'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php else: ?><?php echo '<button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo ' return confirmAction(\'link\', function(el) '; ?>{<?php echo ''; ?><?php if (! $this->_tpl_vars['relation']): ?><?php echo 'updateReferers(el.form, 0)'; ?><?php else: ?><?php echo 'updateCustomersRelations(el.form, \''; ?><?php echo $this->_tpl_vars['relation']; ?><?php echo '\', 0)'; ?><?php endif; ?><?php echo ';'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); '; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo ' return confirmAction(\'link\', function(el) '; ?>{<?php echo ''; ?><?php if (! $this->_tpl_vars['relation']): ?><?php echo 'updateReferers(el.form, 1)'; ?><?php else: ?><?php echo 'updateCustomersRelations(el.form, \''; ?><?php echo $this->_tpl_vars['relation']; ?><?php echo '\', 1)'; ?><?php endif; ?><?php echo ';'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); '; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?>

      <?php else: ?>
        <?php if (( '' )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_severity_legend.html", 'smarty_include_vars' => array('prefix' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
        <br />
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('exclude' => '','include' => 'multiprint,tags','session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <?php if ($this->_tpl_vars['background_colors']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_invoices_reasons_legend.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
</table>