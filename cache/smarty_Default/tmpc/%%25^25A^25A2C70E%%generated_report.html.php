<?php /* Smarty version 2.6.33, created on 2024-04-15 19:01:24
         compiled from /var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 22, false),array('modifier', 'nl2br', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 29, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 29, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 49, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 20, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/bgservice_requests/generated_report.html', 59, false),)), $this); ?>
  <h1><?php echo ((is_array($_tmp=$this->_tpl_vars['reports_additional_options']['table_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
  <table cellpadding="5" cellspacing="0" class="t_table t_list"<?php if (! ($this->_foreach['type_for']['iteration'] == $this->_foreach['type_for']['total'])): ?> style="margin-bottom: 20px;"<?php endif; ?>>
    <tr class="reports_title_row hcenter">
      <td class="t_border" style="vertical-align: middle;"><div style="width: 75px;"><?php echo ((is_array($_tmp=$this->_tpl_vars['reports_additional_options']['num_column_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 190px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 390px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_executor'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <?php if ($this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_REQUEST || $this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_TICKETS): ?>
        <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_added_by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <?php endif; ?>
      <?php if (! $this->_tpl_vars['reports_additional_options']['hide_deadline_column']): ?>
        <td class="t_border" style="vertical-align: middle;"><div style="width: 120px;"><?php if ($this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_REQUEST_CLIENT): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_request_client_deadline'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_request_deadline'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div></td>
      <?php endif; ?>
      <td class="t_border" style="vertical-align: middle;" colspan="2"><div style="width: 380px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_history'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 110px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_total_time'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td style="vertical-align: middle;"><div style="width: 60px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_comments'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <?php $_from = $this->_tpl_vars['reports_results']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['results'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['results']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['result']):
        $this->_foreach['results']['iteration']++;
?>
      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
        <td class="t_border" width="79">
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=view&amp;view=<?php echo $this->_tpl_vars['result']['id']; ?>
"><?php echo ((is_array($_tmp=@$this->_tpl_vars['result']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php if ($this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_REQUEST && $this->_tpl_vars['result']['custom_num']): ?>/<a href="<?php echo $this->_tpl_vars['result']['bugzilla_link']; ?>
"><?php echo ((is_array($_tmp=@$this->_tpl_vars['result']['custom_num'])) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php endif; ?>
        </td>
        <td class="t_border" width="194">
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['result']['customer']; ?>
"><?php echo ((is_array($_tmp=@$this->_tpl_vars['result']['customer_name'])) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
        </td>
        <td class="t_border" width="394">
          <strong><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</strong><br />
          <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <td class="t_border" width="144">
          <?php $_from = $this->_tpl_vars['result']['assigned']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['assig'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['assig']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignee']):
        $this->_foreach['assig']['iteration']++;
?>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['assignee'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php if (! ($this->_foreach['assig']['iteration'] == $this->_foreach['assig']['total'])): ?>
              <br />
            <?php endif; ?>
          <?php endforeach; else: ?>
            &nbsp;
          <?php endif; unset($_from); ?>
        </td>
        <?php if ($this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_REQUEST || $this->_tpl_vars['reports_additional_options']['show_type'] == @TYPE_TICKETS): ?>
          <td class="t_border" width="144">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['added_by_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        <?php endif; ?>
        <?php if (! $this->_tpl_vars['reports_additional_options']['hide_deadline_column']): ?>
          <td class="t_border" width="124">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['deadline'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        <?php endif; ?>
        <td width="164" style="white-space: nowrap;">
          <?php $this->assign('max_char_length', 28); ?>
          <?php $_from = $this->_tpl_vars['result']['history']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['hist'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['hist']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['history']):
        $this->_foreach['hist']['iteration']++;
?>
            <?php if (($this->_foreach['hist']['iteration'] == $this->_foreach['hist']['total'])): ?>
              <strong>
              <?php $this->assign('max_char_length', 24); ?>
            <?php endif; ?>
            <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['history']['current_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")),'break_words' => true,'length' => $this->_tpl_vars['max_char_length']), $this);?>

            <?php if (! ($this->_foreach['hist']['iteration'] == $this->_foreach['hist']['total'])): ?>
              <br />
            <?php else: ?>
              </strong>
            <?php endif; ?>
          <?php endforeach; else: ?>
            &nbsp;
          <?php endif; unset($_from); ?>
        </td>
        <td class="t_border" width="234">
          <?php $_from = $this->_tpl_vars['result']['history']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['hist'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['hist']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['history']):
        $this->_foreach['hist']['iteration']++;
?>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['history']['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php if ($this->_tpl_vars['history']['duration_lbl']): ?> (<?php echo ((is_array($_tmp=$this->_tpl_vars['history']['duration_lbl'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)<?php endif; ?>
            <?php if (! ($this->_foreach['hist']['iteration'] == $this->_foreach['hist']['total'])): ?>
              <br />
            <?php endif; ?>
          <?php endforeach; else: ?>
            &nbsp;
          <?php endif; unset($_from); ?>
        </td>
        <td class="t_border hright" width="94">
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['total_time_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <td class="hright" width="64" onmouseover="showCommunicationsInfo(this, 'comments', 'documents', 'documents', <?php echo $this->_tpl_vars['result']['id']; ?>
, '0')" onmouseout="mclosetime()" onclick="window.location.href='<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=communications&amp;communications=<?php echo $this->_tpl_vars['result']['id']; ?>
&amp;communication_type=comments'" style="cursor: pointer;">
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['result']['comments'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '0') : smarty_modifier_default($_tmp, '0')); ?>

        </td>
      </tr>
    <?php endforeach; else: ?>
      <tr>
        <td class="error" colspan="<?php echo ((is_array($_tmp=@$this->_tpl_vars['reports_additional_options']['total_colspan'])) ? $this->_run_mod_handler('default', true, $_tmp, '10') : smarty_modifier_default($_tmp, '10')); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
    <tr>
      <td class="t_footer" colspan="<?php echo ((is_array($_tmp=@$this->_tpl_vars['reports_additional_options']['total_colspan'])) ? $this->_run_mod_handler('default', true, $_tmp, '10') : smarty_modifier_default($_tmp, '10')); ?>
"></td>
    </tr>
  </table>