<?php /* Smarty version 2.6.33, created on 2023-07-19 18:28:51
         compiled from /var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 18, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 24, false),array('modifier', 'string_format', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 96, false),array('modifier', 'round', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 185, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 294, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 396, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/edit.html', 30, false),)), $this); ?>
﻿<script type='text/javascript'>
  var func1 = function() {initTree('groups')}
  var func2 = function() {initTree('departments')}
  Event.observe(window, 'load', func1);
  Event.observe(window, 'load', func2);
</script>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<form name="users" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" onsubmit="return checkUniqueEmail(this);">
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['user']->get('id'); ?>
" />
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['user']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_main_data_switch"><div class="switch_<?php if ($_COOKIE['user_main_data_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_main_data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr id="user_main_data"<?php if ($_COOKIE['user_main_data_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox"><a name="error_salutation"><label for="salutation"<?php if ($this->_tpl_vars['messages']->getErrors('salutation')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'salutation'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'salutation','options' => $this->_tpl_vars['salutations'],'value' => $this->_tpl_vars['user']->get('salutation'),'width' => 80,'standalone' => true,'label' => $this->_config[0]['vars']['users_salutation'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_firstname"><label for="firstname"<?php if ($this->_tpl_vars['messages']->getErrors('firstname')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'firstname'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="firstname" id="firstname" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('firstname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_firstname'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_lastname"><label for="lastname"<?php if ($this->_tpl_vars['messages']->getErrors('lastname')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'lastname'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="lastname" id="lastname" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_lastname'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_code"><label for="code"<?php if ($this->_tpl_vars['messages']->getErrors('code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'code'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_code'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_invoice_code"><label for="invoice_code"<?php if ($this->_tpl_vars['messages']->getErrors('invoice_code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'invoice_code'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="invoice_code" id="invoice_code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('invoice_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_invoice_code'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_email"><label for="email"<?php if ($this->_tpl_vars['messages']->getErrors('email')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'email'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="email" id="email" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('email'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_ldap"><label for="auth"<?php if ($this->_tpl_vars['messages']->getErrors('auth')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'auth'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="radio" name="auth" id="auth_standard" value="standard"<?php if ($this->_tpl_vars['user']->get('auth') != 'ldap'): ?> checked<?php endif; ?> onclick="togglePasswords()" /><label for="auth_standard"><?php echo $this->_config[0]['vars']['users_auth_standard']; ?>
</label>
                  <input type="radio" name="auth" id="auth_ldap" value="ldap"<?php if ($this->_tpl_vars['user']->get('auth') == 'ldap'): ?> checked<?php endif; ?> onclick="togglePasswords()" /><label for="auth_ldap"><?php echo $this->_config[0]['vars']['users_auth_ldap']; ?>
</label>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_username"><label for="username"<?php if ($this->_tpl_vars['messages']->getErrors('username')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'username'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" name="username" id="username" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('username'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_username'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr class="passwords"<?php if ($this->_tpl_vars['user']->get('auth') == 'ldap'): ?> style="display: none"<?php endif; ?>>
                <td class="labelbox"><a name="error_password"><label for="password"<?php if ($this->_tpl_vars['messages']->getErrors('password')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'password'), $this);?>
</label></a></td>
                <td class="required"><?php if ($this->_tpl_vars['action'] == 'add'): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td nowrap="nowrap">
                  <input type="password" class="txtbox" name="password" id="password" value="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_password'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onkeyup="checkPasswordStrength(this, <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['strong_passwords'])) ? $this->_run_mod_handler('string_format', true, $_tmp, '%d') : smarty_modifier_string_format($_tmp, '%d')); ?>
, 'toggle')" style="float: left" autocomplete="off" />
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
error.png" id="password_validation" width="14" height="14" alt="" style="visibility:hidden; padding-left: 5px" />
                </td>
                <td nowrap="nowrap" rowspan="2" valign="bottom" style="padding-left: 10px; visibility: hidden" id="pass_strength">
                  <?php if ($this->_config[0]['vars']['strong_passwords']): ?><?php echo smarty_function_help(array('label' => 'password_strength_strong'), $this);?>
<?php else: ?><?php echo smarty_function_help(array('label' => 'password_strength'), $this);?>
<?php endif; ?>
                  <span id="password_strength_text"></span><br />
                  <div class="password_strength_container">
                      <div id="password_strength"></div>
                  </div>
                </td>
              </tr>
              <tr class="passwords"<?php if ($this->_tpl_vars['user']->get('auth') == 'ldap'): ?> style="display: none"<?php endif; ?>>
                <td class="labelbox"><a name="error_password2"><label for="password2"<?php if ($this->_tpl_vars['messages']->getErrors('password')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'password2'), $this);?>
</label></a></td>
                <td class="required"><?php if ($this->_tpl_vars['action'] == 'add'): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td nowrap="nowrap">
                  <input type="password" class="txtbox" name="password2" id="password2" value="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_password2'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onkeyup="checkPasswordsMatch(this, <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['strong_passwords'])) ? $this->_run_mod_handler('string_format', true, $_tmp, '%d') : smarty_modifier_string_format($_tmp, '%d')); ?>
, 'toggle')" style="float: left" autocomplete="off" />
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
error.png" id="password2_validation" width="14" height="14" alt="" style="visibility:hidden; padding-left: 5px" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_role"><label for="role"<?php if ($this->_tpl_vars['messages']->getErrors('role')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'role'), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'role','options' => $this->_tpl_vars['roles_dropdown'],'width' => 200,'value' => $this->_tpl_vars['user']->get('role'),'required' => 1,'really_required' => 1,'label' => $this->_config[0]['vars']['users_role'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="permissions_origin"><?php echo smarty_function_help(array('label' => 'permissions_origin'), $this);?>
</label></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <select name="permissions_origin" class="selbox" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_permissions_origin'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                    <option value="role"<?php if ($this->_tpl_vars['user']->get('permissions_origin') != 'self'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['users_role']; ?>
</option>
                    <option value="self"<?php if ($this->_tpl_vars['user']->get('permissions_origin') == 'self'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['users_self']; ?>
</option>
                  </select>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_system_data_switch"><div class="switch_<?php if ($_COOKIE['user_system_data_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_system_data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr id="user_system_data"<?php if ($_COOKIE['user_system_data_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox"><a name="error_employee"><label for="employee"<?php if ($this->_tpl_vars['messages']->getErrors('employee')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'employee'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employee','autocomplete_type' => 'customers','stop_customer_details' => 1,'autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['user']->get('employee'),'value_name' => $this->_tpl_vars['user']->get('employee_name'),'filters_array' => $this->_tpl_vars['autocomplete_employee_filters'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['users_employee'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_office"><label for="office"<?php if ($this->_tpl_vars['messages']->getErrors('office')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'office'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'office','options' => $this->_tpl_vars['offices'],'value' => $this->_tpl_vars['user']->get('office'),'width' => '200','standalone' => true,'onchange' => "changeOfficeWarehousesOptions(this)",'label' => $this->_config[0]['vars']['users_office'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_working_hours"><label for="working_hours"<?php if ($this->_tpl_vars['messages']->getErrors('working_hours')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'working_hours'), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'working_hours','value' => round($this->_tpl_vars['user']->get('working_hours'), 2),'restrict' => 'insertOnlyFloats','custom_class' => 'hright small','standalone' => true,'label' => $this->_config[0]['vars']['users_working_hours'],'back_label' => $this->_config[0]['vars']['hours'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'customer'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['user']->get('customer'),'value_code' => $this->_tpl_vars['user']->get('customer_code'),'value_name' => $this->_tpl_vars['user']->get('customer_name'),'autocomplete' => $this->_tpl_vars['customer_autocomplete'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['users_customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <span id="branch_container" style="display: <?php if ($this->_tpl_vars['user']->get('customer_is_company')): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['user']->getBranchLabels('users_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['user']->getBranchLabels('help_users_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                      <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox<?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?> missing_records<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->getBranchLabels('users_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                        <?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?>
                          <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->getBranchLabels('empty_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <?php else: ?>
                          <?php $_from = $this->_tpl_vars['customer_branches']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['customer_branch']):
?>
                            <?php if (( ! $this->_tpl_vars['customer_branch']->isDeleted() && $this->_tpl_vars['customer_branch']->isActivated() ) || $this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['user']->get('branch')): ?>
                            <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer_branch']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['user']->get('branch')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['customer_branch']->isDeleted() || ! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['customer_branch']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
                            <?php endif; ?>
                          <?php endforeach; endif; unset($_from); ?>
                        <?php endif; ?>
                      </select>
                  </span>
                  <span id="contact_person_container" style="display: <?php if ($this->_tpl_vars['user']->get('customer_is_company')): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['users_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['user']->getBranchLabels('help_users_contact_person'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                      <select name="contact_person" id="contact_person" class="selbox<?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?> missing_records<?php elseif (! $this->_tpl_vars['user']->get('contact_person')): ?> undefined<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                        <?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?>
                          <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['empty_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <?php else: ?>
                          <option value="" class="undefined"<?php if (! $this->_tpl_vars['user']->get('contact_person')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php $_from = $this->_tpl_vars['contact_persons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact_person']):
?>
                            <?php if (( ! $this->_tpl_vars['contact_person']->isDeleted() && $this->_tpl_vars['contact_person']->isActivated() ) || $this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['user']->get('contact_person')): ?>
                            <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contact_person']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['user']->get('contact_person')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['contact_person']->isDeleted() || ! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if ($this->_tpl_vars['contact_person']->get('lastname')): ?> <?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('lastname'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></option>
                            <?php endif; ?>
                          <?php endforeach; endif; unset($_from); ?>
                        <?php endif; ?>
                      </select>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['default_trademark'],'text_content' => $this->_config[0]['vars']['help_default_trademark']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'clear','value' => $this->_tpl_vars['user']->get('trademark'),'value_name' => $this->_tpl_vars['user']->get('trademark_name'),'autocomplete' => $this->_tpl_vars['trademark_autocomplete'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['default_trademark'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'project'), $this);?>
</label></a></td>
                <td class="required"><?php if ($this->_tpl_vars['project_required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['user']->get('project'),'value_code' => $this->_tpl_vars['user']->get('project_code'),'value_name' => $this->_tpl_vars['user']->get('project_name'),'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['users_project'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <span class="help" <?php echo smarty_function_help(array('label' => 'phase','popup_only' => '1'), $this);?>
>&nbsp;</span>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'phase','options' => $this->_tpl_vars['phases'],'no_select_records_label' => $this->_config[0]['vars']['project_phase'],'first_option_label' => $this->_config[0]['vars']['project_phase'],'width' => 100,'value' => $this->_tpl_vars['user']->get('phase'),'label' => $this->_config[0]['vars']['users_phase'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_default_group"><label for="default_group"<?php if ($this->_tpl_vars['messages']->getErrors('default_group')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_group'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <select class="selbox<?php if (! $this->_tpl_vars['user']->get('default_group')): ?> undefined<?php endif; ?>" name="default_group" id="default_group" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_default_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
                    <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                  <?php $_from = $this->_tpl_vars['groups_tree']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                    <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['user']->get('default_group')): ?>
                    <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['user']->get('default_group')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                  </select>
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_default_department"><label for="default_department"<?php if ($this->_tpl_vars['messages']->getErrors('default_department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_department'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <select class="selbox<?php if (! $this->_tpl_vars['user']->get('default_department')): ?> undefined<?php endif; ?>" name="default_department" id="default_department" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_default_department'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
                    <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                  <?php $_from = $this->_tpl_vars['departments_tree']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                    <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['user']->get('default_department')): ?>
                    <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['user']->get('default_department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                  </select>
                </td>
              </tr>
              <?php if ($this->_tpl_vars['companies']): ?>
              <tr>
                <td class="labelbox"><a name="error_default_company"><label for="default_company"<?php if ($this->_tpl_vars['messages']->getErrors('default_company')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_company'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'default_company','options' => $this->_tpl_vars['companies'],'value' => $this->_tpl_vars['user']->get('default_company'),'width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['users_default_company'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php endif; ?>
              <?php if ($this->_tpl_vars['cashboxes']): ?>
              <tr>
                <td class="labelbox"><a name="error_default_cashbox"><label for="default_cashbox"<?php if ($this->_tpl_vars['messages']->getErrors('default_cashbox')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_cashbox'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'default_cashbox','options' => $this->_tpl_vars['cashboxes'],'value' => $this->_tpl_vars['user']->get('default_cashbox'),'width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['users_default_cashbox'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php endif; ?>
              <?php if ($this->_tpl_vars['bank_accounts']): ?>
              <tr>
                <td class="labelbox"><a name="error_default_bank_account"><label for="default_bank_account"<?php if ($this->_tpl_vars['messages']->getErrors('default_bank_account')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_bank_account'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'default_bank_account','options' => $this->_tpl_vars['bank_accounts'],'value' => $this->_tpl_vars['user']->get('default_bank_account'),'width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['users_default_bank_account'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php endif; ?>
              <?php if (isset ( $this->_tpl_vars['warehouses'] )): ?>
                <tr>
                  <td class="labelbox"><a name="error_default_warehouse"><label for="default_warehouse"<?php if ($this->_tpl_vars['messages']->getErrors('default_warehouse')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'default_warehouse'), $this);?>
</label></a></td>
                  <td>&nbsp;</td>
                  <td nowrap="nowrap">
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'default_warehouse','options' => $this->_tpl_vars['warehouses'],'value' => $this->_tpl_vars['user']->get('default_warehouse'),'width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['users_default_warehouse'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
              <?php endif; ?>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_login_data_switch"><div class="switch_<?php if ($_COOKIE['user_login_data_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_login_data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr id="user_login_data"<?php if ($_COOKIE['user_login_data_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox"><label for="last_login"><?php echo smarty_function_help(array('label' => 'last_login'), $this);?>
</label></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php if ($this->_tpl_vars['user']->get('last_login') != '0000-00-00 00:00:00'): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['user']->get('last_login'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_no_login_yet'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                  <input type="hidden" name="last_login" id="last_login" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('last_login'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="remote_addr"><?php echo smarty_function_help(array('label' => 'remote_addr'), $this);?>
</label></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['user']->get('remote_addr'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['users_no_login_yet']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['users_no_login_yet'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <input type="hidden" name="remote_addr" id="remote_addr" value="<?php echo $this->_tpl_vars['user']->get('remote_addr'); ?>
" />
                </td>
              </tr>
            </table>
          </td>
        </tr>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_departments.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_groups.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <tr>
          <td colspan="3" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_email_data_switch"><div class="switch_<?php if ($_COOKIE['user_login_data_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_email_data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr id="user_email_data"<?php if ($_COOKIE['user_email_data_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
          <td id="mail_content" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0" width="100%">
              <tr>
                <td class="labelbox"><a name="error_display_name"><label for="display_name"<?php if ($this->_tpl_vars['messages']->getErrors('display_name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'display_name'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox doubled" name="display_name" id="display_name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['user']->get('display_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_display_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><a name="error_signature"><label for="signature"<?php if ($this->_tpl_vars['messages']->getErrors('signature')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'signature'), $this);?>
</label></a></td>
                <td class="required">&nbsp;</td>
                <td nowrap="nowrap">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="3">
                <?php echo $this->_tpl_vars['editor_content']; ?>

                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['user'],'exclude' => 'groups')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>