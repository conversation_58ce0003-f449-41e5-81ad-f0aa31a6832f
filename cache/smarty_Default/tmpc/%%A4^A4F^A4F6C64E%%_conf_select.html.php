<?php /* Smarty version 2.6.33, created on 2024-02-23 11:05:32
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Default/templates/_conf_select.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_conf_select.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_conf_select.html', 6, false),)), $this); ?>
          <select class="selbox<?php if (! $this->_tpl_vars['available_action']['options']['default_task_type_config']): ?> undefined<?php endif; ?>" name="configurator" id="configurator<?php if ($this->_tpl_vars['custom_id']): ?>_<?php endif; ?>" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_configurator_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);"<?php if (! $this->_tpl_vars['available_action']['options']['configTaskPatterns']): ?> style="display:none"<?php endif; ?>>
            <option value="" class="undefined"<?php if (! $this->_tpl_vars['available_action']['options']['default_task_type_config']): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_configurator_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
          <?php $_from = $this->_tpl_vars['available_action']['options']['configTaskPatterns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optlabel'] => $this->_tpl_vars['optgroup']):
?>
                <optgroup label="<?php echo ((is_array($_tmp=$this->_tpl_vars['optlabel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
          <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['available_action']['options']['default_task_type_config'] == $this->_tpl_vars['option']['option_value']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
          <?php endforeach; endif; unset($_from); ?>
                </optgroup>
          <?php endforeach; endif; unset($_from); ?>
          </select>