<?php /* Smarty version 2.6.33, created on 2025-03-14 11:58:20
         compiled from /var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 2, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 41, false),array('modifier', 'numerate', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 77, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 80, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 203, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 203, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 210, false),array('modifier', 'lower', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 225, false),array('function', 'json', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 30, false),array('function', 'counter', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 72, false),array('function', 'array', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 73, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 111, false),array('function', 'popup', '/var/www/Nzoom-Evolution/resources/outlooks/documents/list_type_121_Users_270.html', 174, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<?php if ($this->_tpl_vars['subtitle']): ?><h2><?php echo ((is_array($_tmp=$this->_tpl_vars['subtitle'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2><?php endif; ?>

<table border="0" cellpadding="0" cellspacing="0">
  <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
    <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=filter&amp;<?php if ($this->_tpl_vars['generate_system_task']): ?>generate_system_task=<?php echo $this->_tpl_vars['generate_system_task']; ?>
&amp;<?php endif; ?><?php if ($_GET['autocomplete_filter']): ?>autocomplete_filter=session&amp;<?php endif; ?><?php if ($_REQUEST['uniqid']): ?>uniqid=<?php echo $_REQUEST['uniqid']; ?>
&amp;<?php endif; ?>page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>

  <?php else: ?>
    <tr>
      <td class="pagemenu">
        <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['controller']; ?>
=<?php echo $this->_tpl_vars['action']; ?>
<?php if ($this->_tpl_vars['type'] && is_numeric ( $this->_tpl_vars['type'] )): ?>&amp;type=<?php echo $this->_tpl_vars['type']; ?>
<?php endif; ?>&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="documents" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>" method="post" enctype="multipart/form-data">
      <?php if ($this->_tpl_vars['action'] == 'filter' && $_REQUEST['autocomplete_filter']): ?>
        <?php $this->assign('uniqid', $_REQUEST['uniqid']); ?>
        <?php $this->assign('autocomplete_params', $_SESSION['autocomplete_params'][$this->_tpl_vars['uniqid']]); ?>
        <?php echo smarty_function_json(array('assign' => 'autocomplete_params_json','encode' => $this->_tpl_vars['autocomplete_params']), $this);?>

        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['autocomplete_params_json'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
      <?php endif; ?>
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php if (! $this->_tpl_vars['autocomplete_params'] || $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <?php $this->assign('hide_selection_stats', true); ?>
              <?php endif; ?>
            <?php else: ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['full_num']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['full_num']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_full_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_full_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['customer']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['customer']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['customer'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_customer']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_customer'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['status']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['status']['link']; ?>
"><?php if ('documents' == 'projects'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status_phase'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['status'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_status']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_status'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['tags']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['tags']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['tags']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['tags'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_tags']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_tags'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['added']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['added']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['added'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['added']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['added'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['added_by']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['added_by']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['added_by']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['added_by'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_added_by']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_added_by'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['emails']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['emails']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['emails']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['emails'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_emails']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_emails'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['owner']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['owner']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['owner']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['owner'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_owner']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_owner'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['history_activity']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['history_activity']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['history_activity']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['history_activity'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_history_activity']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_history_activity'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['comments']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['comments']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['comments']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['comments'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_comments']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_comments'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['validity_term']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['validity_term']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['validity_term']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['validity_term'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_validity_term']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_validity_term'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
          <th class="t_caption t_border <?php echo $this->_tpl_vars['sort']['deadline']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title"<?php if ($this->_tpl_vars['sort']['deadline']['link']): ?> onclick="<?php echo $this->_tpl_vars['sort']['deadline']['link']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['deadline'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_deadline']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_deadline'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php echo smarty_function_array(array('assign' => 'background_colors'), $this);?>

      <?php $_from = $this->_tpl_vars['documents']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['single']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong><u>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_full_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_full_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</u></strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('full_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['single']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['single']->get('direction'))); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['single']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['single']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['single']->get('archived_by')): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['archived'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('archived'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['single']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['single']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['single']->get('status') == 'opened'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_opened']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['single']->get('status') == 'locked'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_locked']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['single']->get('status') == 'closed'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_closed']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['single']->get('substatus_name')): ?><?php echo '<br />'; ?><?php echo $this->_config[0]['vars']['help_documents_substatus']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['single']->get('substatus_name'); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_status', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
      <div id="rf<?php echo $this->_tpl_vars['single']->get('id'); ?>
" style="display: none"><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['single']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['single']->get('direction'))); ?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</div>
      <?php if ($this->_tpl_vars['single']->modelName != 'Event' && ! $this->_tpl_vars['single']->checkPermissions('list')): ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td colspan="13" class="t_border dimmed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'],'disabled' => 'edit,delete,view')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php else: ?>
        <tr <?php if (! $this->_tpl_vars['background_style']): ?>class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['single']->isDefined('active') && ! $this->_tpl_vars['single']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['single']->get('archived_by')): ?> attention<?php endif; ?><?php if ($this->_tpl_vars['single']->get('deleted_by')): ?> t_deleted<?php endif; ?><?php if ($this->_tpl_vars['single']->get('annulled_by') || $this->_tpl_vars['single']->get('subtype_status') == 'failed'): ?> <?php if ($this->_tpl_vars['single']->modelName == 'Contract'): ?>strike<?php else: ?>t_strike<?php endif; ?><?php endif; ?><?php if ($this->_tpl_vars['single']->get('severity')): ?> <?php echo $this->_tpl_vars['single']->get('severity'); ?>
<?php endif; ?>"<?php else: ?>class="t_row<?php if ($this->_tpl_vars['single']->get('annulled_by') || $this->_tpl_vars['single']->get('subtype_status') == 'failed'): ?> t_strike<?php endif; ?><?php if ($this->_tpl_vars['single']->isDefined('active') && ! $this->_tpl_vars['single']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['single']->get('deleted_by')): ?> t_deleted<?php endif; ?>"<?php if ($this->_tpl_vars['single']->isDefined('active') && $this->_tpl_vars['single']->get('active') || ! $this->_tpl_vars['single']->isDefined('active')): ?> <?php echo $this->_tpl_vars['background_style']; ?>
<?php endif; ?><?php endif; ?>>
          <td class="t_border">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php if ($this->_tpl_vars['autocomplete_params'] && ! $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
                <input type="checkbox" name='items[]' value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="return clickOnce(this);" />
              <?php else: ?>
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {
                                                the_element: this,
                                                module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                                controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                                action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                                button_id: '<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall_1'
                                               });"
                       name='items[]'
                       value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['module'] == 'customers' && $this->_tpl_vars['relation']): ?>_<?php if ($this->_tpl_vars['single']->get('is_company')): ?>company<?php else: ?>person<?php endif; ?><?php endif; ?>"
                       title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php endif; ?>
            <?php else: ?>
              <input onclick="sendIds(params = {
                                              the_element: this,
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              session_param: '<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
',
                                              total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                             });"
                     type="checkbox"
                     name='items[]'
                     value="<?php echo $this->_tpl_vars['single']->get('id'); ?>
"
                     title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                     <?php if (@ in_array ( $this->_tpl_vars['single']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['single']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                       checked="checked"
                     <?php endif; ?> />
            <?php endif; ?>
          </td>
          <td class="t_border hright" nowrap="nowrap">
            <?php if ($this->_tpl_vars['single']->get('files_count')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
&amp;<?php echo $this->_tpl_vars['controller']; ?>
<?php else: ?>&amp;<?php echo $this->_tpl_vars['module']; ?>
<?php endif; ?>=attachments&amp;attachments=<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>">
               <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                     onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>, '', 1<?php endif; ?>)"
                     onmouseout="mclosetime()" />
              </a>
            <?php endif; ?>
            <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['full_num']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['single']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['single']->get('direction'))); ?>
</a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['customer']['isSorted']; ?>
"><?php if ($this->_tpl_vars['single']->get('customer')): ?><a href="<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['single']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php else: ?>&nbsp;<?php endif; ?></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['status']['isSorted']; ?>
" nowrap="nowrap">
          <?php ob_start(); ?>
            <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['help_documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
<?php if ($this->_tpl_vars['single']->checkPermissions('setstatus')): ?> onclick="changeStatus(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, 'documents')" style="cursor:pointer;"<?php endif; ?>
          <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_and_onclick', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?>
            <?php if ($this->_tpl_vars['single']->get('status') != 'closed' && $this->_tpl_vars['single']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['single']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
              <?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
            <?php endif; ?>
            <?php if ($this->_tpl_vars['single']->get('status') != 'closed' && $this->_tpl_vars['single']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['single']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
              <?php echo $this->_tpl_vars['documents_expired']; ?>
 <?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
            <?php endif; ?>
          <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
          <?php if ($this->_tpl_vars['single']->get('status') != 'closed' && ( ( $this->_tpl_vars['single']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['single']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) || ( $this->_tpl_vars['single']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['single']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) )): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
 />
          <?php endif; ?>
          <?php if ($this->_tpl_vars['single']->get('substatus_name')): ?>
            <?php if ($this->_tpl_vars['single']->get('icon_name')): ?>
              <img src="<?php echo @PH_DOCUMENTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['single']->get('icon_name'); ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['single']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php endif; ?>
            <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_tpl_vars['single']->get('substatus_name'); ?>
</span>
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['single']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php ob_start(); ?>documents_status_<?php echo $this->_tpl_vars['single']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_param', ob_get_contents());ob_end_clean(); ?>
            <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_param']]; ?>
</span>
          <?php endif; ?>
          </td>
          <?php echo ''; ?><?php if (preg_match ( '#^Finance_.*$#i' , $this->_tpl_vars['single']->modelName )): ?><?php echo ''; ?><?php $this->assign('_module', 'finance'); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '#^Finance_(.*)$#i', '$1') : smarty_modifier_regex_replace($_tmp, '#^Finance_(.*)$#i', '$1')))) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?><?php echo 's'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_controller', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?><?php echo 's'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_module', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_controller', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          <td class="t_border <?php echo $this->_tpl_vars['sort']['tags']['isSorted']; ?>
" <?php if ($this->_tpl_vars['single']->getModelTags() && $this->_tpl_vars['single']->get('available_tags_count') > 0 && $this->_tpl_vars['single']->checkPermissions('tags_view') && $this->_tpl_vars['single']->checkPermissions('tags_edit')): ?> onclick="changeTags(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
'<?php if ($this->_tpl_vars['redirect_to_url'] && $this->_tpl_vars['update_target']): ?>, '<?php echo $this->_tpl_vars['redirect_to_url']; ?>
' + ($$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page').length ? $$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page')[0].innerHTML : 1)<?php endif; ?>)" style="cursor: pointer;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tags_change'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php endif; ?>>
            <?php if ($this->_tpl_vars['single']->get('model_tags') && is_array ( $this->_tpl_vars['single']->get('model_tags') ) && count($this->_tpl_vars['single']->get('model_tags')) > 0 && $this->_tpl_vars['single']->checkPermissions('tags_view')): ?>
              <?php $_from = $this->_tpl_vars['single']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
                <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span><?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?><br /><?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['added']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
" nowrap="nowrap"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['added_by']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <?php echo ''; ?><?php $this->assign('_module', $this->_tpl_vars['single']->getModule()); ?><?php echo ''; ?><?php $this->assign('_controller', $this->_tpl_vars['single']->getController()); ?><?php echo ''; ?>

          <td class="t_border <?php echo $this->_tpl_vars['sort']['emails']['isSorted']; ?>
">
            <div class="t_occupy_cell has_inline_add hright emails emails_<?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)); ?>
_<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('emails') > 0 && $this->_tpl_vars['single']->checkPermissions('emails')): ?> pointer" onmouseenter="showCommunicationsInfo(this, 'emails', '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['single']->get('archived_by'); ?>
')" onmouseleave="mclosetime()" onclick="openHref('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['_module']; ?>
<?php if ($this->_tpl_vars['_controller'] != $this->_tpl_vars['_module']): ?>&amp;controller=<?php echo $this->_tpl_vars['_controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['_controller']; ?>
=communications&amp;communications=<?php echo $this->_tpl_vars['single']->get('id'); ?>
&amp;communication_type=emails<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '_self', event)<?php endif; ?>">
              <span class="emails_total"><?php if ($this->_tpl_vars['single']->get('emails')): ?><?php echo $this->_tpl_vars['single']->get('emails'); ?>
<?php endif; ?></span>
            </div>
            <?php if ($this->_tpl_vars['single']->checkPermissions('emails_add')): ?>
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'email', '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['add_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"></a>
            </div>
            <?php endif; ?>
          </td>
        <?php if (preg_match ( '#^Finance_.*$#i' , $this->_tpl_vars['single']->modelName )): ?>
          <?php $this->assign('_module', 'finance'); ?>
          <?php ob_start(); ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '#^Finance_(.*)$#i', '$1') : smarty_modifier_regex_replace($_tmp, '#^Finance_(.*)$#i', '$1')))) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_controller', ob_get_contents());ob_end_clean(); ?>
        <?php else: ?>
          <?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('_module', ob_get_contents());ob_end_clean(); ?>
        <?php endif; ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['_module']; ?>
_assign_change<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('title_label', ob_get_contents());ob_end_clean(); ?>
        <td class="t_border"<?php if (! ( is_array ( $this->_tpl_vars['single']->get('assignments_owner') ) && count($this->_tpl_vars['single']->get('assignments_owner')) > 3 )): ?><?php if ($this->_tpl_vars['single']->checkPermissions('assign') && ! $this->_tpl_vars['single']->get('archived_by')): ?> onclick="changeAssignments(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['_module']; ?>
', 'assignments_owner'<?php if ($this->_tpl_vars['_controller']): ?>, '<?php echo $this->_tpl_vars['_controller']; ?>
'<?php endif; ?>)" style="cursor:pointer;" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['title_label']]; ?>
"<?php endif; ?><?php endif; ?>>
        <?php $this->assign('long_text', ''); ?>
        <?php $this->assign('short_text', ''); ?>
        <?php if ($this->_tpl_vars['single']->get('assignments_owner')): ?>
              <?php $_from = $this->_tpl_vars['single']->get('assignments_owner'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['assignees'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['assignees']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['assignees']['iteration']++;
?>
                <?php ob_start(); ?>
                  <?php echo $this->_tpl_vars['long_text']; ?>
<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['assignees']['iteration'] == $this->_foreach['assignees']['total'])): ?><br /><?php endif; ?>
                <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('long_text', ob_get_contents());ob_end_clean(); ?>
                <?php if ($this->_foreach['assignees']['iteration'] <= 3): ?>
                  <?php ob_start(); ?>
                    <?php echo $this->_tpl_vars['short_text']; ?>
<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['assignees']['iteration'] == $this->_foreach['assignees']['total'])): ?><br /><?php endif; ?>
                  <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('short_text', ob_get_contents());ob_end_clean(); ?>
                <?php endif; ?>
              <?php endforeach; else: ?>
                &nbsp;
              <?php endif; unset($_from); ?>
              <?php if (is_array ( $this->_tpl_vars['single']->get('assignments_owner') ) && count($this->_tpl_vars['single']->get('assignments_owner')) > 3): ?>
                <div id="assignments_owner_part_<?php echo $this->_foreach['i']['iteration']; ?>
">
                  <div<?php if ($this->_tpl_vars['single']->checkPermissions('assign') && ! $this->_tpl_vars['single']->get('archived_by')): ?> onclick="changeAssignments(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['_module']; ?>
', 'assignments_owner'<?php if ($this->_tpl_vars['_controller']): ?>, '<?php echo $this->_tpl_vars['_controller']; ?>
'<?php endif; ?>)" style="cursor:pointer; padding: 3px 0 3px 0;" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['title_label']]; ?>
"<?php endif; ?>>
                  <?php echo $this->_tpl_vars['short_text']; ?>

                  </div>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['_module']; ?>
_show_full_assignments_list<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('show_all_label', ob_get_contents());ob_end_clean(); ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/arrow_down.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['show_all_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['show_all_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="toggleContent('assignments_owner', <?php echo $this->_foreach['i']['iteration']; ?>
);" align="right" class="pointer" />
                </div>
                <div id="assignments_owner_full_<?php echo $this->_foreach['i']['iteration']; ?>
" style="display: none;">
                  <div<?php if ($this->_tpl_vars['single']->checkPermissions('assign') && ! $this->_tpl_vars['single']->get('archived_by')): ?> onclick="changeAssignments(<?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['_module']; ?>
', 'assignments_owner'<?php if ($this->_tpl_vars['_controller']): ?>, '<?php echo $this->_tpl_vars['_controller']; ?>
'<?php endif; ?>)" style="cursor:pointer; padding: 3px 0 3px 0;" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['title_label']]; ?>
"<?php endif; ?>>
                  <?php echo $this->_tpl_vars['long_text']; ?>

                  </div>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['_module']; ?>
_hide_full_assignments_list<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('hide_all_label', ob_get_contents());ob_end_clean(); ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/arrow_up.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['hide_all_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['hide_all_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="toggleContent('assignments_owner', <?php echo $this->_foreach['i']['iteration']; ?>
);" align="right" class="pointer" />
                </div>
              <?php else: ?>
                <?php echo $this->_tpl_vars['short_text']; ?>

              <?php endif; ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
        </td>
          <?php echo ''; ?><?php $this->assign('_module', $this->_tpl_vars['single']->getModule()); ?><?php echo ''; ?><?php $this->assign('_controller', $this->_tpl_vars['single']->getController()); ?><?php echo ''; ?>

          <td class="t_border history_activity_switch <?php echo $this->_tpl_vars['sort']['history_activity']['isSorted']; ?>
">
            <div class="t_occupy_cell hright history_activity history_activity_<?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)); ?>
_<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('history_activity') > 0 && $this->_tpl_vars['single']->checkPermissions('history')): ?> pointer" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="showDetailsRow(this, {event: event, field: 'history_activity', url: '<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['_module']; ?>
<?php if ($this->_tpl_vars['_controller'] != $this->_tpl_vars['_module']): ?>&amp;controller=<?php echo $this->_tpl_vars['_controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['_controller']; ?>
=history&amp;history=<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>&amp;source=ajax&amp;history_activity=1', click_function: historyActivityGoToAudit});<?php endif; ?>">
              <span class="history_activity_total"><?php if ($this->_tpl_vars['single']->get('history_activity')): ?><?php echo $this->_tpl_vars['single']->get('history_activity'); ?>
<?php endif; ?></span>
            </div>
          </td>
          <?php echo ''; ?><?php $this->assign('_module', $this->_tpl_vars['single']->getModule()); ?><?php echo ''; ?><?php $this->assign('_controller', $this->_tpl_vars['single']->getController()); ?><?php echo ''; ?>

          <td class="t_border <?php echo $this->_tpl_vars['sort']['comments']['isSorted']; ?>
">
            <div class="t_occupy_cell has_inline_add hright comments comments_<?php echo ((is_array($_tmp=$this->_tpl_vars['single']->modelName)) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)); ?>
_<?php echo $this->_tpl_vars['single']->get('id'); ?>
<?php if ($this->_tpl_vars['single']->get('comments') > 0 && $this->_tpl_vars['single']->checkPermissions('comments')): ?> pointer" onmouseenter="showCommunicationsInfo(this, 'comments', '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
, '<?php echo $this->_tpl_vars['single']->get('archived_by'); ?>
')" onmouseleave="mclosetime()" onclick="openHref('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['_module']; ?>
<?php if ($this->_tpl_vars['_controller'] != $this->_tpl_vars['_module']): ?>&amp;controller=<?php echo $this->_tpl_vars['_controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['_controller']; ?>
=communications&amp;communications=<?php echo $this->_tpl_vars['single']->get('id'); ?>
&amp;communication_type=comments<?php if ($this->_tpl_vars['single']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '_self', event)<?php endif; ?>">
              <span class="comments_total"><?php if ($this->_tpl_vars['single']->get('comments')): ?><?php echo $this->_tpl_vars['single']->get('comments'); ?>
<?php endif; ?></span>
            </div>
            <?php if ($this->_tpl_vars['single']->checkPermissions('comments_add')): ?>
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'comment', '<?php echo $this->_tpl_vars['_module']; ?>
', '<?php echo $this->_tpl_vars['_controller']; ?>
', <?php echo $this->_tpl_vars['single']->get('id'); ?>
);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['add_comment'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"></a>
            </div>
            <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['validity_term']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
" nowrap="nowrap"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['deadline']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
" nowrap="nowrap"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['single']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>

          <td class="hcenter" nowrap="nowrap">
            <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'],'exclude' => "edit, view, delete")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['single'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="16"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="16"></td>
        </tr>
      </table>
      <?php if ($this->_tpl_vars['action'] == 'filter'): ?>
        <br />
        <?php echo ''; ?><?php if ($_REQUEST['autocomplete_filter']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['autocomplete_params']['select_multiple']): ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: false'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: true'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php else: ?><?php echo '<button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo 'return confirmAction(\'link\', function(el) '; ?>{<?php echo 'updateReferers(el.form, 0);'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_documents'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\');'; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_documents'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo 'return confirmAction(\'link\', function(el) '; ?>{<?php echo 'updateReferers(el.form, 1);'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_documents'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\');'; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_documents'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?>

      <?php else: ?>
        <?php if (( '' )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_severity_legend.html", 'smarty_include_vars' => array('prefix' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
        <br />
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('exclude' => '','include' => 'purge,multistatus,multiprint,tags','session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <?php if ($this->_tpl_vars['background_colors']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_invoices_reasons_legend.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
</table>