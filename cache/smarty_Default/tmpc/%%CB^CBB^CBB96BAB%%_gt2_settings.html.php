<?php /* Smarty version 2.6.33, created on 2024-08-02 16:57:55
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html', 20, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html', 27, false),array('function', 'array', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html', 205, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html', 30, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/_gt2_settings.html', 31, false),)), $this); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_groups.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<table cellspacing="0" cellpadding="3" border="0">
  <tbody id="gt2_settings">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['num']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 200px"><?php echo $this->_config[0]['vars']['gt2_label']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_varname']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_type']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_alignment']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px"><?php echo $this->_config[0]['vars']['gt2_hidden']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px"><?php echo $this->_config[0]['vars']['gt2_readonly']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px"><?php echo $this->_config[0]['vars']['gt2_required']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_col_width']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_agregate']; ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_js_filter']; ?>
</div></td>
    <td class="t_caption" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_source']; ?>
</div></td>
  </tr>
  <?php $_from = $this->_tpl_vars['table']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['i']['iteration']++;
?>
    <?php if ($this->_tpl_vars['key'] !== 'batch_vars'): ?>
      <?php echo smarty_function_counter(array('name' => 'idx_cnt','assign' => 'current_index'), $this);?>

      <?php ob_start(); ?><?php echo $this->_tpl_vars['var']['name']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_name', ob_get_contents());ob_end_clean(); ?>
      <?php if ($this->_tpl_vars['var']['type'] == 'gt2'): ?>
        <?php $this->assign('hide_row', true); ?>
      <?php else: ?>
        <?php $this->assign('hide_row', false); ?>
      <?php endif; ?>
      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (($this->_foreach['i']['iteration'] <= 1)): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['var']['hidden']): ?> oblique<?php endif; ?>" id="gt2_settings_<?php echo $this->_tpl_vars['current_index']; ?>
"<?php if (preg_match ( '#discount_surplus_field#' , $this->_tpl_vars['var']['name'] )): ?> style="display:none"<?php endif; ?>>
        <td class="t_border" nowrap="nowrap" align="right">
          <?php echo $this->_tpl_vars['current_index']; ?>
.
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
permissions.png" class="pointer" alt="<?php echo $this->_config[0]['vars']['gt2_permissions']; ?>
 <?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
" title="<?php echo $this->_config[0]['vars']['gt2_permissions']; ?>
 <?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
" onmouseover="editGT2Permissions('show', <?php echo $this->_tpl_vars['current_index']; ?>
, '<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
');" onmouseout="editGT2Permissions('close')" onclick="editGT2Permissions('edit', <?php echo $this->_tpl_vars['current_index']; ?>
, '<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
');" />
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
translate.png" class="pointer" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="editGT2Translations(this)" />
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'ids','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['id'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'permissions_edit','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['permissions_edit'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'permissions_view','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['permissions_view'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <span class="pointer" id="handler_<?php echo $this->_tpl_vars['current_index']; ?>
"><?php echo ''; ?><?php if ($this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']]): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']]; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] != 'gt2'): ?><?php echo '<em class="red">'; ?><?php echo $this->_tpl_vars['default_name']; ?><?php echo '</em>'; ?><?php endif; ?><?php echo ''; ?>
</span>
          <?php $_from = $this->_tpl_vars['model_langs']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lng']):
?>
          <div class="translation" style="display:none; margin-top:5px; white-space: nowrap!important;"><?php echo ''; ?><?php ob_start(); ?><?php echo 'lang_'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lang_name', ob_get_contents());ob_end_clean(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo '.png" width="16" height="12" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lang_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lang_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php ob_start(); ?><?php echo 'label_'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('field_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => $this->_tpl_vars['field_name'],'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'width' => '200','value' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['lng']],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>
</div>
          <?php endforeach; endif; unset($_from); ?>
        </td>
        <td class="t_border">
          <?php echo $this->_tpl_vars['var']['name']; ?>

        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'type','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['type'],'options' => $this->_tpl_vars['controls_options']['type'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_type'],'width' => 100,'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'alignment','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['text_align'],'options' => $this->_tpl_vars['controls_options']['alignment'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_alignment'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border">
          <?php ob_start(); ?>if(this.value > 0){addClass(this.parentNode.parentNode, 'oblique')}else{removeClass(this.parentNode.parentNode, 'oblique')}<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('hidden_onchange', ob_get_contents());ob_end_clean(); ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'hidden','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['hidden'],'options' => $this->_tpl_vars['controls_options']['hidden'],'standalone' => true,'required' => 1,'onchange' => $this->_tpl_vars['hidden_onchange'],'label' => $this->_config[0]['vars']['gt2_hidden'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'readonly','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['readonly'],'options' => $this->_tpl_vars['controls_options']['readonly'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_readonly'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'required','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['required'],'options' => $this->_tpl_vars['controls_options']['required'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_required'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border" style="white-space: nowrap">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'width','width' => 35,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['width'],'restrict' => 'insertOnlyDigits','text_align' => 'right','standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_col_width'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'width_measure','width' => 45,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['width_measure'],'options' => $this->_tpl_vars['controls_options']['html_measures'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'agregate','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['agregate'],'options' => $this->_tpl_vars['controls_options']['agregate'],'first_option_label' => $this->_config[0]['vars']['gt2_no'],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_agregate'],'width' => "100%",'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php if (! ($this->_foreach['i']['iteration'] <= 1)): ?>
            <script type="text/javascript">
                 new Zapatec.Utils.Draggable({container:'gt2_settings_<?php echo $this->_tpl_vars['current_index']; ?>
', 
                                              handler: 'handler_<?php echo $this->_tpl_vars['current_index']; ?>
',
                                              direction: 'vertical',
                                              followShape: 'LT',
                                              method: 'cut',
                                              dragCSS: 'gt2_draggable',
                                              beforeDragInit: function(){gt2BeforeDragInit(this);},
                                              onDragEnd : function(){gt2OnDragEnd(this);}
                                             });
                 new Zapatec.Utils.DropArea('gt2_settings_<?php echo $this->_tpl_vars['current_index']; ?>
',
                                            'gt2_settings_<?php echo $this->_tpl_vars['current_index']; ?>
',null , null,
                                            function(){gt2MoveDroppable(this);}, null, null
                                           );
            </script>
          <?php endif; ?>
        </td>
        <td class="t_border">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'js_filter','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['js_filter'],'options' => $this->_tpl_vars['controls_options']['js_filter'],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_js_filter'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td>
          <?php echo smarty_function_array(array('assign' => 'gt2_source_js_methods','onfocus' => "gt2ExpandSourceField(this);"), $this);?>

          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_textarea.html", 'smarty_include_vars' => array('name' => 'source_user','height' => 18,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['source_user'],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_source'],'required' => 1,'js_methods' => $this->_tpl_vars['gt2_source_js_methods'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
      </tr>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
  <tr><td colspan="12" class="t_footer"></td></tr>
  <tr>
    <td colspan="12">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_checkbox.html", 'smarty_include_vars' => array('name' => 'show_totals_inwords','standalone' => true,'option_value' => 1,'value' => $this->_tpl_vars['table']['0']['show_totals_inwords'],'label' => $this->_config[0]['vars']['show_totals_inwords'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  </tbody>
</table>

<?php if ($this->_tpl_vars['table']['batch_vars']): ?>
  <br /><br />
  <h3 style="padding-left: 30px;"><?php echo $this->_config[0]['vars']['finance_documents_types_batch_vars']; ?>
</h3>
  <table cellspacing="0" cellpadding="3" border="0">
    <tbody id="gt2_batch_settings">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['num']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 200px"><?php echo $this->_config[0]['vars']['gt2_label']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_varname']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_type']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_alignment']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px"><?php echo $this->_config[0]['vars']['gt2_hidden']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px"><?php echo $this->_config[0]['vars']['gt2_readonly']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_col_width']; ?>
</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_js_filter']; ?>
</div></td>
	  <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo $this->_config[0]['vars']['gt2_source']; ?>
</div></td>
    </tr>
    <?php $_from = $this->_tpl_vars['table']['batch_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['i']['iteration']++;
?>
    <?php echo smarty_function_counter(array('name' => 'idx_cnt','assign' => 'current_index'), $this);?>

    <?php ob_start(); ?><?php echo $this->_tpl_vars['var']['name']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_name', ob_get_contents());ob_end_clean(); ?>
    <?php if ($this->_tpl_vars['var']['type'] == 'gt2'): ?>
      <?php $this->assign('hide_row', true); ?>
    <?php else: ?>
      <?php $this->assign('hide_row', false); ?>
    <?php endif; ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['var']['hidden']): ?> oblique<?php endif; ?>" id="gt2_batch_settings_<?php echo $this->_tpl_vars['current_index']; ?>
">
      <td class="t_border" nowrap="nowrap" align="right">
        <?php echo $this->_tpl_vars['current_index']; ?>
.
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
permissions.png" class="pointer" alt="<?php echo $this->_config[0]['vars']['gt2_permissions']; ?>
 <?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
" title="<?php echo $this->_config[0]['vars']['gt2_permissions']; ?>
 <?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
" onmouseover="editGT2Permissions('show', <?php echo $this->_tpl_vars['current_index']; ?>
, '<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_name'])); ?>
');" onmouseout="editGT2Permissions('close')" onclick="editGT2Permissions('edit', <?php echo $this->_tpl_vars['current_index']; ?>
, '<?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']]; ?>
');" />
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
translate.png" class="pointer" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="editGT2Translations(this)" />
      </td>
      <td class="t_border">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'ids','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['id'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'permissions_edit','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['permissions_edit'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_hidden.html", 'smarty_include_vars' => array('name' => 'permissions_view','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['permissions_view'],'standalone' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <span class="pointer" id="handler_<?php echo $this->_tpl_vars['current_index']; ?>
"><?php echo ''; ?><?php if ($this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']]): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['lang']]; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] != 'gt2'): ?><?php echo '<em class="red">'; ?><?php echo $this->_tpl_vars['default_name']; ?><?php echo '</em>'; ?><?php endif; ?><?php echo ''; ?>
</span>
        <?php $_from = $this->_tpl_vars['model_langs']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lng']):
?>
        <div class="translation" style="display:none; margin-top:5px; white-space: nowrap!important;"><?php echo ''; ?><?php ob_start(); ?><?php echo 'lang_'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lang_name', ob_get_contents());ob_end_clean(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo '.png" width="16" height="12" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lang_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['lang_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php ob_start(); ?><?php echo 'label_'; ?><?php echo $this->_tpl_vars['lng']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('field_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => $this->_tpl_vars['field_name'],'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'width' => '200','value' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['lng']],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>
</div>
        <?php endforeach; endif; unset($_from); ?>
      </td>
      <td class="t_border">
        <?php echo $this->_tpl_vars['var']['name']; ?>

      </td>
      <td class="t_border">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'type','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['type'],'options' => $this->_tpl_vars['controls_options']['type'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_type'],'width' => 100,'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'alignment','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['text_align'],'options' => $this->_tpl_vars['controls_options']['alignment'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_alignment'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border">
        <?php ob_start(); ?>if(this.value > 0){addClass(this.parentNode.parentNode, 'oblique')}else{removeClass(this.parentNode.parentNode, 'oblique')}<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('hidden_onchange', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'hidden','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['hidden'],'options' => $this->_tpl_vars['controls_options']['hidden'],'standalone' => true,'required' => 1,'onchange' => $this->_tpl_vars['hidden_onchange'],'label' => $this->_config[0]['vars']['gt2_hidden'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'readonly','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['readonly'],'options' => $this->_tpl_vars['controls_options']['readonly'],'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_readonly'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border" style="white-space: nowrap">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'width','width' => 35,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['width'],'restrict' => 'insertOnlyDigits','text_align' => 'right','standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['gt2_col_width'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'width_measure','width' => 45,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['width_measure'],'options' => $this->_tpl_vars['controls_options']['html_measures'],'standalone' => true,'required' => 1,'readonly' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'js_filter','index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['js_filter'],'options' => $this->_tpl_vars['controls_options']['js_filter'],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_js_filter'],'hidden' => $this->_tpl_vars['hide_row'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
      <td class="t_border">
        <?php echo smarty_function_array(array('assign' => 'gt2_source_js_methods','onfocus' => "gt2ExpandSourceField(this);"), $this);?>

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['themes']->templatesDir)."input_textarea.html", 'smarty_include_vars' => array('name' => 'source_user','width' => "100%",'height' => 18,'index' => $this->_tpl_vars['current_index'],'empty_indexes' => true,'value' => $this->_tpl_vars['var']['source_user'],'standalone' => true,'label' => $this->_config[0]['vars']['gt2_source'],'required' => 1,'js_methods' => $this->_tpl_vars['gt2_source_js_methods'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <?php endforeach; endif; unset($_from); ?>
    <tr><td colspan="10" class="t_footer t_border"></td></tr>
    </tbody>
  </table>
<?php endif; ?>