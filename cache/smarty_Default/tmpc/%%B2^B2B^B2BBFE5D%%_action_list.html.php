<?php /* Smarty version 2.6.33, created on 2024-10-09 15:13:19
         compiled from /var/www/Nzoom-Evolution/_libs/modules/turnovers/templates/_action_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/turnovers/templates/_action_list.html', 3, false),)), $this); ?>
  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td class="labelbox"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['turnovers_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td nowrap="nowrap">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','width' => 244,'custom_id' => 'customer1','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','autocomplete' => $this->_tpl_vars['customer_autocomplete1'],'standalone' => true,'label' => $this->_config[0]['vars']['turnovers_customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['trademark'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td nowrap="nowrap">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','width' => 244,'custom_id' => 'trademark1','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','autocomplete' => $this->_tpl_vars['trademark_autocomplete1'],'standalone' => true,'label' => $this->_config[0]['vars']['trademark'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['turnovers_period'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td nowrap="nowrap">
        <select name="period" id="period1" onchange="changePeriodOptions(this)" class="selbox" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['turnovers_period'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
          <option value="day"<?php if ($this->_tpl_vars['period'] == 'day'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['day'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
          <option value="month"<?php if ($this->_tpl_vars['period'] == 'month'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['month'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
          <option value="year"<?php if ($this->_tpl_vars['period'] == 'year'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['year'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
        </select>
      </td>
    </tr>
    <tr>
      <td colspan="3">
        <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><?php echo $this->_tpl_vars['available_action']['label']; ?>
</button>
      </td>
    </tr>
  </table>