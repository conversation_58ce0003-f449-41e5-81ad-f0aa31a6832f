    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#projects_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.code|default:#projects_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#projects_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#projects_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.priority|default:#projects_priority#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.manager|default:#projects_manager#|escape}</th>
          <th nowrap="nowrap">{if 'projects' == 'projects'}{#projects_status_phase#|escape}{else}{$basic_vars_labels.status|default:#projects_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#projects_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.date_end|default:#projects_date_end#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.assignments|default:#projects_assignments#|escape}</th>

        </tr>
      {foreach name='i' from=$projects item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="14-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('code')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          {assign var=priority value=$single->get('priority')}
          <td style="mso-number-format: \@;">{$smarty.config.$priority}</td>
          <td style="mso-number-format: \@;">{$single->get('manager_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">
            {capture assign='popup_and_onclick'}
              {popup text=$project_status|escape|default:'&nbsp;' caption=#help_projects_status# width=250}{if $single->checkPermissions('setstatus') && !$single->checkStages()} style="cursor: pointer;" onclick="changeStatus({$single->get('id')}, 'projects')"{/if}
            {/capture}
            {if $single->get('status') eq 'planning'}
               
              {#projects_status_planning#|escape}
            {elseif $single->get('status') eq 'progress'}
               
              {#projects_status_progress#|escape}
            {elseif $single->get('status') eq 'control'}
               
              {#projects_status_control#|escape}
            {elseif $single->get('status') eq 'finished'}
              
              {if $single->get('finished') === '0'}{#projects_substatus_finished_failed#|escape}{elseif $single->get('finished') === '1'}{#projects_substatus_finished_success#|escape}{else}{#projects_status_finished#|escape}{/if}
            {/if}
            {if $single->get('stage_name')}{$single->get('stage_name')|escape}{/if}
          </td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap">{$single->get('date_end')|date_format:#date_short#|escape|default:"&nbsp;"}</td>
            {capture assign='title_label'}{$single->modelName|mb_lower}s_assign_change{/capture}
            <td style="mso-number-format: \@;">
            {assign var='long_text' value=''}
            {assign var='short_text' value=''}
            {if $single->get('assignments_type')}
              {if $single->get('assignments_type') == 'Users'}
                {assign var='assignment_records' value=$single->get('users_assignments')}
                {assign var='assignments_type' value='users'}
              {else}
                {assign var='assignment_records' value=$single->get('departments_assignments')}
                {assign var='assignments_type' value='departments'}
              {/if}
              {foreach from=$assignment_records item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {capture assign='lang_var'}menu_{$assignments_type}{/capture}
              {$smarty.config.$lang_var}: {$long_text}
            {else}
              &nbsp;
            {/if}
            </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="14">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
