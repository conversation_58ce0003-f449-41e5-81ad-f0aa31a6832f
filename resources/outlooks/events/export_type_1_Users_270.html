    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#events_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#events_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.event_start|default:#events_event_start#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.event_end|default:#events_event_end#|escape}</th>
          <th nowrap="nowrap">{if 'events' == 'projects'}{#events_status_phase#|escape}{else}{$basic_vars_labels.status|default:#events_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#events_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.project|default:#events_project#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.participants|default:#events_participants#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.recurrence_type|default:#events_recurrence_type#|escape}</th>

        </tr>
      {foreach name='i' from=$events item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="13-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          {if $single->get('allday_event')}
            {capture assign='event_start'}{$single->get('event_start')|date_format:#date_short#|escape|default:"&nbsp;"}, ({if $single->get('allday_event') == -1}{$single->get('duration')} {if abs($single->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){/capture}
          {else}
            {capture assign='event_start'}{$single->get('event_start')|date_format:#date_mid#|escape|default:"&nbsp;"}{/capture}
          {/if}
          <td nowrap="nowrap">{$event_start}</td>
          {if $single->get('allday_event')}
            {capture assign='event_end'}{$single->get('event_end')|date_format:#date_short#|escape|default:"&nbsp;"}, ({if $single->get('allday_event') == -1}{$single->get('duration')} {if abs($single->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){/capture}
          {else}
            {capture assign='event_end'}{$single->get('event_end')|date_format:#date_mid#|escape|default:"&nbsp;"}{/capture}
          {/if}
          <td nowrap="nowrap">{$event_end}</td>
          <td nowrap="nowrap">
            {capture assign='status_name'}events_status_{$single->get('status')}{/capture}
            {capture assign='popup_and_onclick'}
              {popup text=$smarty.config.$status_name|escape caption=#help_events_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'events')" style="cursor: pointer;"{/if}
            {/capture}
            
          </td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{if $single->get('project')}{$single->get('project_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">
            {foreach name='cp' from=$single->get('customers_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.cp.last},{/if}
            {/foreach}
            {foreach name='up' from=$single->get('users_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.up.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('recurrence_type')}
              {capture assign='event_recurrence_type'}events_recurrence_types_{$single->get('recurrence_type')}{/capture}
              {$smarty.config.$event_recurrence_type|escape}
            {else}
              {#events_recurrence_types_none#}
            {/if}
          </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="13">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
