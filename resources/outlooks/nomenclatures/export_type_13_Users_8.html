    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.code|default:#nomenclatures_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#nomenclatures_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.subtype|default:#nomenclatures_subtype#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.categories|default:#nomenclatures_categories#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added_by|default:#nomenclatures_added_by#|escape}</th>

        </tr>
      {foreach name='i' from=$nomenclatures item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="9-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('code')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{capture assign='subtype_i18n_param'}nomenclatures_subtype_{$single->get('subtype')|escape}{/capture}{$smarty.config.$subtype_i18n_param}</td>          <td style="mso-number-format: \@;">&nbsp;
            {if $single->get('categories_names')}
              {foreach name='cn' from=$single->get('categories_names') item='cat_name'}
                {$cat_name|escape}{if !$smarty.foreach.cn.last},{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {/if}
          </td>          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="9">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
