<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  {if $action eq 'filter'}
    {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;documents=filter&amp;{if $generate_system_task}generate_system_task={$generate_system_task}&amp;{/if}{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}page={/capture}

  {else}
    <tr>
      <td class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}={$action}{if $type && is_numeric($type)}&amp;type={$type}{/if}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="documents" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
      {if $action eq 'filter' && $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            {if $action eq 'filter'}
              {if !$autocomplete_params || $autocomplete_params.select_multiple}
                {include file="`$theme->templatesDir`_select_items.html"
                  pages=$pagination.pages
                  total=$pagination.total
                  session_param=$session_param|default:$pagination.session_param
                }
              {else}
                {assign var='hide_selection_stats' value=true}
              {/if}
            {else}
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
            {/if}
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{$basic_vars_labels.full_num|default:#documents_full_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#documents_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.name_full_num.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.name_full_num.link} onclick="{$sort.name_full_num.link}"{/if}>{$basic_vars_labels.name_full_num|default:#documents_name_full_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{if 'documents' == 'projects'}{#documents_status_phase#|escape}{else}{$basic_vars_labels.status|default:#documents_status#|escape}{/if}</div></th>
          <th class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.tags.link} onclick="{$sort.tags.link}"{/if}>{$basic_vars_labels.tags|default:#documents_tags#|escape}</div></th>
          <th class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{$basic_vars_labels.added|default:#added#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__prod_recipe_name.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__prod_recipe_name.link} onclick="{$sort.a__prod_recipe_name.link}"{/if}>{$add_vars_labels.8.prod_recipe_name}</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$documents item='single'}
      {strip}
      {capture assign='info'}
        <strong><u>{$basic_vars_labels.full_num|default:#documents_full_num#|escape}:</u></strong> {$single->get('full_num')|escape|numerate:$single->get('direction')}<br />
        <strong>{$basic_vars_labels.name|default:#documents_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{$basic_vars_labels.type|default:#documents_type#|escape}:</strong> {$single->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$single->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('status_modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}
        {if $single->get('archived_by')}<strong>{#archived#|escape}:</strong> {$single->get('archived')|date_format:#date_mid#|escape}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='document_status'}
        {if $single->get('status') eq 'opened'}
          {#help_documents_status_opened#}
        {elseif $single->get('status') eq 'locked'}
          {#help_documents_status_locked#}
        {elseif $single->get('status') eq 'closed'}
          {#help_documents_status_closed#}
        {/if}
        {if $single->get('substatus_name')}
          <br />
          {#help_documents_substatus#}{$single->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      <div id="rf{$single->get('id')}" style="display: none">{$single->get('full_num')|numerate:$single->get('direction')} {$single->get('name')|escape|default:"&nbsp;"}</div>
      {if $single->modelName != 'Event' && !$single->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$single->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            {counter name='item_counter' print=true}
          </td>
          <td colspan="7" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
          <td class="t_border">
            {if $action eq 'filter'}
              {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$single->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
              {else}
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                               {rdelim});"
                       name='items[]'
                       value="{$single->get('id')}{if $module eq 'customers' && $relation}_{if $single->get('is_company')}company{else}person{/if}{/if}"
                       title="{#check_to_include#|escape}" />
              {/if}
            {else}
              <input onclick="sendIds(params = {ldelim}
                                              the_element: this,
                                              module: '{$module}',
                                              controller: '{$controller}',
                                              action: '{$action}',
                                              session_param: '{$session_param|default:$pagination.session_param}',
                                              total: {$pagination.total}
                                             {rdelim});"
                     type="checkbox"
                     name='items[]'
                     value="{$single->get('id')}"
                     title="{#check_to_include#|escape}"
                     {if @in_array($single->get('id'), $selected_items.ids) ||
                         (@$selected_items.select_all eq 1 && @!in_array($single->get('id'), $selected_items.ignore_ids))}
                       checked="checked"
                     {/if} />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $single->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">
               <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.full_num.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">{$single->get('full_num')|numerate:$single->get('direction')}</a></td>
          <td class="t_border {$sort.name.isSorted} {$row_link_class}"{$row_link}>{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module ne $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=view&amp;view={$single->get('id')}">&#91;{$single->get('full_num')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape|default:'&nbsp;' caption=#help_documents_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $single->get('status') != 'closed' && $single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: <strong>{$single->get('deadline')|date_format:#date_mid#}</strong>!
            {/if}
            {if $single->get('status') != 'closed' && $single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: <strong>{$single->get('validity_term')|date_format:#date_mid#}</strong>!
            {/if}
          {/capture}
          {if $single->get('status') != 'closed' && (($single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=$document_expired|escape caption=#documents_expired#|escape width=250} />
          {/if}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              <img src="{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$single->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}documents_{$single->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$single->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}documents_{$single->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}documents_status_{$single->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          {strip}
            {if preg_match('#^Finance_.*$#i', $single->modelName)}
              {assign var='_module' value='finance'}
              {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
            {else}
              {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
              {capture assign='_controller'}{/capture}
            {/if}
          {/strip}
          <td class="t_border {$sort.tags.isSorted}" {if $single->getModelTags() && $single->get('available_tags_count') gt 0 && $single->checkPermissions('tags_view') && $single->checkPermissions('tags_edit')} onclick="changeTags({$single->get('id')}, '{$_module}', '{$_controller}'{if $redirect_to_url && $update_target}, '{$redirect_to_url}' + ($$('#{$update_target} .page_menu_current_page').length ? $$('#{$update_target} .page_menu_current_page')[0].innerHTML : 1){/if})" style="cursor: pointer;" title="{#tags_change#|escape}"{/if}>
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('added')|date_format:#date_short#|escape}</td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'prod_recipe_name')}prod_recipe_name{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('prod_recipe_name', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.8.prod_recipe_name}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}autocompleter{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_prod_recipe_name', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_prod_recipe_name', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_prod_recipe_name_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_prod_recipe_name_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__prod_recipe_name.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>

          <td class="hcenter" nowrap="nowrap">
            {if $action eq 'filter'}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single exclude="edit, view, delete"}
            {else}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single}
            {/if}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      {if $action eq 'filter'}
        <br />
        {strip}
          {if $smarty.request.autocomplete_filter}
  {if $autocomplete_params.select_multiple}
    <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
  {/if}
  <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
{else}
  <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 0);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 1);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
{/if}

        {/strip}
      {else}
        {if ('')}
          {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
        {/if}
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='' include='purge,multistatus,multiprint,tags' session_param=$session_param|default:$pagination.session_param}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
