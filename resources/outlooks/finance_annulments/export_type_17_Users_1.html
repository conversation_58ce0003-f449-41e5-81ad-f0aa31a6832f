    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#finance_annulments_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name_num|default:#finance_annulments_name_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_annulments_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer_name_code|default:#finance_annulments_customer_name_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#finance_annulments_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total|default:#finance_annulments_total#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total_with_vat|default:#finance_annulments_total_with_vat#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total_vat|default:#finance_annulments_total_vat#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.fiscal_total_vat|default:#finance_annulments_fiscal_total_vat#|escape}</th>

        </tr>
      {foreach name='i' from=$finance_annulments item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="13-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->modelName eq 'Contract' and ($single->get('num') eq 'system' or !$single->get('num'))}{if $single->get('num') eq 'system'}{#contracts_system_num#}{else}{#contracts_unfinished_contract#}{/if}{else}&#91;{$single->get('num')|escape|default:"&nbsp;"}&#93;{/if} {$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}&#91;{$single->get('customer_code')|escape|default:"&nbsp;"}&#93; {$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total')|escape} {$single->get('currency')|escape}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total_with_vat')|escape} {$single->get('currency')|escape}</td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total_vat')|escape} {$single->get('currency')|escape}</td>
          <td style="mso-number-format: \@;" align="right">{if $single->get('status') eq 'finished'}{$single->get('fiscal_total_vat')|escape} {$single->get('fiscal_currency')|escape}{/if}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="13">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
