    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#finance_warehouses_documents_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.warehouse|default:#finance_warehouses_documents_warehouse#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.date|default:#finance_warehouses_documents_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#finance_warehouses_documents_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added_by|default:#finance_warehouses_documents_added_by#|escape}</th>
          <th nowrap="nowrap">{if 'finance_warehouses_documents' == 'projects'}{#finance_warehouses_documents_status_phase#|escape}{else}{$basic_vars_labels.status|default:#finance_warehouses_documents_status#|escape}{/if}</th>

        </tr>
      {foreach name='i' from=$finance_warehouses_documents item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="10-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td nowrap="nowrap">
            {if $single->get('type') == $smarty.const.PH_FINANCE_TYPE_HANDOVER}
              {capture assign='direction_title'}finance_warehouses_documents_direction_{$single->get('direction')}{/capture}
              {$smarty.config.$direction_title|escape}
            {elseif $single->get('type') == $smarty.const.PH_FINANCE_TYPE_COMMODITIES_RESERVATION}
              
            {elseif $single->get('type') == $smarty.const.PH_FINANCE_TYPE_COMMODITIES_RELEASE}
              
            {/if}
            {$single->get('name')|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('warehouse_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">{$single->get('date')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">
            {capture assign='popup_and_onclick'}{popup text=$finance_warehouses_document_status|escape|default:'&nbsp;' caption=#help_finance_documents_status#|escape width=250}{/capture}
            {if $single->get('status') eq 'opened'}
              
            {elseif $single->get('status') eq 'locked'}
              
            {elseif $single->get('status') eq 'finished'}
              
            {/if}
            {capture assign='status_param'}finance_documents_status_{$single->get('status')}{/capture}
            {$status_label_type|default:$smarty.config.$status_param}
          </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
