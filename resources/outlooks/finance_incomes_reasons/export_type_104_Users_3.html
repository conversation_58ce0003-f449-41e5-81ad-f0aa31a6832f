    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_incomes_reasons_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.description|default:#finance_incomes_reasons_description#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.issue_date|default:#finance_incomes_reasons_issue_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added_by|default:#finance_incomes_reasons_added_by#|escape}</th>

        </tr>
      {foreach name='i' from=$finance_incomes_reasons item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="7-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('description')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td nowrap="nowrap">{$single->get('issue_date')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
