<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  {if $action eq 'filter'}
    
  {else}
    <tr>
      <td class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}={$action}{if $type && is_numeric($type)}&amp;type={$type}{/if}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_incomes_reasons" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
      {if $action eq 'filter' && $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            {if $action eq 'filter'}
              {if !$autocomplete_params || $autocomplete_params.select_multiple}
                {include file="`$theme->templatesDir`_select_items.html"
                  pages=$pagination.pages
                  total=$pagination.total
                  session_param=$session_param|default:$pagination.session_param
                }
              {else}
                {assign var='hide_selection_stats' value=true}
              {/if}
            {else}
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
            {/if}
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer|default:#finance_incomes_reasons_customer#|escape}</div></th>
          <th class="t_caption t_border {$sort.total.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.total.link} onclick="{$sort.total.link}"{/if}>{$basic_vars_labels.total|default:#finance_incomes_reasons_total#|escape}</div></th>
          <th class="t_caption t_border {$sort.total_with_vat.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.total_with_vat.link} onclick="{$sort.total_with_vat.link}"{/if}>{$basic_vars_labels.total_with_vat|default:#finance_incomes_reasons_total_with_vat#|escape}</div></th>
          <th class="t_caption t_border {$sort.total_paid_amount.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.total_paid_amount.link} onclick="{$sort.total_paid_amount.link}"{/if}>{$basic_vars_labels.total_paid_amount|default:#finance_incomes_reasons_total_paid_amount#|escape}</div></th>
          <th class="t_caption t_border {$sort.total_remaining_amount.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.total_remaining_amount.link} onclick="{$sort.total_remaining_amount.link}"{/if}>{$basic_vars_labels.total_remaining_amount|default:#finance_incomes_reasons_total_remaining_amount#|escape}</div></th>
          <th class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{if 'finance_incomes_reasons' == 'projects'}{#finance_incomes_reasons_status_phase#|escape}{else}{$basic_vars_labels.status|default:#finance_incomes_reasons_status#|escape}{/if}</div></th>
          <th class="t_caption t_border {$sort.issue_date.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.issue_date.link} onclick="{$sort.issue_date.link}"{/if}>{$basic_vars_labels.issue_date|default:#finance_incomes_reasons_issue_date#|escape}</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$finance_incomes_reasons item='single'}
    {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#finance_incomes_reasons_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='finance_incomes_reason_status'}
        {if $single->get('status') eq 'opened'}
          {#help_finance_documents_status_opened#}
        {elseif $single->get('status') eq 'locked'}
          {#help_finance_documents_status_locked#}
        {elseif $single->get('status') eq 'finished'}
          {#help_finance_documents_status_finished#}
        {/if}
        {if $single->get('substatus_name')}
          <br />
          {#help_finance_documents_substatus#}{$single->get('substatus_name')}
        {/if}
      {/capture}
    {/strip}
    {assign var='background_style' value=''}
    {assign var='background_properties' value=$single->getBackgroundColor()}
    {if !empty($background_properties)}
      {array_push array='background_colors' new_item=$background_properties.background_color key=$background_properties.definition}
      {capture assign='background_style'}style="background-color: #{$background_properties.background_color}; color: #{$background_properties.text_color};"{/capture}
    {/if}
    {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
    {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
    <div id="rf{$single->get('id')}" style="display: none">{$single->get('name')|escape|default:"&nbsp;"} <input type="hidden" id="unpaid{$single->get('id')}" value="{$single->get('unpaid_amount')|escape}" /></div>
      {if $single->modelName != 'Event' && !$single->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$single->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            {counter name='item_counter' print=true}
          </td>
          <td colspan="7" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
          <td class="t_border">
            {if $action eq 'filter'}
              {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$single->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
              {else}
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                               {rdelim});"
                       name='items[]'
                       value="{$single->get('id')}{if $module eq 'customers' && $relation}_{if $single->get('is_company')}company{else}person{/if}{/if}"
                       title="{#check_to_include#|escape}" />
              {/if}
            {else}
              <input onclick="sendIds(params = {ldelim}
                                              the_element: this,
                                              module: '{$module}',
                                              controller: '{$controller}',
                                              action: '{$action}',
                                              session_param: '{$session_param|default:$pagination.session_param}',
                                              total: {$pagination.total}
                                             {rdelim});"
                     type="checkbox"
                     name='items[]'
                     value="{$single->get('id')}"
                     title="{#check_to_include#|escape}"
                     {if @in_array($single->get('id'), $selected_items.ids) ||
                         (@$selected_items.select_all eq 1 && @!in_array($single->get('id'), $selected_items.ignore_ids))}
                       checked="checked"
                     {/if} />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $single->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">
               <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.customer.isSorted}">{if $single->get('customer')}<a href="{$smarty.server.PHP_SELF}?{$module_param}=customers&amp;customers=view&amp;view={$single->get('customer')}" title="{#view#|escape}: {$single->get('customer_name')|escape}">{$single->get('customer_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          <td class="t_border hright {$sort.total.isSorted} {$row_link_class}"{$row_link}>{$single->get('total')|escape} {$single->get('currency')|escape}</td>
          <td class="t_border hright {$sort.total_with_vat.isSorted} {$row_link_class}"{$row_link}>{$single->get('total_with_vat')|escape} {$single->get('currency')|escape}</td>
          <td class="t_border hright {$row_link_class}" nowrap="nowrap"{$row_link}>
          {if $single->get('type') ne $smarty.const.PH_FINANCE_TYPE_CORRECT_REASON && !($single->get('type') eq $smarty.const.PH_FINANCE_TYPE_PRO_INVOICE and $single->get('payment_status') eq 'invoiced')}
            {$single->getFullPaidAmount()|string_format:"%.2F"} {$single->get('currency')|escape}
          {else}
            &nbsp;
          {/if}
          </td>
          <td class="t_border hright {$row_link_class}" nowrap="nowrap"{$row_link}>
            {if $single->get('type') ne $smarty.const.PH_FINANCE_TYPE_CORRECT_REASON && !($single->get('type') eq $smarty.const.PH_FINANCE_TYPE_PRO_INVOICE and $single->get('payment_status') eq 'invoiced')}
              {math equation=x-y x=$single->get('total_with_vat')|string_format:"%.2F" y=$single->getFullPaidAmount() format="%.2F"} {$single->get('currency')|escape}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$finance_incomes_reason_status|escape|default:'&nbsp;' caption=#help_finance_documents_status#|escape width=250}{if !$single->get('annulled_by') && $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'finance', 'incomes_reasons')" style="cursor:pointer;"{/if}
          {/capture}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_STATUSES_URL}{$single->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              {if $single->get('status') eq 'opened'}
                <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {elseif $single->get('status') eq 'locked'}
                <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {elseif $single->get('status') eq 'finished'}
                <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {/if}
            {/if}
            <span {$popup_and_onclick}>{$single->get('substatus_name')|escape}</span>
          {else}
            {if $single->get('status') eq 'opened'}
              <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $single->get('status') eq 'locked'}
              <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $single->get('status') eq 'finished'}
              <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            {capture assign='status_param'}finance_documents_status_{$single->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.issue_date.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('issue_date')|date_format:#date_short#|escape}</td>

          <td class="hcenter" nowrap="nowrap">
            {if $action eq 'filter'}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single exclude="edit, view, delete"}
            {else}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single}
            {/if}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      {if $action eq 'filter'}
        <br />
        {strip}
          
        {/strip}
      {else}
        {if ('')}
          {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
        {/if}
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit,delete,restore' include='multistatus,multiprint,tags' session_param=$session_param|default:$pagination.session_param}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
