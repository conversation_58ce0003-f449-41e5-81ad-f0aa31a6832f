    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#customers_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#customers_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.assigned|default:#customers_assigned#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.3.specialty}</th>
          <th nowrap="nowrap">{$add_vars_labels.3.region_name}</th>

        </tr>
      {foreach name='i' from=$customers item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="10-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('assigned_to_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          {capture assign='var_value'}{$single->getVarValue('specialty')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.3.specialty}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          {capture assign='var_value'}{$single->getVarValue('region_name')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.3.region_name}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
