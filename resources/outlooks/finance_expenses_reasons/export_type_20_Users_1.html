    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_expenses_reasons_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total|default:#finance_expenses_reasons_total#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total_with_vat|default:#finance_expenses_reasons_total_with_vat#|escape}</th>
          <th nowrap="nowrap">{if 'finance_expenses_reasons' == 'projects'}{#finance_expenses_reasons_status_phase#|escape}{else}{$basic_vars_labels.status|default:#finance_expenses_reasons_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.issue_date|default:#finance_expenses_reasons_issue_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.admit_VAT_credit|default:#finance_expenses_reasons_admit_VAT_credit#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.company|default:#finance_expenses_reasons_company#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total_vat|default:#finance_expenses_reasons_total_vat#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.description|default:#finance_expenses_reasons_description#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.fiscal_event_date|default:#finance_expenses_reasons_fiscal_event_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.invoice_num|default:#finance_expenses_reasons_invoice_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.num|default:#finance_expenses_reasons_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.handovered_status|default:#finance_expenses_reasons_handovered_status#|escape}</th>

        </tr>
      {foreach name='i' from=$finance_expenses_reasons item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="16-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total')|escape} {$single->get('currency')|escape}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total_with_vat')|escape} {$single->get('currency')|escape}</td>
          <td nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$finance_expenses_reason_status|escape|default:'&nbsp;' caption=#help_finance_documents_status#|escape width=250}{if !$single->get('annulled_by') && $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'finance', 'expenses_reasons')" style="cursor:pointer;"{/if}
          {/capture}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              
            {else}
              {if $single->get('status') eq 'opened'}
                
              {elseif $single->get('status') eq 'locked'}
                
              {elseif $single->get('status') eq 'finished'}
                
              {/if}
            {/if}
              {$single->get('substatus_name')|escape}
          {else}
            {if $single->get('status') eq 'opened'}
              
            {elseif $single->get('status') eq 'locked'}
              
            {elseif $single->get('status') eq 'finished'}
              
            {/if}
            {capture assign='status_param'}finance_documents_status_{$single->get('status')}{/capture}
            {$smarty.config.$status_param}
          {/if}
          </td>
          <td nowrap="nowrap">{$single->get('issue_date')|date_format:#date_short#|escape}</td>
          <td nowrap="nowrap">
            {if $single->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('company_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total_vat')|escape} {$single->get('currency')|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('description')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {if $single->get('fiscal_event_date') ne 0}
              {if $single->modelName eq 'Finance_Incomes_Reason'}
                {if in_array($single->get('type'), array($smarty.const.PH_FINANCE_TYPE_INVOICE, $smarty.const.PH_FINANCE_TYPE_CREDIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_DEBIT_NOTICE))}
                  {$single->get('fiscal_event_date')|date_format:#date_short#|escape}
                {else}
                  -
                {/if}
              {elseif $single->modelName eq 'Finance_Expenses_Reason'}
                {if in_array($single->get('type'), array($smarty.const.PH_FINANCE_TYPE_EXPENSES_INVOICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))}
                  {$single->get('fiscal_event_date')|date_format:#date_short#|escape}
                {else}
                  -
                {/if}
              {else}
                {$single->get('fiscal_event_date')|date_format:#date_short#|escape}
              {/if}
            {else}
              -
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('invoice_num')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('num')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {capture assign='invoice_status'}finance_handovered_{$single->get('handovered_status')}{/capture}
            {$smarty.config.$invoice_status|escape|default:"&nbsp;"}
          </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="16">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
