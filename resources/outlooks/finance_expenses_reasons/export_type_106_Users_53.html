    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_expenses_reasons_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total|default:#finance_expenses_reasons_total#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.total_with_vat|default:#finance_expenses_reasons_total_with_vat#|escape}</th>
          <th nowrap="nowrap">{if 'finance_expenses_reasons' == 'projects'}{#finance_expenses_reasons_status_phase#|escape}{else}{$basic_vars_labels.status|default:#finance_expenses_reasons_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#finance_expenses_reasons_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.issue_date|default:#finance_expenses_reasons_issue_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.num|default:#finance_expenses_reasons_num#|escape}</th>

        </tr>
      {foreach name='i' from=$finance_expenses_reasons item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="10-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total')|escape} {$single->get('currency')|escape}</td>
          <td style="mso-number-format: \@;" align="right">{$single->get('total_with_vat')|escape} {$single->get('currency')|escape}</td>
          <td nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$finance_expenses_reason_status|escape|default:'&nbsp;' caption=#help_finance_documents_status#|escape width=250}{if !$single->get('annulled_by') && $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'finance', 'expenses_reasons')" style="cursor:pointer;"{/if}
          {/capture}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              
            {else}
              {if $single->get('status') eq 'opened'}
                
              {elseif $single->get('status') eq 'locked'}
                
              {elseif $single->get('status') eq 'finished'}
                
              {/if}
            {/if}
              {$single->get('substatus_name')|escape}
          {else}
            {if $single->get('status') eq 'opened'}
              
            {elseif $single->get('status') eq 'locked'}
              
            {elseif $single->get('status') eq 'finished'}
              
            {/if}
            {capture assign='status_param'}finance_documents_status_{$single->get('status')}{/capture}
            {$smarty.config.$status_param}
          {/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap">{$single->get('issue_date')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('num')|escape|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
