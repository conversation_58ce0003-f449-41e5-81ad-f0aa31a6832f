    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.full_num|default:#tasks_full_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#tasks_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.severity|default:#tasks_severity#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#tasks_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.department|default:#tasks_department#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.owner|default:#tasks_owner#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.planned_start_date|default:#tasks_planned_start_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.planned_finish_date|default:#tasks_planned_finish_date#|escape}</th>
          <th nowrap="nowrap">{#tasks_age#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.exec_time|default:#tasks_exec_time#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#tasks_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.description|default:#tasks_description#|escape}</th>

        </tr>
      {foreach name='i' from=$tasks item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="15-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('full_num')|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          {capture assign='severity_name'}tasks_{$single->get('severity')}{/capture}
          <td style="mso-number-format: \@;">{$smarty.config.$severity_name|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('department_name')|escape|default:"&nbsp;"}</td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td style="mso-number-format: \@;">
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_owner')}
              {foreach from=$single->get('assignments_owner') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {$long_text}
            {else}
              &nbsp;
            {/if}
        </td>
          <td nowrap="nowrap">{$single->get('planned_start_date')|date_format:#date_mid#|escape}</td>
          <td nowrap="nowrap">{$single->get('planned_finish_date')|date_format:#date_mid#|escape}</td>
          <td nowrap="nowrap">
            {$single->get('age_formatted')|escape}
          </td>
          <td nowrap="nowrap">
            {$single->get('exec_time_formatted')|escape}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('description')|escape|nl2br|url2href|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="15">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
