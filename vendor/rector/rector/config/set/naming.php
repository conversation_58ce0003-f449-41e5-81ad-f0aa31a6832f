<?php

declare (strict_types=1);
namespace RectorPrefix202410;

use <PERSON>\Config\RectorConfig;
use <PERSON>\Naming\Rector\Assign\RenameVariableToMatchMethodCallReturnTypeRector;
use <PERSON>\Naming\Rector\Class_\RenamePropertyToMatchTypeRector;
use <PERSON>\Naming\Rector\ClassMethod\RenameParamToMatchTypeRector;
use <PERSON>\Naming\Rector\ClassMethod\RenameVariableToMatchNewTypeRector;
use <PERSON>\Naming\Rector\Foreach_\RenameForeachValueVariableToMatchExprVariableRector;
use <PERSON>\Naming\Rector\Foreach_\RenameForeachValueVariableToMatchMethodCallReturnTypeRector;
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->rules([RenameParamToMatchTypeRector::class, RenamePropertyToMatchTypeRector::class, RenameVariableToMatchNewTypeRector::class, RenameVariableToMatchMethodCallReturnTypeRector::class, RenameForeachValueVariableToMatchMethodCallReturnTypeRector::class, RenameForeachValueVariableToMatchExprVariableRector::class]);
};
