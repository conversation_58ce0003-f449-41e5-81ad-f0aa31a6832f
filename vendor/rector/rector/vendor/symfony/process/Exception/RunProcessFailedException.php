<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace RectorPrefix202410\Symfony\Component\Process\Exception;

use RectorPrefix202410\Symfony\Component\Process\Messenger\RunProcessContext;
/**
 * <AUTHOR> <<EMAIL>>
 */
final class RunProcessFailedException extends RuntimeException
{
    /**
     * @readonly
     * @var \Symfony\Component\Process\Messenger\RunProcessContext
     */
    public $context;
    public function __construct(ProcessFailedException $exception, RunProcessContext $context)
    {
        $this->context = $context;
        parent::__construct($exception->getMessage(), $exception->getCode());
    }
}
