<?php

declare (strict_types=1);
namespace Rector\RectorInstaller;

/**
 * This class is generated by rector/extension-installer.
 * @internal
 */
final class GeneratedConfig
{
    public const EXTENSIONS = array('rector/rector-doctrine' => array('install_path' => '/home/<USER>/work/rector-src/rector-src/rector-build/vendor/rector/rector-doctrine', 'relative_install_path' => '../../rector-doctrine', 'extra' => NULL, 'version' => 'dev-main e75008c'), 'rector/rector-downgrade-php' => array('install_path' => '/home/<USER>/work/rector-src/rector-src/rector-build/vendor/rector/rector-downgrade-php', 'relative_install_path' => '../../rector-downgrade-php', 'extra' => NULL, 'version' => 'dev-main d9cef57'), 'rector/rector-phpunit' => array('install_path' => '/home/<USER>/work/rector-src/rector-src/rector-build/vendor/rector/rector-phpunit', 'relative_install_path' => '../../rector-phpunit', 'extra' => NULL, 'version' => 'dev-main 8fabbb0'), 'rector/rector-symfony' => array('install_path' => '/home/<USER>/work/rector-src/rector-src/rector-build/vendor/rector/rector-symfony', 'relative_install_path' => '../../rector-symfony', 'extra' => NULL, 'version' => 'dev-main 928f117'));
    private function __construct()
    {
    }
}
