<?php

declare (strict_types=1);
namespace Rector\StaticTypeMapper\PhpParser;

use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON><PERSON><PERSON>\Node\Identifier;
use PHPStan\Type\Type;
use <PERSON>\StaticTypeMapper\Contract\PhpParser\PhpParserNodeMapperInterface;
use <PERSON>\StaticTypeMapper\Mapper\ScalarStringToTypeMapper;
/**
 * @implements PhpParserNodeMapperInterface<Identifier>
 */
final class IdentifierNodeMapper implements PhpParserNodeMapperInterface
{
    /**
     * @readonly
     * @var \Rector\StaticTypeMapper\Mapper\ScalarStringToTypeMapper
     */
    private $scalarStringToTypeMapper;
    public function __construct(ScalarStringToTypeMapper $scalarStringToTypeMapper)
    {
        $this->scalarStringToTypeMapper = $scalarStringToTypeMapper;
    }
    public function getNodeType() : string
    {
        return Identifier::class;
    }
    /**
     * @param Identifier $node
     */
    public function mapToPHPStan(Node $node) : Type
    {
        return $this->scalarStringToTypeMapper->mapScalarStringToType($node->name);
    }
}
