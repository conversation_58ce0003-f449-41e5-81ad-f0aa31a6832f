<?php

declare (strict_types=1);
namespace Rector\DeadCode\Rector\Concat;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Expr;
use Php<PERSON><PERSON>er\Node\Expr\BinaryOp\Concat;
use Php<PERSON><PERSON><PERSON>\Node\Expr\Cast\String_;
use <PERSON>\Rector\AbstractRector;
use Symplify\RuleDocGenerator\ValueObject\CodeSample\CodeSample;
use Symplify\RuleDocGenerator\ValueObject\RuleDefinition;
/**
 * @see \Rector\Tests\DeadCode\Rector\Concat\RemoveConcatAutocastRector\RemoveConcatAutocastRectorTest
 */
final class RemoveConcatAutocastRector extends AbstractRector
{
    public function getRuleDefinition() : RuleDefinition
    {
        return new RuleDefinition('Remove (string) casting when it comes to concat, that does this by default', [new CodeSample(<<<'CODE_SAMPLE'
class SomeConcatingClass
{
    public function run($value)
    {
        return 'hi ' . (string) $value;
    }
}
CODE_SAMPLE
, <<<'CODE_SAMPLE'
class SomeConcatingClass
{
    public function run($value)
    {
        return 'hi ' . $value;
    }
}
CODE_SAMPLE
)]);
    }
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes() : array
    {
        return [Concat::class];
    }
    /**
     * @param Concat $node
     */
    public function refactor(Node $node) : ?Node
    {
        if (!$node->left instanceof String_ && !$node->right instanceof String_) {
            return null;
        }
        $node->left = $this->removeStringCast($node->left);
        $node->right = $this->removeStringCast($node->right);
        return $node;
    }
    private function removeStringCast(Expr $expr) : Expr
    {
        return $expr instanceof String_ ? $expr->expr : $expr;
    }
}
