<?php

declare (strict_types=1);
namespace Rector\DeadCode\Rector\ClassConst;

use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON><PERSON><PERSON>\Node\Stmt\ClassConst;
use Php<PERSON><PERSON>er\NodeTraverser;
use PHPStan\Analyser\Scope;
use PHPStan\Reflection\ClassReflection;
use <PERSON>\NodeManipulator\ClassConstManipulator;
use <PERSON>\Rector\AbstractScopeAwareRector;
use <PERSON>\Reflection\ReflectionResolver;
use Symplify\RuleDocGenerator\ValueObject\CodeSample\CodeSample;
use Symplify\RuleDocGenerator\ValueObject\RuleDefinition;
/**
 * @see \Rector\Tests\DeadCode\Rector\ClassConst\RemoveUnusedPrivateClassConstantRector\RemoveUnusedPrivateClassConstantRectorTest
 */
final class RemoveUnusedPrivateClassConstantRector extends AbstractScopeAwareRector
{
    /**
     * @readonly
     * @var \Rector\NodeManipulator\ClassConstManipulator
     */
    private $classConstManipulator;
    /**
     * @readonly
     * @var \Rector\Reflection\ReflectionResolver
     */
    private $reflectionResolver;
    public function __construct(ClassConstManipulator $classConstManipulator, ReflectionResolver $reflectionResolver)
    {
        $this->classConstManipulator = $classConstManipulator;
        $this->reflectionResolver = $reflectionResolver;
    }
    public function getRuleDefinition() : RuleDefinition
    {
        return new RuleDefinition('Remove unused class constants', [new CodeSample(<<<'CODE_SAMPLE'
class SomeClass
{
    private const SOME_CONST = 'dead';

    public function run()
    {
    }
}
CODE_SAMPLE
, <<<'CODE_SAMPLE'
class SomeClass
{
    public function run()
    {
    }
}
CODE_SAMPLE
)]);
    }
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes() : array
    {
        return [ClassConst::class];
    }
    /**
     * @param ClassConst $node
     */
    public function refactorWithScope(Node $node, Scope $scope) : ?int
    {
        if ($this->shouldSkipClassConst($node, $scope)) {
            return null;
        }
        $classReflection = $this->reflectionResolver->resolveClassReflection($node);
        if (!$classReflection instanceof ClassReflection) {
            return null;
        }
        if ($this->classConstManipulator->hasClassConstFetch($node, $classReflection)) {
            return null;
        }
        return NodeTraverser::REMOVE_NODE;
    }
    private function shouldSkipClassConst(ClassConst $classConst, Scope $scope) : bool
    {
        if (!$classConst->isPrivate()) {
            return \true;
        }
        if (\count($classConst->consts) !== 1) {
            return \true;
        }
        $classReflection = $scope->getClassReflection();
        if (!$classReflection instanceof ClassReflection) {
            return \false;
        }
        return $this->hasParentClassOfEnumSuffix($classReflection);
    }
    private function hasParentClassOfEnumSuffix(ClassReflection $classReflection) : bool
    {
        foreach ($classReflection->getParentClassesNames() as $parentClassesName) {
            if (\substr_compare($parentClassesName, 'Enum', -\strlen('Enum')) === 0) {
                return \true;
            }
        }
        return \false;
    }
}
