<?php
namespace Bgservice\NzoomClient\Service;

use Bgservice\NzoomClient\Service\Exception\NotAuthorizedException;
use Bgservice\NzoomClient\Service\Exception\NotJsonResponseException;
use Bgservice\NzoomClient\Service\Exception\NzoomException;
use Bgservice\NzoomClient\Service\Exception\RequestError;
use Bgservice\NzoomClient\Service\Exception\ServerError;
use GuzzleHttp\Exception\GuzzleException;

/**
 * @method sendPost(array $params = [], array $data = [], array $headers = [], array $options = [], int   $flags = NzoomService::POST_DATA_MODE_JSON) : NzoomResponse
 * @method sendGet(array $params = [], array $headers = [], array $options = []) : NzoomResponse
 */
trait CommonRequestsNzoomServiceTrait
{
    /**
     * @throws NotAuthorizedException
     * @throws NzoomException
     * @throws RequestError
     * @throws ServerError|GuzzleException
     */
    public function view(string $module, int $id, array $params=[]): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'view';
        $params['view'] = (string) $id;
        return $this->sendGet($params);
    }

    /**
     * @throws NotAuthorizedException
     * @throws RequestError
     * @throws GuzzleException
     * @throws NzoomException
     * @throws ServerError
     */
    public function getEmpty(string $module, int $typeId, array $params=[]): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'adds';
        $params['operation'] = 'add';
        $params['type'] = $typeId;
        return $this->sendGet($params);
    }

    /**
     * @throws NotAuthorizedException
     * @throws RequestError
     * @throws GuzzleException
     * @throws NzoomException
     * @throws ServerError
     */
    public function add(string $module, int $typeId, array $data, array $params=[], int $flags=self::POST_DATA_MODE_JSON): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'adds';
        $params['operation'] = 'add';
        $params['type'] = $typeId;

        return $this->sendPost($params, $data, [], [], $flags);
    }

    /**
     * Directly send an edit request to Nzoom.
     * @param string $module
     * @param int $id
     * @param array $data
     * @param array $params
     * @param int $flags
     * @return NzoomResponse
     * @throws GuzzleException
     * @throws NotAuthorizedException
     * @throws NzoomException
     * @throws RequestError
     * @throws ServerError
     */
    public function edit(string $module, int $id, array $data, array $params=[], int $flags=self::POST_DATA_MODE_JSON) :NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'edit';
        $params['edit'] = (string) $id;

        $headers =  [
            'Nzoom-Request-Method' => 'patch',
        ];

        return $this->sendPost($params, $data, $headers, [], $flags);
    }

    /**
     * @throws NotAuthorizedException
     * @throws GuzzleException
     * @throws RequestError
     * @throws NzoomException
     * @throws ServerError
     */
    public function setStatus(string $module, int $id, string $status, int $subStatus = 0): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'setstatus';
        $params['setstatus'] = (string) $id;
        $params['view'] = (string) $id;

        $data = [
            'status' => $status,
            'substatus' => "{$status}_{$subStatus}",
        ];

        return $this->sendPost($params, $data);
    }

    /**
     * @param string $module
     * @param SearchCondition $conditions
     * @param array $params
     * @param int $display
     * @param string[]|null $sort
     * @return NzoomResponse
     * @throws NotAuthorizedException
     * @throws NzoomException
     * @throws RequestError
     * @throws ServerError
     * @throws GuzzleException
     */
    public function search(string $module, SearchCondition $conditions, array $params=[], int $display=1000, array $sort=NULL): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'search';
        $params['search_module'] = $module;
        $singularModule = static::plural2singular($module);
        $params['search_'.$singularModule] = 1;
        $params += $conditions->renderFilter();

        $params['display'] = $display;
        if($sort && is_array($sort) && count($sort) == 2) {
            $params['sort'][] = $sort[0];
            $params['order'][] = $sort[1];
        }

        return $this->sendGet($params);
    }

    public function viewFile(string $module, int $modelId, int $fileId, array $params=[]): NzoomResponse
    {
        $params['launch'] = $module;
        $params[$module] = 'viewfile';
        $params['viewfile'] = (string) $modelId;
        $params['file'] = (string) $fileId;
        return $this->sendGet($params);
    }

    /**
     * @throws NotAuthorizedException
     * @throws GuzzleException
     * @throws NzoomException
     * @throws RequestError
     * @throws ServerError
     */
    public function report(string $action, string $reportType, array $params=[]) : ?NzoomResponse
    {
        $params['launch'] = 'reports';
        $params['reports'] = $action;
        $params['report_type'] = $reportType;
        return $this->sendGet($params);
    }
}
